-- 新增 已设支委会标签
INSERT INTO `t_tag`(`parent_id`, `name`, `status`, `tag_type`, `seq`, `org_type`,`organization_id`, `region_id`, `last_change_user`, `belong`, `create_time`, `update_time`)
VALUES (0, '已设置支委', 1, 9, 7, 2, 3, 3, -1, 2, SYSDATE(), SYSDATE());

-- 新增冗余同步主键id
ALTER TABLE `t_user_org_period`
ADD COLUMN `sync_org_period_id` bigint(20) NULL COMMENT 't_org_period 主键id' AFTER `party_affairs_office`;

-- 监督预警定时任务表新增字段
ALTER TABLE `t_user_org_period_result`
ADD COLUMN `has_org_tag` tinyint(3) NOT NULL DEFAULT 2 COMMENT '是否存在支委会标签 1:是 2:否' AFTER `has_tag`;

-- 冗余标签
ALTER TABLE `t_statistical_org_life`
ADD COLUMN `has_period_tag` tinyint(3) NULL COMMENT '是否存在已设支委会标签 1:是 2:否' AFTER `meeting_ids`;

-- 新增支委会人员user_id 索引
ALTER TABLE `t_user_org_period_member`
ADD INDEX `user_id`(`user_id`);

-- 领导班子新增用户id索引
ALTER TABLE `t_user_org_leader`
ADD INDEX `user_id`(`user_id`);

-- 清理原届次和领导班子数据
TRUNCATE TABLE t_org_period;
ALTER TABLE t_org_period MODIFY COLUMN `type` VARCHAR(20) NULL COMMENT '选举方式';
TRUNCATE TABLE t_org_leadergroup_user;

-- 批量修复届次同步时间包含时分秒的问题
update t_user_org_period set start_time=DATE_FORMAT(start_time,'%Y-%m-%d'),end_time=DATE_FORMAT(end_time,'%Y-%m-%d') where is_delete=2;