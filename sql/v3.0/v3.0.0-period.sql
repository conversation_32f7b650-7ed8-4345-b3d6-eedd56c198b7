ALTER TABLE `t_user_org_period_result`
ADD COLUMN `has_tag` tinyint(3) NOT NULL COMMENT '包含标签类型7或6  1:是2:否' AFTER `batch`;

ALTER TABLE `t_user_org_period_result`
MODIFY COLUMN `has_tag` tinyint(3) NOT NULL DEFAULT 2 COMMENT '包含标签类型7或6  1:是2:否' AFTER `batch`;

ALTER TABLE `t_user_org_period_result`
ADD COLUMN `parent_org_name` varchar(255) NULL COMMENT '顶层党委全称' AFTER `parent_id`;

ALTER TABLE `t_user_org_period_result`
ADD COLUMN `period_id` bigint(20) NULL COMMENT '所关联的支委会id' AFTER `result_id`;