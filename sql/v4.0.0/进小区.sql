create table t_community_user
(
    community_id  bigint auto_increment primary key,
    region_id     bigint               not null,
    user_id       bigint               not null comment '用户id',
    user_name     varchar(50)          not null,
    phone         varchar(255)         not null,
    company_id    bigint               null comment '单位ID 如果有',
    company_name  varchar(255)         null comment '单位名称 系统用户记录的组织关联的单位',
    org_id        bigint               null comment '组织id',
    org_name      varchar(255)         null comment '组织名称',
    address       varchar(255)         null,
    status        tinyint(3) default 0 not null comment '数据状态 0:失效 1:有效',
    check_user    bigint               null comment '管理员用户Id',
    join_type     tinyint(3)           not null comment '加入类型  0:未加入(外部用户只编辑了信息) 1:系统用户加入  2:外部用户加入 3:审核未通过退回',
    join_org_id   bigint               null comment '加入的组织id',
    join_org_name varchar(255)         null comment '加入的组织名称',
    create_user   bigint               not null,
    create_time   datetime             not null,
    change_user   bigint               null,
    change_time   datetime             null
)
    comment '进云区用户表';

create index t_community_user
    on t_community_user (region_id, user_id);

INSERT INTO `t_option` (`code`, `op_key`, `op_value`, `seq`, `has_child`) VALUES ('1028', 102814, '小区', NULL, 1);
INSERT INTO t_option(code,op_key,op_value,has_child) VALUES('102814',10281401,'小区组织',0);

INSERT INTO `t_organization` (`parent_id`, `region_id`, `name`, `short_name`, `org_id`, `org_pid`, `org_type`, `org_code`, `org_property`, `org_create_time`, `org_phone`, `org_leader`, `org_leader_phone`, `org_area`, `postcode`, `org_address`, `longitude`, `latitude`, `manager_phone`, `status`, `last_change_user`, `create_time`, `update_time`, `org_type_child`, `industry_type`, `area_code`, `org_unique_code`, `org_contacts`, `seq`, `child_org_num`, `company_num`, `work_num`, `user_num`, `party_member_num`, `org_level`, `activate`, `owner_id`, `third_data_info`, `third_source`, `owner_tree`, `is_retire`, `is_flow`, `is_consistent`, `party_leader`)
VALUES (1, 3, '小区', NULL, NULL, NULL, 102814, NULL, NULL, SYSDATE(), NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, -999, SYSDATE(), SYSDATE(), NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, 0, 0, '-0-1-', 1, NULL, NULL, 1, 2, 2, 2, 2, NULL);

INSERT INTO `t_organization_tree` (`organization_id`, `region_id`, `tree_name`, `tree_type`, `org_type`, `status`, `create_time`, `last_change_user`, `update_time`)
VALUES ((SELECT organization_id FROM t_organization where org_type = 102814 and parent_id = 1), 3, '小区树', 2, 102814, 1, SYSDATE(), -999, SYSDATE());

INSERT INTO `t_role` (`name`, `status`, `role_type`, `org_type`, `corporation_id`, `organization_id`, `region_id`, `last_change_user`, `create_time`, `update_time`, `belong`, `parent_role_id`, `remark`)
VALUES ('小区组织管理员', 1, 14, 2, NULL, (SELECT organization_id FROM t_organization where org_type = 102814 and parent_id = 1), 3, -999, SYSDATE(), SYSDATE(), 1, NULL, '小区组织管理员');
INSERT INTO `t_role` (`name`, `status`, `role_type`, `org_type`, `corporation_id`, `organization_id`, `region_id`, `last_change_user`, `create_time`, `update_time`, `belong`, `parent_role_id`, `remark`)
VALUES ('小区超管', 1, 15, 2, NULL, (SELECT organization_id FROM t_organization where org_type = 102814 and parent_id = 1), 3, -999, SYSDATE(), SYSDATE(), 1, NULL, '小区超管');