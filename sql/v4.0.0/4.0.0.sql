-- 字段表
CREATE TABLE t_field (
     field_id         bigint(20) NOT NULL AUTO_INCREMENT,
     org_tree_id      bigint(20) NOT NULL,
     field_rule_id    bigint(20) NOT NULL,
     own              tinyint(3) NOT NULL comment '所属 1.用户、2.组织',
     field_name       varchar(100) NOT NULL comment '系统生成的字段名称',
     alias            varchar(100) NOT NULL comment '别名（用户输入，与own组成联合索引）',
     is_default       tinyint(3) NOT NULL comment '是否系统默认字段（1-是，2-否，自定义字段）',
     is_show          int(1) comment '是否显示（1-是，2-否）',
     component_type   tinyint(3) NOT NULL comment '组件类型，1.普通文本框;2.密码框;3.时间控件;4.地图控件;5.用户选择器;6.组织选择器;7.字典选择器;8.自定义联动选择器',
     component_id     bigint(20) comment '如果type=8的时候，这里存储关联的自定义联动菜单id',
     form_order       tinyint(3) DEFAULT 0 NOT NULL comment '表单顺序',
     query_type       varchar(255) comment '支持查询的类型，用逗号分隔：1.混合查询框（模糊查询） 2.独立模糊查询，3.独立精确查询，4.独立系统字典选择，5，独立自定义字典选择',
     status           tinyint(3) NOT NULL comment '状态。1.正常,2.不启用单可以计入统计,3.不启用不计入统计',
     create_time      datetime NOT NULL,
     update_time      datetime NULL,
     create_user      bigint(20) NOT NULL,
     last_change_user bigint(20),
     PRIMARY KEY (field_id),
     INDEX (component_id),
     INDEX (form_order),
     INDEX (status)) comment='字段表';

-- 字段历史表
CREATE TABLE t_field_his (
     field_his_id      bigint(20) NOT NULL AUTO_INCREMENT,
     field_id          bigint(20) NOT NULL comment '字段ID',
     before_json       longtext comment '更新前',
     after_json        longtext,
     type              tinyint(1) comment '1-增 2-删 3-改',
     create_user       bigint(20) NOT NULL,
     create_time       datetime NOT NULL,
     PRIMARY KEY (field_his_id),
     INDEX (field_id)) comment='字段变化流水';


-- 查询框配置表
CREATE TABLE t_query_config (
    query_config_id  bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
    field_id         bigint(20) NOT NULL comment '字段ID',
    own              tinyint(3) NOT NULL comment '1-用户列表 2 组织列表',
    level            tinyint(3) NOT NULL comment '1-普通查询 2-高级查询',
    row_order        tinyint(3) NOT NULL comment '行顺序',
    col_order        tinyint(3) NOT NULL comment '列顺序',
    org_tree_id      bigint(20) NOT NULL comment '对应的组织树id',
    status           tinyint(1) comment '状态 1-启用 2-禁用',
    create_time      datetime NOT NULL,
    update_time      datetime NULL,
    create_user      bigint(20) NOT NULL,
    last_change_user bigint(20),
    PRIMARY KEY (query_config_id),
    INDEX (field_id),
    INDEX (org_tree_id)) comment='查询框配置';

-- 表格表头配置表
CREATE TABLE t_table_config (
    table_config_id  bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
    field_id         bigint(20) NOT NULL comment '字段ID',
    org_tree_id      bigint(20) NOT NULL comment '对应的组织树id',
    own              tinyint(3) NOT NULL comment '1-用户列表 2 组织列表',
    table_order      tinyint(3) comment '表头顺序',
    rank_type        tinyint(1) comment '排序方式：1-升序 2-降序',
    rank_order       tinyint(3) comment '排序优先级，越小权重越大',
    status           tinyint(1) comment '状态 1-启用 2-禁用',
    create_time      datetime NOT NULL,
    update_time      datetime NULL,
    create_user      bigint(20) NOT NULL,
    last_change_user bigint(20),
    PRIMARY KEY (table_config_id),
    INDEX (field_id),
    INDEX (org_tree_id)) comment='表格表头顺序配置';

-- 字段规则表
CREATE TABLE t_field_rule (
      field_rule_id    bigint(20) NOT NULL AUTO_INCREMENT,
      rule_type        varchar(127) NOT NULL comment '验证类型(逗号分割):
                                                        RULE_TYPE_ASSOCIATE = 0;// 关联验证（会去找对应的associate）
                                                        RULE_TYPE_REGULAR = 1;// 正则表达式
                                                        RULE_TYPE_CHINESE = 2;// 中文
                                                        RULE_TYPE_INTEGER = 3;// 整数
                                                        RULE_TYPE_FLOAT = 4;// 小数（两位小数位）
                                                        RULE_TYPE_UPPERCASE = 5;// 大写字母
                                                        RULE_TYPE_LOWERCASE = 6; // 小写字母
                                                        RULE_TYPE_LENGTH = 7;// 长度验证，具体验证值需要通过maxLength和minLength处理
                                                        RULE_TYPE_NUMBER_SIZE = 8;// 数字验证，具体验证值需要通过max和min处理
                                                        RULE_TYPE_DATE = 9;// 日期类型，可以自定义验证格式 dateFormat（如果为空，使用内建格式验证）',
      message          varchar(255) comment '默认错误提示信息',
      required         tinyint(1) NOT NULL comment '是否必填，0:否，1:是',
      single         int(1) comment '是否唯一（1-是，0-否）',
      regular          text comment '正则表达式',
      max_length       bigint(20) comment '最大长度',
      min_length       int(11) comment '最小长度',
      max_value        bigint(20) comment '最大值',
      min_value        bigint(20) comment '最小值',
      associate        bigint(20),
      associate_oper   char(1) comment 'associate操作符(''='',''>'',''≥'',''<'',''≤'',''≠'')，associate存在时才有用',
      date_format      text comment '日期类型的验证格式，支持逗号分割的多个',
      create_time      datetime NOT NULL,
      update_time      datetime NULL,
      create_user      bigint(20) NOT NULL,
      last_change_user bigint(20),
      PRIMARY KEY (field_rule_id),
      INDEX (associate)) comment='字段验证表';

-- 组件表
CREATE TABLE t_component (
     component_id        bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
     name                varchar(50) NOT NULL comment '组件名称',
     code                tinyint(1) NOT NULL comment '组件编码：1.系统生成 2.自定义',
     link_num            tinyint(1) comment '组件引用次数',
     level               tinyint(1) NOT NULL comment '显示层次，最高能显示5级',
     option_code         varchar(10) comment '关联t_option表中的code',
     component_option_id bigint(20),
     status              tinyint(1) NOT NULL comment '状态 1-可用 2-禁用',
     create_time         datetime NOT NULL,
     create_user         bigint(20) NOT NULL,
     last_change_user    bigint(20),
     update_time         datetime NULL,
     PRIMARY KEY (component_id),
     INDEX (option_code),
     INDEX (component_option_id)) comment='组件';

-- 动态值
CREATE TABLE t_field_value (
  field_value_id   bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  field_id         bigint(20) NOT NULL comment '字段ID',
  field_name       varchar(100) NOT NULL comment '字段名称',
  field_value      text NOT NULL comment '字段内容',
  source_id        bigint(20),
  source_type      tinyint(3) comment '1-用户 2-组织',
  own_org_id       bigint(20) comment '所属组织id，考虑一个人可以属于多个组织',
  org_tree_id      bigint(20) NOT NULL comment '组织树ID',
  create_time      datetime NOT NULL,
  update_time      datetime NULL,
  create_user      bigint(20) NOT NULL,
  last_change_user bigint(20),
  PRIMARY KEY (field_value_id));

create index idx_time on t_corp_hs(corp_id,create_time);

ALTER TABLE `t_user_third`
    CHANGE COLUMN `nick_name` `nick_name` BLOB NULL DEFAULT NULL COMMENT '昵称' ;
