CREATE TABLE `t_role_menu_tmp` (
   `role_id` bigint(20) NOT NULL COMMENT '角色ID',
   `menu_id` varchar(20) NOT NULL COMMENT '菜单ID',
   `region_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '区域ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='角色菜单关联表';

INSERT into t_role_menu_tmp
select role_id, menu_id, region_id from t_role_menu GROUP BY role_id, menu_id, region_id HAVING count(0) > 1;

INSERT into t_role_menu_tmp
select role_id, menu_id, region_id from t_role_menu GROUP BY role_id, menu_id, region_id HAVING count(0) = 1;

TRUNCATE TABLE t_role_menu;

INSERT INTO t_role_menu
select role_id, menu_id, region_id from t_role_menu_tmp;

DROP TABLE t_role_menu_tmp;

