ALTER TABLE t_menu ADD COLUMN `seq` int(10) comment '顺序';

UPDATE t_menu SET `seq` = 100;
INSERT INTO t_menu (menu_id, parent_id, name, status, create_time, update_time)
VALUES
  ('11','0','新闻中心',1,SY<PERSON><PERSON><PERSON>(),SYSDATE()),
  ('1101','11','新闻发布',1,SY<PERSON><PERSON><PERSON>(),SYSDATE()),
  ('1102','11','新闻管理',1,SYSDATE(),SYSDATE()),
  ('1103','11','新闻栏目',1,SYSDATE(),SYSDATE());

UPDATE t_menu SET `seq` = 1 WHERE `menu_id` = '04';
UPDATE t_menu SET `seq` = 2 WHERE `menu_id` = '0401';
UPDATE t_menu SET `seq` = 3 WHERE `menu_id` = '0402';
UPDATE t_menu SET `seq` = 4 WHERE `menu_id` = '01';
UPDATE t_menu SET `seq` = 5 WHERE `menu_id` = '0112';
UPDATE t_menu SET `seq` = 6 WHERE `menu_id` = '0102';
UPDATE t_menu SET `seq` = 7 WHERE `menu_id` = '08';
UPDATE t_menu SET `seq` = 8 WHERE `menu_id` = '0801';
UPDATE t_menu SET `seq` = 9 WHERE `menu_id` = '0802';
UPDATE t_menu SET `seq` = 10 WHERE `menu_id` = '0803';
UPDATE t_menu SET `seq` = 11 WHERE `menu_id` = '11';
UPDATE t_menu SET `seq` = 12 WHERE `menu_id` = '1101';
UPDATE t_menu SET `seq` = 13 WHERE `menu_id` = '1102';
UPDATE t_menu SET `seq` = 14 WHERE `menu_id` = '1103';
UPDATE t_menu SET `seq` = 15 WHERE `menu_id` = '02';
UPDATE t_menu SET `seq` = 16 WHERE `menu_id` = '0201';
UPDATE t_menu SET `seq` = 17 WHERE `menu_id` = '020101';
UPDATE t_menu SET `seq` = 18 WHERE `menu_id` = '020102';
UPDATE t_menu SET `seq` = 19 WHERE `menu_id` = '020103';
UPDATE t_menu SET `seq` = 20 WHERE `menu_id` = '020104';
UPDATE t_menu SET `seq` = 21 WHERE `menu_id` = '0202';
UPDATE t_menu SET `seq` = 22 WHERE `menu_id` = '0203';
UPDATE t_menu SET `seq` = 23 WHERE `menu_id` = '0204';
UPDATE t_menu SET `seq` = 24 WHERE `menu_id` = '020401';
UPDATE t_menu SET `seq` = 25 WHERE `menu_id` = '020402';
UPDATE t_menu SET `seq` = 26 WHERE `menu_id` = '020403';
UPDATE t_menu SET `seq` = 27 WHERE `menu_id` = '020404';
UPDATE t_menu SET `seq` = 28 WHERE `menu_id` = '09';
UPDATE t_menu SET `seq` = 29 WHERE `menu_id` = '0901';
UPDATE t_menu SET `seq` = 30 WHERE `menu_id` = '0902';
UPDATE t_menu SET `seq` = 31 WHERE `menu_id` = '10';
UPDATE t_menu SET `seq` = 32 WHERE `menu_id` = '1001';
UPDATE t_menu SET `seq` = 33 WHERE `menu_id` = '1002';
UPDATE t_menu SET `seq` = 34 WHERE `menu_id` = '1003';
UPDATE t_menu SET `seq` = 35 WHERE `menu_id` = '07';
UPDATE t_menu SET `seq` = 36 WHERE `menu_id` = '0109';
UPDATE t_menu SET `status` = 2 WHERE `menu_id` = '0113';

ALTER TABLE t_user ADD COLUMN `is_edit` int(10) DEFAULT 1 COMMENT '是否允许更新 1-是 2-否';

CREATE TABLE `t_user_update_his` (
  `his_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `old_org_id` VARCHAR (50) DEFAULT NULL COMMENT '原组织ID',
  `new_org_id` VARCHAR (50) DEFAULT NULL COMMENT '现组织ID',
  `create_time` datetime NOT NULL COMMENT '变更时间',
  PRIMARY KEY (`his_id`)) COMMENT='用户更新流水表'

