CREATE TABLE t_last_time (
    last_time_id bigint(20) NOT NULL AUTO_INCREMENT,
    user_last_time datetime comment '用户更新时间',
    org_last_time datetime comment '组织更新时间',
    PRIMARY KEY (last_time_id)
) comment='12371数据同步最后更新时间表';

insert into t_last_time(last_time_id) values (1);

ALTER TABLE `t_user` ADD COLUMN birthplace VARCHAR(50) comment '籍贯';
ALTER TABLE `t_user` ADD COLUMN joining_time VARCHAR(20) comment '入党日期';
ALTER TABLE `t_user` ADD COLUMN is_retire tinyint(4) comment '是否退休';

--线网管理员添加 上传资源 权限
INSERT INTO t_role (name, status, role_type, last_change_user, create_time, update_time, org_type,organization_id) VALUES
('系统超级管理员权限', 1, 3, 1, SYSDATE(),SYSDATE(),2,3);
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '01');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0101');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0102');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010201');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010202');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010203');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0103');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '03');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010301');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010302');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0104');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0105');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010501');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010502');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0106');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0107');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0108');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0109');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010901');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '010902');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0111');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '011101');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '011102');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '02');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0201');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020101');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020102');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020103');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020104');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0202');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0203');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0204');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020401');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020402');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020403');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020404');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0205');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020501');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020502');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '020503');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '03');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0301');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0302');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0303');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '04');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0401');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0402');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '05');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '06');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0601');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '07');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '08');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0801');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0802');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '09');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0901');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0902');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '10');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '1001');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '1002');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '1003');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0112');
INSERT INTO `gs_ows_szf_online`.`t_role_menu` (`role_id`, `menu_id`) VALUES ('1039', '0113');


ALTER TABLE `t_user` ADD INDEX cert_number_index (cert_number);
ALTER TABLE `t_user` ADD INDEX password_index (password);
ALTER TABLE `t_user_third` ADD INDEX th_token_index (th_token);
