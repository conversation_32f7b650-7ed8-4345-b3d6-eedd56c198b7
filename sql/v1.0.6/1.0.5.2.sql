DELIMITER ;;
CREATE DEFINER=`gszw_owsszf`@`%` FUNCTION `findTopOrgName`(`orgId` bigint) RETURNS varchar(4000) CHARSET utf8mb4
    COMMENT '根据传入组织Id 查询3下面的顶层党委组织名称并返回'
BEGIN
	#Routine body goes here...
	DECLARE tempOrgId BIGINT;
	DECLARE tempOrgName VARCHAR(500);

	-- 初始化定义传入的组织id
	SET tempOrgId=orgId;
	SET tempOrgName=NULL;


	-- 开始递归循环往上查询
	WHILE true DO

		-- 根据传入orgId 查询
		select `name`,parent_id INTO tempOrgName,tempOrgId  from t_organization where  organization_id=tempOrgId and `status`=1;

		-- 判断上级组织是否为3 如果是直接返回
		if tempOrgId =3 THEN

			RETURN tempOrgName;
		-- 为避免无限循环 判断是否已经遍历到了比3还小的组织 如果是的话说明没有找到 直接返回''
		else IF tempOrgId <=3 THEN
			return '';
		-- 为避免无限循环 用于判断是否 查询到数据 如果没有任何数据 说明传入参数或数据库数据错误
		ELSE IF tempOrgId is null or tempOrgName is null THEN
			return '';

		END IF;

		END IF;

		END IF;

	END WHILE;
END ;;
DELIMITER ;