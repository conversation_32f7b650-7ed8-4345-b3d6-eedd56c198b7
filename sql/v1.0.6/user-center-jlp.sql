TRUNCATE TABLE t_user;
INSERT INTO t_user (`name`, `password`, phone, `status`,create_time, update_time)
VALUES ('九龙坡超级管理员','e10adc3949ba59abbe56e057f20f883e','18600000000',1,SYSDATE(),SYSDATE());

TRUNCATE TABLE t_role;
INSERT INTO t_role (`name`, `status`,role_type,org_type,organization_id,create_time, update_time)
VALUES ('默认权限',1,1,2,2,SYSDATE(),SYSDATE());

#超级权限ID=1，九龙坡管理员ID=2
TRUNCATE TABLE t_role_menu;
INSERT INTO t_role_menu VALUES (1,'01'),(1,'0101'),(1,'0102'),(1,'010201'),(1,'010202'),(1,'010203'),(1,'0103'),(1,'03'),(1,'010301'),
  (1,'010302'),(1,'0104'),(1,'0105'),(1,'010501'),(1,'010502'),(1,'0106'),(1,'0107'),(1,'0108'),(1,'0109'),(1,'010901'),(1,'010902'),
  (1,'0110'),(1,'0111'),(1,'011101'),(1,'011102'),(1,'02'),(1,'0201'),(1,'020101'),(1,'020102'),(1,'020103'),(1,'020104'),(1,'0202'),(1,'0203'),(1,'0204'),(1,'020401'),(1,'020402'),
  (1,'020403'),(1,'020404'),(1,'0205'),(1,'020501'),(1,'020502'),(1,'020503'),(1,'03'),(1,'0301'),(1,'0302'),(1,'0303');

TRUNCATE TABLE t_corporation;
INSERT INTO t_corporation (`name`, address, `status`, extra_number, staff_number,corp_type,create_time, update_time)
  VALUES ('root单位','单位地址',1,0,0,1,SYSDATE(),SYSDATE());

TRUNCATE TABLE t_organization;
INSERT INTO t_organization (parent_id, `name`, `status`,create_time, update_time)
  VALUES (0, 'root',1,SYSDATE(),SYSDATE()),(1, '重庆市九龙坡总工会',1,SYSDATE(),SYSDATE());

#user_id为超级管理员ID，role_id为超级权限ID，organization_id组织ID
TRUNCATE TABLE t_user_role;
INSERT INTO t_user_role (user_id,role_id,organization_id) VALUES (1,1,2);

TRUNCATE TABLE t_tag;
INSERT INTO t_tag (parent_id, `name`, `status`, tag_type,org_type,organization_id,create_time, update_time)
  VALUES (0,'人员标签',1,1,2,2,SYSDATE(),SYSDATE());
#parent_id为 人员标签 的ID
INSERT INTO t_tag (parent_id, `name`, `status`, tag_type,org_type,organization_id,create_time, update_time,seq)
  SELECT tag_id,'党员',1,1,2,2,SYSDATE(),SYSDATE(),1 from t_tag WHERE `name` = '人员标签' and parent_id = 0 and tag_type = 1;

#九龙坡管理员
TRUNCATE TABLE t_user_org_corp;
INSERT INTO t_user_org_corp (user_id, organization_id, score) VALUES (1,2,0);

#九龙坡root部门
TRUNCATE TABLE t_department;
INSERT INTO t_department(parent_id,organization_id,dep_type,is_root,`name`,`status`,staff_number,create_time,update_time)
  VALUES (0,2,2,1,'九龙坡root部门',1,0,SYSDATE(),SYSDATE());

TRUNCATE TABLE t_user_department;
INSERT INTO t_user_department(user_id, department_id, duty)
  VALUES (1,1,9);

TRUNCATE TABLE t_user_login_log;
TRUNCATE TABLE t_user_third;







INSERT INTO t_user (name, password, phone, status,create_time, update_time)
  VALUES ('唐礼林', 'e10adc3949ba59abbe56e057f20f883e', '15086646655', 1,SYSDATE(),SYSDATE()),
  ('许力多', 'e10adc3949ba59abbe56e057f20f883e', '18623558132', 1,SYSDATE(),SYSDATE()),
  ('黄康杰','e10adc3949ba59abbe56e057f20f883e', '15283771727', 1,SYSDATE(),SYSDATE()),
  ('曾竞','e10adc3949ba59abbe56e057f20f883e', '15922989373', 1,SYSDATE(),SYSDATE()),
  ('吕诃','e10adc3949ba59abbe56e057f20f883e', '13594070504', 1,SYSDATE(),SYSDATE()),
  ('陈安顺','e10adc3949ba59abbe56e057f20f883e', '17388218553', 1,SYSDATE(),SYSDATE()),
  ('李晓','e10adc3949ba59abbe56e057f20f883e', '18983229761', 1,SYSDATE(),SYSDATE()),
  ('李英劼','e10adc3949ba59abbe56e057f20f883e', '15223319055', 1,SYSDATE(),SYSDATE()),
  ('龙焘','e10adc3949ba59abbe56e057f20f883e', '18580209786', 1,SYSDATE(),SYSDATE()),
  ('李波', 'e10adc3949ba59abbe56e057f20f883e', '18166566250', 1,SYSDATE(),SYSDATE());

INSERT INTO t_user_org_corp (user_id, organization_id, score)
  VALUES (2,2,0),(3,2,0),(4,2,0),(5,2,0),(6,2,0),(7,2,0),(8,2,0),(9,2,0),(10,2,0),(11,2,0);
INSERT INTO t_user_role (user_id,role_id,organization_id)
  VALUES (2,1,2),(3,1,2),(4,1,2),(5,1,2),(6,1,2),(7,1,2),(8,1,2),(9,1,2),(10,1,2),(11,1,2);
INSERT INTO t_user_department VALUES (2,2,1),(3,2,1),(4,2,1),(5,2,1),(6,2,1),(7,2,1),(8,2,1),(9,2,1),(10,2,1),(11,2,1);


INSERT INTO t_user (`name`, `password`, phone, `status`,create_time, update_time) VALUES ('九龙坡超级管理员','e10adc3949ba59abbe56e057f20f883e','18600000000',1,SYSDATE(),SYSDATE());

INSERT INTO t_user_org_corp (user_id, organization_id, score) VALUES (3,2,0);
UPDATE t_user_org_crop set organization_id = 1 where `user_id` = 1;
INSERT INTO t_user_role (user_id,role_id,organization_id) VALUES (3,1,2);
INSERT INTO t_user_department(user_id, department_id, duty) VALUES (3,1,9);












































