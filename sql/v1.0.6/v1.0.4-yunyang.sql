
update t_role set role_type = 2,last_change_user = 52676,update_time = SYSDATE() where role_id in (1,2);

INSERT INTO t_menu (menu_id, parent_id, `name`, status, create_time, update_time,seq,belong)
VALUES ('21','pc01','考核管理',1,SY<PERSON>AT<PERSON>(),SYSDATE(),100,1),
('2101','21','考核任务管理',1,SYSDATE(),SYSDATE(),100,1),
('2102','21','任务执行',1,SYSDATE(),SYSDATE(),100,1),
('2103','21','任务审核',1,SYSDATE(),SYSDATE(),100,1),
('2104','21','问题查处',1,SYSDATE(),SYSDATE(),100,1),
('2105','21','任务抽查',1,SY<PERSON><PERSON><PERSON>(),SYSDATE(),100,1),
('2106','21','考核统计',1,<PERSON>Y<PERSON><PERSON><PERSON>(),SYSDATE(),100,1),
('2001','20','考核设置',1,SYSDATE(),SYSDATE(),100,1),
('200101','2001','考核分值管理',1,SYSDATE(),SYSDATE(),100,1),
('200102','2001','考核组管理',1,SYSDATE(),SYSDATE(),100,1),
('0403','04','市管领导班子成员设置',1,SYSDATE(),SYSDATE(),100,1);

-- 给高级管理员添加考核相关权限
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'21' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'2101' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'2102' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'2103' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'2106' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'20' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'2001' from t_role WHERE role_type = 3 and belong = 1;
insert into t_role_menu (role_id,menu_id)
SELECT role_id,'200102' from t_role WHERE role_type = 3 and belong = 1;

INSERT INTO `t_role_menu` (`role_id`, `menu_id`) VALUES (1, '14'),(1, '1401'),(1, '1402'),(1, '1403'),
(1,'21'),(1,'2101'),(1,'2102'),(1,'2103'),(1,'2104'),(1,'2106'),
(1,'20'),(1,'2001'),(1,'200102'),(1,'200101');