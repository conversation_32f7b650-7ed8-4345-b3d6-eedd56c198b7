alter table t_organization add column `is_flow` TINYINT(3) DEFAULT 2 COMMENT '是否流动党员党组织 1-是 2-否';
alter table t_organization add column `is_consistent` TINYINT(3) DEFAULT 1 COMMENT '组织关联单位是否与上级组织保持一致 1-是 2-否';
update t_organization set is_consistent = 2 where `status` = 1 and parent_id <> 2 and owner_id is not null;

-- 以下为存储过程
CREATE PROCEDURE `update_org_owner`()
BEGIN
	DECLARE orgId BIGINT DEFAULT NULL;
	DECLARE parentId BIGINT DEFAULT NULL;
	DECLARE departmentId BIGINT DEFAULT NULL;
	DECLARE loopOrgId BIGINT DEFAULT NULL;
	DECLARE loopParentId BIGINT DEFAULT NULL;
	DECLARE isSuccess INT DEFAULT FALSE;
	DECLARE i INT DEFAULT 1;

  -- 遍历数据结束标志
  DECLARE done INT DEFAULT FALSE;
	-- user游标
	DECLARE org_data CURSOR FOR
		SELECT organization_id, parent_id FROM t_organization where status = 1 and parent_id <> 2 and owner_id is null;
	-- 将结束标志绑定到游标
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

	-- 打开user游标
	OPEN org_data;

	-- 遍历
	org_loop : LOOP
		-- 循环游标中的数据，并赋值到变量中
		FETCH org_data INTO orgId, parentId;
		-- 判断结束标识
		IF done THEN
			LEAVE org_loop;
		END IF;

		-- 循环查询上级组织的挂靠单位
		label : LOOP
			-- 判断结束标识
			IF isSuccess THEN
				LEAVE label;
			END IF;
			-- 查询当前组织上级组织是否挂靠单位
			SELECT organization_id, parent_id, owner_id
			INTO loopOrgId, parentId, departmentId
			FROM t_organization WHERE organization_id = parentId;
			-- 判断是否存在挂靠单位
			IF departmentId IS NOT NULL THEN
				SET isSuccess = TRUE;
			END IF;
			-- 判断是否不存在上级组织
			IF parentId = 0 THEN
				SET isSuccess = TRUE;
			END IF;
			-- 判断是否不存在上级组织
			IF parentId = -999 THEN
				SET isSuccess = TRUE;
			END IF;
		END LOOP label;
		UPDATE t_organization SET owner_id = departmentId where organization_id = orgId;
		-- 重新打开结束标识
    SET done = FALSE;
		SET i = i + 1;
		SET orgId = NULL;
		SET parentId = NULL;
		SET departmentId = NULL;
		SET loopOrgId = NULL;
		SET loopParentId = NULL;
		SET isSuccess = FALSE;
	END LOOP org_loop;

	-- 关闭user游标
	CLOSE org_data;
END