DROP TABLE IF EXISTS `t_user_org_corp_bak`;
CREATE TABLE t_user_org_corp_bak SELECT * FROM t_user_org_corp;

TRUNCATE TABLE t_user_org_corp_bak;

ALTER TABLE `t_user_org_corp_bak` ADD COLUMN user_org_corp_id BIGINT(20) AUTO_INCREMENT PRIMARY KEY;

INSERT INTO t_user_org_corp_bak(`user_id`,`corporation_id`,`organization_id`,`score`,`tag_id`,`position`,`member`,`work_status`,`create_time`,`update_time`,`job_number`,`youth_league`,`communist`,`union_member`,`women_league`,`email`,`entry_date`,`job_grade`,`last_change_user`)
SELECT  `user_id`,`corporation_id`,`organization_id`,`score`,`tag_id`,`position`,`member`,`work_status`,`create_time`,`update_time`,`job_number`,`youth_league`,`communist`,`union_member`,`women_league`,`email`,`entry_date`,`job_grade`,`last_change_user`
FROM t_user_org_corp;

DROP TABLE IF EXISTS `t_user_org_corp_del_id`;
CREATE TABLE t_user_org_corp_del_id
(SELECT a.user_org_corp_id
FROM
	t_user_org_corp_bak a
WHERE
	(a.organization_id,
		a.user_id) IN (
		SELECT
			organization_id,
			user_id
		FROM
			t_user_org_corp_bak
		GROUP BY
			organization_id,
			user_id
		HAVING
			COUNT(*) > 1
	)
AND user_org_corp_id NOT IN (
	SELECT
		min(user_org_corp_id)
	FROM
		t_user_org_corp_bak
	GROUP BY
		organization_id,
			user_id
		HAVING
			COUNT(*) > 1
));

DELETE FROM t_user_org_corp_bak WHERE user_org_corp_id IN (SELECT user_org_corp_id FROM t_user_org_corp_del_id);

DROP TABLE t_user_org_corp_del_id;

DROP TABLE IF EXISTS `t_user_org_corp_bak1`;
CREATE TABLE t_user_org_corp_bak1 SELECT * FROM t_user_org_corp;

TRUNCATE TABLE t_user_org_corp;

INSERT INTO t_user_org_corp(`user_id`,`corporation_id`,`organization_id`,`score`,`tag_id`,`position`,`member`,`work_status`,`create_time`,`update_time`,`job_number`,`youth_league`,`communist`,`union_member`,`women_league`,`email`,`entry_date`,`job_grade`,`last_change_user`)
SELECT `user_id`,`corporation_id`,`organization_id`,`score`,`tag_id`,`position`,`member`,`work_status`,`create_time`,`update_time`,`job_number`,`youth_league`,`communist`,`union_member`,`women_league`,`email`,`entry_date`,`job_grade`,`last_change_user`
FROM t_user_org_corp_bak;

DROP TABLE t_user_org_corp_bak;

-- ALTER TABLE `t_user_org_corp` ADD CONSTRAINT user_org_id UNIQUE (`user_id`, `organization_id`);