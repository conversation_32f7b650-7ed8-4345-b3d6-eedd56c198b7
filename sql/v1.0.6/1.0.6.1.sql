-- t_tag 新增belong字段
ALTER TABLE `t_tag` ADD COLUMN `belong` int(2) NULL COMMENT '标签所属，1-人员标签，2-组织标签' AFTER `last_change_user`;
UPDATE t_tag SET belong=1 WHERE tag_type = 1;
UPDATE t_tag SET belong=1 WHERE tag_type = 3;
UPDATE t_tag SET belong=1 WHERE tag_type = 4;
UPDATE t_tag SET belong=1 WHERE tag_type = 5;

-- 新增默认组织考核、管理标签
INSERT INTO `t_tag`(`parent_id`, `name`, `status`, `tag_type`, `seq`, `org_type`, `corporation_id`, `organization_id`, `last_change_user`, `belong`, `create_time`, `update_time`) VALUES (0, '考核组织', 1, 6, NULL, 2, NULL, 0, -999, 2, SYSDATE(), SYSDATE());
INSERT INTO `t_tag`(`parent_id`, `name`, `status`, `tag_type`, `seq`, `org_type`, `corporation_id`, `organization_id`, `last_change_user`, `belong`, `create_time`, `update_time`) VALUES (0, '管理组织', 1, 7, NULL, 2, NULL, 0, -999, 2, SYSDATE(), SYSDATE());

-- 新增标签与组织的关联关系
CREATE TABLE `t_org_tag` (
  `org_tag_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `org_id` bigint(20) NOT NULL comment '组织ID',
  `tag_id` bigint(20) NOT NULL comment '标签ID',
  PRIMARY KEY (`org_tag_id`),
  KEY `org_id_idx` (`org_id`) USING BTREE,
  KEY `tag_id_idx` (`tag_id`) USING BTREE
) comment='组织标签关联表';
-- 移动原t_tag的数据到t_org_tag
insert t_org_tag(org_id, tag_id) select organization_id, (select tag_id from t_tag where organization_id = 0 and tag_type = 6) from t_tag where tag_type = 6 and organization_id <> 0 and status = 1;

-- 删除原t_tag的数据
DELETE from t_tag where organization_id <> 0 and tag_type = 6;

-- 新增button表
CREATE TABLE t_button (
  button_id   varchar(20) NOT NULL comment '主键',
  menu_id     varchar(20) NOT NULL comment '菜单ID',
  name        varchar(100) NOT NULL comment '按钮名称',
  url         varchar(100) NULL comment '请求url',
  create_time datetime NOT NULL comment '创建时间',
  update_time datetime NULL comment '更新时间',
  CONSTRAINT button_id
    PRIMARY KEY (button_id)) comment='按钮表';

-- 新增角色和按钮关联关系表
CREATE TABLE t_role_button (
  role_button_id bigint(20) NOT NULL AUTO_INCREMENT comment '主键',
  role_id        bigint(20) NOT NULL comment '角色ID',
  button_id      varchar(20) NOT NULL comment '按钮ID',
  PRIMARY KEY (role_button_id)) comment='角色和按钮关联关系表';

INSERT INTO t_button(button_id, menu_id, `name`, url, create_time, update_time) VALUES('org_add_tag', '0407', '添加标签', '/org/tag/insert', SYSDATE(), SYSDATE());
INSERT INTO t_button(button_id, menu_id, `name`, url, create_time, update_time) VALUES('org_del_tag', '0407', '删除标签', '/org/tag/delete', SYSDATE(), SYSDATE());

INSERT INTO `t_role_button`(`role_id`, `button_id`) VALUES ((select role_id from t_role where name = '系统运维'), 'org_add_tag');
INSERT INTO `t_role_button`(`role_id`, `button_id`) VALUES ((select role_id from t_role where name = '系统运维'), 'org_del_tag');

-- 组织标签管理功能
INSERT INTO t_menu (menu_id, parent_id, `name`, status, create_time, update_time,seq,belong)
VALUES ('0407','04','组织标签管理',1,SYSDATE(),SYSDATE(),100,1),
('1507','15','党费对账报表',1,SYSDATE(),SYSDATE(),100,1),
('1508','15','党费交纳流水',1,SYSDATE(),SYSDATE(),100,1);

-- 党组织委员会参数
INSERT INTO t_menu (menu_id, parent_id, `name`, status, create_time, update_time,seq,belong)
VALUES ('120401','1204','党组织委员会参数',1,SYSDATE(),SYSDATE(),100,1);

