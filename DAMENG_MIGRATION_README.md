# 达梦数据库迁移说明 - MySQL到达梦数据库完全切换

## 概述

本项目已成功实现从MySQL数据库到达梦数据库的完全迁移。通过使用MyBatis拦截器技术，实现了SQL语法的自动转换，使得原有的mapper代码无需任何修改即可在达梦数据库上正常运行。

## 主要特性

- ✅ **完全迁移**: 系统完全切换到达梦数据库，不再使用MySQL
- ✅ **无侵入式**: 原有mapper代码无需任何修改
- ✅ **自动SQL转换**: 通过MyBatis拦截器自动将MySQL语法转换为达梦兼容语法
- ✅ **实时转换**: 运行时动态转换SQL语句
- ✅ **完整测试**: 提供完整的测试API接口

## 数据库配置

### 达梦数据库配置
```yaml
sys-dm-db:
  db-url: 192.168.20.180:5236
  db-name: GS_OWS_ZY_TEST
  db-user: SYSDAB
  db-password: Dameng@123456
```

### Spring数据源配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    username: ${sys-dm-db.db-user}
    url: jdbc:log4jdbc:dm://${sys-dm-db.db-url}/${sys-dm-db.db-name}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8
    password: ${sys-dm-db.db-password}
    driver-class-name: net.sf.log4jdbc.sql.jdbcapi.DriverSpy
```

## 核心组件

### 1. SqlCompatibilityUtil (SQL兼容性工具类)
- 位置: `src/main/java/com/goodsogood/ows/util/SqlCompatibilityUtil.java`
- 功能: 处理MySQL到达梦的SQL语法转换
- 支持的转换规则:
  - `LIMIT offset, count` → `LIMIT count OFFSET offset`
  - 反引号 `` `table` `` → `table`
  - `NOW()` → `SYSDATE`
  - `IFNULL(a, b)` → `NVL(a, b)`
  - `UNIX_TIMESTAMP()` → `EXTRACT(EPOCH FROM SYSDATE)`
  - `DATE_FORMAT(date, format)` → `TO_CHAR(date, format)`
  - `AUTO_INCREMENT` → `IDENTITY(1,1)`

### 2. SqlConversionAspect (SQL转换拦截器)
- 位置: `src/main/java/com/goodsogood/ows/aspect/SqlConversionAspect.java`
- 功能: MyBatis拦截器，自动转换SQL语句
- 拦截所有的查询和更新操作

### 3. SqlConversionConfig (SQL转换配置)
- 位置: `src/main/java/com/goodsogood/ows/configuration/SqlConversionConfig.java`
- 功能: 注册SQL转换拦截器到MyBatis

### 4. DamengConnectionTester (连接测试器)
- 位置: `src/main/java/com/goodsogood/ows/component/DamengConnectionTester.java`
- 功能: 应用启动时自动测试达梦数据库连接

## API接口

### 达梦数据库测试接口
基础路径: `/dameng-test`

#### 1. 测试数据库连接
```
GET /dameng-test/connection
```
返回达梦数据库连接状态和详细信息

#### 2. 执行查询测试
```
GET /dameng-test/query
```
执行简单的数据库查询测试

#### 3. 测试SQL转换
```
POST /dameng-test/sql-conversion
Content-Type: application/json

{
  "sql": "SELECT * FROM users LIMIT 10, 20"
}
```
测试SQL语法转换功能

#### 4. 获取转换示例
```
GET /dameng-test/conversion-examples
```
获取常见的SQL转换示例

#### 5. 获取系统信息
```
GET /dameng-test/system-info
```
获取达梦数据库系统详细信息

## SQL转换规则

### 1. LIMIT语法转换
```sql
-- MySQL
SELECT * FROM users LIMIT 10, 20;

-- 达梦 (转换后)
SELECT * FROM users LIMIT 20 OFFSET 10;
```

### 2. 反引号转换
```sql
-- MySQL
SELECT `name`, `age` FROM `users`;

-- 达梦 (转换后)
SELECT name, age FROM users;
```

### 3. 时间函数转换
```sql
-- MySQL
SELECT NOW(), UNIX_TIMESTAMP(), DATE_FORMAT(created_at, '%Y-%m-%d');

-- 达梦 (转换后)
SELECT SYSDATE, EXTRACT(EPOCH FROM SYSDATE), TO_CHAR(created_at, 'YYYY-MM-DD');
```

### 4. 字符串函数转换
```sql
-- MySQL
SELECT IFNULL(name, 'Unknown') FROM users;

-- 达梦 (转换后)
SELECT NVL(name, 'Unknown') FROM users;
```

### 5. DDL语句转换
```sql
-- MySQL
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100)
) ENGINE=InnoDB CHARSET=utf8;

-- 达梦 (转换后)
CREATE TABLE users (
  id INT IDENTITY(1,1) PRIMARY KEY,
  name VARCHAR(100)
);
```

## 使用方法

### 1. 自动转换（推荐）
系统会自动拦截所有mapper方法的SQL执行，并进行语法转换：

```java
// 原有的mapper方法无需任何修改
@Select("SELECT * FROM users WHERE id = #{id} LIMIT 1")
User findById(Long id);

// SQL会被自动转换为达梦兼容语法
```

### 2. 手动转换
如果需要手动转换SQL，可以使用工具类：

```java
@Autowired
private SqlCompatibilityUtil sqlUtil;

String originalSql = "SELECT * FROM users LIMIT 10, 20";
String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
```

## 配置变更

### 1. MyBatis配置
```yaml
mapper:
  identity: ORACLE  # 从MYSQL改为ORACLE以支持达梦数据库
```

### 2. 日志驱动配置
```properties
log4jdbc.drivers=com.mysql.cj.jdbc.Driver,dm.jdbc.driver.DmDriver
```

### 3. 依赖配置
确保已添加达梦数据库驱动依赖：
```xml
<dependency>
    <groupId>com.dameng</groupId>
    <artifactId>DmJdbcDriver18</artifactId>
    <version>*********</version>
</dependency>
```

## 监控和日志

### 启动日志
应用启动时会自动测试达梦数据库连接：
```
=== 开始达梦数据库连接测试 ===
✓ 达梦数据库连接成功
数据库信息:
  - 产品名称: DM DBMS
  - 产品版本: *********
  - 驱动名称: DmJdbcDriver18
=== 达梦数据库连接测试完成 ===
```

### SQL转换日志
拦截器会记录SQL转换的详细日志：
```
SQL转换 - 方法: UserMapper.findById, 原SQL: SELECT * FROM users LIMIT 1, 转换后: SELECT * FROM users LIMIT 1
```

## 故障排除

### 1. 达梦数据库连接失败
- 检查数据库服务是否启动
- 验证连接参数（IP、端口、数据库名）
- 确认用户名密码正确
- 检查网络连接和防火墙设置

### 2. SQL转换问题
- 查看日志中的SQL转换记录
- 使用测试接口验证转换规则
- 检查是否有不支持的MySQL语法

### 3. 性能问题
- 调整连接池参数
- 监控SQL转换的性能影响
- 优化复杂的SQL查询

## 注意事项

1. **数据迁移**: 确保MySQL中的数据已正确迁移到达梦数据库
2. **数据类型**: 注意MySQL和达梦之间的数据类型差异
3. **索引和约束**: 重新创建必要的索引和约束
4. **存储过程**: 如有存储过程需要重写为达梦语法
5. **备份策略**: 制定完整的数据备份和恢复策略

## 测试建议

1. 启动应用后访问 `/dameng-test/connection` 检查连接状态
2. 使用 `/dameng-test/sql-conversion` 测试SQL转换功能
3. 执行完整的业务功能测试
4. 进行性能测试，确保转换不影响性能
5. 测试事务处理和并发操作

## 技术支持

如有问题，请检查：
1. 应用启动日志
2. 达梦数据库连接测试结果
3. SQL转换日志
4. 达梦数据库服务状态

系统现已完全切换到达梦数据库，原有的mapper代码无需任何修改即可正常工作。
