# 数据库迁移说明 - MySQL到达梦数据库

## 概述

本项目已成功实现从MySQL数据库到达梦数据库的迁移，同时保持了原有mapper代码不变。通过使用AOP切面技术和动态数据源，实现了数据库的自动切换和故障转移。

## 主要特性

- ✅ **无侵入式迁移**: 原有mapper代码无需修改
- ✅ **自动故障转移**: 达梦数据库不可用时自动切换到MySQL
- ✅ **动态数据源**: 支持运行时动态切换数据源
- ✅ **连接监控**: 实时监控数据库连接状态
- ✅ **测试接口**: 提供完整的测试API接口

## 数据库配置

### 达梦数据库配置
```yaml
sys-dm-db:
  db-url: 192.168.20.180:5236
  db-name: GS_OWS_ZY_TEST
  db-user: SYSDAB
  db-password: Dameng@123456
```

### MySQL数据库配置（备用）
```yaml
sys-db0:
  db-url: 192.168.20.186:3306
  db-name: gs_ows_zhongyan_test
  db-user: root
  db-password: 9sjTIw3TiL3RWOWB
```

## 核心组件

### 1. DynamicDataSource (动态数据源)
- 位置: `src/main/java/com/goodsogood/ows/configuration/DynamicDataSource.java`
- 功能: 根据线程上下文动态选择数据源
- 支持自动故障转移和手动切换

### 2. DataSourceConfig (数据源配置)
- 位置: `src/main/java/com/goodsogood/ows/configuration/DataSourceConfig.java`
- 功能: 配置多个数据源和相关的SqlSessionFactory
- 默认使用达梦数据库作为主数据源

### 3. DatabaseSwitchAspect (数据库切换切面)
- 位置: `src/main/java/com/goodsogood/ows/aspect/DatabaseSwitchAspect.java`
- 功能: 拦截mapper方法调用，实现自动数据库切换
- 在达梦数据库失败时自动重试MySQL

### 4. DatabaseConnectionTester (连接测试器)
- 位置: `src/main/java/com/goodsogood/ows/component/DatabaseConnectionTester.java`
- 功能: 应用启动时自动测试数据库连接
- 提供连接状态监控

## API接口

### 数据库测试接口
基础路径: `/database-test`

#### 1. 测试连接状态
```
GET /database-test/connection-status
```
返回所有数据库的连接状态

#### 2. 获取数据库信息
```
GET /database-test/database-info
```
返回当前使用数据库的详细信息

#### 3. 手动切换到达梦数据库
```
POST /database-test/switch-to-dm
```

#### 4. 手动切换到MySQL数据库
```
POST /database-test/switch-to-mysql
```

#### 5. 自动选择数据源
```
POST /database-test/auto-select
```

#### 6. 执行查询测试
```
GET /database-test/query-test
```

## 使用方法

### 1. 自动模式（推荐）
系统会自动检测数据库可用性并选择最佳数据源：
- 优先使用达梦数据库
- 达梦不可用时自动切换到MySQL
- 支持自动故障恢复

### 2. 手动模式
可以通过API接口手动控制数据源切换：

```java
// 切换到达梦数据库
DynamicDataSource.useDmDataSource();

// 切换到MySQL数据库
DynamicDataSource.useMysqlDataSource();

// 清除设置，使用默认数据源
DynamicDataSource.clearDataSource();
```

### 3. 编程方式
在代码中可以这样使用：

```java
@Autowired
private DynamicDataSource dynamicDataSource;

// 检查达梦数据库是否可用
if (dynamicDataSource.isDmDatabaseAvailable()) {
    // 使用达梦数据库
} else {
    // 使用MySQL数据库
}

// 自动选择最佳数据源
dynamicDataSource.autoSelectDataSource();
```

## 配置变更

### 1. MyBatis配置
```yaml
mapper:
  identity: ORACLE  # 从MYSQL改为ORACLE以支持达梦数据库
```

### 2. 日志驱动配置
```properties
log4jdbc.drivers=com.mysql.cj.jdbc.Driver,dm.jdbc.driver.DmDriver
```

### 3. 依赖配置
已添加达梦数据库驱动依赖：
```xml
<dependency>
    <groupId>com.dameng</groupId>
    <artifactId>DmJdbcDriver18</artifactId>
    <version>8.1.2.192</version>
</dependency>
```

## 监控和日志

### 启动日志
应用启动时会自动测试数据库连接并输出详细信息：
```
=== 开始数据库连接测试 ===
✓ 达梦数据库连接正常
✓ MySQL数据库连接正常
当前使用数据源: dmDataSource
=== 数据库连接测试完成 ===
```

### 运行时日志
切面会记录数据库切换的详细日志：
```
使用数据源 dmDataSource 执行方法: UserMapper.selectById(..)
达梦数据库操作失败，尝试切换到MySQL重试
MySQL数据库操作成功
```

## 故障排除

### 1. 达梦数据库连接失败
- 检查数据库服务是否启动
- 验证连接参数（IP、端口、数据库名）
- 确认用户名密码正确
- 检查网络连接

### 2. 自动切换不工作
- 检查切面是否正确配置
- 确认@EnableAspectJAutoProxy注解存在
- 查看日志中的切面执行信息

### 3. 性能问题
- 调整连接池参数
- 监控数据库连接数
- 优化SQL查询

## 注意事项

1. **数据一致性**: 确保两个数据库的数据结构和数据保持一致
2. **事务管理**: 跨数据源的事务需要特别注意
3. **连接池配置**: 根据实际负载调整连接池参数
4. **监控告警**: 建议配置数据库连接状态监控
5. **备份策略**: 制定完整的数据备份和恢复策略

## 测试建议

1. 启动应用后访问 `/database-test/connection-status` 检查连接状态
2. 执行 `/database-test/query-test` 验证查询功能
3. 模拟达梦数据库故障，测试自动切换功能
4. 验证业务功能在两种数据库下都能正常工作

## 技术支持

如有问题，请检查：
1. 应用启动日志
2. 数据库连接测试结果
3. 切面执行日志
4. 数据库服务状态
