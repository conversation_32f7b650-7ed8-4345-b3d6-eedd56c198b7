package com.goodsogood.ows.annotation;

import com.goodsogood.ows.component.FieldType;
import java.lang.annotation.*;

/**
 * 添加动态字段注解
 * 参数包含 :
 * 	fieldType	字段类型 <br/>
 * 		type -> 用户 <br/>
 * 	 	returnType -> 返回参数命名格式 会和 注解上 returnType 进行校验<br/>
 * 	 	fieldName -> 对应的取值参数 <br/>
 * 	clazzType	标识接口返回对象class <br/>
 * 	returnType	返回参数命名格式, 默认是下划线格式返回 1
 * <AUTHOR>
 */
@Documented
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface AddDynamicFields {

	/** 字段类型，包含组织和人员，通过类型找到所属参数 */
	FieldType fieldType();

	/** 返回的实体对象，泛型的class类型 */
	Class<?> clazzType();

	/** 0 -> 通用，不校验
	 * 1 -> 默认返回的参数是下划线<br/>
	 * 2 -> 返回的参数是驼峰命名
	 */
	int returnType() default 1;

}



