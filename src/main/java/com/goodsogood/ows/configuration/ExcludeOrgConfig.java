package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 统计排除的组织IDList
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "exclude-org")
public class ExcludeOrgConfig {

    private List<Long> orgIds;

    private List<Integer> notChangeOrgType;
}
