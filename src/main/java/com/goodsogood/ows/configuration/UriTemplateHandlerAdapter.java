package com.goodsogood.ows.configuration;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.util.DefaultUriBuilderFactory;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 对restTemplate进行了适配，
 * 解决未明确指定路径时参数替换之后无法正确替换出请求地址的问题，
 * 有问题的请求地址示例：{wechat-center}/component/authorizer_access_token?project_name={project_name}&oid={oid}
 * 如果是上诉情况{wechat-center}将无法被替换出来，
 * 必须修改为：http://{wechat-center}/component/authorizer_access_token?project_name={project_name}&oid={oid}
 * 或者http://www.wechat.com/component/authorizer_access_token?project_name={project_name}&oid={oid}
 *
 * <AUTHOR>
 * @date 2020/5/29
 */
public class UriTemplateHandlerAdapter extends DefaultUriBuilderFactory {

    private static final Pattern URL_PATTERN = Pattern.compile("(\\{)([\\S])+(\\})");

    @Override
    public URI expand(String uriTemplate, Map<String, ?> uriVars) {
        return super.expand(pretreatmentUrl(uriTemplate, uriVars), uriVars);
    }

    @Override
    public URI expand(String uriTemplate, Object... uriVars) {
        Result result = pretreatmentUrl(uriTemplate, uriVars);
        if (result.getUriVars() != null) {
            return super.expand(result.getUriTemplate(), result.getUriVars());
        } else {
            return super.expand(result.getUriTemplate());
        }
    }

    private String pretreatmentUrl(String uriTemplate, Map<String, ?> uriVars) {
        if (StringUtils.isNotBlank(uriTemplate) && uriVars != null && !uriVars.isEmpty()) {
            // 判断url是否以http或者https开头
            if (!StringUtils.startsWithIgnoreCase(uriTemplate, "http")
                    || !StringUtils.startsWithIgnoreCase(uriTemplate, "https")) {

                UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(uriTemplate);
                String path = builder.build().getPath();

                // 如果不是以http获取https开头需要先行替换请求地址，否则后续执行将报错！！！
                Matcher matcher = URL_PATTERN.matcher(path);
                if (matcher.find()) {
                    String group = matcher.group();
                    String key = StringUtils.substringBetween(group, "{", "}");
                    if (uriVars.containsKey(key)) {
                        uriTemplate = uriTemplate.replace(group, String.valueOf(uriVars.get(key)));
                        uriVars.remove(key);
                    }
                }
            }
        }
        return uriTemplate;
    }

    private Result pretreatmentUrl(String uriTemplate, Object... uriVars) {
        Result result = new Result(uriTemplate, uriVars);
        if (StringUtils.isNotBlank(uriTemplate) && uriVars.length > 0) {
            // 判断url是否以http或者https开头
            if (!StringUtils.startsWithIgnoreCase(uriTemplate, "http")
                    || !StringUtils.startsWithIgnoreCase(uriTemplate, "https")) {

                UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(uriTemplate);
                String path = builder.build().getPath();

                // 如果不是以http获取https开头需要先行替换请求地址，否则后续执行将报错！！！
                Matcher matcher = URL_PATTERN.matcher(path);
                if (matcher.find()) {
                    String group = matcher.group();
                    uriTemplate = uriTemplate.replace(group, String.valueOf(uriVars[0]));
                    result.setUriTemplate(uriTemplate);
                    if (uriVars.length == 1) {
                        result.setUriVars(null);
                    } else {
                        Object[] newUriVars = new Object[uriVars.length - 1];
                        for (int i = 0; i < uriVars.length; i++) {
                            if (i != 0) {
                                newUriVars[i - 1] = uriVars[i];
                            }
                        }
                        result.setUriVars(newUriVars);
                    }
                }
            }
        }
        return result;
    }

    @Data
    @AllArgsConstructor
    static class Result {
        private String uriTemplate;
        private Object[] uriVars;
    }

//    public static void main(String[] args) {
//        UriTemplateHandlerAdapter uriTemplateHandlerAdapter = new UriTemplateHandlerAdapter();
//
//        String accessTokenUrl = "{wechat-center}/component/authorizer_access_token";
//        uriTemplateHandlerAdapter.expand(accessTokenUrl, "http://owswx.yeyeku.com");
//
//        accessTokenUrl = "{wechat-center}/component/authorizer_access_token?project_name={project_name}&oid={oid}";
//        uriTemplateHandlerAdapter.expand(accessTokenUrl, "http://owswx.yeyeku.com", "owsyeyeku", "3");
//
//
//        String accessTokenUrl1 = "{wechat-center}/component/authorizer_access_token";
//        Map<String, Object> map = new HashMap<>();
//        map.put("wechat-center", "http://owswx.yeyeku.com");
////        uriTemplateHandlerAdapter.expand(accessTokenUrl1, map);
//
//        accessTokenUrl1 = "{wechat-center}/component/authorizer_access_token?project_name={project_name}&oid={oid}";
//        map.put("project_name", "owsyeyeku");
//        map.put("oid", "3");
//        uriTemplateHandlerAdapter.expand(accessTokenUrl1, map);
//    }
}
