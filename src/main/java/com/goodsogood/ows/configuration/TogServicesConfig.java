package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义配置文件读取类
 *
 * @auther Administrator tc
 * @date 2018/6/15
 */
@Data
@Component
@ConfigurationProperties(prefix = "")
public class TogServicesConfig {
    private Map<String, String> togServices = new HashMap<>();
}
