package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "report-org")
public class TestOrgConfig {

    private Map<Long,List<Long>> excludeMap;//key为region_id  value为测试组织

    public List<Long> testOrgIds(Long regionId){
        if(CollectionUtils.isEmpty(excludeMap)){
            return Collections.emptyList();
        }
        return excludeMap.get(regionId);

    }

}
