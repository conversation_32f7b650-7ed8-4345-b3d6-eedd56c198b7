package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName ZuulPrefixConfig
 * @description
 * @date 2019-01-29 9:10
 */
@Data
@Component
@ConfigurationProperties(prefix = "")
public class ZuulPrefixConfig {
    private Map<String, String> zuulPrefix = new HashMap<>();
}
