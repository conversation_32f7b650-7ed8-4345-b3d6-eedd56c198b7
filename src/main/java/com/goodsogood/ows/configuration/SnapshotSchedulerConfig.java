package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-12-03 15:35
 * @since 3.0.1
 **/
@Component
@ConfigurationProperties(prefix = SnapshotSchedulerConfig.PREFIX)
@Data
public class SnapshotSchedulerConfig {

     public static final String PREFIX = "snapshot-scheduler";

     private List<Long> regionId;
}
