package com.goodsogood.ows.configuration;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 多数据源配置类
 * 配置MySQL和达梦两个数据源，达梦为主数据源
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
@MapperScan(basePackages = "com.goodsogood.ows.mapper", sqlSessionFactoryRef = "sqlSessionFactory")
public class DataSourceConfig {

    @Value("${sys-dm-db.db-url}")
    private String dmUrl;
    
    @Value("${sys-dm-db.db-name}")
    private String dmDatabase;
    
    @Value("${sys-dm-db.db-user}")
    private String dmUsername;
    
    @Value("${sys-dm-db.db-password}")
    private String dmPassword;
    
    @Value("${sys-db0.db-url}")
    private String mysqlUrl;
    
    @Value("${sys-db0.db-name}")
    private String mysqlDatabase;
    
    @Value("${sys-db0.db-user}")
    private String mysqlUsername;
    
    @Value("${sys-db0.db-password}")
    private String mysqlPassword;

    /**
     * 动态数据源配置（主数据源）
     */
    @Primary
    @Bean(name = "dataSource")
    public DynamicDataSource dataSource() {
        return new DynamicDataSource(dmDataSource(), mysqlDataSource());
    }

    /**
     * 达梦数据源配置
     */
    @Bean(name = "dmDataSource")
    public DataSource dmDataSource() {
        log.info("初始化达梦数据源...");
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("net.sf.log4jdbc.sql.jdbcapi.DriverSpy");
        dataSource.setJdbcUrl("jdbc:log4jdbc:dm://" + dmUrl + "/" + dmDatabase + "?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=GMT%2B8");
        dataSource.setUsername(dmUsername);
        dataSource.setPassword(dmPassword);
        
        // HikariCP 连接池配置
        dataSource.setConnectionTestQuery("SELECT 1 FROM DUAL");
        dataSource.setMinimumIdle(1);
        dataSource.setMaximumPoolSize(5);
        dataSource.setPoolName("hikari-dm-pool");
        dataSource.setIdleTimeout(30000);
        dataSource.setMaxLifetime(7170000);
        dataSource.setConnectionTimeout(60000);
        
        // 数据源属性
        dataSource.addDataSourceProperty("prepStmtCacheSize", "250");
        dataSource.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        dataSource.addDataSourceProperty("cachePrepStmts", "true");
        dataSource.addDataSourceProperty("useServerPrepStmts", "true");
        
        log.info("达梦数据源初始化完成: {}", dataSource.getJdbcUrl());
        return dataSource;
    }

    /**
     * MySQL数据源配置（备用数据源）
     */
    @Bean(name = "mysqlDataSource")
    public DataSource mysqlDataSource() {
        log.info("初始化MySQL数据源...");
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName("net.sf.log4jdbc.sql.jdbcapi.DriverSpy");
        dataSource.setJdbcUrl("jdbc:log4jdbc:mysql://" + mysqlUrl + "/" + mysqlDatabase + "?useSSL=false&useUnicode=true&zeroDateTimeBehavior=convertToNull&serverTimezone=GMT%2B8&allowMultiQueries=true");
        dataSource.setUsername(mysqlUsername);
        dataSource.setPassword(mysqlPassword);
        
        // HikariCP 连接池配置
        dataSource.setMinimumIdle(1);
        dataSource.setMaximumPoolSize(5);
        dataSource.setPoolName("hikari-mysql-pool");
        dataSource.setIdleTimeout(30000);
        dataSource.setMaxLifetime(7170000);
        dataSource.setConnectionTimeout(60000);
        
        // 数据源属性
        dataSource.addDataSourceProperty("prepStmtCacheSize", "250");
        dataSource.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        dataSource.addDataSourceProperty("cachePrepStmts", "true");
        dataSource.addDataSourceProperty("useServerPrepStmts", "true");
        
        log.info("MySQL数据源初始化完成: {}", dataSource.getJdbcUrl());
        return dataSource;
    }

    /**
     * 动态数据源的SqlSessionFactory
     */
    @Primary
    @Bean(name = "sqlSessionFactory")
    public SqlSessionFactory sqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        log.info("初始化动态SqlSessionFactory...");
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);

        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setCallSettersOnNulls(true);
        bean.setConfiguration(configuration);

        log.info("动态SqlSessionFactory初始化完成");
        return bean.getObject();
    }

    /**
     * 动态数据源的事务管理器
     */
    @Primary
    @Bean(name = "transactionManager")
    public PlatformTransactionManager transactionManager(@Qualifier("dataSource") DataSource dataSource) {
        log.info("初始化动态数据源事务管理器...");
        return new DataSourceTransactionManager(dataSource);
    }
}
