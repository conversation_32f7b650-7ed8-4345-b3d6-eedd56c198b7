package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description: 发展党员
 * @author: z<PERSON><PERSON><PERSON>
 * @create: 2021-12-27 15:49
 */
@Data
@Component
@ConfigurationProperties(prefix = DevelopConfig.PREFIX)
public class DevelopConfig {
    public static final String PREFIX = "develop";
    /**
     * 新增录入
     */
    private String addDesc;
    /**
     * 新增
     */
    private String add;
    /**
     * 取消
     */
    private String recusal;
    /**
     * 资格
     */
    private String qualification;
    /**
     * 入党申请人
     */
    private String proposer;
    /**
     * 积极分子
     */
    private String activist;
    /**
     * 发展对象
     */
    private String developmentobject;
    /**
     * 预备党员
     */
    private String propartymember;
    /**
     * 删除发展人员
     */
    private String deletepeople;
    //推荐方式
    /**
     * 党员推荐
     */
    private String partymemberrecommend;
    /**
     * 群团组织推荐
     */
    private String teamrecommend;
    /**
     * 其他
     */
    private String elserecommend;
    //转正类型
    /**
     * 按期转正
     */
    private String ontime;
    /**
     * 延长预备期满转正
     */
    private String lengthen;
    /**
     * 推迟谈论转正
     */
    private String postpone;
    private String confirm;
    private String receive;
    private String positive;
    private String recommentTime;
    /**
     * 上级党委备案时间
     */
    private String parentRecordTime;
    /**
     * 支部
     */
    private String branch;

    /**
     * 在党员管理中心已删除了该党员
     */
    private String deleteUser;
    /**
     * 党员转正公示标题
     */
    private String publicityTitle;
    /**
     * 党员转正公示内容
     */
    private String text;
}
