package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Data
@Component
@Validated
@ConfigurationProperties(prefix = "field")
public class FieldConfig {

    public static final String PREFIX = "field";

    //过滤那些字段不显示
    public List<String> name;
}
