package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020-06-30 16:44
 * @since 3.0.0
 **/
@Data
@Component
@ConfigurationProperties(prefix = VolunteerTeamMessageConfig.PREFIX)
public class VolunteerTeamMessageConfig {

     public static final String PREFIX = "volunteer-team-message";

     // 模板ID
     private Long templateId;
     // 服务类别
     private String serviceType;
     // 渠道类型
     private Integer channelType;
     // 来源
     private String source;
     // 重试次数
     private Integer maxRepeatCount;

     private ValidComment validComment;
     private InvalidComment invalidComment;

     @Data
     public static class ValidComment {
          private String title;
          private String remark;
     }

     @Data
     public static class InvalidComment {
          private String title;
     }
}
