package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018/8/3
 * @description class SyncJDGJConfig
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = SyncJDGJConfig.PREFIX)
public class SyncJDGJConfig {
    public static final String PREFIX = "sync-jdgc-config";
    private String url;
    private String key;
    // t_last_time中对应的id 默认为1
    private Integer lastTimeId = 1;
    // 是否启动定时器
    private Boolean run;
    // 启动延迟，毫秒
    @NotNull
    private Long initialDelay;
    // 每多少时间执行一次（等待上一次执行完以后）
    @NotNull
    private Integer fixedDelay;
    // 每次同步多少条，默认为20
    private Integer pageSize = 20;

}
