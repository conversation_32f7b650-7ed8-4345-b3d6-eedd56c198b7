package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/1/8
 * Description:
 */
@ConfigurationProperties(prefix = MongoCollectionNameConfig.PREFIX)
@Component
@Data
public class MongoCollectionNameConfig {
    public static final String PREFIX = "mongo-connection";

    private Map<String, String> collectionNames;

    private String host;

    private Integer port;

    private String databaseName;

    private String userName;

    private String password;

}
