package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "show-type")
public class ShowTypeConfig {

    /**
     * 展示的标签列表
     */
    private List<Integer> tagType;

    /**
     * 基于tagType 需要过滤的标签 满足只展示不管理条件
     */
    private List<Integer> filterType;
}
