package com.goodsogood.ows.configuration;

import com.goodsogood.ows.config.ApplicationConfig;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.model.db.ApplicationConfigEntity;
import com.goodsogood.ows.service.ApplicationConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2020/4/14
 */
@Configuration
public class ApplicationConfiguration {

    private final SaasConfig saasConfig;

    @Autowired
    public ApplicationConfiguration(SaasConfig saasConfig) {
        this.saasConfig = saasConfig;
    }

    public static class MyApplicationConfigHelper extends SimpleApplicationConfigHelper {

        private final ApplicationConfigService applicationConfigService;

        public MyApplicationConfigHelper(StringRedisTemplate redisTemplate,
                                         RestTemplate restTemplate,
                                         String userServerUrl,
                                         ApplicationConfigService applicationConfigService,
                                         String label) {
            super(redisTemplate, restTemplate, userServerUrl, label);
            this.applicationConfigService = applicationConfigService;
        }

        @Override
        protected ApplicationConfig getConfigByRedis(String application, String label) {
            ApplicationConfigEntity config = applicationConfigService.getApplicationConfig(application, label);
            if (config != null) {
                ApplicationConfig applicationConfig = new ApplicationConfig();
                applicationConfig.setApplication(config.getApplication());
                applicationConfig.setLabel(config.getLabel());
                applicationConfig.setContent(config.getContent());
                return applicationConfig;
            }
            return null;
        }

        @Override
        protected ApplicationConfig getConfigByHttp(String application, String label, String url) throws IOException {
            return null;
        }
    }

    @Bean
    public SimpleApplicationConfigHelper applicationConfigHelper(StringRedisTemplate redisTemplate,
                                                                 ApplicationConfigService applicationConfigService) {
        return new MyApplicationConfigHelper(redisTemplate, null, null, applicationConfigService, saasConfig.getRegionLabel());
    }
}
