package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * 【监督预警】组织概况 生成缓存
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = GenerateOrgProfileConfig.PREFIX)
public class GenerateOrgProfileConfig {

    public static final String PREFIX = "generate-org-profile-config";

    // 是否启动定时器
    private Boolean run;

    // 启动延迟，毫秒
    @NotNull
    private Long initialDelay;

    // 每多少时间执行一次（等待上一次执行完以后）
    @NotNull
    private Integer fixedDelay;
}
