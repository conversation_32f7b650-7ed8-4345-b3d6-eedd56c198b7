package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * 自定义配置文件读取类
 *
 * @auther Administrator tc
 * @date 2018/6/15
 */
@Data
@Component
@ConfigurationProperties(prefix = "msg")
@PropertySource(value = "classpath:remindMessages.properties")
public class MessagesConfig {
    private String importName;
    private String importNameLength;
    private String importPhone;
    private String importErrorPhone;
    private String importCertNumber;
    private String importRepetitionPhone;
    private String importErrorCertNumber;
    private String importVerifyCertNumber;
    private String importDBError;
    private String importSystemError;
}
