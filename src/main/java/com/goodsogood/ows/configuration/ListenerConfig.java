package com.goodsogood.ows.configuration;

import com.goodsogood.ows.listener.MyApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2018/8/17
 * @description class ListenerConfig
 */
@Configuration
public class ListenerConfig {
    @Bean
    public MyApplicationListener applicationStartListener() {
        return new MyApplicationListener();
    }
}
