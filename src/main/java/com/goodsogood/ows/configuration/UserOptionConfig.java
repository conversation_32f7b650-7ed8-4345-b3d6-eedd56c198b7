package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-02-03 09:04
 * @since 3.0.1
 **/
@Component
@ConfigurationProperties(prefix = UserOptionConfig.PREFIX)
@Data
public class UserOptionConfig {

    public static final String PREFIX = "user-option";

    /**
     * 离退休工作岗位code
     */
    private String retireCode;

    /**
     * 党员政治面貌code
     */
    private String politicalCode;

    /**
     * 预备党员政治面貌code
     */
    private String prePoliticalCode;

    /**
     * 发展党员类型104401
     */
    private Integer joinPartyType;

    /**
     * 市内转入10440201
     */
    private String joinType;

    /**
     * 离开类型10450201，10450202
     */
    private List<Integer> leaveTypes;

    /**
     * 组织届次书记数据字典 19-党支部书记
     */
    private List<Integer> leaderTypes;

    /**
     * 取消预备党员资格 104506
     */
    private Integer recusalType;
}
