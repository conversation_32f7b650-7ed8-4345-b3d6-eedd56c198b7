package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-11-03 18:26
 * @since 3.0.1
 **/
@Data
@Component
@Validated
@ConfigurationProperties(prefix = HistoryConfig.PREFIX)
public class HistoryConfig {

     public static final String PREFIX = "history";

     public List<String> month;

}
