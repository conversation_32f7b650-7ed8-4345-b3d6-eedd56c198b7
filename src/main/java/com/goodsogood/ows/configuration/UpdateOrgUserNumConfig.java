package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import javax.validation.constraints.NotNull;

import java.util.List;

/**
 * 更新组织人员数量的配置文件
 * <AUTHOR>
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "update-org-user-num")
public class UpdateOrgUserNumConfig {

	private boolean run;

	private String cron;

	@NotNull
	private List<Integer> orgType;
}
