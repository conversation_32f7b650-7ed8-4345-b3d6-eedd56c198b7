package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021-08-11 14:37
 * @description
 */
@Component
@ConfigurationProperties(prefix = EcsConfig.PREFIX)
@Data
public class EcsConfig {

    public static final String PREFIX = "ecs";

    private Boolean run;

    private String url;

    /**
     * 应用ID
     */
    private String clientId;

    /**
     * 应用密码
     */
    private String clientSecret;

    /**
     * 自建应用ID
     */
    private String applicationId;

    /**
     * 自建应用密码
     */
    private String applicationPass;

    /**
     * SM4对称秘钥
     */
    private String secretKey;


}
