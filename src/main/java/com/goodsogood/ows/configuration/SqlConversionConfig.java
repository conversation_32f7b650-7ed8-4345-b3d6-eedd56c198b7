package com.goodsogood.ows.configuration;

import com.goodsogood.ows.aspect.SqlConversionAspect;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * SQL转换配置类
 * 用于注册SQL转换拦截器到MyBatis
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
public class SqlConversionConfig {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    private SqlConversionAspect sqlConversionAspect;

    @PostConstruct
    public void addInterceptor() {
        log.info("开始注册SQL转换拦截器...");
        
        for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
            sqlSessionFactory.getConfiguration().addInterceptor(sqlConversionAspect);
            log.info("SQL转换拦截器已注册到SqlSessionFactory: {}", sqlSessionFactory);
        }
        
        log.info("SQL转换拦截器注册完成，共注册到 {} 个SqlSessionFactory", sqlSessionFactoryList.size());
    }
}
