package com.goodsogood.ows.configuration;

import com.goodsogood.ows.aspect.SqlConversionAspect;
import com.goodsogood.ows.aspect.SimpleTestInterceptor;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * SQL转换配置类
 * 用于注册SQL转换拦截器到MyBatis
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Configuration
@Log4j2
public class SqlConversionConfig {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    private SqlConversionAspect sqlConversionAspect;

    @Autowired
    private SimpleTestInterceptor simpleTestInterceptor;

    @PostConstruct
    public void addInterceptor() {
        log.info("=== 开始注册SQL转换拦截器 ===");
        log.info("找到 {} 个SqlSessionFactory", sqlSessionFactoryList.size());

        if (sqlSessionFactoryList.isEmpty()) {
            log.warn("没有找到SqlSessionFactory，拦截器无法注册！");
            return;
        }

        for (int i = 0; i < sqlSessionFactoryList.size(); i++) {
            SqlSessionFactory sqlSessionFactory = sqlSessionFactoryList.get(i);
            log.info("正在注册拦截器到第{}个SqlSessionFactory: {}", i + 1, sqlSessionFactory.getClass().getName());

            // 检查是否已经有拦截器
            int interceptorCountBefore = sqlSessionFactory.getConfiguration().getInterceptors().size();
            log.info("注册前拦截器数量: {}", interceptorCountBefore);

            // 注册拦截器
            sqlSessionFactory.getConfiguration().addInterceptor(sqlConversionAspect);

            int interceptorCountAfter = sqlSessionFactory.getConfiguration().getInterceptors().size();
            log.info("注册后拦截器数量: {}", interceptorCountAfter);

            if (interceptorCountAfter > interceptorCountBefore) {
                log.info("✓ SQL转换拦截器成功注册到SqlSessionFactory #{}", i + 1);
            } else {
                log.warn("✗ SQL转换拦截器注册可能失败");
            }
        }

        log.info("=== SQL转换拦截器注册完成 ===");
    }
}
