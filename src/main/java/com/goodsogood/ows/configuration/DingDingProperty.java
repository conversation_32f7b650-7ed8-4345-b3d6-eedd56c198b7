package com.goodsogood.ows.configuration;

import com.goodsogood.ows.component.DingDingConstant;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/1/24
 * @description class DingdingProperty
 */
@Data
@Configuration
@ConfigurationProperties(prefix = DingDingProperty.PROPERTIES)
public class DingDingProperty {

    /**
     * 使用的是区县名称作为名称，主要是防止多个局点部署时忘了更新节点名称
     */
    @Value("${dingding.server}")
    @NotNull
    private String server;

    @Value("${dingding.accessToken}")
    @NotNull
    private String accessToken;

    protected final static String PROPERTIES = "dd";
    private String pushModel = "{\"msgtype\": \"markdown\",\"markdown\": {\"title\":\"用户中心定时任务\",\"text\":\"## 用户中心定时任务 \\n > 来自-微服务：["
            + DingDingConstant.KEY_SOURCE
            + "]\\n\\n- trackId：["
            + DingDingConstant.KEY_TRACK_ID
            + "]\\n- 告警时间：["
            + DingDingConstant.KEY_TIME
            + "]\\n- 消息："
            + DingDingConstant.KEY_MSG
            + " \\n\\n"
            + ""
            + "\"},\"at\": {\"atMobiles\": ["
            + ""
            + "],\"isAtAll\": false}}";

    private List<String> atUsers = new ArrayList<>();
}
