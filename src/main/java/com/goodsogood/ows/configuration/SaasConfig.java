package com.goodsogood.ows.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Saas配置
 *
 * <AUTHOR>
 * @date 2020-06-08 11:33
 * @since 3.0.0
 **/
@Component
@ConfigurationProperties(prefix = SaasConfig.PREFIX)
@Data
public class SaasConfig {

    public static final String PREFIX = "saas";

    /**
     * 区县label配置
     */
    @JsonProperty("region-label")
    private String regionLabel;

    /**
     * 第三方数据ID前缀
     */
    @JsonProperty("third-id-prefix")
    private String thirdIdPrefix;

    /**
     * 是否生成第三方数据唯一ID
     */
    @JsonProperty("generate-third-id")
    private Boolean generateThirdId;

    /**
     * 组织类型，按顺序 0-行政单位 1-党组织 2-志愿者组织 3-第二党支部
     */
    private List<Integer> orgType;

    /**
     * 区县regionId列表
     */
    private List<Long> regions;

    private Integer partyTagId;
}
