package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织类型
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "org-type")
public class OrgTypeConfig {

    private List<Integer> communistChild;

    private List<Integer> generalBranchChild;

    private List<Integer> branchChild;

    private List<Integer> noStatisticsChild;

    public List<Integer> getBasicChild() {
        List<Integer> basicChild = new ArrayList<>();
        basicChild.addAll(communistChild);
        basicChild.addAll(generalBranchChild);
        basicChild.addAll(branchChild);
        return basicChild;
    }

    public List<Integer> getCommunistChild() {
        return communistChild;
    }

    public List<Integer> getGeneralBranchChild() {
        return generalBranchChild;
    }

    public List<Integer> getBranchChild() {
        return branchChild;
    }

    public List<Integer> getNoStatisticsChild() {
        return noStatisticsChild;
    }
}
