package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/9/2
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat")
@Deprecated
public class WechatConfig {

    /**
     * 微信中心地址
     */
    private String wechatCenterUrl;

    private Integer callbackVersion = 2;

    /**
     * 顶级组织和projectName
     */
    private Map<Long, String> projectName = new HashMap<>();

}
