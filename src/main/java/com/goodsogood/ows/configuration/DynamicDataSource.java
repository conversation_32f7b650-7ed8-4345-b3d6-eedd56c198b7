package com.goodsogood.ows.configuration;

import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;

/**
 * 动态数据源
 * 根据当前线程的数据源标识来决定使用哪个数据源
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Log4j2
public class DynamicDataSource extends AbstractRoutingDataSource {

    /**
     * 数据源标识的ThreadLocal
     */
    private static final ThreadLocal<String> DATA_SOURCE_KEY = new ThreadLocal<>();

    /**
     * 达梦数据源标识
     */
    public static final String DM_DATA_SOURCE = "dmDataSource";

    /**
     * MySQL数据源标识
     */
    public static final String MYSQL_DATA_SOURCE = "mysqlDataSource";

    /**
     * 默认数据源标识（达梦）
     */
    public static final String DEFAULT_DATA_SOURCE = DM_DATA_SOURCE;

    private DataSource dmDataSource;
    private DataSource mysqlDataSource;

    public DynamicDataSource(DataSource dmDataSource, DataSource mysqlDataSource) {
        this.dmDataSource = dmDataSource;
        this.mysqlDataSource = mysqlDataSource;
        
        // 设置目标数据源
        Map<Object, Object> targetDataSources = Map.of(
            DM_DATA_SOURCE, dmDataSource,
            MYSQL_DATA_SOURCE, mysqlDataSource
        );
        setTargetDataSources(targetDataSources);
        
        // 设置默认数据源
        setDefaultTargetDataSource(dmDataSource);
        
        log.info("动态数据源初始化完成，默认使用达梦数据库");
    }

    @Override
    protected Object determineCurrentLookupKey() {
        String dataSourceKey = DATA_SOURCE_KEY.get();
        if (dataSourceKey == null) {
            dataSourceKey = DEFAULT_DATA_SOURCE;
        }
        log.debug("当前使用数据源: {}", dataSourceKey);
        return dataSourceKey;
    }

    @Override
    public Connection getConnection() throws SQLException {
        try {
            // 首先尝试获取当前指定的数据源连接
            return super.getConnection();
        } catch (SQLException e) {
            String currentKey = (String) determineCurrentLookupKey();
            log.warn("获取{}连接失败: {}", currentKey, e.getMessage());
            
            // 如果当前是达梦数据源失败，尝试切换到MySQL
            if (DM_DATA_SOURCE.equals(currentKey)) {
                log.info("达梦数据库连接失败，自动切换到MySQL数据库");
                setDataSource(MYSQL_DATA_SOURCE);
                try {
                    return mysqlDataSource.getConnection();
                } catch (SQLException mysqlException) {
                    log.error("MySQL数据库连接也失败: {}", mysqlException.getMessage());
                    throw mysqlException;
                }
            } else {
                // 如果MySQL也失败，抛出异常
                throw e;
            }
        }
    }

    /**
     * 设置当前线程使用的数据源
     */
    public static void setDataSource(String dataSourceKey) {
        log.debug("切换数据源到: {}", dataSourceKey);
        DATA_SOURCE_KEY.set(dataSourceKey);
    }

    /**
     * 获取当前线程使用的数据源
     */
    public static String getDataSource() {
        String dataSourceKey = DATA_SOURCE_KEY.get();
        return dataSourceKey != null ? dataSourceKey : DEFAULT_DATA_SOURCE;
    }

    /**
     * 清除当前线程的数据源设置
     */
    public static void clearDataSource() {
        DATA_SOURCE_KEY.remove();
    }

    /**
     * 使用达梦数据源
     */
    public static void useDmDataSource() {
        setDataSource(DM_DATA_SOURCE);
    }

    /**
     * 使用MySQL数据源
     */
    public static void useMysqlDataSource() {
        setDataSource(MYSQL_DATA_SOURCE);
    }

    /**
     * 检查达梦数据库是否可用
     */
    public boolean isDmDatabaseAvailable() {
        try (Connection connection = dmDataSource.getConnection()) {
            return connection != null && !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            log.debug("达梦数据库连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查MySQL数据库是否可用
     */
    public boolean isMysqlDatabaseAvailable() {
        try (Connection connection = mysqlDataSource.getConnection()) {
            return connection != null && !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            log.debug("MySQL数据库连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 自动选择可用的数据源
     */
    public void autoSelectDataSource() {
        if (isDmDatabaseAvailable()) {
            useDmDataSource();
            log.info("自动选择达梦数据库");
        } else if (isMysqlDatabaseAvailable()) {
            useMysqlDataSource();
            log.info("达梦数据库不可用，自动选择MySQL数据库");
        } else {
            log.error("所有数据库都不可用！");
            // 仍然使用默认的达梦数据源，让具体的操作抛出异常
            useDmDataSource();
        }
    }

    /**
     * 获取当前数据源状态信息
     */
    public String getDataSourceStatus() {
        StringBuilder status = new StringBuilder();
        status.append("数据源状态:\n");
        status.append("- 达梦数据库: ").append(isDmDatabaseAvailable() ? "可用" : "不可用").append("\n");
        status.append("- MySQL数据库: ").append(isMysqlDatabaseAvailable() ? "可用" : "不可用").append("\n");
        status.append("- 当前使用: ").append(getDataSource());
        return status.toString();
    }
}
