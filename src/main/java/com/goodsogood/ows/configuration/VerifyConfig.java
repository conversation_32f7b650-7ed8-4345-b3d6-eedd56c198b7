package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021-05-06 10:59
 * @since 3.0.1
 **/
@Data
@Component
@ConfigurationProperties(prefix = VerifyConfig.PREFIX)
public class VerifyConfig {

     public static final String PREFIX = "verify";

     private String url;
     private String algo;
     private String secretKey;

}
