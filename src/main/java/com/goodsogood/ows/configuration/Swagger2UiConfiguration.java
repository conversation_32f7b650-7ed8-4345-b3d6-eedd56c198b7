package com.goodsogood.ows.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.schema.ScalarType;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 07/02/2018
 * @description class Swagger2UiConfiguration  -> 升级到swagger3
 */
@Configuration
//@EnableSwagger2
public class Swagger2UiConfiguration implements WebMvcConfigurer {
    @Value("${swagger.show}")
    private boolean swaggerShow = false;

    @Bean
    public Docket api() {

        //设置请求头
        List<RequestParameter> pars = new ArrayList<>();

        RequestParameterBuilder token = new RequestParameterBuilder();
        token.name("_tk").description("token").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(token.build());

        RequestParameterBuilder uid = new RequestParameterBuilder();
        uid.name("_uid").description("用户id").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(uid.build());

        RequestParameterBuilder name = new RequestParameterBuilder();
        name.name("_un").description("用户姓名").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(name.build());

        RequestParameterBuilder type = new RequestParameterBuilder();
        type.name("_type").description("类型").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(type.build());

        RequestParameterBuilder oid = new RequestParameterBuilder();
        oid.name("_oid").description("组织ID").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(oid.build());

        RequestParameterBuilder regionId = new RequestParameterBuilder();
        oid.name("_region_id").description("区县ID").in(ParameterType.HEADER).query(q -> q.model(m -> m.scalarModel(ScalarType.STRING))).required(false);
        pars.add(oid.build());

        //Register the controllers to swagger
        //Also it is configuring the Swagger Docket
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(swaggerShow)
                .groupName("ows-user-center")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.goodsogood.ows.controller"))
                .paths(PathSelectors.any())
                .build()
                .globalRequestParameters(pars)
                .apiInfo(apiInfo());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("ows-user-center")
                .description("ows-user-center")
                .version("*******")
                .build();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .resourceChain(false);
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/swagger-ui/")
                .setViewName("forward:/swagger-ui/index.html");
    }
}
