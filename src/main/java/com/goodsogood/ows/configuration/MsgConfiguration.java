package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 消息中心的配置文件
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = MsgConfiguration.PREFIX)
public class MsgConfiguration {

    public static final String PREFIX = "message";

    /**
     * 是否发送短信
     */
    private Boolean run;

    /**
     * 消息中心地址
     */
    private String messageCenterUrl;

    /**
     * 消息中心系统识别码
     */
    private String secretKey;

    /**
     * 短信验证码模板ID
     */
    private Long smsCodeTemplateId;
}
