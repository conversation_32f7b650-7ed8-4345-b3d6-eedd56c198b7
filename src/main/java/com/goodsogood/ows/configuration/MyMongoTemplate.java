package com.goodsogood.ows.configuration;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ClassUtils;

import org.jetbrains.annotations.NotNull;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020/1/8
 * Description:
 */
@Log4j2
public class MyMongoTemplate extends MongoTemplate {

    @Autowired
    private MongoCollectionNameConfig mongoCollectionNameConfig;

    @Autowired
    private MongoConverter mongoConverter;

    public MyMongoTemplate(String host, Integer port, String databaseName) {
        super(MongoClients.create(MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString("mongodb://" + host + ":" + port + "/" + databaseName))
                .build()), databaseName);
    }

    public MyMongoTemplate(String host, Integer port, String databaseName, String userName, String password) {
        super(MongoClients.create(MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString("mongodb://" + host + ":" + port + "/" + databaseName))
                .credential(MongoCredential.createCredential(userName, databaseName, password.toCharArray()))
                .build()), databaseName);
    }


    @NotNull
    @Override
    public String getCollectionName(Class<?> entityClass) {
        String name = entityClass.getName();
        String collectionName = mongoCollectionNameConfig.getCollectionNames().get(name);
        if (StringUtils.isNotBlank(collectionName)) {
            return collectionName;
        } else {
            log.warn("通过类名从配置中获得collectionName失败，使用springboot默认方式：通过注解获得连接名称");
            return super.getCollectionName(entityClass);
        }
    }

    @NotNull
    public UpdateResult upsert(@NotNull Query query, @NotNull Update update, @NotNull Class<?> entityClass) {
        return super.upsert(query, update, this.getCollectionName(entityClass));
    }

//    @Override
//    public void save(Object objectToSave) {
//        super.save(objectToSave, this.getCollectionName(objectToSave.getClass()));
//    }


    @NotNull
    @Override
    public <T> T save(@NotNull T objectToSave) {
        return super.save(objectToSave, this.getCollectionName(objectToSave.getClass()));
    }

    @NotNull
    @Override
    public <T> List<T> find(@NotNull Query query, @NotNull Class<T> entityClass) {
        return super.find(query, entityClass, this.getCollectionName(entityClass));
    }

    @Override
    public <T> List<T> findAll(@NotNull Class<T> entityClass) {
        return findAll(entityClass, this.getCollectionName(entityClass));
    }

    @NotNull
    public UpdateResult updateFirst(@NotNull Query query, @NotNull Update update, @NotNull Class<?> entityClass) {
        return super.updateFirst(query, update, this.getCollectionName(entityClass));
    }

    @NotNull
    @Override
    public DeleteResult remove(@NotNull Query query, @NotNull Class<?> entityClass) {
        return super.remove(query, entityClass, this.getCollectionName(entityClass));
    }

    @Override
    public <T> boolean collectionExists(@NotNull Class<T> entityClass) {
        return super.collectionExists(this.getCollectionName(entityClass));
    }

    @NotNull
    @Override
    public IndexOperations indexOps(@NotNull Class<?> entityClass) {
        return super.indexOps(this.getCollectionName(entityClass));
    }

//    @Override
//    public <T> DBCollection createCollection(Class<T> entityClass) {
//        super.createCollection(this.getCollectionName(entityClass))
//        return super.createCollection(this.getCollectionName(entityClass));
//    }

    @NotNull
    @Override
    public <T> MongoCollection<Document> createCollection(@NotNull Class<T> entityClass) {
        return super.createCollection(this.getCollectionName(entityClass));
    }

    @Override
    public long count(@NotNull Query query, @NotNull Class<?> entityClass) {
        return super.count(query, this.getCollectionName(entityClass));
    }

    @Override
    public boolean exists(@NotNull Query query, @NotNull Class<?> entityClass) {
        return super.exists(query, this.getCollectionName(entityClass));
    }

//    @Override
//    public void insert(Object objectToSave) {
//        super.insert(objectToSave, this.getCollectionName(objectToSave.getClass()));
//    }


    @NotNull
    @Override
    public <T> T insert(@NotNull T objectToSave) {
        return super.insert(objectToSave, this.getCollectionName(objectToSave.getClass()));
    }

    @NotNull
    @Override
    public <T> Collection<T> insertAll(@NotNull Collection<? extends T> objectsToSave) {
        Map<String, List<T>> elementsByCollection = new HashMap<>();
        List<T> savedObjects = new ArrayList<>(objectsToSave.size());

        for (T element : objectsToSave) {

            if (element == null) {
                continue;
            }

            String collection = this.getCollectionName(ClassUtils.getUserClass(element));
            List<T> collectionElements = elementsByCollection.computeIfAbsent(collection, k -> new ArrayList<>());

            collectionElements.add(element);
        }

        for (Map.Entry<String, List<T>> entry : elementsByCollection.entrySet()) {
            savedObjects.addAll((Collection<T>) doInsertBatch(entry.getKey(), entry.getValue(), this.mongoConverter));
        }

        return savedObjects;
    }

    @NotNull
    public UpdateResult updateMulti(@NotNull Query query, @NotNull Update update, @NotNull Class<?> entityClass) {
        return super.updateMulti(query, update, this.getCollectionName(entityClass));
    }

    @NotNull
    @Override
    public BulkOperations bulkOps(@NotNull BulkOperations.BulkMode bulkMode, @NotNull Class<?> entityClass) {
        return super.bulkOps(bulkMode, entityClass, this.getCollectionName(entityClass));
    }

    @Override
    public <T> T findOne(@NotNull Query query, @NotNull Class<T> entityClass) {
        return super.findOne(query, entityClass, this.getCollectionName(entityClass));
    }

    public <T> AggregationResults<T> aggregate(Aggregation aggregation, Class<T> entityClass) {
        return super.aggregate(aggregation, this.getCollectionName(entityClass), entityClass);
    }

    public <T> AggregationResults<T> aggregateOutType(Aggregation aggregation, Class<?> entityClass, Class<T> outTypeClass) {
        return super.aggregate(aggregation, this.getCollectionName(entityClass), outTypeClass);
    }

    public <T> AggregationResults<Map> aggregateMap(Aggregation aggregation, Class<T> entityClass) {
        return super.aggregate(aggregation, this.getCollectionName(entityClass), Map.class);
    }
}
