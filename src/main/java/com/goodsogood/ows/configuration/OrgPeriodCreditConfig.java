package com.goodsogood.ows.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022-05-31 14:32:01
 * @Description OrgPeriodCreditConfig
 */
@Data
@Component
@ConfigurationProperties(prefix = OrgPeriodCreditConfig.PREFIX)
public class OrgPeriodCreditConfig {

    public static final String PREFIX = "org-period-credit";
    private String accessKey;
    private String prefixName;
    private String branchRemark;
    private String communistRemark;
    private Integer creditTimes;
    private Integer scores;
    private Integer scoreType;
    private Integer operType;

}
