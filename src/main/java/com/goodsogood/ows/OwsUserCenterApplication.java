package com.goodsogood.ows;

import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.filter.TranslateRequestFilter;
import com.goodsogood.ows.helper.SpringContextUtil;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableAsync
@EnableTransactionManagement
@EnableScheduling
@EnableCaching
@EnableAspectJAutoProxy
public class OwsUserCenterApplication {

    private final HttpComponentsClientHttpRequestFactory clientHttpRequestFactory;

    @Autowired
    public OwsUserCenterApplication(HttpComponentsClientHttpRequestFactory clientHttpRequestFactory) {
        this.clientHttpRequestFactory = clientHttpRequestFactory;
    }

    /**
     * 调用17321的rest template
     * private val clientHttpRequestFactory: HttpComponentsClientHttpRequestFactory
     *
     * @return RestTemplate
     */
    @Bean
    public RestTemplate jdgcRestTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(120000);
        httpRequestFactory.setConnectTimeout(120000);
        httpRequestFactory.setReadTimeout(120000);

        return new RestTemplate(httpRequestFactory);
    }

    @Bean
    public RestTemplate msgRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    @Bean
    public RestTemplate callbackRestTemplate() {
        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        httpRequestFactory.setConnectionRequestTimeout(30000);
        httpRequestFactory.setReadTimeout(30000);
        httpRequestFactory.setConnectTimeout(30000);
        return new RestTemplate(httpRequestFactory);
    }

    @LoadBalanced
    @Bean //必须new 一个RestTemplate并放入spring容器当中,否则启动时报错
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setErrorHandler(new ClientExceptionHandler());
        return restTemplate;
    }

    @Bean
    public Executor scoreCallbackExecutor() {
        return getExecutor(10, 20, 1000, "asyn-score-login-callback-");
    }

    /**
     * 刷新缓存线程池
     *
     * @return
     */
    @Bean
    public Executor flushCacheExecutor() {
        return getExecutor(10, 20, 5000, "asyn-flush-cache-");
    }

    @Bean
    public Executor callbackExecutor() {
        return getExecutor(10, 20, 5000, "asyn-callback-");
    }

    @Bean
    public Executor saveUserBaseInfoInRedisExecutor() {
        return getExecutor(1, 1, 5, "asyn-save-to-redies-");
    }

    @Bean
    public Executor addMenuUrl() {
        return getExecutor(1, 1, 12000, "asyn-menu-url-");
    }

    @Bean
    public Executor buildTokenBaseExecutor() {
        return getExecutor(1, 1, 500, "asyn-token-base-");
    }

    @Bean
    public Executor insertCallback() {
        return getExecutor(10, 20, 500, "insert-callback-");
    }

    @Bean
    public Executor dealOrgTagExecutor() {
        return getExecutor(10, 20, 10000, "deal-org-tag-");
    }

    @Bean
    public Executor pushDataExecutor() {
        return getExecutor(10, 20, 10000, "volunteer-update-status-");
    }

    @Bean
    public Executor asyncSnapshotExecutor() {
        return getExecutor(1, 5, 100000, "async-snapshot-");
    }

    @Bean
    public Executor asynDisposeExcelExecutor() {
        return getExecutor(1, 1, 1000, "async-dispose-excel-");
    }

    @Bean
    public Executor asyncGenerateExcelExecutor() {
        return getExecutor(5, 10, 100, "async-generate-excel-");
    }

    @Bean
    public Executor asyncMongoExecutor() {
        return getExecutor(5, 10, 100000, "async-mongo-");
    }

    @Bean
    public Executor asyncVolExecutor() {
        return getExecutor(5, 10, 10000, "async-volunteer-");
    }

    @Bean
    public Executor asyncExamUserExecutor() {
        return getExecutor(5, 10, 10000, "async-exam-user-");
    }

    @Bean("dingDingExecutor")
    public Executor dingDingExecutor() {
        return getExecutor(1, 1, 1, "DD_PUSH_");
    }

    @Bean("asyncImportExecutor")
    public Executor asyncImportExecutor() {
        return getExecutor(1, 5, 10000, "ASYNC_IMPORT_EXECUTOR");
    }

    @Bean("saveDevelopLogExecutor")
    public Executor saveDevelopLogExecutor() {
        return getExecutor(1, 2, 1000, "SAVE_DEVELOP_LOG_EXECUTOR");
    }

    @Bean
    public Executor developPushExecutor() {
        return getExecutor(1, 2, 1000, "DEVELOP_PUSH_EXECUTOR");
    }

    @Bean("developAsyncExecutor")
    public Executor developAsyncExecutor() {
        return getExecutor(1, 1, 1000, "DEVELOP_ASYNC");
    }

    @Bean("exportMyGloryExecutor")
    public Executor exportMyGloryExecutor() {
        return getExecutor(1, 1, 5, "EXPORT-MY-GLORY-");
    }

    @Bean("OrgHighlightExecutor")
    public Executor hExecutor() {
        return getExecutor(1, 1, 5, "ORG-HIGHLIGHT-");
    }

    @Bean("userOrgPeriodScoreExecutor")
    public Executor userOrgPeriodScoreExecutor() {
        return getExecutor(1, 1, 1, "async-period-score");
    }

    @Bean("addDynamicInfoExecutor")
    public Executor addDynamicInfoExecutor() {
        return getExecutor(1, 2, 20, "add-dynamic-info");
    }

    private Executor getExecutor(int i, int i2, int i3, String s) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(i);
        executor.setMaxPoolSize(i2);
        executor.setQueueCapacity(i3);
        executor.setThreadNamePrefix(s);
        executor.initialize();
        return executor;
    }

    /**
     * 调用学习系统线程池配置
     *
     * @return
     */
    @Bean
    public Executor commonSynchExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(1);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(100000);
        executor.setThreadNamePrefix("common-synch-");
        executor.initialize();
        return executor;
    }

    /**
     * 忽略https请求ssl证书
     *
     * @return
     * @throws NoSuchAlgorithmException
     * @throws KeyManagementException
     */
    @Bean
    public RestTemplate httpsRestTemplate() throws NoSuchAlgorithmException, KeyManagementException {
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, new TrustManager[]{new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {

            }

            @Override
            public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {

            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                return new X509Certificate[]{};
            }
        }}, new java.security.SecureRandom());
        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        //处理中文乱码
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    /**
     * 获得 拷贝请求的 过滤器（重复提交验证用）
     * @return TranslateRequestFilter
     */
    @Bean
    public FilterRegistrationBean<TranslateRequestFilter> translateRequestFilterRegistration() {
        FilterRegistrationBean<TranslateRequestFilter> registration = new FilterRegistrationBean<>();
        registration.setFilter(new TranslateRequestFilter());
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }

    public static void main(String[] args) {
        // 关闭spring自动装载日志
        ApplicationContext app = SpringApplication.run(OwsUserCenterApplication.class, args);
        SpringContextUtil.setApplicationContext(app);
    }
}
