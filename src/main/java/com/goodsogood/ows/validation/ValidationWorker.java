package com.goodsogood.ows.validation;

import org.springframework.http.HttpHeaders;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/9
 * @description 通过自定义代码去验证动态信息的实现接口
 */
public abstract class ValidationWorker {

    public String name;// 当前字段名称
    public String alias;// 当前字段别名
    public Map<String, ?> data; // 当前请求体
    public String message = ""; // 错误提示信息
    public int errorCode = 0;// 错误码（如果错误，并且错误码不为0的时候，message是errorCode的错误内容）
    public HttpHeaders httpHeaders; //请求头

    /**
     * 关闭空构造函数
     */
    private ValidationWorker() {
    }

    /**
     * @param name        当前字段名称
     * @param alias       当前字段别名
     * @param data        当前字段别名
     * @param httpHeaders 请求头
     */
    public ValidationWorker(String name, String alias, Map<String, ?> data, HttpHeaders httpHeaders) {
        this.name = name;
        this.alias = alias;
        this.data = data;
        this.httpHeaders = httpHeaders;
    }

    /**
     * 验证
     *
     * @return 是否验证通过
     */
    public abstract boolean check();

    /**
     * 获得错误信息
     *
     * @return message
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获得错误码
     *
     * @return errorCode
     */
    public int getErrorCode() {
        return errorCode;
    }

}
