package com.goodsogood.ows.validation

import cn.hutool.core.convert.Convert
import com.goodsogood.ows.aspect.OwsValidationAspect
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.SpringContextUtil
import com.goodsogood.ows.service.OrgService
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.http.HttpHeaders

class OrgNameRepeatWorker(name: String?,
                          alias: String?,
                          data: MutableMap<String, Any?>?,
                          httpHeaders: HttpHeaders?) :
    ValidationWorker(name, alias, data, httpHeaders) {

    private val logger: Logger = LogManager.getLogger(OwsValidationAspect.Checker::class.java)

    override fun check(): Boolean {
        val orgService = SpringContextUtil.getBean(OrgService::class.java) as OrgService
        val orgName = Convert.toStr(data[name])
        val sysHeader = HeaderHelper.buildMyHeader(httpHeaders)
        if (orgService.isNameExists(null, orgName, sysHeader.regionId)) {
            logger.debug("新建组织 - 该组织名称已存在 name:{},regionId:[{}]", orgName, sysHeader.regionId)
            message = "该组织名称已存在"
            errorCode = 600
            return false
        }
        return true
    }
}