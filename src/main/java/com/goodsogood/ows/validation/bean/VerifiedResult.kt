package com.goodsogood.ows.validation.bean

import com.goodsogood.ows.component.Errors

/**
 * 验证结果的封装类
 */
data class VerifiedResult(
    var fieldErrors: Boolean = false,
    // key->fieldsName : value -> fieldsAlias
    val fields: MutableMap<String, String> = mutableMapOf(),
    val messages: MutableMap<String, String?> = mutableMapOf(),
    var errors: MutableMap<String, String> = mutableMapOf(),
    val codes: MutableMap<String, Int> = mutableMapOf(),
    var defaultMessage: String = "no errors",
    var defaultCode: Int? = null
) {
    /**
     * 构造错误码
     */
    fun build(out: Errors): VerifiedResult {
        fieldErrors = fields.isNotEmpty()
        fields.onEach {
            defaultMessage = if (codes[it.key] != null || out.errors[codes[it.key]] != null) {
                defaultCode = codes[it.key]
                out.errors[codes[it.key]]?:"{0}该错误提醒未配置"
            } else {
                defaultCode = null
                (messages[it.key] ?: "{0}该错误提醒未配置")
                    .replace("{0}", "${it.value}[${it.key}]")
            }
            // entry ->  名称：中文名
            errors[it.key] = defaultMessage
        }
        return this
    }

    /**
     * 添加错误字段，该方法为了保证顺序
     * @param fieldsName 验证失败的字段名
     * @param errorCode 错误码。默认为0，如果为0的时候，使用默认错误（9906）
     * @param alias 验证失败的字段别名
     * @param message 提示
     */
    fun pushResult(fieldsName: String, alias: String, message: String?, errorCode: Int = 0) {
        fields[fieldsName] = alias
        messages[fieldsName] = message
        if (errorCode != 0) {
            codes[fieldsName] = errorCode
        }
    }
}
