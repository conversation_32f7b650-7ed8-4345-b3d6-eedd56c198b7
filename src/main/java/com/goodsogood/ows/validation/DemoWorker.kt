package com.goodsogood.ows.validation

import org.springframework.http.HttpHeaders

/**
 * <AUTHOR>
 * @date 2021/2/9
 * @description class DemoWorker
 */
class DemoWorker(name: String?, alias: String?, data: MutableMap<String, Any?>?, httpHeaders: HttpHeaders?) :
    ValidationWorker(name, alias, data, httpHeaders) {
    override fun check(): <PERSON><PERSON><PERSON> {
        message = "demo错误"
        errorCode = 9006
        return false
    }
}