package com.goodsogood.ows.validation

import cn.hutool.core.convert.Convert
import com.goodsogood.ows.aspect.OwsValidationAspect
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.component.UserFieldEnum
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.SpringContextUtil
import com.goodsogood.ows.service.OrgService
import com.goodsogood.ows.service.UserService
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.http.HttpHeaders

class UserPhoneWorker(name: String?,
                      alias: String?,
                      data: MutableMap<String, Any?>?,
                      httpHeaders: HttpHeaders?) :
    ValidationWorker(name, alias, data, httpHeaders) {

    private val logger: Logger = LogManager.getLogger(OwsValidationAspect.Checker::class.java)

    override fun check(): Boolean {
        val userService = SpringContextUtil.getBean(UserService::class.java) as UserService
        val phone = data[name]
        val userId = data[UserFieldEnum.USER_ID.key]
        val code = userService.ValidaPhone(userId as Long?, phone as String?)
        if (code != Constants.STATUS_YES) {
            logger.debug("人员手机号验证失败 userId:{}, phone:{}", userId, phone)
            message = "人员手机号验证失败"
            errorCode = code
            return false
        }
        return true
    }
}