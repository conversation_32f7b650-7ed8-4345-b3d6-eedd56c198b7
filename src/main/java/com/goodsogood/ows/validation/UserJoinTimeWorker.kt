package com.goodsogood.ows.validation

import com.goodsogood.ows.aspect.OwsValidationAspect
import com.goodsogood.ows.common.Utils
import com.goodsogood.ows.component.UserFieldEnum
import org.apache.commons.lang.StringUtils

import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.http.HttpHeaders

class UserJoinTimeWorker(name: String?,
                         alias: String?,
                         data: MutableMap<String, Any?>?,
                         httpHeaders: HttpHeaders?) :
    ValidationWorker(name, alias, data, httpHeaders) {

    private val logger: Logger = LogManager.getLogger(OwsValidationAspect.Checker::class.java)

    override fun check(): Boolean {
        val joiningTime = data[UserFieldEnum.JOINING_TIME.key] as String?
        val joiningPositiveTime = data[UserFieldEnum.JOINING_POSITIVE_TIME.key] as String?
        // 校验入党日期和转正日期
        if (StringUtils.isNotBlank(joiningTime) && StringUtils.isNotBlank(joiningPositiveTime)) {
            if (Utils.compareDate(joiningPositiveTime, joiningTime)) {
                logger.debug("新增党员-入党时间不能晚于转正时间joinTime[{}],joiningPositiveTime[{}]", joiningTime, joiningPositiveTime)
                message = "入党时间不能晚于转正时间"
                errorCode = 30009
                return false
            }
        }
        return true
    }
}