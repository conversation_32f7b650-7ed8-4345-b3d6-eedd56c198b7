package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.TempGroupMapper;
import com.goodsogood.ows.mapper.TempRelationMapper;
import com.goodsogood.ows.mapper.UserMapper;
import com.goodsogood.ows.model.db.TempGroupEntity;
import com.goodsogood.ows.model.db.TempRelationEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TempGroupForm;
import com.goodsogood.ows.model.vo.TempRelationNewVo;
import com.goodsogood.ows.model.vo.TempRelationVo;
import lombok.extern.log4j.Log4j2;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Log4j2
public class TempGroupService {
    private final TempGroupMapper tempGroupMapper;
    private final TempRelationMapper tempRelationMapper;
    private final UserMapper userMapper;
    private final Errors errors;


    @Autowired
    public TempGroupService(TempGroupMapper tempGroupMapper,
                            TempRelationMapper tempRelationMapper,
                            UserMapper userMapper,
                            Errors errors) {
        this.tempGroupMapper = tempGroupMapper;
        this.tempRelationMapper = tempRelationMapper;
        this.userMapper = userMapper;
        this.errors = errors;
    }

    /**
     * 分页查询
     *
     * @param headers
     * @param name
     * @param type
     * @param orgId
     * @param page
     * @param pageSize
     * @return
     */
    public Page<TempGroupEntity> queryListForPage(HttpHeaders headers, String name, Short type, Long orgId, Integer page, Integer pageSize) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(orgId)) {//为空则查询自己组织创建的临时群组
            orgId = header.getOid();
        }
        Long finalOrgId = orgId;

        return PageHelper.startPage(page, pageSize).doSelectPage(() -> this.queryList(name, type, finalOrgId, header.getRegionId()));
    }

    /**
     * 列表查询
     *
     * @param name
     * @param type
     * @param orgId
     * @return
     */
    public List<TempGroupEntity> queryList(String name, Short type, Long orgId, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(orgId)) {//为空则查询自己组织创建的临时群组
            orgId = header.getOid();
        }
        Long finalOrgId = orgId;
        return queryList(name, type, finalOrgId, header.getRegionId());
    }

    /**
     * 列表查询
     *
     * @param name
     * @param type
     * @param orgId
     * @return
     */
    private List<TempGroupEntity> queryList(String name, Short type, Long orgId, Long regionId) {
        if (type.equals(Constants.TEMP_GROUP_TASK)) {//如果是任务系统使用的临时群组
            Example example = new Example(TempGroupEntity.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("regionId", regionId);
            criteria.andEqualTo("orgId", orgId);
            criteria.andNotEqualTo("isDel", 1);
            if (!Objects.isNull(name)) {

                criteria.andLike("name", "%"+name+"%");
            }
            example.orderBy("createTime").desc();
            example.selectProperties("tempGroupId", "orgName", "name", "explation");//说明内容不多，直接返回前端
            return tempGroupMapper.selectByExample(example);
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 单个详情查询
     *
     * @param headers
     * @param tempGroupId
     * @return
     */
    public TempRelationVo queryInfo(HttpHeaders headers, Long tempGroupId, Integer page, Integer pageSize) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        TempGroupEntity tempGroupEntity = tempGroupMapper.selectByPrimaryKey(tempGroupId);
        TempRelationVo vo = new TempRelationVo();
        vo.setTempGroupId(tempGroupId);
        vo.setOrgName(tempGroupEntity.getOrgName());
        vo.setName(tempGroupEntity.getName());
        vo.setHasRelate(tempGroupEntity.getHasRelate());
        vo.setExplation(tempGroupEntity.getExplation());
        //根据群组id查询关联用户ids
        List<TempRelationEntity> userRelationEntity = getTempRelationEntities(tempGroupId);
        if (CollectionUtils.isEmpty(userRelationEntity)) {
            return vo;
        }
        List<Long> userIds = userRelationEntity.stream().map(TempRelationEntity::getUserId).collect(Collectors.toList());
        Page<TempRelationVo.RelateUserVo> userInfo = PageHelper.startPage(page, pageSize).doSelectPage(() -> userMapper.queryTempGroupUser(userIds));
        vo.setRelateUser(userInfo);
        vo.setTotal(userInfo.getTotal());
        return vo;
    }

    /**
     * 单个详情查询--用户不分页
     *
     * @param headers
     * @param tempGroupId
     * @return
     */
    public TempRelationNewVo queryUserInfoList(HttpHeaders headers, Long tempGroupId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        TempGroupEntity tempGroupEntity = tempGroupMapper.selectByPrimaryKey(tempGroupId);
        TempRelationNewVo vo = new TempRelationNewVo();
        vo.setTempGroupId(tempGroupId);
        vo.setOrgName(tempGroupEntity.getOrgName());
        vo.setName(tempGroupEntity.getName());
        vo.setHasRelate(tempGroupEntity.getHasRelate());
        vo.setExplation(tempGroupEntity.getExplation());
        //根据群组id查询关联用户ids
        List<TempRelationEntity> userRelationEntity = getTempRelationEntities(tempGroupId);
        if (CollectionUtils.isEmpty(userRelationEntity)) {
            return vo;
        }
        List<Long> userIds = userRelationEntity.stream().map(TempRelationEntity::getUserId).collect(Collectors.toList());
        vo.setRelateUser(userMapper.queryTempGroupUserList(userIds));
        return vo;
    }


    /**
     * 根据前端传入的用户ids获取用户信息-分页
     *
     * @param userIds
     * @return
     */
    public Page<TempRelationVo.RelateUserVo> queryTempUser(List<Long> userIds, Integer page, Integer pageSize) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new Page<>();
        }
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> userMapper.queryTempGroupUser(userIds));
    }



    /**
     * 根据前端传入的用户ids获取用户信息-不分页
     *
     * @param userIds
     * @return
     */
    public List<TempRelationNewVo.RelateUserVo> queryTempUserList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return userMapper.queryTempGroupUserList(userIds);
    }

    /**
     * 根据tempGroupId查询关联关系表
     *
     *
     * @param tempGroupId
     * @return
     */
    private List<TempRelationEntity> getTempRelationEntities(Long tempGroupId) {
        Example example = new Example(TempRelationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("tempGroupId", tempGroupId);
        return tempRelationMapper.selectByExample(example);
    }

    /**
     * 新增或编辑群组：包含对关联关系的新增移除
     *
     * @param headers
     * @param tempGroupForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean addGroup(HttpHeaders headers, TempGroupForm tempGroupForm) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(tempGroupForm.getTempGroupId())) {//id为空说明是新增群组
            TempGroupEntity tempGroupEntity = addTempGroup(tempGroupForm, header);
            addUser(header, tempGroupEntity.getTempGroupId(), tempGroupForm.getUserIds());
            return true;
        }
        //判断是否被关联，没有被关联才能编辑--目前都是没有关联
        if (checkRealted(tempGroupForm.getTempGroupId())) {
            throw new ApiException("该群组已被关联不允许修改", new Result<>(errors, 33100, HttpStatus.OK.value()));
        }
        updateTempGroupEntity(tempGroupForm, header);
//        if (CollectionUtils.isEmpty(tempGroupForm.getUserIds())) {
//            return true;
//        }
        log.debug("查询群组关联的人员id");
        List<TempRelationEntity> userRelation = getTempRelationEntities(tempGroupForm.getTempGroupId());
        List<Long> userDb = userRelation.stream().map(TempRelationEntity::getUserId).collect(Collectors.toList());
        List<Long> userDbTemp = new ArrayList<>(userDb);
        List<Long> userId = tempGroupForm.getUserIds();//测试remove不存在的值
        //如果有减少的则删除
        log.debug("mt_删除前"+userDbTemp);
        userDbTemp.removeAll(userId);
        log.debug("mt_删除后"+userDbTemp);
        delUser(headers, userDbTemp,tempGroupForm.getTempGroupId());
        //如果有新增的则插入
        userId.removeAll(userDb);
        addUser(header, tempGroupForm.getTempGroupId(), userId);
        return true;
    }

    private void updateTempGroupEntity(TempGroupForm tempGroupForm, HeaderHelper.SysHeader header) {
        TempGroupEntity tempGroupEntity = new TempGroupEntity();
        LocalDateTime now = LocalDateTime.now();
        tempGroupEntity.setTempGroupId(tempGroupForm.getTempGroupId());
        tempGroupEntity.setUpdateTime(now);
        tempGroupEntity.setLastChangeUser(header.getUserId());
        tempGroupEntity.setOrgName(header.getOrgName());
        tempGroupEntity.setRegionId(header.getRegionId());
        tempGroupEntity.setType(tempGroupForm.getType());
        tempGroupEntity.setName(tempGroupForm.getName());
        tempGroupEntity.setExplation(tempGroupForm.getExplation() == null ? "" : tempGroupForm.getExplation());
        tempGroupMapper.updateByPrimaryKeySelective(tempGroupEntity);
    }

    private TempGroupEntity addTempGroup(TempGroupForm tempGroupForm, HeaderHelper.SysHeader header) {
        TempGroupEntity tempGroupEntity = new TempGroupEntity();
        LocalDateTime now = LocalDateTime.now();
        tempGroupEntity.setCreateTime(now);
        tempGroupEntity.setCreateUser(header.getUserId());
        tempGroupEntity.setOrgId(header.getOid());
        tempGroupEntity.setOrgName(header.getOrgName());
        tempGroupEntity.setRegionId(header.getRegionId());
        tempGroupEntity.setType(tempGroupForm.getType());
        tempGroupEntity.setName(tempGroupForm.getName());
        tempGroupEntity.setExplation(tempGroupForm.getExplation() == null ? "" : tempGroupForm.getExplation());
        tempGroupMapper.insert(tempGroupEntity);
        return tempGroupEntity;
    }

    private boolean checkRealted(Long tempGroupId) {
        return tempGroupMapper.selectByPrimaryKey(tempGroupId).getHasRelate() == 1;
    }


    private void addUser(HeaderHelper.SysHeader header, Long tempGroupId, List<Long> userId) {
        if (CollectionUtils.isEmpty(userId)) {
            return;
        }
        List<TempRelationEntity> list = new ArrayList<>();
        userId.forEach(i -> {
            TempRelationEntity tempRelationEntity = new TempRelationEntity();
            tempRelationEntity.setTempGroupId(tempGroupId);
            tempRelationEntity.setUserId(i);
            tempRelationEntity.setCreateUser(header.getUserId());
            tempRelationEntity.setCreateTime(LocalDateTime.now());
            list.add(tempRelationEntity);
        });
        tempRelationMapper.insertList(list);
    }

    /**
     * 根据关联表id移除人员
     *
     * @param headers
     * @param userIds
     * @param groupId
     * @return
     */
    public Boolean delUser(HttpHeaders headers, List<Long> userIds,Long groupId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return true;
        }
        Example example = new Example(TempRelationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("userId", userIds);
        criteria.andEqualTo("tempGroupId",groupId);
        tempRelationMapper.deleteByExample(example);
        return true;
    }


    /**
     * 删除群组
     *
     * @param headers
     * @param tempGroupIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delGroup(HttpHeaders headers, List<Long> tempGroupIds) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("删除群组");
        tempGroupMapper.delGroup(tempGroupIds, header.getUserId());
        log.debug("根据群组id删除关联人员");
        tempRelationMapper.delUser(tempGroupIds, header.getUserId());
        return true;
    }


    public Page<TempRelationVo.RelateUserVo> queryUserInfoByGroup(Long tempGroupId, Integer page, Integer pageSize) {
        List<TempRelationEntity> relationEntities = getTempRelationEntities(tempGroupId);
        List<Long> userIds = relationEntities.stream().map(TempRelationEntity::getUserId).collect(Collectors.toList());
        return queryTempUser(userIds, page, pageSize);
    }
}
