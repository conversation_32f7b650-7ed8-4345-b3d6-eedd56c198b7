package com.goodsogood.ows.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.common.ErrorCode;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.ConsigneeAddressMapper;
import com.goodsogood.ows.mapper.ConsigneeAddressOutMapper;
import com.goodsogood.ows.model.db.ConsigneeAddressEntity;
import com.goodsogood.ows.model.db.ConsigneeAddressOutEntity;
import com.goodsogood.ows.model.vo.ConsigneeAddressForm;
import com.goodsogood.ows.model.vo.Result;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 收货人地址管理服务类
 *
 * <AUTHOR>
 * @date 2019/12/10
 */
@Service
@Log4j2
public class ConsigneeAddressService {

    private final Errors errors;
    private final ConsigneeAddressMapper consigneeAddressMapper;
    private final ConsigneeAddressOutMapper consigneeAddressOutMapper;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public ConsigneeAddressService(Errors errors, ConsigneeAddressMapper consigneeAddressMapper, ConsigneeAddressOutMapper consigneeAddressOutMapper, StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.consigneeAddressMapper = consigneeAddressMapper;
        this.consigneeAddressOutMapper = consigneeAddressOutMapper;
        this.redisTemplate = redisTemplate;
    }


    /**
     * 检查上报的用户收货人地址字段
     */
    public void checkConsigneeAddress(ConsigneeAddressForm form) {
        String logText = "<用户收货人地址推送>";
        log.debug(logText + "参数：param = {} ", form);
        if (StringUtils.isEmpty(form.getUserId())) {
            log.error(logText + "：参数校验： user_id(用户编号) 不能为空");
            throw new ApiException(logText + "：参数校验： user_id(用户编号) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "user_id(用户编号) 不能为空"));
        }
        if (StringUtils.isEmpty(form.getAddressType())) {
            log.error(logText + "：参数校验： address_type(地址来源) 不能为空");
            throw new ApiException(logText + "：参数校验： address_type(地址来源) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "address_type(地址来源) 不能为空"));
        }
        if(form.getAddressType() == Config.ConsigneeAddress.ADDRESS_TYPE_DJ){
            if (StringUtils.isEmpty(form.getAddressId())) {
                log.error(logText + "：参数校验： address_id(地址编号) 不能为空");
                throw new ApiException(logText + "：参数校验： address_id(地址编号) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "address_id(地址编号) 不能为空"));
            }
        }
        if (form.getAddressType() != Config.ConsigneeAddress.ADDRESS_TYPE_DJ) {
            if (StringUtils.isEmpty(form.getToken())) {
                log.error(logText + "：参数校验： token(唯一标识) 不能为空");
                throw new ApiException(logText + "：参数校验： token(唯一标识) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "token(唯一标识) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getOutAddressId())) {
                log.error(logText + "：参数校验： out_address_id(外部地址编号) 不能为空");
                throw new ApiException(logText + "：参数校验： out_address_id(外部地址编号) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "out_address_id(外部地址编号) 不能为空"));
            }
        }
        if (StringUtils.isEmpty(form.getOperationType())) {
            log.error(logText + "：参数校验： operation_type(操作类型) 不能为空");
            throw new ApiException(logText + "：参数校验： operation_type(操作类型) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "operation_type(操作类型) 不能为空"));
        }
        if(form.getOperationType()!=Config.ConsigneeAddress.OPERATION_TYPE_DEL){
            if (StringUtils.isEmpty(form.getUserName())) {
                log.error(logText + "：参数校验： user_name(收货人名称) 不能为空");
                throw new ApiException(logText + "：参数校验： user_name(收货人名称) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "user_name(收货人名称) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getUserPhone())) {
                log.error(logText + "：参数校验： user_phone(收货人联系电话) 不能为空");
                throw new ApiException(logText + "：参数校验： user_phone(收货人联系电话) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "user_phone(收货人联系电话) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getProvinceCode())) {
                log.error(logText + "：参数校验： province_code(省编码) 不能为空");
                throw new ApiException(logText + "：参数校验： province_code(省编码) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "province_code(省编码) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getProvince())) {
                log.error(logText + "：参数校验： province(省名称) 不能为空");
                throw new ApiException(logText + "：参数校验：  province(省名称) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "province(省名称) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getCityCode())) {
                log.error(logText + "：参数校验： city_code(市编码) 不能为空");
                throw new ApiException(logText + "：参数校验： city_code(市编码) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "city_code(市编码) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getCity())) {
                log.error(logText + "：参数校验： city(市名称) 不能为空");
                throw new ApiException(logText + "：参数校验： city(市名称) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "city(市名称) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getAreaCode())) {
                log.error(logText + "：参数校验： area_code(区编码) 不能为空");
                throw new ApiException(logText + "：参数校验： area_code(区编码) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "area_code(区编码) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getArea())) {
                log.error(logText + "：参数校验： area(区名称) 不能为空");
                throw new ApiException(logText + "：参数校验： area(区名称) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "area(区名称) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getStreetCode())) {
                log.error(logText + "：参数校验： street_code(街道编码) 不能为空");
                throw new ApiException(logText + "：参数校验： street_code(街道编码) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "street_code(街道编码) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getStreet())) {
                log.error(logText + "：参数校验： street(街道名称) 不能为空");
                throw new ApiException(logText + "：参数校验： street(街道名称) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "street(街道名称) 不能为空"));
            }
            if (StringUtils.isEmpty(form.getAddress())) {
                log.error(logText + "：参数校验： address(详细地址) 不能为空");
                throw new ApiException(logText + "：参数校验： address(详细地址) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "address(详细地址) 不能为空"));
            }
        }
        if (form.getOperationType()!=Config.ConsigneeAddress.OPERATION_TYPE_DEL&&StringUtils.isEmpty(form.getDefaultFlag())) {
            log.error(logText + "：参数校验： default_flag(默认标记) 不能为空");
            throw new ApiException(logText + "：参数校验： default_flag(默认标记) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "default_flag(默认标记) 不能为空"));
        }
        if(form.getAddressType() != Config.ConsigneeAddress.ADDRESS_TYPE_DJ){
            //检查数据是否重复
            checkToken(form.getAccessKey(), form.getToken(),form.getOutAddressId());
        }
    }

    /**
     * 检查并缓存token
     *
     * @param accessKey
     * @param token
     */
    public void checkToken(String accessKey, String token,String outAddressId) {
        String key = Config.PUSH_TOKEN_PREFIX + accessKey + "_" + token;
        String ck = redisTemplate.opsForValue().get(key);
        if (ck != null && ck.equals(token)) {
            log.error("重复校验：重复推送数据,token:" + token);
            //获取地址编号addressId
            Example example = new Example(ConsigneeAddressOutEntity.class);
            example.createCriteria().andEqualTo("accessKey", accessKey).andEqualTo("outAddressId", outAddressId);
            ConsigneeAddressOutEntity caoe = consigneeAddressOutMapper.selectOneByExample(example);
            String addressId = caoe!=null?caoe.getAddressId().toString():"-1";
            throw new ApiException("重复校验：重复推送数据", new Result<>(errors, ErrorCode.PUSH_REPEAT_SUBMIT_ERROR, HttpStatus.OK.value(), addressId));
        } else {
            //检查通过就缓存
            redisTemplate.opsForValue().set(key, token);
            redisTemplate.expire(key, Config.ConsigneeAddress.PUSH_TOKEN_TIMEOUT, TimeUnit.DAYS);
        }
    }

    /**
     * 处理失败后删除缓存的token
     *
     * @param accessKey
     * @param token
     */
    public void delToken(String accessKey, String token) {
        String key = Config.PUSH_TOKEN_PREFIX + accessKey + "_" + token;
        redisTemplate.delete(key);
    }

    /**
     * 添加收货人地址通用方法
     */
    public Long addAddress(ConsigneeAddressForm form){
        ConsigneeAddressEntity cae = new ConsigneeAddressEntity();
        BeanUtil.copyProperties(form,cae,CopyOptions.create().setIgnoreNullValue(true));
        consigneeAddressMapper.addConsigneeAddress(cae);
        //如果来源不是党建，添加外部收货地址关联表
        if(form.getAddressType()!=1){
            ConsigneeAddressOutEntity caeOut =new ConsigneeAddressOutEntity();
            caeOut.setAccessKey(form.getAccessKey());
            caeOut.setAddressId(cae.getAddressId());
            caeOut.setAddressType(form.getAddressType());
            caeOut.setOutAddressId(form.getOutAddressId());
            consigneeAddressOutMapper.insert(caeOut);
        }
        return cae.getAddressId();
    }

    /**
     * 添加收货人地址
     */
    @Transactional
    public Long addConsigneeAddress(ConsigneeAddressForm form){
        Long addressId;
        try{
            //新增新的默认地址
            addressId = this.addAddress(form);
        }catch(Exception e){
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
        return addressId;
    }

    /**
     * 添加默认收货人地址
     */
    @Transactional
    public Long addConsigneeAddressForDefaultFlag(ConsigneeAddressForm form){
        Long addressId;
        try{
            //查询之前默认地址的主键ID,然后再通过主键进行修改()
            Long defaultAddressId = consigneeAddressMapper.findDefaultAddressId(form);
            //根据主键取消之前的默认地址
            consigneeAddressMapper.cancelDefaultAddress(defaultAddressId);
            //新增新的默认地址
            addressId = this.addAddress(form);
        }catch(Exception e){
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
        return addressId;
    }

    /**
     * 修改收货人地址
     */
    public void updConsigneeAddress(ConsigneeAddressForm form){
        ConsigneeAddressEntity cae = new ConsigneeAddressEntity();
        BeanUtil.copyProperties(form,cae,CopyOptions.create().setIgnoreNullValue(true));
        cae.setUpdateTime(new Date());
        consigneeAddressMapper.updateByPrimaryKey(cae);
    }

    /**
     * 修改默认的收货人地址
     */
    @Transactional
    public void updConsigneeAddressForDefaultFlag(ConsigneeAddressForm form){
        try{
            //查询之前默认地址的主键ID,然后再通过主键进行修改()
            Long defaultAddressId = consigneeAddressMapper.findDefaultAddressId(form);
            //根据主键取消之前的默认地址
            consigneeAddressMapper.cancelDefaultAddress(defaultAddressId);
            //更新新的默认地址
            this.updConsigneeAddress(form);
        }catch(Exception e){
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    /**
     * 删除收货人地址
     */
    public void delConsigneeAddress(ConsigneeAddressForm form){
        ConsigneeAddressEntity cae = new ConsigneeAddressEntity();
        if(form.getAddressType()!=1){
            //如果不是党建的地址，通过数据库查找addressId
            cae.setAddressId(findAddressId(form));
        }else {
            cae.setAddressId(form.getAddressId());
        }
        cae.setUpdateTime(new Date());
        cae.setStatus(0);
        consigneeAddressMapper.updateByPrimaryKeySelective(cae);
    }

    /**
     * 删除默认的收货人地址
     */
    @Transactional
    public void delConsigneeAddressForDefaultFlag(ConsigneeAddressForm form){
        try{
            //更新新的默认地址
            this.delConsigneeAddress(form);
            //设置最新的一条为默认地址
            consigneeAddressMapper.setDefaultAddress(form);
        }catch(Exception e){
            // 配合Transactional手动让spring回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw e;
        }
    }

    /**
     * 添加外部用户收货人地址关联信息
     * @param form
     * @return
     */
    public void addOutAddressInfo(ConsigneeAddressForm form){
        consigneeAddressOutMapper.addOutAddressInfo(form);
    }

    /**
     * 根据外部用户收货地址编号查找相关联的党建用户收货地址编号
     * @param form
     * @return
     */
    public Long findAddressId(ConsigneeAddressForm form){
        Example example = new Example(ConsigneeAddressOutEntity.class);
        example.createCriteria().andEqualTo("accessKey", form.getAccessKey()).andEqualTo("outAddressId", form.getOutAddressId());
        ConsigneeAddressOutEntity caoe = consigneeAddressOutMapper.selectOneByExample(example);
        if(!(caoe!=null&&caoe.getAddressId()!=null)){
            log.error("通过外部渠道标识(accessKey)和外部收货地址编号(outAddressId)查询党建收货地址编号(addressId)出错！form={}",form);
            throw new RuntimeException("通过外部渠道标识(accessKey)和外部收货地址编号(outAddressId)查询党建收货地址编号(addressId)出错！");
        }
        return caoe.getAddressId();
    }

    /**
     * 根据党建用户收货地址编号，查询地址信息
     * @param form
     * @return
     */
    public ConsigneeAddressEntity findConsigneeAddressInfo(ConsigneeAddressForm form){
        Example example = new Example(ConsigneeAddressEntity.class);
        example.createCriteria().andEqualTo("addressId", form.getAddressId());
        ConsigneeAddressEntity cae = consigneeAddressMapper.selectOneByExample(example);
        return cae;
    }
}
