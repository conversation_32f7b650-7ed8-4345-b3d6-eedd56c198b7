package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.goodsogood.ows.aspect.OwsValidationAspect;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.JsonUtils;
import com.goodsogood.ows.common.RateUtils;
import com.goodsogood.ows.common.ReflectUtil;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldComponentEnum;
import com.goodsogood.ows.component.UserFieldEnum;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.UserOrgAndCorpMapper;
import com.goodsogood.ows.model.db.FieldEntity;
import com.goodsogood.ows.model.db.FieldRuleEntity;
import com.goodsogood.ows.model.db.UserOrgAndCorpEntity;
import com.goodsogood.ows.model.mongo.Org;
import com.goodsogood.ows.model.mongo.User;
import com.goodsogood.ows.model.vo.AttrForm;
import com.goodsogood.ows.model.vo.DyOptionsForm;
import com.goodsogood.ows.model.vo.OrgUserForm;
import com.goodsogood.ows.model.vo.UserForm;
import com.goodsogood.ows.validation.bean.VerifiedResult;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Log4j2
public class ImportUserAsyncService {

    @Value("${import-user.file-vild}")
    private Long importFileValid;

    private final Errors errors;
    private final UserService userService;
    private final FieldService fieldService;
    private final OwsValidationAspect.Checker checker;
    private final OrgUserService orgUserService;
//    private final ExaminationService examinationService;
    private final StringRedisTemplate redisTemplate;
    @Autowired
    public ImportUserAsyncService(Errors errors,
                                  UserService userService, FieldService fieldService,
                                  OwsValidationAspect.Checker checker,
                                  OrgUserService orgUserService,
                                  StringRedisTemplate redisTemplate) {
        this.errors = errors;
        this.userService = userService;
        this.fieldService = fieldService;
        this.checker = checker;
        this.orgUserService = orgUserService;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 异步上传
     * @param excelDatas    表格数据
     * @param fieldList     字段数据
     * @param orgList       组织列表
     * @param uuid          串联码/计数器
     * @param headers       请求头
     */
    @Async("asyncImportExecutor")
    public void asyncImportUser(List<List<String>> excelDatas, List<FieldEntity> fieldList, List<Org> orgList,
                                String uuid,  HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 获取校验规则
        Map<FieldEntity, FieldRuleEntity> ruleEntityMap = this.fieldService.fieldRule(fieldList);
        // 失败列表，到处完毕后，需要保存到redis
        List<List<String>> errorList = new ArrayList<>();
        // 成功数量
        int success = 0;
        // 失败数量
        int failure = 0;
        // 总数
        int total = excelDatas.size() - 1;
        RateUtils.delete(uuid);
        RateUtils.build((total), uuid);
        // 清理缓存
        this.deleteCache(uuid);
        this.redisTemplate.opsForValue().set(Constants.USER_IMPORT_TOTAL_REDIS_KEY + uuid, String.valueOf(total), importFileValid, TimeUnit.DAYS);
        for (int i = 1; i <= total; i++) {
            List<String> datas = excelDatas.get(i);
            try {
                log.debug("第[{}]行 -> 数据：[{}]", i, datas);
                Map<String, Object> objectMap = new HashMap<>(datas.size());
                for (int j = 0, dataSize = datas.size(); j < dataSize; j++) {
                    String data = datas.get(j);
                    if (j == fieldList.size()) {
                        datas.remove(j);
                        break;
                    }
                    FieldEntity field = fieldList.get(j);
                    if (StringUtils.isNotBlank(data)) {
                        Integer componentType = field.getComponentType();
                        if (Objects.nonNull(componentType)) {
                            switch (Objects.requireNonNull(FieldComponentEnum.get(componentType))) {
                                case SELECT:
                                case RADIO:
                                    AttrForm attr = field.getAttr();
                                    if (Objects.nonNull(attr)) {
                                        List<DyOptionsForm> options = attr.getOptions();
                                        if (!CollectionUtils.isEmpty(options)) {
                                            String finalData1 = data;
                                            Optional<DyOptionsForm> formOptional = options.stream()
                                                    .filter(option -> option.getLabel().equals(finalData1)).findFirst();
                                            if (formOptional.isPresent()) {
                                                DyOptionsForm optionsForm = formOptional.get();
                                                data = optionsForm.getValue();
                                            }
                                        }
                                    }
                                    break;
                                case TIME:
                                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                    Date date;
                                    try {
                                        date = dateFormat.parse(data);
                                    } catch (Exception e) {
                                        try {
                                            date = DateUtil.parse(data);
                                        } catch (Exception e1) {
                                            throw new Exception("时间格式错误，请参照 2020-01-01");
                                        }
                                    }
                                    data = dateFormat.format(date);
                                    datas.add(j, data);
                                    datas.remove(j + 1);
                                    break;
                                case ORG:
                                    if (!CollectionUtils.isEmpty(orgList)) {
                                        String finalData = data;
                                        Optional<Org> orgOptional = orgList.stream().filter(org -> org.getName().equals(finalData)).findFirst();
                                        if (orgOptional.isPresent()) {
                                            Org org = orgOptional.get();
                                            data = org.getOrganizationId().toString();
                                        } else {
                                            throw new Exception("请选择列表中的组织");
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        objectMap.put(field.getFieldName(), data);
                    }
                }
                objectMap.put("flag", 1);
                log.debug("第[{}]行 -> 组装数据 -> [{}]", i, objectMap);
                // 校验数据
                VerifiedResult result = new VerifiedResult();
                result = this.checker.checkRule(objectMap, ruleEntityMap, headers, result);
                if (result.build(errors).getFieldErrors()) {
                    log.error("验证字段错误 -> [{}]", result.getMessages());
                    throw new Exception(result.getDefaultMessage());
                }
                // 以下为特殊处理
                // 手机号
                if (objectMap.containsKey(UserFieldEnum.PHONE.getKey())) {
                    String phone = Convert.toStr(objectMap.get(UserFieldEnum.PHONE.getKey()));
                    String name = Convert.toStr(objectMap.get(UserFieldEnum.NAME.getKey()));
                    // 查询人员是否存在系统中
                    final UserForm form = this.userService.findIdByPhone(phone);
                    if (Objects.nonNull(form) && !form.getName().equals(name)) {
                        throw new Exception("手机号与姓名不匹配");
                    }
                    if (Objects.nonNull(form) && Objects.nonNull(form.getUserId())) {
                        objectMap.put(UserFieldEnum.USER_ID.getKey(), form.getUserId());
                    }
                    objectMap.put(UserFieldEnum.PHONE_SECRET.getKey(), NumEncryptUtils.numSecret(phone, NumEncryptUtils.PHONE));
                    objectMap.put(UserFieldEnum.PHONE.getKey(), NumEncryptUtils.encrypt(phone, NumEncryptUtils.PHONE));
                }
                // 身份证
                if (objectMap.containsKey(UserFieldEnum.CERT_NUMBER.getKey())) {
                    String certNumber = Convert.toStr(objectMap.get(UserFieldEnum.CERT_NUMBER.getKey()));
                    objectMap.put(UserFieldEnum.CERT_NUMBER_SECRET.getKey(), NumEncryptUtils.numSecret(certNumber, NumEncryptUtils.IDCARD));
                    objectMap.put(UserFieldEnum.CERT_NUMBER.getKey(), NumEncryptUtils.encrypt(certNumber, NumEncryptUtils.IDCARD));
                }
                // 籍贯 暂不支持

                OrgUserForm orgUserForm = this.orgUserService.getOrgUserByMap(objectMap);
                Long userId = this.orgUserService.addOrgUser(orgUserForm, Constants.STATUS_YES, header);
                if (Objects.isNull(userId)) {
                    log.error("新增党员-系统异常，新增组织人员失败");
                    throw new Exception("新增党员失败，请联系管理员");
                } else {
                    // 调用后续操作
                    Map<String, Object> dynamicField = ReflectUtil.getDynamicField(objectMap, User.class);
                    this.orgUserService.afterDealWithUser(userId, orgUserForm.getOrgId(), dynamicField, 1, true, header);
                }
//                try {
//                    //新增用户成功过后 同步数据到考试系统
//                    examinationService.synUserInfo(Collections.singletonList(orgUserForm.getOrgId()), 2, header);
//                } catch (Exception e) {
//                    log.error("同步考试系统报错");
//                }
                log.debug("新增党员-完成user[{}]", objectMap);
                this.redisTemplate.opsForValue().set(Constants.USER_IMPORT_SUCCESS_REDIS_KEY + uuid, String.valueOf(++success), importFileValid, TimeUnit.DAYS);
            } catch (Exception e) {
                log.error("批量新增人员报错 -> ", e);
                datas.add(e.getMessage());
                errorList.add(datas);
                this.redisTemplate.opsForValue().set(Constants.USER_IMPORT_FAILURE_REDIS_KEY + uuid, String.valueOf(++failure), importFileValid, TimeUnit.DAYS);
            } finally {
                RateUtils.auto(uuid);
            }
        }
        log.debug("数据处理完毕");
        this.redisTemplate.opsForValue().set(Constants.USER_IMPORT_ERROR_REDIS_KEY + uuid, JsonUtils.toJson(errorList),  importFileValid, TimeUnit.DAYS);
    }

    public void deleteCache(String uuid) {
        List<String> redisKeyList = new ArrayList<>(4);
        redisKeyList.add(Constants.USER_IMPORT_SUCCESS_REDIS_KEY + uuid);
        redisKeyList.add(Constants.USER_IMPORT_FAILURE_REDIS_KEY + uuid);
        redisKeyList.add(Constants.USER_IMPORT_ERROR_REDIS_KEY + uuid);
        redisKeyList.add(Constants.USER_IMPORT_TOTAL_REDIS_KEY + uuid);
        this.redisTemplate.delete(redisKeyList);
    }
}
