package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.HttpClientUtil;
import com.goodsogood.ows.common.MD5Helper;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 同步用户信息service
 *
 * <AUTHOR>
 * @Date 2018-06-22 13:57
 */
@Service
@Log4j2
public class SynchService {

    @Value("${broadcast.authCode}")
    private String authCode;

    @Value("${broadcast.authUrl}")
    private String authUrl;

    @Value("${ppmd-tag-name}")
    private String ppmdTagName;

    private final UserMapper userMapper;
    private final UserThirdMapper userThirdMapper;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final DepartmentMapper departmentMapper;
    private final OrganizationMapper organizationMapper;
    private final SynchOrgHisMapper synchOrgHisMapper;
    private final SynchDepHisMapper synchDepHisMapper;
    private final SynchUserHisMapper synchUserHisMapper;
    private final SynchUserDepHisMapper synchUserDepHisMapper;
    private final UserDepartmentMapper userDepartmentMapper;
    private final TagMapper tagMapper;
    private final Errors errors;
    private final BroadcastService broadcastService;
    private final OrgService orgService;
    private final OrgUserService orgUserService;
    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;
    private final UserUpdateHisMapper userUpdateHisMapper;
    private final UserOrgCacheCallService userOrgCacheCallService;
    private final CallbackService callbackService;
    private final User12371Mapper user12371Mapper;
    private final Org12371Mapper org12371Mapper;
    private final UserHsMapper userHsMapper;
    private final UserThirdHsMapper userThirdHsMapper;
    private final UserChangeLogMapper userChangeLogMapper;

    @Autowired
    @Lazy
    public SynchService(UserMapper userMapper,
                        UserThirdMapper userThirdMapper,
                        UserOrgAndCorpMapper userOrgAndCorpMapper, DepartmentMapper departmentMapper, OrganizationMapper organizationMapper,
                        SynchOrgHisMapper synchOrgHisMapper, SynchDepHisMapper synchDepHisMapper, SynchUserHisMapper synchUserHisMapper,
                        SynchUserDepHisMapper synchUserDepHisMapper,
                        UserDepartmentMapper userDepartmentMapper,
                        TagMapper tagMapper, Errors errors, BroadcastService broadcastService,
                        OrgService orgService, OrgUserService orgUserService, RoleMapper roleMapper,
                        UserRoleMapper userRoleMapper, UserUpdateHisMapper userUpdateHisMapper, UserOrgCacheCallService userOrgCacheCallService,
                        CallbackService callbackService, User12371Mapper user12371Mapper, Org12371Mapper org12371Mapper,
                        UserHsMapper userHsMapper, UserThirdHsMapper userThirdHsMapper,
                        UserChangeLogMapper userChangeLogMapper) {
        this.userMapper = userMapper;
        this.userThirdMapper = userThirdMapper;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.departmentMapper = departmentMapper;
        this.organizationMapper = organizationMapper;
        this.synchOrgHisMapper = synchOrgHisMapper;
        this.synchDepHisMapper = synchDepHisMapper;
        this.synchUserHisMapper = synchUserHisMapper;
        this.synchUserDepHisMapper = synchUserDepHisMapper;
        this.userDepartmentMapper = userDepartmentMapper;
        this.tagMapper = tagMapper;
        this.errors = errors;
        this.broadcastService = broadcastService;
        this.orgService = orgService;
        this.orgUserService = orgUserService;
        this.roleMapper = roleMapper;
        this.userRoleMapper = userRoleMapper;
        this.userUpdateHisMapper = userUpdateHisMapper;
        this.userOrgCacheCallService = userOrgCacheCallService;
        this.callbackService = callbackService;
        this.user12371Mapper = user12371Mapper;
        this.org12371Mapper = org12371Mapper;
        this.userHsMapper = userHsMapper;
        this.userThirdHsMapper = userThirdHsMapper;
        this.userChangeLogMapper = userChangeLogMapper;
    }


    /**
     * 组织信息接口
     *
     * @param synchOrgForms synchOrgForms
     */
    @Transactional
    public void toOrgInfo(List<SynchOrgForm> synchOrgForms, Integer ignoredFlag) {
        log.debug("同步12371组织数据开始，data:[{}]", synchOrgForms);
        //获取当前时间
        Date curTime = DateTime.now().toDate();
        try {
            List<SynchOrgForm> pList = new ArrayList<>();
            for (SynchOrgForm synchOrgForm : synchOrgForms) {
                // 根据上级组织序列号查询parentId
                Example pExample = new Example(OrganizationEntity.class);
                /*if (!Strings.isNullOrEmpty(synchOrgForm.getOrgId()) && !synchOrgForm.getOrgId().startsWith("0")) {
                    synchOrgForm.setOrgId("0".concat(synchOrgForm.getOrgId()));
                }
                if (!Strings.isNullOrEmpty(synchOrgForm.getOrgPid()) && !synchOrgForm.getOrgPid().startsWith("0")) {
                    synchOrgForm.setOrgPid("0".concat(synchOrgForm.getOrgPid()));
                }*/
                pExample.createCriteria().andEqualTo("orgId", synchOrgForm.getOrgPid())
                        .andEqualTo("status", Constants.YES);
                OrganizationEntity oe = this.organizationMapper.selectOneByExample(pExample);
                // 根据组织序列号查询是否存在该组织
                Example example = new Example(OrganizationEntity.class);
                example.createCriteria().andEqualTo("orgId", synchOrgForm.getOrgId())
                        .andEqualTo("status", Constants.YES);
                OrganizationEntity oldEntity = this.organizationMapper.selectOneByExample(example);
                if (oe == null) {
                    pList.add(synchOrgForm);
                } else {
                    //如果为空则新增，反之为修改
                    if (oldEntity != null && oldEntity.getOrganizationId() != null) {
                        if (oldEntity.getOrgType() != null && oldEntity.getOrgTypeChild() != null
                                && !oldEntity.getOrgType().equals(Constants.ORG_TYPE_COMMUNIST)) {
                            continue;
                        }
                        if (oldEntity.getOrgType() == null) {
                            oldEntity.setOrgType(Constants.ORG_TYPE_COMMUNIST);
                        }
                        if (oldEntity.getOrgTypeChild() == null) {
                            oldEntity.setOrgTypeChild(Constants.ORG_TYPE_COMMUNIST_CHILD);
                        }
                        // 现上级组织ID
                        long newPrantId = oe.getOrganizationId();
                        // 如果12371上级组织改变,则该组织所有下级组织org_level都得变化
                        if (!synchOrgForm.getOrgPid().equals(oldEntity.getOrgPid())) {
                            // 再根据原org_pid查询organizationId是否匹配(固守未作组织上下级变更操作)
                            Example oldEx = new Example(OrganizationEntity.class);
                            oldEx.createCriteria().andEqualTo("orgId", oldEntity.getOrgPid())
                                    .andEqualTo("status", Constants.YES);
                            OrganizationEntity oldOrg = this.organizationMapper.selectOneByExample(oldEx);
                            if (oldOrg != null && oldOrg.getOrganizationId() != null) {
                                // 若组织pid与orgPid对应的orgId匹配，则可以变更组织上下级关系
                                if (oldOrg.getOrganizationId().equals(oldEntity.getParentId())) {
                                    log.debug("12371-组织上级变化 org_id:[{}],org_old_pid:[{}],org_new_pid:[{}]",
                                            synchOrgForm.getOrgId(), oldEntity.getOrgPid(), synchOrgForm.getOrgPid());
                                    oldEntity.setOrgLevel(oe.getOrgLevel() + oe.getOrganizationId() + "-");
                                    // 查询组织的所有下级
                                    Example childEx = new Example(OrganizationEntity.class);
                                    childEx.createCriteria().andEqualTo("parentId", oldEntity.getOrganizationId())
                                            .andEqualTo("status", Constants.YES);
                                    List<OrganizationEntity> childList = this.organizationMapper.selectByExample(childEx);
                                    if (childList != null && !childList.isEmpty()) {
                                        // 更新所有下级的org_level
                                        childList.forEach(childEntity -> {
                                            childEntity.setParentId(newPrantId);
                                            childEntity.setOrgLevel(oldEntity.getOrgLevel() + oldEntity.getOrganizationId() + "-");
                                            childEntity.setUpdateTime(curTime);
                                            childEntity.setLastChangeUser(-1L);
                                            this.organizationMapper.updateByPrimaryKeySelective(childEntity);
                                        });
                                    }
                                    oldEntity.setParentId(newPrantId);
                                    oldEntity.setOrgPid(synchOrgForm.getOrgPid());
                                }
                            }
                        }
                        // 如果组织名称变化，广播到其他模块
                        if (!oldEntity.getName().equalsIgnoreCase(synchOrgForm.getOrgName())) {
                            OrganizationBase base = new OrganizationBase();
                            base.setOrgId(oldEntity.getOrganizationId());
                            base.setParentId(oldEntity.getParentId());
                            base.setName(synchOrgForm.getOrgName());
                            // this.callbackService.orgChangeCallback(base, null);
                        }
                        oldEntity.setName(synchOrgForm.getOrgName());
                        oldEntity.setUpdateTime(curTime);
                        oldEntity.setLastChangeUser(-1L);
                        Show12371OrgForm showForm = new Show12371OrgForm();
                        showForm.setUpdateTime(DateFormatUtils.format(curTime, "yyyy-HH-mm HH:mm:ss"));
                        showForm.setSynchOrgForm(synchOrgForm);
                        oldEntity.setThirdDataInfo(Utils.toJson(showForm));
                        oldEntity.setThirdSource(Constants.THIRD_SOURCE_12371);
                        this.organizationMapper.updateByPrimaryKeySelective(oldEntity);

                        //刷新缓存 tc2018.08.17 2018-09-06 刷新组织缓存时，参数改为组织entity
                        OrganizationEntity newEntity = this.organizationMapper.selectByPrimaryKey(oldEntity);
                        userOrgCacheCallService.flushOrgInfo(newEntity, 1);

                        // 学习系统组织信息订阅接口-更新
                        OrganizationEntity organizationEntity = new OrganizationEntity();
                        organizationEntity.setId(oldEntity.getOrganizationId());
                        organizationEntity.setParentId(oe.getOrganizationId());
                        organizationEntity.setName(synchOrgForm.getOrgName());
                        organizationEntity.setOrganizationId(oldEntity.getOrganizationId());
                        this.broadcastService.commonOrgUpdateBroadcast(organizationEntity, oldEntity.getParentId());
                        continue;
                    } else {
                        // 新增组织信息
                        OrganizationEntity orgEntity = new OrganizationEntity();
                        orgEntity.setName(synchOrgForm.getOrgName());
                        orgEntity.setOrgId(synchOrgForm.getOrgId());
                        orgEntity.setParentId(oe.getOrganizationId());
                        orgEntity.setOrgPid(synchOrgForm.getOrgPid());
                        orgEntity.setUpdateTime(curTime);
                        orgEntity.setCreateTime(curTime);
                        orgEntity.setStatus(1);
                        // 新增组织时组织类型默认值102803
                        orgEntity.setOrgType(Constants.ORG_TYPE_COMMUNIST);
                        // 默认组织下级为 10280301
                        orgEntity.setOrgTypeChild(Constants.ORG_TYPE_COMMUNIST_CHILD);
                        // 设置org_level
                        orgEntity.setOrgLevel(oe.getOrgLevel() + oe.getOrganizationId() + "-");
                        // 新增组织默认为激活，设置组织标识码
                        orgEntity.setOrgUniqueCode(Utils.getRandomCode(8));
                        orgEntity.setLastChangeUser(-1L);
                        Show12371OrgForm showForm = new Show12371OrgForm();
                        showForm.setUpdateTime(DateFormatUtils.format(curTime, "yyyy-HH-mm HH:mm:ss"));
                        showForm.setSynchOrgForm(synchOrgForm);
                        // 保存组织原始数据JSON
                        orgEntity.setThirdDataInfo(Utils.toJson(showForm));
                        // 第三方数据来源 1-12371
                        orgEntity.setThirdSource(Constants.THIRD_SOURCE_12371);
                        this.organizationMapper.insertSelective(orgEntity);
                        // 新增组织下的root部门
                        DepartmentEntity de = new DepartmentEntity();
                        de.setParentId(0L);
                        de.setOrganizationId(orgEntity.getOrganizationId());
                        de.setIsRoot(1);
                        de.setDepType(2);
                        de.setName(synchOrgForm.getOrgName().concat("root部门"));
                        de.setStaffNumber(0);
                        de.setStatus(1);
                        de.setCreateTime(curTime);
                        de.setUpdateTime(curTime);
                        this.departmentMapper.insertSelective(de);

                        orgEntity.setId(orgEntity.getOrganizationId());
                        // 新建组织中转站
                        this.organizationMapper.insertOrgTransInfo(orgEntity.getId(), 0, "【会员中转站】" + orgEntity.getName());
                        // 新增组织管理员权限，新建角色
                        this.orgService.saveRole(orgEntity);

                        // 刷新缓存 tc2018.08.17 2018-09-06 刷新组织缓存时，参数改为组织entity
                        orgEntity = this.organizationMapper.selectByPrimaryKey(orgEntity);
                        userOrgCacheCallService.flushOrgInfo(orgEntity, 1);

                        // 学习系统组织信息订阅接口-新增
                        OrganizationEntity organizationEntity = new OrganizationEntity();
                        organizationEntity.setId(orgEntity.getOrganizationId());
                        organizationEntity.setParentId(orgEntity.getParentId());
                        organizationEntity.setName(orgEntity.getName());
                        this.broadcastService.commonOrgAddBroacast(organizationEntity);

                        // 新增组织回调接口
                        OrganizationBase base = new OrganizationBase();
                        base.setOrgId(orgEntity.getOrganizationId());
                        base.setParentId(orgEntity.getParentId());
                        base.setName(orgEntity.getName());
                        this.callbackService.addOrgCallback(base, null);
                    }
                }
            }
            // 处理上级组织不存在的情况
            if (!pList.isEmpty()) {
                pList.forEach(orgForm -> {
                    // 根据上级组织序列号查询parentId
                    Example pExample = new Example(OrganizationEntity.class);
                    pExample.selectProperties("organizationId");
                    pExample.createCriteria().andEqualTo("orgId", orgForm.getOrgPid())
                            .andEqualTo("status", Constants.YES);
                    OrganizationEntity oe = this.organizationMapper.selectOneByExample(pExample);
                    if (oe != null) {
                        // 根据组织序列号查询是否存在该组织
                        Example example = new Example(OrganizationEntity.class);
                        example.createCriteria().andEqualTo("orgId", orgForm.getOrgId())
                                .andEqualTo("status", Constants.YES);
                        OrganizationEntity entity = this.organizationMapper.selectOneByExample(example);
                        // 修改组织信息
                        if (entity != null && entity.getOrganizationId() != null) {
                            // 如果组织类型是党组织才允许修改组织信息
                            if (oe.getOrgType() != null && oe.getOrgTypeChild() != null
                                    && oe.getOrgType().equals(Constants.ORG_TYPE_COMMUNIST)) {
                                // 现上级组织ID
                                long newPrantId = oe.getOrganizationId();
                                // 如果上级组织改变,则该组织所有下级组织org_level都得变化
                                if (!orgForm.getOrgPid().equals(entity.getOrgPid())) {
                                    // 再根据原org_pid查询organizationId是否匹配(固守未作组织上下级变更)
                                    Example oldEx = new Example(OrganizationEntity.class);
                                    oldEx.createCriteria().andEqualTo("orgId", entity.getOrgPid())
                                            .andEqualTo("status", Constants.YES);
                                    OrganizationEntity org = this.organizationMapper.selectOneByExample(oldEx);
                                    if (org != null && org.getOrganizationId() != null) {
                                        if (org.getOrganizationId().equals(entity.getParentId())) {
                                            log.debug("12371-组织上级变化 org_id:[{}],org_old_pid:[{}],org_new_pid:[{}]",
                                                    orgForm.getOrgId(), entity.getOrgPid(), orgForm.getOrgPid());
                                            entity.setOrgLevel(oe.getOrgLevel() + oe.getOrganizationId() + "-");
                                            Example ex = new Example(OrganizationEntity.class);
                                            ex.createCriteria().andEqualTo("parentId", entity.getOrganizationId())
                                                    .andEqualTo("status", Constants.YES);
                                            List<OrganizationEntity> cList = this.organizationMapper.selectByExample(ex);
                                            if (cList != null && !cList.isEmpty()) {
                                                cList.forEach(orgEntity -> {
                                                    orgEntity.setOrgLevel(entity.getOrgLevel() + entity.getOrganizationId() + "-");
                                                    orgEntity.setParentId(newPrantId);
                                                    orgEntity.setUpdateTime(curTime);
                                                    orgEntity.setLastChangeUser(-1L);
                                                    this.organizationMapper.updateByPrimaryKeySelective(orgEntity);
                                                    // 刷新缓存 tc2018.08.17
                                                    orgEntity = this.organizationMapper.selectByPrimaryKey(orgEntity);
                                                    userOrgCacheCallService.flushOrgInfo(orgEntity, 1);
                                                });
                                            }
                                            entity.setParentId(newPrantId);
                                            entity.setOrgPid(orgForm.getOrgPid());
                                        }
                                    }
                                }
                                // 如果组织名称变化，广播到其他模块
                                if (!entity.getName().equalsIgnoreCase(orgForm.getOrgName())) {
                                    OrganizationBase base = new OrganizationBase();
                                    base.setOrgId(entity.getOrganizationId());
                                    base.setName(orgForm.getOrgName());
                                    base.setParentId(entity.getParentId());
                                    // this.callbackService.orgChangeCallback(base, null);
                                }
                                entity.setName(orgForm.getOrgName());
                                entity.setUpdateTime(curTime);
                                entity.setLastChangeUser(-1L);
                                entity.setThirdDataInfo(Utils.toJson(orgForm));
                                entity.setThirdSource(Constants.THIRD_SOURCE_12371);
                                this.organizationMapper.updateByPrimaryKeySelective(entity);
                                // 刷新缓存 tc2018.08.17
                                OrganizationEntity newEntity = this.organizationMapper.selectByPrimaryKey(entity);
                                userOrgCacheCallService.flushOrgInfo(newEntity, 1);

                                // 学习系统组织信息订阅接口-更新
                                OrganizationEntity organizationEntity = new OrganizationEntity();
                                organizationEntity.setId(entity.getOrganizationId());
                                organizationEntity.setOrgId(entity.getOrganizationId() + "");
                                organizationEntity.setParentId(oe.getOrganizationId());
                                organizationEntity.setName(orgForm.getOrgName());
                                organizationEntity.setOrganizationId(entity.getOrganizationId());
                                this.broadcastService.commonOrgUpdateBroadcast(organizationEntity, entity.getParentId());
                            }
                        } else {
                            // 新增组织信息
                            OrganizationEntity orgEntity = new OrganizationEntity();
                            orgEntity.setName(orgForm.getOrgName());
                            orgEntity.setParentId(oe.getOrganizationId());
                            orgEntity.setOrgId(orgForm.getOrgId());
                            orgEntity.setOrgPid(orgForm.getOrgPid());
                            orgEntity.setUpdateTime(curTime);
                            orgEntity.setCreateTime(curTime);
                            orgEntity.setStatus(1);
                            // 新增组织时组织类型默认值102803
                            orgEntity.setOrgType(Constants.ORG_TYPE_COMMUNIST);
                            // 默认组织下级为 10280301
                            orgEntity.setOrgTypeChild(Constants.ORG_TYPE_COMMUNIST_CHILD);
                            // 设置org_level
                            orgEntity.setOrgLevel(oe.getOrgLevel().concat(oe.getOrganizationId() + "-"));
                            // 新增组织默认为未激活，设置组织标识码
                            orgEntity.setOrgUniqueCode(Utils.getRandomCode(8));
                            orgEntity.setLastChangeUser(-1L);
                            // 保存组织原始数据JSON
                            orgEntity.setThirdDataInfo(Utils.toJson(orgForm));
                            // 第三方数据来源 1-12371
                            orgEntity.setThirdSource(Constants.THIRD_SOURCE_12371);
                            this.organizationMapper.insertSelective(orgEntity);

                            // 新增组织下的root部门
                            DepartmentEntity de = new DepartmentEntity();
                            de.setParentId(0L);
                            de.setOrganizationId(orgEntity.getOrganizationId());
                            de.setIsRoot(1);
                            de.setDepType(2);
                            de.setName(orgForm.getOrgName().concat("root部门"));
                            de.setStaffNumber(0);
                            de.setStatus(1);
                            de.setCreateTime(curTime);
                            de.setUpdateTime(curTime);
                            this.departmentMapper.insertSelective(de);

                            orgEntity.setId(orgEntity.getOrganizationId());
                            // 新建组织中转站
                            this.organizationMapper.insertOrgTransInfo(orgEntity.getId(), 0, "【会员中转站】" + orgEntity.getName());
                            // 新增组织管理员权限，新建角色
                            this.orgService.saveRole(orgEntity);
                            // 刷新缓存 tc2018.08.17
                            orgEntity = this.organizationMapper.selectByPrimaryKey(orgEntity);
                            userOrgCacheCallService.flushOrgInfo(orgEntity, 1);
                            // 学习系统组织信息订阅接口
                            OrganizationEntity organizationEntity = new OrganizationEntity();
                            organizationEntity.setId(orgEntity.getOrganizationId());
                            organizationEntity.setParentId(orgEntity.getParentId());
                            organizationEntity.setName(orgEntity.getName());
                            this.broadcastService.commonOrgAddBroacast(organizationEntity);

                            // 新增组织回调接口
                            OrganizationBase base = new OrganizationBase();
                            base.setOrgId(orgEntity.getOrganizationId());
                            base.setParentId(orgEntity.getParentId());
                            base.setName(orgEntity.getName());
                            this.callbackService.addOrgCallback(base, null);
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error("同步12371组织信息失败：" + e.getMessage(), e);
            throw new ApiException("同步12371组织信息失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "组织信息"));
        }
    }

    /**
     * 新增组织流水
     *
     * @param form form
     */
    @Async
    @Transactional
    public void addOrgHis(SynchOrgForm form) {
        SynchOrgHisEntity entity = new SynchOrgHisEntity();
        entity.setCreateTime(new Date());
        entity.setOldOrgName(form.getOldOrgName());
        entity.setOldOrgPid(form.getOldOrgPid());
        entity.setOrgId(form.getOrgId());
        entity.setOrgName(form.getOrgName());
        entity.setOrgPid(form.getOrgPid());
        this.synchOrgHisMapper.insertSelective(entity);
    }

    /**
     * 新增用户流水
     *
     * @param form form
     */
    @Async
    @Transactional
    public void addUserHis(SynchUserForm form) {
        SynchUserHisEntity entity = new SynchUserHisEntity();
        entity.setBirthday(form.getBirthday());
        entity.setCensusType(form.getCensusType());
        entity.setCertNumber(form.getCertNumber());
        entity.setCreateTime(new Date());
        entity.setEducation(form.getEducation());
        entity.setEthnic(form.getEthnic());
        entity.setGender(form.getGender());
        entity.setId(form.getId());
        entity.setMember(form.getMember());
        entity.setOpenId(form.getOpenId());
        entity.setPoliticalType(form.getPoliticalType());
        entity.setPhone(form.getPhone());
        entity.setPosition(form.getPosition());
        entity.setTags(form.getTags());
        entity.setWorkStatus(form.getWorkStatus());
        entity.setName(form.getName());
        this.synchUserHisMapper.insertSelective(entity);
    }

    /**
     * 新增部门流水
     *
     * @param form form
     */
    @Async
    @Transactional
    public void addDepHis(SynchDepForm form) {
        SynchDepHisEntity entity = new SynchDepHisEntity();
        entity.setCreateTime(new Date());
        entity.setDepId(form.getDepId());
        entity.setDepName(form.getDepName());
        entity.setDepPid(form.getDepPid());
        entity.setOrgId(form.getOrgId());
        entity.setOldDepName(form.getOldDepName());
        entity.setOldDepPid(form.getOldDepPid());
        this.synchDepHisMapper.insertSelective(entity);
    }

    /**
     * 新增用户部门流水
     *
     * @param form form
     */
    @Async
    @Transactional
    public void addUserDepHis(SynchUserDepForm form) {
        SynchUserDepHisEntity entity = new SynchUserDepHisEntity();
        entity.setCreateTime(new Date());
        entity.setName(form.getName());
        entity.setDepId(form.getDepId());
        entity.setDepName(form.getDepName());
        entity.setDepPid(form.getDepPid());
        entity.setId(form.getId());
        entity.setOldDepName(form.getOldDepName());
        entity.setOldDepId(form.getOldDepId());
        entity.setOldDepPid(form.getOldDepPid());
        entity.setPosition(form.getPosition());
        this.synchUserDepHisMapper.insertSelective(entity);
    }

    /**
     * 同步12371用户信息接口
     *
     * @param userList List
     * @param flag     1-全量 2-增量
     */
    @Transactional(rollbackFor = Throwable.class)
    public void memList(List<SynchUserForm> userList, Integer flag) {
        log.debug("同步12371用户数据开始,memInfo:{},flag:{}", userList, flag);
        // 更新数据不为空
        if (userList != null && !userList.isEmpty()) {
            Date curDate = new Date();
            try {
                for (SynchUserForm suf : userList) {
                    // 根据用户序列号查询是否存在该用户
                    Example userEx = new Example(UserEntity.class);
                    userEx.createCriteria().andEqualTo("id", suf.getId())
                            .andEqualTo("status", Constants.YES);
                    UserEntity ue = this.userMapper.selectOneByExample(userEx);
                    if (ue == null) {
                        // 新增用户
                        UserEntity newEntity = new UserEntity();
                        BeanUtils.copyProperties(suf, newEntity);
                        setCommonOption(suf, newEntity);
                        // 校验电话号码是否存在
                        Example phoneEx = new Example(UserEntity.class);
                        // 12371-明文
                        phoneEx.createCriteria().andEqualTo("phone", newEntity.getPhone());
                        int cnt = this.userMapper.selectCountByExample(phoneEx);
                        if (cnt > 0) {
                            String newPhone = "a".concat(newEntity.getPhone()).concat(getRandom());
                            newEntity.setPhone(NumEncryptUtils.encrypt(newPhone, 2));
                            newEntity.setPhoneSecret(NumEncryptUtils.numSecret(newPhone, 2));
                        } else {
                            // 自有加密方式
                            phoneEx.clear();
                            phoneEx.createCriteria().andEqualTo("phone", NumEncryptUtils.encrypt(newEntity.getPhone(), 2));
                            int c = this.userMapper.selectCountByExample(phoneEx);
                            if (c > 0) {
                                String encryptPhone = "a".concat(newEntity.getPhone()).concat(getRandom());
                                newEntity.setPhone(NumEncryptUtils.encrypt(encryptPhone, 2));
                                newEntity.setPhoneSecret(NumEncryptUtils.numSecret(encryptPhone, 2));
                            } else {
                                newEntity.setPhone(NumEncryptUtils.encrypt(newEntity.getPhone(), 2));
                                newEntity.setPhoneSecret(NumEncryptUtils.numSecret(suf.getPhone(), 2));
                            }
                        }
                        // 新增用户
                        newEntity.setCreateTime(curDate);
                        newEntity.setUpdateTime(curDate);
                        newEntity.setPassword(MD5Helper.getMD5(Constants.PASSWORD));
                        newEntity.setStatus(Constants.STATUS_YES);
                        // 身份证
                        newEntity.setCertType(1);
                        // 2-未认证
                        newEntity.setIsVerify(Constants.IS_VERIFY_NO);
                        this.userMapper.insertSelective(newEntity);
                        //根据组织序列号查询组织
                        Example orgEx = new Example(OrganizationEntity.class);
                        /*if (!suf.getOrgId().startsWith("0")) {
                            suf.setOrgId("0" + suf.getOrgId());
                        }*/
                        orgEx.createCriteria().andEqualTo("orgId", suf.getOrgId())
                                .andEqualTo("status", Constants.YES);
                        OrganizationEntity oe = this.organizationMapper.selectOneByExample(orgEx);
                        // openId存在，同步第三方用户数据 2019-01-25注释掉 edit by TangLilin
                        if (oe != null) {
                            // 新增用户组织信息
                            UserOrgAndCorpEntity uoce = new UserOrgAndCorpEntity();
                            uoce.setOrganizationId(oe.getOrganizationId());
                            uoce.setUserId(newEntity.getUserId());
                            uoce.setPosition(newEntity.getPosition());
                            uoce.setCreateTime(curDate);
                            uoce.setUpdateTime(curDate);
                            // 设置用户为该组织成员
                            uoce.setIsEmployee(Constants.YES);
                            this.userOrgAndCorpMapper.insertSelective(uoce);
                            // 组织用户人数加1
                            this.organizationMapper.updateUserNum(1, oe.getOrganizationId(), -1L);
                            // 刷新组织缓存
                            this.userOrgCacheCallService.flushOrgInfo(oe, 1);

                            // 用户添加组织的默认权限-PC端
                            Example roleEx = new Example(RoleEntity.class);
                            roleEx.createCriteria().andEqualTo("roleType", Constants.ROLE_TYPE_DEFAULT)
                                    .andEqualTo("organizationId", oe.getOrganizationId())
                                    .andEqualTo("belong", Constants.MENU_BELONG_PC);
                            RoleEntity re = this.roleMapper.selectOneByExample(roleEx);
                            if (re != null && re.getRoleId() != null) {
                                UserRoleEntity ure = new UserRoleEntity();
                                ure.setOrganizationId(oe.getOrganizationId());
                                ure.setUserId(newEntity.getUserId());
                                ure.setRoleId(re.getRoleId());
                                this.userRoleMapper.insertSelective(ure);
                            }

                            // 用户添加组织的默认权限-微信端
                            Example wxEx = new Example(RoleEntity.class);
                            wxEx.createCriteria().andEqualTo("roleType", Constants.ROLE_TYPE_DEFAULT)
                                    .andEqualTo("organizationId", oe.getOrganizationId())
                                    .andEqualTo("belong", Constants.MENU_BELONG_WX);
                            RoleEntity wxEntity = this.roleMapper.selectOneByExample(roleEx);
                            if (wxEntity != null && wxEntity.getRoleId() != null) {
                                UserRoleEntity ure = new UserRoleEntity();
                                ure.setUserId(newEntity.getUserId());
                                ure.setOrganizationId(oe.getOrganizationId());
                                ure.setRoleId(wxEntity.getRoleId());
                                this.userRoleMapper.insertSelective(ure);
                            }

                            // 新增用户root部门信息,根据组织ID查询root部门ID
                            Example example = new Example(DepartmentEntity.class);
                            example.selectProperties("departmentId");
                            example.createCriteria().andEqualTo("organizationId", oe.getOrganizationId())
                                    .andEqualTo("isRoot", 1);
                            DepartmentEntity de = this.departmentMapper.selectOneByExample(example);
                            UserDepartmentEntity ude = new UserDepartmentEntity();
                            ude.setUserId(newEntity.getUserId());
                            ude.setDepartmentId(de.getDepartmentId());
                            ude.setDuty(1);
                            this.userDepartmentMapper.insertSelective(ude);

                            // 刷新缓存 tc2018.08.17**/
                            // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24**/
                            newEntity = this.userMapper.selectByPrimaryKey(newEntity);
                            userOrgCacheCallService.flushUserInfo(null, newEntity, 1);
                            // 调用学习系统订阅接口
                            // this.broadcastService.commonBroadcast(newEntity, oe.getOrganizationId(), oe.getOrganizationId() + "");
                            // 新增用户调用回调接口
                            UserChangeForm form = this.orgUserService.commonUserChangeForm(newEntity.getUserId(),
                                    oe.getOrganizationId(), null, curDate, 2, null);
                            this.callbackService.userOrgChangeCallback(form, null);
                        }

                    } else {
                        suf.setUserId(ue.getUserId());
                        // 更新用户
                        BeanUtils.copyProperties(suf, ue);
                        setCommonOption(suf, ue);
                        if (Constants.IS_VERIFY_YES == ue.getIsVerify()) {
                            ue.setPhoneSecret(null);
                            ue.setPhone(null);
                            ue.setCertNumberSecret(null);
                            ue.setCertNumber(null);
                            log.debug("该用户已经通过实名认证，userId:{}", ue.getUserId());
                        } else {
                            // 校验电话号码是否存在
                            Example phoneEx = new Example(UserEntity.class);
                            // 12371-明文
                            phoneEx.createCriteria().andEqualTo("phone", ue.getPhone())
                                    .andNotEqualTo("userId", ue.getUserId());
                            int cnt = this.userMapper.selectCountByExample(phoneEx);
                            if (cnt > 0) {
                                String phone = "a".concat(ue.getPhone()).concat(getRandom());
                                ue.setPhone(NumEncryptUtils.encrypt(phone, 2));
                                ue.setPhoneSecret(NumEncryptUtils.numSecret(phone, 2));
                            } else {
                                // 自有加密方式
                                phoneEx.clear();
                                phoneEx.createCriteria().andEqualTo("phone", NumEncryptUtils.encrypt(ue.getPhone(), 2))
                                        .andNotEqualTo("userId", ue.getUserId());
                                int count = this.userMapper.selectCountByExample(phoneEx);
                                if (count > 0) {
                                    String enPhone = "a".concat(ue.getPhone()).concat(getRandom());
                                    ue.setPhoneSecret(NumEncryptUtils.numSecret(enPhone, 2));
                                    ue.setPhone(NumEncryptUtils.encrypt(enPhone, 2));
                                } else {
                                    ue.setPhone(NumEncryptUtils.encrypt(ue.getPhone(), 2));
                                    ue.setPhoneSecret(NumEncryptUtils.numSecret(suf.getPhone(), 2));
                                }
                            }
                            log.debug("该用户未实名认证，userId:{}", ue.getUserId());
                        }
                        ue.setUpdateTime(curDate);
                        this.userMapper.updateByPrimaryKeySelective(ue);
                        //根据组织序列号查询组织
                        Example orgEx = new Example(OrganizationEntity.class);
                        orgEx.createCriteria().andEqualTo("orgId", suf.getOrgId())
                                .andEqualTo("status", Constants.YES);
                        OrganizationEntity oe = this.organizationMapper.selectOneByExample(orgEx);
                        // 更新openId 2019-01-25注释掉 edit by TangLilin

                        if (oe != null) {
                            // 同步12371用户信息接口修改  tc 2018.08.27
                            //新增用户更新流水（第一步）
                            UserUpdateHisEntity uh = new UserUpdateHisEntity();
                            //记录用户原所在组织
                            //根据userID查询所在组织
                            Example userOrgEx = new Example(UserOrgAndCorpEntity.class);
                            userOrgEx.createCriteria().andEqualTo("userId", ue.getUserId());
                            List<UserOrgAndCorpEntity> oList = this.userOrgAndCorpMapper.selectByExample(userOrgEx);
                            if (oList != null && !oList.isEmpty()) {
                                StringBuilder orgStr = new StringBuilder();
                                for (UserOrgAndCorpEntity uoce : oList) {
                                    orgStr.append(uoce.getOrganizationId()).append(",");
                                }
                                uh.setOldOrgId(orgStr.substring(0, orgStr.length() - 1));
                            }

                            // 根据不同的情况同步数据
                            String synchModeFlag;
                            List<OrganizationEntity> orgList =
                                    this.userOrgAndCorpMapper.getOrgList(ue.getUserId(), 0, 0, null);
                            List<Integer> manageType = new ArrayList<>(2);
                            manageType.add(Constants.ROLE_TYPE_DEFINED_USER);
                            manageType.add(Constants.ROLE_TYPE_DEFINED_ROOT);
                            List<Long> rootOrgIds =
                                    this.userOrgAndCorpMapper.findOrgIdByUserIdAndRoleType(ue.getUserId(), manageType
                                            , null, null);
                            if (orgList != null && orgList.size() > 0) {
                                if (rootOrgIds != null && rootOrgIds.size() > 0) {
                                    //判断同步的组织是否已经是用户现有存在的组织
                                    if (rootOrgIds.contains(oe.getOrganizationId())) {
                                        //如果包含就更新管理组织的职务信息
                                        synchModeFlag = "updateRoot";
                                    } else {
                                        if (rootOrgIds.size() != orgList.size()) {
                                            synchModeFlag = "updateUnRoot";
                                        } else {//size相同，表示用户在所有组织里都为管理员
                                            synchModeFlag = "add";
                                        }
                                    }
                                } else {
                                    //用户没有为超级管理员的组织，直接修改为同步的组织
                                    synchModeFlag = "updateUnRoot";
                                }
                            } else {//用户当前不存在于任何组织就直接添加同步的组织关系
                                synchModeFlag = "add";
                            }

                            // 根据情况进行操作数据
                            if ("add".equals(synchModeFlag)) {
                                // 新增用户组织信息
                                UserOrgAndCorpEntity uoce = new UserOrgAndCorpEntity();
                                uoce.setOrganizationId(oe.getOrganizationId());
                                uoce.setTagId(this.addMemTag(oe.getOrganizationId(), curDate).toString());
                                uoce.setUserId(ue.getUserId());
                                uoce.setPosition(ue.getPosition());
                                uoce.setCreateTime(curDate);
                                uoce.setUpdateTime(curDate);
                                // 设置用户为该组织成员
                                uoce.setIsEmployee(Constants.YES);
                                this.userOrgAndCorpMapper.insertSelective(uoce);
                                // 组织用户人数加1
                                this.organizationMapper.updateUserNum(1, oe.getOrganizationId(), -1L);
                                // 刷新组织缓存
                                this.userOrgCacheCallService.flushOrgInfo(oe, 1);

                                // 用户添加组织的默认权限-PC
                                Example roleEx = new Example(RoleEntity.class);
                                roleEx.createCriteria().andEqualTo("roleType", Constants.ROLE_TYPE_DEFAULT)
                                        .andEqualTo("organizationId", oe.getOrganizationId())
                                        .andEqualTo("belong", Constants.MENU_BELONG_PC);
                                RoleEntity re = this.roleMapper.selectOneByExample(roleEx);
                                if (re != null && re.getRoleId() != null) {
                                    UserRoleEntity ure = new UserRoleEntity();
                                    ure.setOrganizationId(oe.getOrganizationId());
                                    ure.setRoleId(re.getRoleId());
                                    ure.setUserId(ue.getUserId());
                                    this.userRoleMapper.insertSelective(ure);
                                }
                                // 用户添加组织的默认权限-微信端
                                Example wxEx = new Example(RoleEntity.class);
                                wxEx.createCriteria().andEqualTo("roleType", Constants.ROLE_TYPE_DEFAULT)
                                        .andEqualTo("organizationId", oe.getOrganizationId())
                                        .andEqualTo("belong", Constants.MENU_BELONG_WX);
                                RoleEntity wxEntity = this.roleMapper.selectOneByExample(roleEx);
                                if (wxEntity != null && wxEntity.getRoleId() != null) {
                                    UserRoleEntity ure = new UserRoleEntity();
                                    ure.setRoleId(wxEntity.getRoleId());
                                    ure.setOrganizationId(oe.getOrganizationId());
                                    ure.setUserId(ue.getUserId());
                                    this.userRoleMapper.insertSelective(ure);
                                }

                                // 新增用户root部门信息,根据组织ID查询root部门ID
                                Example example = new Example(DepartmentEntity.class);
                                example.selectProperties("departmentId");
                                example.createCriteria().andEqualTo("organizationId", oe.getOrganizationId())
                                        .andEqualTo("isRoot", 1);
                                DepartmentEntity de = this.departmentMapper.selectOneByExample(example);
                                UserDepartmentEntity ude = new UserDepartmentEntity();
                                ude.setUserId(ue.getUserId());
                                ude.setDepartmentId(de.getDepartmentId());
                                ude.setDuty(1);
                                this.userDepartmentMapper.insertSelective(ude);
                            } else {
                                UserOrgAndCorpEntity uoce = new UserOrgAndCorpEntity();
                                uoce.setOrganizationId(oe.getOrganizationId());
                                uoce.setUserId(ue.getUserId());
                                uoce.setPosition(ue.getPosition());
                                uoce.setUpdateTime(curDate);
                                if ("updateRoot".equals(synchModeFlag)) {
                                    this.userOrgAndCorpMapper.synchByUserIdAndOrgId(uoce, oe.getOrganizationId(), null);
                                } else {
                                    this.userOrgAndCorpMapper.synchByUserIdAndOrgId(uoce, null, rootOrgIds);
                                }
                            }

                            //调用学习系统订阅接口
                            List<OrganizationEntity> communistList = this.userOrgAndCorpMapper.findCommunistOrg(ue.getUserId(), Constants.ORG_TYPE_COMMUNIST);
                            if (null != communistList && !communistList.isEmpty()) {
                                Long oldOrgId = communistList.get(0).getOrganizationId();
                                if (!oe.getOrganizationId().equals(oldOrgId)) {
                                    // this.broadcastService.commonBroadcast(ue, oe.getOrganizationId(), oldOrgId + "");
                                    // 调用用户组织信息变化回调接口
                                    // 1.用户变化前组织相关信息
                                    UserChangeForm beforeForm =
                                            this.orgUserService.commonUserChangeForm(ue.getUserId(), null, oldOrgId,
                                                    curDate, 3, null);
                                    this.callbackService.userOrgChangeCallback(beforeForm, null);
                                    // 2.用户变化后组织相关信息
                                    UserChangeForm afterForm =
                                            this.orgUserService.commonUserChangeForm(ue.getUserId(),
                                                    oe.getOrganizationId(), oldOrgId, curDate, 1, null);
                                    this.callbackService.userOrgChangeCallback(afterForm, null);
                                }
                            }

                            //新增用户更新流水（第二步）
                            //记录去更新的组织
                            uh.setUserId(ue.getUserId());
                            uh.setNewOrgId(oe.getOrganizationId().toString());
                            uh.setCreateTime(curDate);
                            this.userUpdateHisMapper.insertSelective(uh);
                        } else {
                            // 12371同步过来的用户所在组织不存在,记录流水
                            Example userOrgEx = new Example(UserOrgAndCorpEntity.class);
                            userOrgEx.createCriteria().andEqualTo("userId", ue.getUserId());
                            List<UserOrgAndCorpEntity> oList = this.userOrgAndCorpMapper.selectByExample(userOrgEx);
                            StringBuilder orgIds = new StringBuilder();
                            if (oList != null && !oList.isEmpty()) {
                                for (UserOrgAndCorpEntity uoce : oList) {
                                    orgIds.append(uoce.getOrganizationId().toString()).append(",");
                                }
                            }
                            UserUpdateHisEntity uhis = new UserUpdateHisEntity();
                            uhis.setCreateTime(curDate);
                            uhis.setUserId(ue.getUserId());
                            uhis.setOldOrgId(!StringUtils.isBlank(orgIds.toString()) ? orgIds.substring(0, orgIds.length() - 1) : orgIds.toString());
                            uhis.setNewOrgId(suf.getOrgId());
                            this.userUpdateHisMapper.insertSelective(uhis);
                        }
                        // 刷新缓存 tc2018.08.17
                        // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24
                        ue = this.userMapper.selectByPrimaryKey(ue);
                        userOrgCacheCallService.flushUserInfo(null, ue, 1);
                        // 修改电话号码广播到其他模块
                        log.debug("修改手机号码调用广播接口 phone：{},phoneSecret:{}", suf.getPhone(), ue.getPhone());
                        // this.orgUserCommonService.callbackDoGet("user_change_phone_callback",
                        //        ue.getUserId(), "phone", suf.getPhone(), togServicesConfig, restTemplate);
                    }
                }
            } catch (Exception e) {
                log.error("同步12371党员信息接口失败[{}]", e.getMessage(), e);
                throw new ApiException("同步12371党员信息接口失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value()));
            }
        }
    }

    /**
     * @param orgId   orgId
     * @param curTime curTime
     * @return long
     */
    private Long addMemTag(Long orgId, Date curTime) {
        Example tagEx = new Example(TagEntity.class);
        tagEx.createCriteria().andEqualTo("organizationId", orgId)
                .andEqualTo("tagType", Constants.TAG_TYPE_PAY)
                .andEqualTo("status", Constants.STATUS_YES);
        TagEntity tagEntity = this.tagMapper.selectOneByExample(tagEx);
        if (null == tagEntity) {
            TagEntity tag = new TagEntity();
            tag.setName(ppmdTagName);
            tag.setTagType(Constants.TAG_TYPE_PAY);
            tag.setOrganizationId(orgId);
            tag.setStatus(Constants.STATUS_YES);
            tag.setParentId(Constants.PARENT_CODE);
            tag.setOrgType(Constants.DEP_TYPE_ORG);
            tag.setBelong(Constants.TAG_BELONG_USER);
            tag.setLastChangeUser(-1L);
            tag.setUpdateTime(curTime);
            tag.setCreateTime(curTime);
            this.tagMapper.insertSelective(tag);
            return tag.getTagId();
        } else {
            return tagEntity.getTagId();
        }
    }

    /**
     * @param ignoredSuf       ignoredSuf
     * @param ignoredNewEntity ignoredNewEntity
     */
    private void setCommonOption(SynchUserForm ignoredSuf, UserEntity ignoredNewEntity) {
    }

    /**
     * 获取8位随机数
     */
    private static String getRandom() {
        Random random = new Random();
        int code = random.nextInt(99999999 - 10000000 + 1) + 10000000;
        return String.valueOf(code);
    }

    /**
     * 用户信息广播接口
     */
    @Async
    @Transactional
    public void userInfoBroadcast(SynchUserForm form) {

        Map<String, String> param = new HashMap<>();
        if (form.getUserId() != null) {
            param.put("id", form.getUserId() + "");
        } else {
            Example example = new Example(UserEntity.class);
            example.selectProperties("userId");
            example.createCriteria().andEqualTo("id", form.getId());
            UserEntity ue = this.userMapper.selectOneByExample(example);
            param.put("id", ue.getUserId() + "");
        }
        Example openID = new Example(UserThirdEntity.class);
        openID.createCriteria().andEqualTo("userId", param.get("id"));
        UserThirdEntity ude = this.userThirdMapper.selectOneByExample(openID);
        if (ude != null && ude.getUserId() != null) {
            param.put("open_id", ude.getThToken());
        } else {
            param.put("open_id", "");
        }
        param.put("name", form.getName());
        param.put("gender", form.getGender());
        param.put("education", form.getEducation());
        param.put("birthday", form.getBirthday());
        param.put("ethnic", form.getEthnic());
        param.put("work_status", form.getWorkStatus());
        param.put("political_type", form.getPoliticalType());
        param.put("census_type", form.getCensusType());

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("authCode", authCode);
        headerMap.put("Content-Type", "application/json");

        String rusult = HttpClientUtil.doPost(authUrl.concat("/user-info-broadcast"), headerMap, param);
        log.debug("调用学习系统用户信息广播接口，返回值：result->{}   ", rusult);
        log.debug("调用学习系统用户信息广播接口，返回值：authCode->{}   ", authCode);
    }

    /**
     * 组织信息广播接口
     */
    @Async
    @Transactional
    public void orgInfoBroadcast(SynchOrgForm form) {

        Example example = new Example(OrganizationEntity.class);
        example.createCriteria().andEqualTo("orgId", form.getOrgId());
        OrganizationEntity oe = this.organizationMapper.selectOneByExample(example);
        example.createCriteria().andEqualTo("orgId", form.getOrgPid());
        OrganizationEntity poe = this.organizationMapper.selectOneByExample(example);

        Map<String, String> param = new HashMap<>();
        param.put("org_id", oe.getOrganizationId() + "");
        param.put("org_name", form.getOrgName());
        param.put("org_pid", poe.getOrganizationId() + "");
        param.put("old_org_pid", form.getOldOrgPid());

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("authCode", authCode);
        headerMap.put("Content-Type", "application/json");

        String rusult = HttpClientUtil.doPost("http://71sy.datajun.com/wechat/public/org-info-broadcast", headerMap, param);
        log.debug("调用学习系统组织信息广播接口，返回值：result->{}", rusult);
    }

    /**
     * 用户组织信息广播接口
     */
    @Async
    @Transactional
    public void userDepInfoBroadcast(SynchUserForm form) {

        Example example = new Example(UserEntity.class);
        example.selectProperties("userId");
        example.createCriteria().andEqualTo("id", form.getId());
        UserEntity ue = this.userMapper.selectOneByExample(example);
        Example orgExample = new Example(OrganizationEntity.class);
        orgExample.createCriteria().andEqualTo("orgId", form.getOrgId());
        OrganizationEntity oe = this.organizationMapper.selectOneByExample(orgExample);

        Example userOrg = new Example(UserOrgAndCorpEntity.class);
        userOrg.selectProperties("organizationId");
        userOrg.createCriteria().andEqualTo("userId", ue.getUserId());
        UserOrgAndCorpEntity uoe = this.userOrgAndCorpMapper.selectOneByExample(userOrg);
        orgExample.createCriteria().andEqualTo("organizationId", oe.getOrganizationId());
        OrganizationEntity oeName = this.organizationMapper.selectOneByExample(orgExample);

        Map<String, String> param = new HashMap<>();
        param.put("id", ue.getUserId() + "");
        param.put("dep_id", oe.getOrganizationId() + "");
        param.put("dep_name", oe.getName());
        param.put("old_dep_id", uoe.getOrganizationId() + "");
        param.put("old_dep_name", oeName.getName());

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("authCode", authCode);
        headerMap.put("Content-Type", "application/json");

        String rusult = HttpClientUtil.doPost(authUrl.concat("/user-dep-info-broadcast"), headerMap, param);
        log.debug("调用学习系统组织信息广播接口，返回值：result->{}", rusult);
    }

    /**
     * @return list
     */
    public List<User12371Entity> getAllUser12371List() {
        return this.user12371Mapper.selectAll();
    }

    /**
     * @return list
     */
    public List<Org12371Entity> getAllOrg12371List() {
        return this.org12371Mapper.selectAll();
    }


    @Async("callbackExecutor")
    @Transactional
    public void updateVisiterInfo(UserEntity visitorUserEntity, UserEntity oldUserRntity,
                                  UserThirdEntity oldUserThirdEntity, UserThirdEntity visitorUserThirdEntity,
                                  Long oid) {
        // step8 清理无用数据 游客数据、
        this.userMapper.deleteByPrimaryKey(visitorUserEntity.getUserId());
        //step9 删除用户组织关系表关联
        this.deleteUserOrgRealtion(visitorUserEntity.getUserId(), oid);
        if (null != oldUserThirdEntity) {
            this.userThirdMapper.deleteByPrimaryKey(oldUserThirdEntity.getThirdId());
        }
        //step8 插入备份流水
        this.insertHs(visitorUserEntity, oldUserRntity, visitorUserThirdEntity, oldUserThirdEntity);
    }

    /**
     * 删除用户和组织的绑定关系
     *
     * @param userId 用户id
     * @param oid    公众号id
     * <AUTHOR>
     * @Date 2020-02-13 3:30 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public void deleteUserOrgRealtion(long userId, long oid) {
        //根据oid查找出组织的id
        Example orgExam = new Example(OrganizationEntity.class);
        orgExam.createCriteria()
                .andEqualTo("parentId", oid)
                .andEqualTo("orgTypeChild", 10281001);
        OrganizationEntity org = this.organizationMapper.selectOneByExample(orgExam);

        if (org != null) {
            //删除t_user_org_corp
            Example userOrgExam = new Example(UserOrgAndCorpEntity.class);
            userOrgExam.createCriteria()
                    .andEqualTo("userId", userId)
                    .andEqualTo("organizationId", org.getOrganizationId());
            this.userOrgAndCorpMapper.deleteByExample(userOrgExam);
            //删除t_user_department
            Example userDepExam = new Example(UserDepartmentEntity.class);
            userDepExam.createCriteria()
                    .andEqualTo("userId", userId);
            //.andEqualTo("departmentId", org.getOrganizationId());
            this.userDepartmentMapper.deleteByExample(userDepExam);
            //删除t_user_role
            Example userRoleExam = new Example(UserRoleEntity.class);
            userRoleExam.createCriteria()
                    .andEqualTo("userId", userId)
                    .andEqualTo("organizationId", org.getOrganizationId());
            this.userRoleMapper.deleteByExample(userRoleExam);
        }
    }

    /**
     * 根据身份证查询用户
     *
     * @param visitorUserEntityBak    游客用户信息
     * @param cernumberUserEntityBak  身份证用户信息
     * @param visitorThirdEntityBak   游客第三方信息
     * @param cernumberThirdEntityBak 身份证第三方信息
     * <AUTHOR>
     * @Date 2020-01-20 4:34 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public void insertHs(UserEntity visitorUserEntityBak, UserEntity cernumberUserEntityBak,
                         UserThirdEntity visitorThirdEntityBak, UserThirdEntity cernumberThirdEntityBak) {
        String taskId = UUID.randomUUID().toString().replace("-", "");
        UserHsEntity visitorUserEntityHs = new UserHsEntity();
        UserHsEntity cernumberUserEntityHs = new UserHsEntity();
        UserThirdHsEntity visitorThirdEntityHs = new UserThirdHsEntity();
        UserThirdHsEntity cernumberThirdHsEntityHs = new UserThirdHsEntity();
        //拷贝用户信息到用户历史表
        //设置公共属性
        Date date = DateTime.now().toDate();
        if (cernumberUserEntityBak != null) {
            BeanUtils.copyProperties(cernumberUserEntityBak, cernumberUserEntityHs);
            cernumberUserEntityHs.setTaskId(taskId);
            cernumberUserEntityHs.setUserHsCreateTime(date);
            //因为12371的数据里面包含了id字段，所以暂时保存起来
            this.userHsMapper.insertUseGeneratedKeys(cernumberUserEntityHs);
            log.debug("用户合并插入数据：cernumberUserEntityHs = {}", cernumberUserEntityHs);
            if (visitorUserEntityBak != null) {
                BeanUtils.copyProperties(visitorUserEntityBak, visitorUserEntityHs);
                visitorUserEntityHs.setUserHsCreateTime(date);
                visitorUserEntityHs.setTaskId(taskId);
                visitorUserEntityHs.setToUserHsId(cernumberUserEntityHs.getUserHsId());
                this.userHsMapper.insertSelective(visitorUserEntityHs);
                log.debug("用户合并插入数据：visitorUserEntityHs = {}", visitorUserEntityHs);
            }
        }

        Long userThirdHsId = null;
        if (cernumberThirdEntityBak != null) {
            BeanUtils.copyProperties(cernumberThirdEntityBak, cernumberThirdHsEntityHs);
            cernumberThirdHsEntityHs.setTaskId(taskId);
            cernumberThirdHsEntityHs.setThirdHsCreateTime(date);
            this.userThirdHsMapper.insertUseGeneratedKeys(cernumberThirdHsEntityHs);
            log.debug("用户合并插入数据：cernumberThirdHsEntityHs = {}", cernumberThirdHsEntityHs);
            userThirdHsId = cernumberThirdHsEntityHs.getId();
        }

        if (visitorThirdEntityBak != null) {
            BeanUtils.copyProperties(visitorThirdEntityBak, visitorThirdEntityHs);
            visitorThirdEntityHs.setThirdHsCreateTime(date);
            visitorThirdEntityHs.setTaskId(taskId);
            if (userThirdHsId != null) {
                visitorThirdEntityHs.setToThirdHsId(userThirdHsId);
            }
            this.userThirdHsMapper.insertSelective(visitorThirdEntityHs);
            log.debug("用户合并插入数据：visitorThirdEntityHs = {}", visitorThirdEntityHs);
        }
    }

    @Async("dealOrgTagExecutor")
    @Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
    public void dealUserTag(Long userId, Long regionId, String tagAfter, String tagBefore,
                            Integer sequenceAfter, Integer sequenceBefore) {
        UserChangeLogEntity entity = new UserChangeLogEntity();
        entity.setUserId(userId);
        entity.setTagsAfter(tagAfter);
        entity.setTagsBefore(tagBefore);
        entity.setSequenceAfter(sequenceAfter);
        entity.setSequenceBefore(sequenceBefore);
        entity.setCreateTime(LocalDateTime.now());
        // 根据用户ID查询用户所在组织
        List<UserInfoBase> list = this.userOrgAndCorpMapper.findUserByKey(1, 1, userId, regionId);
        if (!CollectionUtils.isEmpty(list)) {
            UserInfoBase base = list.get(0);
            entity.setUserName(base.getUserName());
            try {
                entity.setPhone(NumEncryptUtils.decrypt(base.getPhone(), base.getPhoneSecret()));
            } catch (Exception e) {
                log.error("电话号码解密失败", e);
            }
            entity.setOrgId(base.getOrgId());
            // 设置所属单位
            OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(base.getOrgId());
            if (Objects.nonNull(org)) {
                entity.setUnitId(org.getOwnerId());
            }
        }
        this.userChangeLogMapper.insertSelective(entity);
    }
}
















