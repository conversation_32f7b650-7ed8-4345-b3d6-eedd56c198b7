package com.goodsogood.ows.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.mapper.MenuLinkUrlMapper;
import com.goodsogood.ows.mapper.MenuUrlMapper;
import com.goodsogood.ows.model.db.MenuLinkUrlEntity;
import com.goodsogood.ows.model.db.MenuUrlEntity;
import com.goodsogood.ows.model.db.PageNumber;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * 菜单按钮（uri）service
 */
@Service
@Log4j2
public class MenuUrlService extends MyService<MenuUrlMapper, MenuUrlEntity> {
    private final MenuLinkUrlMapper menuLinkUrlMapper;

    @Autowired
    public MenuUrlService(MenuUrlMapper menuUrlMapper, MenuLinkUrlMapper menuLinkUrlMapper) {
        super(menuUrlMapper);
        this.menuLinkUrlMapper = menuLinkUrlMapper;
    }

    /**
     * 添加菜单url，如果method和link_url都同时存在的话，那么就认为已经存在了，跳过
     *
     * @param menuId        menu_id
     * @param menuUrlEntity 菜单对象
     * @return 1 or 0
     */
    @Async("addMenuUrl")
    @Transactional
    public void addMenuUrl(String menuId, MenuUrlEntity menuUrlEntity) {
        log.debug("开始处理菜单->{}--{}", menuId, menuUrlEntity);
        Example example = new Example(MenuUrlEntity.class);
        example.selectProperties("menuUrlId").createCriteria().andEqualTo("linkUrl", menuUrlEntity.getLinkUrl())
                .andEqualTo("method", menuUrlEntity.getMethod());
        List<MenuUrlEntity> list = this.mapper.selectByExample(example);
        if (list.size() == 0) {
            this.mapper.insert(menuUrlEntity);
        } else {
            menuUrlEntity = list.get(0);
        }
        if (StringUtils.isNotEmpty(menuId) && !"*".equals(menuId)) {
            // 处理menuId关系
            Example example1 = new Example(MenuLinkUrlEntity.class);
            example1.createCriteria().andEqualTo("menuId", menuId).andEqualTo("menuUrlId", menuUrlEntity.getMenuUrlId());
            if (this.menuLinkUrlMapper.selectByExample(example1).size() == 0) {
                MenuLinkUrlEntity menuLinkUrlEntity = new MenuLinkUrlEntity();
                menuLinkUrlEntity.setMenuId(menuId);
                menuLinkUrlEntity.setMenuUrlId(menuUrlEntity.getMenuUrlId());
                this.menuLinkUrlMapper.insert(menuLinkUrlEntity);
            }
        }
    }

    /**
     * 获得所有url列表
     *
     * @param menuUrlEntity url对象
     * @return page
     */
    public Page<MenuUrlEntity> findAllMenuUrl(MenuUrlEntity menuUrlEntity, PageNumber pageNumber) {
        Example example = new Example(MenuUrlEntity.class);
        example.selectProperties("menuUrlId", "linkUrl", "method", "status", "createTime");
        Example.Criteria criteria = example.createCriteria();
//        if (menuUrlEntity.getName() != null) {
//            // criteria.andLike("name", "%" + menuUrlEntity.getName() + "%");
//        }
        if (menuUrlEntity.getLinkUrl() != null) {
            criteria.andLike("linkUrl", "%" + menuUrlEntity.getLinkUrl() + "%");
        }
        if (menuUrlEntity.getStatus() != null) {
            criteria.andEqualTo("status", menuUrlEntity.getStatus());
        }
        return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows(), "menu_url_id desc")
                .doSelectPage(() -> this.mapper.selectByExample(example));
    }

    /**
     * 通过menuid 或者url列表
     *
     * @param menuUrlEntity url对象
     * @param pageNumber    page number
     * @return page
     */
    public Page<MenuUrlEntity> findAllMenuUrlByMenuId(MenuUrlEntity menuUrlEntity, PageNumber pageNumber) {
        if (menuUrlEntity.getInc() == null || !menuUrlEntity.getInc()) {
            return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows(), "menuUrlId desc")
                    .doSelectPage(() -> mapper.findAllMenuUrlByMenuId(menuUrlEntity.getMenuId()));
        } else {
            return PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows(), "menuUrlId desc")
                    .doSelectPage(() -> mapper.findAllMenuUrlByNotMenuId(menuUrlEntity.getMenuId(), menuUrlEntity.getLinkUrl()));
        }
    }

    /**
     * 通过id删除menu uri
     *
     * @param id menu_url_id
     * @return 1 or 0
     */
    @Transactional
    public int removeMenuUrlById(long id) {
        this.mapper.deleteMenuLinkUrlByMenuUrlId(id);
        return this.mapper.deleteByPrimaryKey(id);
    }

}
