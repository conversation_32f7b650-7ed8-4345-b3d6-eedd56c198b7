package com.goodsogood.ows.service

import cn.hutool.core.convert.Convert
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.*
import com.goodsogood.ows.common.AmapHttpHelper.getAdcode
import com.goodsogood.ows.common.AmapHttpHelper.getGeocode
import com.goodsogood.ows.common.AmapHttpHelper.takeCoordinates
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.component.FieldComponentEnum
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.HeaderHelper.SysHeader
import com.goodsogood.ows.helper.NumEncryptUtils
import com.goodsogood.ows.mapper.*
import com.goodsogood.ows.model.db.*
import com.goodsogood.ows.model.mongo.Org
import com.goodsogood.ows.model.mongo.User
import com.goodsogood.ows.model.vo.OrgUserForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.UserHighlightForm
import com.goodsogood.ows.model.vo.UserPartyTimeForm
import lombok.extern.log4j.Log4j2
import org.apache.commons.lang3.StringUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cglib.beans.BeanMap
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.CollectionUtils
import org.springframework.web.multipart.MultipartFile
import tk.mybatis.mapper.entity.Example
import java.io.File
import java.io.IOException
import java.net.URLEncoder
import java.time.LocalDateTime
import java.util.*
import java.util.function.Consumer
import java.util.stream.Collectors
import java.util.stream.Stream
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 */
@Service
@Log4j2
class TestService @Autowired constructor(
    private val errors: Errors,
    private val userMapper: UserMapper,
    private val optionService: OptionService,
    private val userOrgAndCorpMapper: UserOrgAndCorpMapper,
    private val tagMapper: TagMapper,
    private val organizationMapper: OrganizationMapper,
    private val userMongoService: UserMongoService,
    private val fieldService: FieldService, private val orgUserService: OrgUserService,
    private val redisTemplate: StringRedisTemplate, private val userHighlightService: UserHighlightService,
    private val userSnapshotMapper: UserSnapshotMapper, private val userOrgCacheCallService: UserOrgCacheCallService,
    private val userDevelopMapper: UserDevelopMapper,
    private val mongoTemplate: MyMongoTemplate
) {
    private val names = arrayOf(
        "姓名", "性别", "移动电话", "公民身份证号码", "所在党组织", "工号", "人员类别", "党员类型",
        "民族", "学历", "出生日期", "入党时间", "是否台湾省籍", "转正时间", "籍贯", "现居住地", "其他联系方式", "工作岗位",
        "取得专业技术职务", "取得技术职务日期", "当前聘任专业技术职务", "新社会阶层类型", "一线情况", "是否为农民工", "党员档案所在单位",
        "是否为失联党员", "是否流动党员"
    )

    val log: Logger = LoggerFactory.getLogger(TestService::class.java)

    @Transactional(rollbackFor = [Exception::class])
    fun updateNewTag() {
        val rootOrgId = rootOrgId
        val ppmdTag = getTagByTagType(3, rootOrgId)
        val ldTag = getTagByTagType(4, rootOrgId)
        val fzTag = getTagByTagType(5, rootOrgId)
        val vTag = getTagByTagType(8, rootOrgId)
        val ex = Example(UserOrgAndCorpEntity::class.java)
        ex.createCriteria().andIsNotNull("tagId").andNotEqualTo("tagId", "")
        val entityList = userOrgAndCorpMapper.selectByExample(ex)
        entityList.forEach(Consumer { entity: UserOrgAndCorpEntity ->
            val newTag: MutableList<Long?> = ArrayList()
            val tagIds = entity.getTagId()
            val split = tagIds.split(",").toTypedArray()
            val list = listOf(*split)
            list.forEach(Consumer { tagStr: String? ->
                val tagId = java.lang.Long.valueOf(tagStr)
                val tagEntity = tagMapper.selectByPrimaryKey(tagId)
                if (null != tagEntity) {
                    when (tagEntity.tagType) {
                        3 -> {
                            newTag.add(ppmdTag.tagId)
                        }
                        4 -> {
                            newTag.add(ldTag.tagId)
                        }
                        5 -> {
                            newTag.add(fzTag.tagId)
                        }
                        8 -> {
                            newTag.add(vTag.tagId)
                        }
                        else -> {
                            newTag.add(tagId)
                        }
                    }
                }
            })
            val updateTagStr = StringUtils.join(newTag, ",")
            entity.setTagId(updateTagStr)
            entity.setUpdateTime(Date())
            entity.setLastChangeUser(-6666L)
            userOrgAndCorpMapper.updateByPrimaryKeySelective(entity)
        })
    }

    fun getTagByTagType(tagType: Int, rootOrgId: Long?): TagEntity {
        val ex = Example(TagEntity::class.java)
        ex.createCriteria().andEqualTo("tagType", tagType).andEqualTo("organizationId", rootOrgId)
        return tagMapper.selectOneByExample(ex)
    }

    val rootOrgId: Long
        get() {
            val ex = Example(OrganizationEntity::class.java)
            ex.createCriteria().andEqualTo("parentId", 1L).andEqualTo("orgType", 102803)
            return organizationMapper.selectOneByExample(ex).getOrganizationId()
        }

    fun dealUserDynamic(userId: Long?) {
        val arrayComponent = arrayOf(
            FieldComponentEnum.SELECT.type,
            FieldComponentEnum.CHECK_BOX.type,
            FieldComponentEnum.RADIO.type,
            FieldComponentEnum.USER.type,
            FieldComponentEnum.ORG.type
        )
        val list = Arrays.stream(arrayComponent).collect(Collectors.toList())
        var userList: MutableList<User> = ArrayList()
        if (Objects.nonNull(userId)) {
            val userById = userMongoService.getUserById(userId)
            userList.add(userById)
        } else {
            userList = userMongoService.getUserListByWhere(null)
        }
        val newUserList: MutableList<User> = ArrayList()
        for (user in userList) {
            val fields = user.fields
            val newFields: MutableMap<String, Any?> = HashMap()
            for (key in fields.keys) {
                val valueObj = fields[key]
                val entityList = fieldService.getFieldByName(key)
                if (entityList.size == 1) {
                    val fieldEntity = entityList[0]
                    val componentType = fieldEntity.componentType
                    if (list.contains(componentType)) {
                        if (valueObj is String) {
                            val value = Convert.toStr(valueObj)
                            val split = value.split(",").toTypedArray()
                            newFields[key] = split
                            continue
                        }
                    }
                }
                newFields[key] = valueObj
            }
            user.fields = newFields
            newUserList.add(user)
        }
        userMongoService.insertOrUpdateAll(newUserList)
    }

    fun importTabaccoUser(file: MultipartFile, regionId: Long) {
        val titleNames = Stream.of(*names).collect(Collectors.toList())
        try {
            val excelData = ExcelUtils.getExcelData(transferToFile(file), 0, titleNames.toTypedArray(), false)
            // 检查表格头部信息
            val datas = excelData[0]
            for (j in titleNames.indices) {
                val titleName = titleNames[j]
                val data = datas[j]
                if (ExcelUtils.trimStr(titleName) != ExcelUtils.trimStr(data)) {
                    throw ApiException(
                        "表格头部数据发生变动",
                        Result<Any>(errors, 32001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "表格头部数据发生变动")
                    )
                }
            }
            for (i in 1 until excelData.size) {
                val strList = excelData[i]
                // 姓名
                val name = strList[0]
                // 手机号
                val phone = strList[2]
                // 判断手机号是否为空
                var e = 1
                if (StringUtils.isNotBlank(phone)) {
                    val phoneEncrypt = NumEncryptUtils.encrypt(phone, NumEncryptUtils.PHONE)
                    val userEx = Example(UserEntity::class.java)
                    val criteria = userEx.createCriteria()
                    criteria.andEqualTo("phone", phoneEncrypt)
                    criteria.andEqualTo("name", name)
                    criteria.andEqualTo("status", Constants.STATUS_YES)
                    val userEntity = userMapper.selectOneByExample(userEx)
                    if (Objects.nonNull(userEntity) && Objects.nonNull(userEntity.userId)) {
                        val result = updateUser(regionId, userEntity, strList)
                        if (result == 0) {
                            e = 0
                        }
                    } else {
                        e = 0
                    }
                } else {
                    e = 0
                }
                log.debug("根据手机号查询后， e -> [{}]", e)
                e = if (e == 0) {
                    1
                } else {
                    continue
                }
                // 根据姓名查询
                val userEx1 = Example(UserEntity::class.java)
                val criteria1 = userEx1.createCriteria()
                criteria1.andEqualTo("name", name)
                criteria1.andEqualTo("status", Constants.STATUS_YES)
                val userList = userMapper.selectByExample(userEx1)
                if (userList.size == 1) {
                    val result = updateUser(regionId, userList[0], strList)
                    if (result == 0) {
                        e = 0
                    }
                } else {
                    e = 0
                }
                log.debug("根据姓名查询后， e -> [{}]", e)
                if (e == 1) {
                    continue
                }
                // 组织名称层级
                val orgNames = strList[4]
                val orgIdList: Array<String?> = orgNames.split("-").toTypedArray()
                val organizationEntity = getChildOrg(orgIdList, 0, null)
                if (Objects.nonNull(organizationEntity)) {
                    val ownerId = organizationEntity!!.getOwnerId()
                    if (Objects.nonNull(ownerId)) {
                        val userEx = Example(UserEntity::class.java)
                        val userExCriteria = userEx.createCriteria()
                        userExCriteria.andEqualTo("name", name)
                        userExCriteria.andEqualTo("status", Constants.STATUS_YES)
                        val entityList = userMapper.selectByExample(userEx)
                        val userIds =
                            entityList.stream().map { obj: UserEntity -> obj.userId }.collect(Collectors.toList())
                        if (!CollectionUtils.isEmpty(userIds)) {
                            // 查询所在单位
                            val orgEx2 = Example(
                                OrganizationEntity::class.java
                            )
                            val orgEx2Criteria = orgEx2.createCriteria()
                            orgEx2Criteria.andEqualTo("organizationId", ownerId)
                            orgEx2Criteria.orLike("orgLevel", "%-$ownerId-%")
                            val orgList = organizationMapper.selectByExample(orgEx2)
                            val orgIds = orgList.stream().map { obj: OrganizationEntity -> obj.getOrganizationId() }
                                .collect(Collectors.toList())
                            val uocEx = Example(
                                UserOrgAndCorpEntity::class.java
                            )
                            val uocExCriteria = uocEx.createCriteria()
                            uocExCriteria.andIn("organizationId", orgIds)
                            uocExCriteria.andIn("userId", userIds)
                            val uocList = userOrgAndCorpMapper.selectByExample(uocEx)
                            if (uocList.size == 1) {
                                val userEntity = userMapper.selectByPrimaryKey(uocList[0].getUserId())
                                val result = updateUser(regionId, userEntity, strList)
                                if (result == 0) {
                                    setErrorMsg(i + 1, "调用更新方法失败", strList)
                                    log.debug("更新失败，[{}]", JsonUtils.toJson(strList))
                                }
                            } else {
                                setErrorMsg(i + 1, "根据用户姓名和单位查询失败", strList)
                                log.debug("根据用户姓名和单位查询失败，[{}]", JsonUtils.toJson(strList))
                            }
                        } else {
                            setErrorMsg(i + 1, "用姓名查询失败", strList)
                            log.debug("用姓名查询失败，[{}]", JsonUtils.toJson(strList))
                        }
                    }
                } else {
                    setErrorMsg(i + 1, "组织查询失败", strList)
                    log.debug("组织查询失败，[{}]", JsonUtils.toJson(strList))
                }
            }
        } catch (e: Exception) {
            log.error("获取表格数据异常", e)
            throw ApiException(
                "获取表格数据异常",
                Result<Any>(errors, 32001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取表格数据异常")
            )
        }
    }

    fun updateUser(regionId: Long, userEntity: UserEntity, strList: List<String>): Int {
        val user = buildUser(userEntity, strList)
        if (Objects.isNull(user)) {
            log.debug("组织在数据库查询失败, [{}]", JsonUtils.toJson(strList))
            return 0
        }
        val httpHeaders = HttpHeaders()
        httpHeaders["_region_id"] = regionId.toString()
        httpHeaders["_uid"] = "-999"
        httpHeaders["_org_type"] = "102803"
        val sysHeader = HeaderHelper.buildMyHeader(httpHeaders)
        val userId = orgUserService.addOrgUser(user, 2, sysHeader)
        if (Objects.isNull(userId)) {
            log.debug("导入党员失败，新增党员失败，[{}]", JsonUtils.toJson(strList))
            return 0
        } else {
            // 调用后续操作
            orgUserService.afterDealWithUser(userId, user!!.orgId, null, 1, false, sysHeader)
        }
        return 1
    }

    private fun transferToFile(multipartFile: MultipartFile): File? {
        // 选择用缓冲区来实现这个转换即使用java 创建的临时文件 使用 MultipartFile.transferto()方法 。
        val file: File?
        try {
            val originalFilename = multipartFile.originalFilename
            val filename = Objects.requireNonNull(originalFilename).split("\\.").toTypedArray()
            file = File.createTempFile(filename[0], "." + filename[1])
            if (file != null) {
                multipartFile.transferTo(file)
            }
            file.deleteOnExit()
        } catch (e: IOException) {
            throw ApiException("文件转换异常", Result<Any>(errors, 32001, HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件转换异常"))
        }
        return file
    }

    fun getOpKeyByValue(code: String?, value: String): Long? {
        return if (StringUtils.isNotBlank(value)) {
            val list = optionService.getList(code)
            val first = list.stream().filter { op: OptionEntity -> op.opValue == value }.findFirst()
            first.map { obj: OptionEntity -> obj.opKey }.orElse(null)
        } else {
            null
        }
    }

    fun buildUser(user: UserEntity, strList: List<String>): OrgUserForm? {
        val form = OrgUserForm()
        form.userId = user.userId
        form.phone = user.phone
        form.phoneSecret = user.phoneSecret
        /*String cert = strList.get(3);
        if (StringUtils.isNotBlank(cert)) {
            String certEncrypt = NumEncryptUtils.encrypt(cert, NumEncryptUtils.IDCARD);
            String certSecret = NumEncryptUtils.numSecret(cert, NumEncryptUtils.IDCARD);
            form.setCertNumber(certEncrypt);
            form.setCertNumberSecret(certSecret);
        }*/
        val genValue = getOpKeyByValue(Constants.GENDER_CODE, strList[1])
        if (Objects.nonNull(genValue)) {
            form.gender = genValue!!.toInt()
        }
        form.jobNumber = strList[5]
        val polValue = getOpKeyByValue(Constants.POLITICAL_CODE, strList[6])
        if (Objects.nonNull(polValue)) {
            form.politicalType = polValue!!.toInt()
        }
        val ethValue = getOpKeyByValue(Constants.ETHNIC_CODE, strList[8])
        if (Objects.nonNull(ethValue)) {
            form.ethnic = ethValue!!.toInt()
        }
        val eduValue = getOpKeyByValue(Constants.EDUCATION_CODE, strList[9])
        if (Objects.nonNull(eduValue)) {
            form.education = eduValue!!.toInt()
        }
        form.birthday = strList[10]
        form.joiningTime = strList[11]
        form.birthPlace = strList[14]
        form.address = strList[15]
        form.position = strList[17]
        form.isLose = if (StringUtils.isBlank(strList[25]) || strList[25] == "否"
        ) 2 else 1
        form.isFlow = if (StringUtils.isBlank(strList[26]) || strList[26] == "否"
        ) 2 else 1
        val orgNames = strList[4]
        val split: Array<String?> = orgNames.split("-").toTypedArray()
        val organizationEntity = getChildOrg(split, 0, null)
        if (Objects.nonNull(organizationEntity)) {
            form.orgId = organizationEntity!!.getOrganizationId()
            form.orgName = organizationEntity.getName()
            form.joiningPositiveTime = strList[13]
            form.position = strList[17]
            val posValue = getOpKeyByValue(Constants.POSITION_CODE.toString(), strList[17])
            if (Objects.nonNull(posValue)) {
                form.positionCode = posValue!!.toLong()
            }
        } else {
            return null
        }
        form.flag = "1"
        return form
    }

    fun setErrorMsg(index: Int, errorMsg: String, strList: List<String>) {
        val map: MutableMap<String, Any> = HashMap()
        map["index"] = index
        map["name"] = strList[0]
        map["errorMsg"] = errorMsg
        redisTemplate.opsForList().leftPush("CACHE_TABACCO_ERROR", JsonUtils.toJson(map))
    }

    fun saveError(file: MultipartFile, response: HttpServletResponse) {
        val titleNames = Stream.of(*names).collect(Collectors.toList())
        try {
            val excelData = ExcelUtils.getExcelData(transferToFile(file), 0, titleNames.toTypedArray(), false)
            val data: MutableList<List<String>> = ArrayList()
            data.add(titleNames)
            for (i in 0..499) {
                val d = redisTemplate.opsForList().rightPop("CACHE_TABACCO_ERROR")
                if (StringUtils.isBlank(d)) {
                    break
                }
                try {
                    val map = JsonUtils.fromJson(d, Map::class.java)
                    if (Objects.nonNull(map)) {
                        val index = map["index"] as Int?
                        val strList = excelData[index!! - 1]
                        val name = map["name"] as String?
                        if (name == strList[0]) {
                            strList.add(map["errorMsg"].toString())
                            data.add(strList)
                        } else {
                            if (d != null) {
                                redisTemplate.opsForList().leftPush("CACHE_TABACCO_ERROR", d)
                            }
                        }
                    }
                } catch (e: Exception) {
                    log.error("报错 -> ", e)
                    if (d != null) {
                        redisTemplate.opsForList().leftPush("CACHE_TABACCO_ERROR", d)
                    }
                }
            }
            val resultMap: MutableMap<String, List<List<String>>> = HashMap()
            resultMap["error"] = data
            val workbook = ExcelUtils.generateExcel("error-list.xlsx", resultMap)
            try {
                if (workbook != null) {
                    // 设置响应头导出文件格式
                    // String contentDisposition = "attachment;filename=" + new String(cName.getBytes("UTF-8"),"ISO-8859-1")+"." + ExcelUtils.XLSX_SUFFIX;
                    val contentDisposition =
                        "attachment;filename=" + URLEncoder.encode("error-list", "UTF-8") + "." + ExcelUtils.XLSX_SUFFIX
                    // 设置响应头的文件名称信息
                    response.setHeader("Content-Disposition", contentDisposition)
                    response.setHeader("Access-Control-Expose-Headers", "Content-Disposition")
                    // 设置响应头导出文件格式vnd.ms-excel
                    response.contentType = "application/vnd.ms-excel"
                    response.characterEncoding = "UTF-8"
                    // 使用响应的输出流导出excel文件
                    workbook.write(response.outputStream)
                }
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                if (null != workbook) {
                    try {
                        workbook.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
        } catch (e: Exception) {
            log.error("转存文件报错 -> ", e)
        }
    }

    fun getChildOrg(orgList: Array<String?>, index: Int, parentId: Long?): OrganizationEntity? {
        var i = index
        val orgName = orgList[i]
        val orgEx = Example(OrganizationEntity::class.java)
        val criteria = orgEx.createCriteria()
        criteria.andEqualTo("name", orgName)
        if (Objects.nonNull(parentId)) {
            criteria.andEqualTo("parentId", parentId)
        }
        criteria.andEqualTo("status", Constants.STATUS_YES)
        val organizationList = organizationMapper.selectByExample(orgEx)
        return if (organizationList.size == 1) {
            val entity = organizationList[0]
            if (i == orgList.size - 1) {
                entity
            } else {
                getChildOrg(orgList, ++i, entity.getOrganizationId())
            }
        } else {
            log.error("组织名称[{}]查询组织出现问题", orgName)
            null
        }
    }

    fun flushUserHighlight(regionId: Long?, header: SysHeader) {
        val formList = userOrgAndCorpMapper.selectPartyTimeByUser(regionId)
        formList.forEach(Consumer { form: UserPartyTimeForm ->
            // 判断入党时间
            if (StringUtils.isNotBlank(form.joiningTime)) {
                val list = userHighlightService.getUserHighlightByDesc(form.userId, 1, Constants.USER_HIGHLIGHT_PREP)
                if (CollectionUtils.isEmpty(list)) {
                    addHighlight(form.userId, form.joiningTime, Constants.USER_HIGHLIGHT_PREP, header)
                }
            }

            // 判断转正时间
            if (form.turnTime != null) {
                val list =
                    userHighlightService.getUserHighlightByDesc(form.userId, 1, Constants.USER_HIGHLIGHT_OFFICIAL)
                if (CollectionUtils.isEmpty(list)) {
                    addHighlight(
                        form.userId,
                        Utils.localDateTimeToString(form.turnTime),
                        Constants.USER_HIGHLIGHT_OFFICIAL,
                        header
                    )
                }
            }
        })
    }

    private fun addHighlight(userId: Long?, time: String?, desc: String, header: SysHeader) {
        val form = UserHighlightForm()
        form.type = 1
        form.userId = userId
        form.nodeTime = time
        form.desc = desc
        userHighlightService.addUserHighlight(form, header)
    }

    fun dataProcess(type: Int, userId: Long?, headers: HttpHeaders?) {
        val header = HeaderHelper.buildMyHeader(headers)
        val size = 100
        try {
            if (type == 1) {
                val example = Example(UserEntity::class.java)
                val criteria = example.createCriteria()
                if (userId != null) {
                    criteria.andEqualTo("userId", userId)
                }
                criteria.andIsNotNull("certNumber")
                example.orderBy("userId")
                var i = 1
                while (i > 0) {
                    val page = PageHelper.startPage<Any>(i, size)
                        .doSelectPage<UserEntity> { userMapper.selectByExample(example) }
                    log.debug("第[{}]页，数据量:[{}]", i, page.size)
                    if (page.size == 0) {
                        break
                    }
                    for (user in page) {
                        try {
                            val certNumber = NumEncryptUtils.decrypt(user.certNumber, user.certNumberSecret)
                            val certNumberSecret = NumEncryptUtils.numSecret(certNumber, Constants.CERT_TYPE)
                            val certNumberNew = NumEncryptUtils.encrypt(certNumber, Constants.CERT_TYPE)
                            user.certNumber = certNumberNew
                            user.certNumberSecret = certNumberSecret
                            user.updateTime = Date()
                            user.lastChangeUser = -999L
                            userMapper.updateByPrimaryKeySelective(user)
                            // 刷洗MONGO
                            userMongoService.conversionUser(user, null, true, header)
                            // 刷新缓存
                            userOrgCacheCallService.flushUserInfo(header.regionId, user, 1)
                        } catch (e: Exception) {
                            log.error("更新身份证报错", e)
                        }
                    }
                    i++
                }
            } else if (type == 2) {
                val updateList: MutableList<UserSnapshotEntity> = ArrayList()
                var i = 1
                while (i > 0) {
                    val example = Example(
                        UserSnapshotEntity::class.java
                    )
                    val criteria = example.createCriteria()
                    if (userId != null) {
                        criteria.andEqualTo("userId", userId)
                    }
                    example.orderBy("userSnapshotId")
                    val page = PageHelper.startPage<Any>(i, size)
                        .doSelectPage<UserSnapshotEntity> { userSnapshotMapper.selectByExample(example) }
                    log.debug("第[{}]页，数据量:[{}]", i, page.size)
                    if (page.size == 0) {
                        break
                    }
                    for (en in page) {
                        try {
                            val certNumber = NumEncryptUtils.decrypt(en.certNumber, en.certNumberSecret)
                            val certNumberSecret = NumEncryptUtils.numSecret(certNumber, Constants.CERT_TYPE)
                            val certNumberNew = NumEncryptUtils.encrypt(certNumber, Constants.CERT_TYPE)
                            en.certNumber = certNumberNew
                            en.certNumberSecret = certNumberSecret
                            updateList.add(en)
                        } catch (e: Exception) {
                            log.error("更新身份证报错", e)
                        }
                    }
                    userSnapshotMapper.updateListByPrimaryKey(updateList)
                    updateList.clear()
                    i++
                }
            } else if (type == 3) {
                val entities = userDevelopMapper.selectAll()
                for (en in entities) {
                    try {
                        val certNumber = NumEncryptUtils.decrypt(en.certNumber, en.certNumberSecret)
                        val certNumberSecret = NumEncryptUtils.numSecret(certNumber, Constants.CERT_TYPE)
                        val certNumberNew = NumEncryptUtils.encrypt(certNumber, Constants.CERT_TYPE)
                        en.certNumber = certNumberNew
                        en.certNumberSecret = certNumberSecret
                        en.updateTime = LocalDateTime.now()
                        en.lastChangeUser = -999L
                        userDevelopMapper.updateByPrimaryKey(en)
                    } catch (e: Exception) {
                        log.error("更新身份证报错", e)
                    }
                }
            }
        } catch (e: Exception) {
            log.error("更新身份证报错", e)
        }
    }

    /**
     * @title 从Mongo中抽取数据到Mysql
     * <AUTHOR>
     * @param   dynamicField    mongo中的动态字段名称
     * @param   fieldName       实体类中字段名称
     * @param   type            1 - 用户，2 - 组织
     * @updateTime 2022/7/4 10:14
     * @return
     * @throws
     */
    fun transferData(dynamicField: String, fieldName: String, type: Int, userId: Long?, header: SysHeader) {
        val clazz = if(type == 1) {
            User::class.java
        } else {
            Org::class.java
        }
        // 从Mongo动态字段里取值到字段中
        val criteria = Criteria.where("fields.$dynamicField").ne(null)
        if (userId != null) {
            criteria.and("userId").`is`(userId)
        }
        val resultList = mongoTemplate.find(Query(criteria), clazz)
        var data: Any? = null
        resultList.forEachIndexed { index, it ->
            if (it is User) {
                if (it.fields.isNotEmpty()) {
                    val value = it.fields[dynamicField]
                    data = if (value is Collection<*>) {
                        if (value.isNotEmpty()) {
                            val str = value.joinToString(",")
                            if (str.contains(",")) {
                                str
                            } else {
                                str.toInt()
                            }
                        } else {
                            null
                        }
                    } else {
                        value
                    }
                }
                val entity = userMapper.selectByPrimaryKey(it.userId)
                if (entity != null) {
                    val beanMap = BeanMap.create(entity)
                    beanMap[fieldName] = data
                    userMapper.updateByPrimaryKey(entity)
                    userMongoService.conversionUser(entity, null, false, header)
                }
            } else if (it is Org) {
                val value = it.fields[dynamicField]
                data = if (value is Collection<*>) {
                    val str = value.joinToString(",")
                    if (str.isNotBlank()) {
                        if (str.contains(",")) {
                            str
                        } else {
                            str.toInt()
                        }
                    } else {
                        null
                    }
                } else {
                    value
                }
                val entity = organizationMapper.selectByPrimaryKey(it.organizationId)
                if (entity != null) {
                    val beanMap = BeanMap.create(entity)
                    beanMap[fieldName] = data
                    organizationMapper.updateByPrimaryKey(entity)
                }
            }
        }
    }

    fun tissueLocationData() {
        val example = Example(OrganizationEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andIsNotNull("orgAddress")
        val orgs = organizationMapper.selectByExample(example)
        orgs.forEach { it ->
            // 转换地址
            if (org.apache.commons.lang.StringUtils.isNotBlank(it.getOrgAddress())) {
                val amapResponse = getGeocode(it.getOrgAddress())
                if (amapResponse != null) {
                    val coordinates = takeCoordinates(amapResponse)
                    if (org.apache.commons.lang.StringUtils.isNotBlank(coordinates)) {
                        val str = coordinates!!.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                        it.setLongitude(java.lang.Double.valueOf(str[0]))
                        it.setLatitude(java.lang.Double.valueOf(str[1]))
                    }
                    it.setAdcode(getAdcode(amapResponse))
                }
            }
            it.updateTime = Date()
            it.lastChangeUser = -1
            this.organizationMapper.updateByPrimaryKey(it)
        }
    }

    /**
     * 测试SQL拦截器的方法
     * 执行一个简单的查询来触发拦截器
     */
    fun findUsersByLimit(limit: Int): List<UserEntity> {
        log.info("=== TestService.findUsersByLimit被调用，limit: {} ===", limit)

        val example = Example(UserEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("status", Constants.STATUS_YES)
        example.orderBy("userId")

        // 使用PageHelper来生成LIMIT语句，这会触发SQL转换
        val page = PageHelper.startPage<Any>(1, limit)
            .doSelectPage<UserEntity> { userMapper.selectByExample(example) }

        log.info("查询结果数量: {}", page.size)
        return page
    }

    /**
     * 简单的测试方法，直接调用mapper
     */
    fun testSimpleQuery(): List<UserEntity> {
        log.info("=== TestService.testSimpleQuery被调用 ===")

        val example = Example(UserEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("status", Constants.STATUS_YES)
        example.orderBy("userId")

        // 直接调用selectByExample，这应该会触发拦截器
        val users = userMapper.selectByExample(example)

        log.info("查询结果数量: {}", users.size)
        return users.take(5) // 只返回前5个
    }
}