package com.goodsogood.ows.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.OrgChildForm;
import com.goodsogood.ows.model.vo.OrgNameResultForm;
import com.goodsogood.ows.model.vo.RecusalForm;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.OpenBase;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * 用户缓存服务
 * Created by huangyongpei on 2018/8/17.
 */
@Service
@Log4j2
public class UserOrgCacheService {

    /**
     * 缓存服务
     */
    private final StringRedisTemplate redisTemplate;
    /**
     * 用户服务
     */
    private final UserService userService;

    /**
     * 开发接口服务
     */
    private final OpenService openService;
    private final OrganizationMapper organizationMapper;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;
    private final RoleMenuMapper roleMenuMapper;
    private final RecusalService recusalService;

    /**
     * json 操作
     */
    private final ObjectMapper json = new ObjectMapper();

    @Autowired
    public UserOrgCacheService(StringRedisTemplate redisTemplate, UserService userService,
                               OpenService openService, OrganizationMapper organizationMapper,
                               UserOrgAndCorpMapper userOrgAndCorpMapper, RoleMapper roleMapper,
                               UserRoleMapper userRoleMapper, RoleMenuMapper roleMenuMapper, RecusalService recusalService) {
        this.redisTemplate = redisTemplate;
        this.userService = userService;
        this.openService = openService;
        this.organizationMapper = organizationMapper;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.roleMapper = roleMapper;
        this.userRoleMapper = userRoleMapper;
        this.roleMenuMapper = roleMenuMapper;
        this.recusalService = recusalService;
    }

    /**
     * 刷新用户信息（实体，user base info）
     * <p>
     * 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24
     *
     * @param user
     * @param type     1 刷新缓存，2 清理缓存
     * @param regionId 区县ID
     * @return
     */
    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public Boolean flushUserInfo(Long regionId, UserEntity user, Integer type) {
        if (user == null || type == null || StringUtils.isEmpty(user.getUserId())) {
            log.debug("刷新缓存失败：user == null 或者user_id == null。 user入参 = {}", user);
            return Boolean.FALSE;
        }
        // 当前时间
        String curTime = Utils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
        Long userId = user.getUserId();
        log.debug("用户信息缓存：userId:[{}], type:[{}](1.刷新缓存 2.清理缓存),regionId:[{}]", userId, type, regionId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("刷新用户信息缓存，开始时间：[{}]", curTime);
        String infoKey = Global.CACHE_USER_ID_PREFIX + userId;
        String baseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        String tagKey = Constants.CACHE_USER_TAG_PREFIX + userId + "_" + regionId;
        String userMgrKey = Constants.CACHE_USER_MGR_PREFIX + userId + "_" + regionId;
        String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId;
        String roleWxKey = Constants.USER_ROLE_WX_CACHE_KEY_PREFIX + userId + "_" + regionId;
        if (2 == type) {
            redisTemplate.delete(Arrays.asList(infoKey, baseKey, tagKey, userMgrKey, roleKey, roleWxKey));
            log.debug("用户信息缓存：userId:[{}], 清理成功！", userId);
            // 清理用户活动缓存
            this.redisTemplate.delete(Constants.ACTIVITY_ALL_UID_PREFIX + userId);
            log.debug("用户活动缓存：userId:[{}], 清理成功！", userId);
            log.debug("清理用户信息缓存，耗时：[{}]", stopWatch.toString());
            return Boolean.TRUE;
        }
        // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24
        // 由于是异步，当主线程还未提交事务的时候，子线程已经执行了改数据库读的操作，导致数据脏读
        try {
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(infoKey))) {
                this.redisTemplate.delete(infoKey);
            }
            redisTemplate.opsForValue().set(infoKey, json.writeValueAsString(user),
                    Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            UserInfoBase base = openService.getBaseByUserEntity(user, regionId);
            log.debug("用户信息缓存构建userInfoBase:[{}]成功", base);
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(baseKey))) {
                this.redisTemplate.delete(baseKey);
            }
            redisTemplate.opsForValue().set(baseKey, json.writeValueAsString(base),
                    Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            log.debug("用户信息缓存：userId:[{}],刷新成功！", userId);
            // 刷新openId对应userId缓存
            if (!CollectionUtils.isEmpty(base.getOpenList())) {
                for (OpenBase openBase : base.getOpenList()) {
                    String openIdKey = Constants.CACHE_USER_THTOKEN_PREFIX + openBase.getOpenId();
                    if (Boolean.TRUE.equals(this.redisTemplate.hasKey(openIdKey))) {
                        this.redisTemplate.delete(openIdKey);
                    }
                    this.redisTemplate.opsForValue().set(openIdKey, user.getUserId().toString(), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                }
            }

            // 刷新用户为管理员的组织列表缓存
            List<Integer> manageType = new ArrayList<>(2);
            manageType.add(Constants.ROLE_TYPE_DEFINED_USER);
            manageType.add(Constants.ROLE_TYPE_DEFINED_ROOT);
            List<Long> manageOrgIds = this.userOrgAndCorpMapper.findOrgIdByUserIdAndRoleType(user.getUserId(),
                    manageType, null, regionId);
            if (!CollectionUtils.isEmpty(manageOrgIds)) {
                this.redisTemplate.opsForValue().set(userMgrKey, json.writeValueAsString(manageOrgIds), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            } else {
                // 删除原用户管理员组织列表
                this.redisTemplate.delete(userMgrKey);
            }
            // 刷新用户PC权限列表缓存
            List<UserRoleEntity> list = this.userRoleMapper.getUserRoleList(Constants.ROLE_BELONG_PC,
                    Constants.STATUS_YES, userId, regionId);
            if (!CollectionUtils.isEmpty(list)) {
                this.redisTemplate.opsForValue().set(roleKey, json.writeValueAsString(list), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            } else {
                // 删除用户权限列表缓存
                this.redisTemplate.delete(roleKey);
            }
            // 刷新用户WX权限列表缓存
            List<UserRoleEntity> listWx = this.userRoleMapper.getUserRoleList(Constants.ROLE_BELONG_WX,
                    Constants.STATUS_YES, userId, regionId);
            if (!CollectionUtils.isEmpty(listWx)) {
                this.redisTemplate.opsForValue().set(roleWxKey, json.writeValueAsString(listWx), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            } else {
                // 删除用户权限列表缓存
                this.redisTemplate.delete(roleWxKey);
            }
            // 刷新用户标签缓存
            Example example = new Example(UserOrgAndCorpEntity.class);
            example.createCriteria().andEqualTo("userId", userId);
            List<UserOrgAndCorpEntity> userOrgList = this.userOrgAndCorpMapper.selectByExample(example);
            if (!CollectionUtils.isEmpty(userOrgList)) {
                this.redisTemplate.opsForValue().set(tagKey, json.writeValueAsString(userOrgList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            } else {
                // 删除用户权限列表缓存
                this.redisTemplate.delete(tagKey);
            }
            stopWatch.stop();
            log.debug("刷新用户信息缓存，耗时：[{}]", stopWatch.toString());
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("刷新用户信息缓存失败：[{}]", e.getMessage(), e);
        }
        log.debug("刷新用户信息缓存时用户不存在。userId：[{}]", userId);
        return Boolean.FALSE;
    }

    //    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public Boolean flushUserInfo(Long regionId, Long userId, Integer type) {
        if (userId == null || type == null) {
            return Boolean.FALSE;
        }
        // 当前时间
        String curTime = Utils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
        log.debug("用户信息缓存：userId:{}, type:{}(1.刷新缓存 2.清理缓存)", userId, type);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("刷新用户信息缓存，开始时间：[{}]", curTime);
        String infoKey = Global.CACHE_USER_ID_PREFIX + userId;
        String baseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        if (2 == type) {
            // 清理用户活动缓存
            this.redisTemplate.delete(Constants.ACTIVITY_ALL_UID_PREFIX + userId);
            log.debug("用户活动缓存：userId:[{}], 清理成功！", userId);
            // 清理用户信息缓存
            redisTemplate.delete(Arrays.asList(infoKey, baseKey));
            log.debug("用户信息缓存：userId:[{}], 清理成功！", userId);
            log.debug("清理用户信息缓存，耗时：[{}]", stopWatch.toString());
            return Boolean.TRUE;
        }
        UserEntity user = userService.findUserById(userId);
        return this.flushUserInfo(regionId, user, type);
    }

    /**
     * 刷新组织缓存
     *
     * @param org
     * @param type 1 刷新缓存，2 清理缓存
     * @return
     */
    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public Boolean flushOrgInfo(OrganizationEntity org, Integer type, Integer... oprType) {
        if (null == org || null == type) {
            return Boolean.FALSE;
        }
        // 当前时间
        String curTime = Utils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
        long orgId = org.getOrganizationId();
        log.debug("组织信息缓存：orgId:{}, type:{}(1.刷新缓存 2.清理缓存)", orgId, type);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("刷新组织信息缓存，开始时间：[{}]", curTime);
        String key = Global.CACHE_ORG_ID_PREFIX + orgId;
        if ((2 == type) || (oprType.length > 0 && 1 == oprType[0])) {
            // 刷新该组织所有上级组织缓存
            List<OrganizationEntity> parentList = new ArrayList<>();
            this.getParentEntity(org.getParentId(), org.getOrgType(), parentList);
            if (!parentList.isEmpty()) {
                for (OrganizationEntity child : parentList) {
                    String orgChildKey = Constants.CACHE_ORG_CHILD_PREFIX + child.getOrganizationId();
                    List<OrgNameResultForm> childList = this.getChildList(child, org.getRegionId());
                    if (!CollectionUtils.isEmpty(childList)) {
                        this.redisTemplate.opsForValue().set(orgChildKey, Utils.toJson(childList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                        log.debug("组织所有下级组织信息缓存：orgId:[{}], 刷新成功！", orgId);
                    } else {
                        this.redisTemplate.delete(orgChildKey);
                    }
                }
            }
        }
        if (2 == type) {
            redisTemplate.delete(key);
            log.info("组织信息缓存：orgId:[{}], 清理成功！", orgId);
            return Boolean.TRUE;
        }
        try {
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(key))) {
                this.redisTemplate.delete(key);
            }
            this.updateNum(org);
            this.redisTemplate.opsForValue().set(key, Utils.toJson(org),
                    Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            log.debug("组织信息缓存：orgId:[{}], 刷新成功！", orgId);

            // 刷新组织所有下级缓存
            String orgChildKey = Constants.CACHE_ORG_CHILD_PREFIX + org.getOrganizationId();
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgChildKey))) {
                this.redisTemplate.delete(orgChildKey);
            }
            List<OrgNameResultForm> orgList = this.getChildList(org, org.getRegionId());
            if (!CollectionUtils.isEmpty(orgList)) {
                this.redisTemplate.opsForValue().set(orgChildKey, Utils.toJson(orgList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                log.debug("组织所有下级组织信息缓存：orgId:[{}], 刷新成功！", orgId);
            }

            // 刷新组织用户列表缓存
            List<UserInfoBase> userList = this.userOrgAndCorpMapper.buildOrgUsers(orgId, null);
            String orgUsersKey = Constants.CACHE_ORG_USERS + org.getOrganizationId();
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgUsersKey))) {
                this.redisTemplate.delete(orgChildKey);
            }
            if (!CollectionUtils.isEmpty(userList)) {
                this.redisTemplate.opsForValue().set(orgUsersKey, Utils.toJson(userList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                log.debug("组织所有用户列表信息缓存：orgId:[{}]，刷新成功! ", orgId);
            }
            stopWatch.stop();
            log.debug("刷新组织信息缓存，耗时：[{}]", stopWatch.toString());
            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("刷新组织缓存失败：[{}]", e.getMessage(), e);
        }
        return Boolean.FALSE;
    }

    /**
     * 刷新组织缓存
     *
     * @param orgId
     * @param type  1 刷新缓存，2 清理缓存
     * @return
     */
    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public Boolean flushOrgInfo(Long orgId, Integer type, Integer... oprType) {
        if (orgId == null || type == null) {
            return Boolean.FALSE;
        }
        OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(orgId);
        return this.flushOrgInfo(org, type, oprType);
    }

    /**
     * @param orgId
     * @return
     */
    private Set<OrgChildForm> findOrgListByOwnerId(long orgId, Long regionId) {
        Set<OrgChildForm> set = new HashSet<>();
        List<OrgChildForm> idList = this.organizationMapper.getFormList(orgId, regionId);
        if (null != idList && !idList.isEmpty()) {
            set.addAll(idList);
            for (OrgChildForm form : idList) {
                set.addAll(findOrgListByOwnerId(form.getOrgId(), regionId));
            }
        }
        return set;
    }

    /**
     * @param pid
     * @param orgType
     * @return
     */
    private List<OrganizationEntity> getParentEntity(long pid, int orgType, List<OrganizationEntity> parentList) {
        OrganizationEntity resultEntity;
        String redisOrgKey = Global.CACHE_ORG_ID_PREFIX + pid;
        if (this.redisTemplate.hasKey(redisOrgKey)) {
            resultEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(redisOrgKey), OrganizationEntity.class);
        } else {
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria().andEqualTo("organizationId", pid)
                    .andEqualTo("orgType", orgType)
                    .andEqualTo("status", Constants.YES);
            resultEntity = this.organizationMapper.selectOneByExample(example);
        }
        if (null != resultEntity && resultEntity.getParentId() != null) {
            parentList.add(resultEntity);
            return getParentEntity(resultEntity.getParentId(), orgType, parentList);
        }
        return null;
    }

    /**
     * @param org
     * @return
     */
    private List<OrgNameResultForm> getChildList(OrganizationEntity org, Long regionId) {
        // 刷新组织所有下级缓存
        String orgChildKey = Constants.CACHE_ORG_CHILD_PREFIX + org.getOrganizationId();
        if (this.redisTemplate.hasKey(orgChildKey)) {
            this.redisTemplate.delete(orgChildKey);
        }
        // 查询owenerId=组织ID的数据
        List<OrgChildForm> idList = this.organizationMapper.getFormList(org.getOrganizationId(), regionId);
        // 查询组织信息
        OrganizationEntity resultEntity;
        String redisOrgKey = Global.CACHE_ORG_ID_PREFIX + org.getOrganizationId();
        if (this.redisTemplate.hasKey(redisOrgKey)) {
            resultEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(redisOrgKey), OrganizationEntity.class);
        } else {
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria()
                    .andEqualTo("status", Constants.STATUS_YES)
                    .andEqualTo("organizationId", org.getOrganizationId());
            resultEntity = this.organizationMapper.selectOneByExample(example);
        }
        List<Long> orgList = new ArrayList<>();
        orgList.add(org.getOrganizationId());
        List<String> orgLevelList = new ArrayList<>();
        orgLevelList.add(resultEntity.getOrgLevel() + org.getOrganizationId() + "-");
        idList.forEach(orgForm -> {
            orgList.add(orgForm.getOrgId());
            orgLevelList.add(orgForm.getOrgLevel() + orgForm.getOrgId() + "-");
        });
        return this.organizationMapper.findAllChildOrg(orgLevelList, orgList, null, null, org.getRegionId());
    }

    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public Boolean flushRoleInfo(Long roleId, Integer type) {
        if (roleId == null || type == null) {
            return Boolean.FALSE;
        }
        log.debug("角色信息缓存：roleId:{}, type:{}(1.刷新缓存 2.清理缓存)", roleId, type);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.debug("刷新角色信息缓存，开始时间：{}", stopWatch.toString());
        String roleRedisKey = Constants.CACHE_USER_ROLE_PREFIX + roleId;
        if (2 == type) {
            // 清理角色缓存
            this.redisTemplate.delete(Constants.CACHE_USER_ROLE_PREFIX + roleId);
            log.debug("角色信息缓存：roleId:{}, 清理成功！", roleId);
            return Boolean.TRUE;
        }
        RoleEntity roleEntity = this.roleMapper.selectByPrimaryKey(roleId);
        if (Objects.nonNull(roleEntity)) {
            List<Long> oList = new ArrayList<>();
            oList.add(roleId);
            List<MenuForm> mList = this.roleMenuMapper.getMenuByRoles(oList, roleEntity.getRegionId());
            this.redisTemplate.opsForValue().set(roleRedisKey, Utils.toJson(mList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
        }
        return Boolean.TRUE;
    }

    /**
     * 仅刷新组织信息缓存
     *
     * @param org  组织实体
     * @param flag 操作类型 1-删除
     */
    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public void flushOrgSingle(OrganizationEntity org, Integer... flag) {
        if (org != null) {
            String curTime = Utils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            log.debug("仅刷新组织信息缓存,orgId:[{}],开始时间：[{}]", org.getOrganizationId(), curTime);
            String orgKey = Global.CACHE_ORG_ID_PREFIX + org.getOrganizationId();
            if (this.redisTemplate.hasKey(orgKey)) {
                this.redisTemplate.delete(orgKey);
            }
            redisTemplate.opsForValue().set(orgKey, Utils.toJson(org),
                    Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
            log.debug("组织信息缓存：orgId:[{}], 刷新成功！耗时：[{}]", org.getOrganizationId(), stopWatch.toString());
            stopWatch.stop();
        }
    }

    /**
     * 仅删除openID缓存
     *
     * @param openId openID
     */
    @Async("flushCacheExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public void flushOpenIdSingle(String openId) {
        if (!StringUtils.isEmpty(openId)) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            String orgKey = Global.CACHE_USER_THTOKEN_PREFIX + openId;
            log.debug("仅删除openID缓存,redisKey：[{}]", openId);
            this.redisTemplate.delete(orgKey);
        }
    }

    /**
     * 删除用户所有缓存
     *
     * @param userId openID
     */
    @Async("flushCacheExecutor")
    @Transactional
    public void deleteUserAllCache(Long userId, Long regionId) {
        String infoKey = Global.CACHE_USER_ID_PREFIX + userId;
        String baseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        String tagKey = Constants.CACHE_USER_TAG_PREFIX + userId + "_" + regionId;
        String userMgrKey = Constants.CACHE_USER_MGR_PREFIX + userId + "_" + regionId;
        String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId;
        String roleWxKey = Constants.USER_ROLE_WX_CACHE_KEY_PREFIX + userId + "_" + regionId;
        redisTemplate.delete(Arrays.asList(infoKey, baseKey, tagKey, userMgrKey, roleKey, roleWxKey));
        log.debug("用户信息缓存：userId:[{}], 清理成功！", userId);
        // 清理用户活动缓存
        this.redisTemplate.delete(Constants.ACTIVITY_ALL_UID_PREFIX + userId);
        log.debug("用户缓存：userId:[{}], 清理成功！", userId);
    }

    @Async("developAsyncExecutor")
    @Transactional(propagation = Propagation.SUPPORTS)
    public void asyncDevelop(RecusalForm recusalForm, HeaderHelper.SysHeader header, Integer type) throws Exception {
        Long userDevelopId = this.recusalService.getUserDevelopId(recusalForm.getUserId());
        recusalForm.setUserDevelopId(userDevelopId);
        this.recusalService.recusal(recusalForm, header, type);
    }

    /**
     *
     * @param org
     */
    private void updateNum(OrganizationEntity org) {
        int userNum = this.organizationMapper.findCountByOrg(org.getOrganizationId(),
                org.getRegionId(), 0, 1);
        int partyMemberNum = this.organizationMapper.findCountByOrg(org.getOrganizationId(),
                org.getRegionId(), 1, 1);
        int workNum = this.organizationMapper.findCountByOrg(org.getOrganizationId(),
                org.getRegionId(), 0, 0);
        org.setUserNum(userNum);
        org.setPartyMemberNum(partyMemberNum);
        org.setWorkNum(workNum);
        this.organizationMapper.updateByPrimaryKeySelective(org);
    }
}
