package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.VolunteerTeamMessageConfig;
import com.goodsogood.ows.helper.RemoteApiHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.VolunteerTeamPushDataForm;
import com.goodsogood.ows.push.template.wechat.WechatServiceRemindTemplate;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 推送消息service
 *
 * <AUTHOR>
 * @date 2020-06-30 15:41
 * @since 3.0.0
 **/
@Service
@Log4j2
public class PushDataService {

     @Value("${tog-services.push}")
     @NotBlank
     private String pushCenter;

     private final VolunteerTeamMessageConfig volunteerTeamMessageConfig;
     private final RestTemplate restTemplate;

     @Autowired
     public PushDataService(VolunteerTeamMessageConfig volunteerTeamMessageConfig, RestTemplate restTemplate) {
          this.volunteerTeamMessageConfig = volunteerTeamMessageConfig;
          this.restTemplate = restTemplate;
     }

     /**
      * 推送消息
      *
      * @return
      */
     @Async("pushDataExecutor")
     public void volunteerPushData(List<WechatServiceRemindTemplate.ServiceRemind> pushDataList, Long regionId) {
          HttpHeaders headers = new HttpHeaders();
          headers.set("_tk", "-1");
          headers.set("_region_id", String.valueOf(regionId));
          this.restTemplate.setErrorHandler(new ClientExceptionHandler());
          // 组装请求体
          VolunteerTeamPushDataForm requestBody = new VolunteerTeamPushDataForm();
          requestBody.setTemplateId(this.volunteerTeamMessageConfig.getTemplateId());
          requestBody.setChannelType(this.volunteerTeamMessageConfig.getChannelType().byteValue());
          requestBody.setSource(this.volunteerTeamMessageConfig.getSource());
          requestBody.setData(pushDataList);

          Integer resultCode = null;
          int count = 0;
          do {
               try {
                    resultCode = RemoteApiHelper.post(this.restTemplate, String.format("http://%s/global/push/diff", pushCenter),
                            requestBody, headers, new TypeReference<Result<Integer>>() {
                            });
               } catch (Exception e) {
                    log.error("调用推送中心失败！templateId[{}],channelType[{}],第[{}]次调用",
                            this.volunteerTeamMessageConfig.getTemplateId(),
                            this.volunteerTeamMessageConfig.getChannelType(), count, e);
               }
               count++;
          } while (null == resultCode && count < volunteerTeamMessageConfig.getMaxRepeatCount());
          log.debug("调用推送中心返回结果: templateId[{}],channelType[{}],resultCode[{}],调用次数[{}]",
                  this.volunteerTeamMessageConfig.getTemplateId(),
                  this.volunteerTeamMessageConfig.getChannelType(), resultCode, count);
     }
}
