package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.OrganizationMapper;
import com.goodsogood.ows.mapper.UserOrgAndCorpMapper;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.db.UserThirdEntity;
import com.goodsogood.ows.model.vo.GuestOrgResultForm;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.OrgNameResultForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.RoleMenuForm;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.TokenBase;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2018-03-21
 */
@Service
@Log4j2
public class UserForWxService {

     private final UserThirdService userThirdService;
     private final UserService userService;
     private final StringRedisTemplate redisTemplate;
     private final OrganizationMapper organizationMapper;
     private final UserOrgAndCorpMapper userOrgAndCorpMapper;
     private final OpenService openService;
     private final Errors errors;
     private final ObjectMapper OBJECTMAPPER = new ObjectMapper();


     public UserForWxService(UserThirdService userThirdService,
                             UserService userService, StringRedisTemplate redisTemplate,
                             OrganizationMapper organizationMapper,
                             UserOrgAndCorpMapper userOrgAndCorpMapper,
                             OpenService openService, Errors errors) {
          this.userThirdService = userThirdService;
          this.userService = userService;
          this.redisTemplate = redisTemplate;
          this.organizationMapper = organizationMapper;
          this.userOrgAndCorpMapper = userOrgAndCorpMapper;
          this.openService = openService;
          this.errors = errors;
     }


     /**
      * 查询用户的第三方信息
      *
      * @param thToken 第三方token （微信为openId）
      * @return UserThirdEntity
      */
     public UserThirdEntity findUserThirdInfoByToken(String thToken, Long oid) {
          return userThirdService.findByToken(thToken, oid);
     }

     /**
      * 查询用户的第三方信息
      *
      * @param uid 用户id
      * @return UserEntity
      */
     public UserEntity findUserInfoById(Long uid) {
          return userService.selectOne(uid);
     }

     /**
      * 查询组织信息
      *
      * @param oid 组织id
      * @return OrganizationEntity
      */
     public OrganizationEntity findOrganizationInfo(long oid) {
          // 缓存中获取
          OrganizationEntity redisOrg = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_ORG_ID_PREFIX + oid),
                  OrganizationEntity.class);
          if (redisOrg == null) {
               redisOrg = this.organizationMapper.selectByPrimaryKey(oid);
          }
          return redisOrg;
     }

     /**
      * 查询用户所在的所有组织
      *
      * @param uid  用户id
      * @param type 组织类型 登录时为-1
      * @return List<GuestOrgResultForm>
      */
     public List<GuestOrgResultForm> getOidList(long uid, int type, Long regionId) {
          UserEntity userEntity = new UserEntity();
          userEntity.setUserId(uid);
          try {
               return userService.getOidList(userEntity, type, regionId);
          } catch (IOException e) {
               log.error("查询用户所在的所有组织失败:" + e.getMessage(), e);
               throw new ApiException("查询用户所在的所有组织失败！", new Result<>(errors, 186, HttpStatus.OK.value()));
          }
     }


     /**
      * 查询用户所在的组织的标签
      *
      * @param uid  用户id
      * @param oid  用户所在组织id
      * @param type 组织类型 登录时为-1
      * @return String
      */
     public String findTags(long uid, long oid, int type) {
          return this.userOrgAndCorpMapper.findTag(uid, type, oid);
     }


     /**
      * 根据oid所有下级组织
      *
      * @param oid      用户所在组织id
      * @param orgLevel 组织树父级路径
      * @return List<OrgNameResultForm>
      */
     public List<OrgNameResultForm> findAllOrg(long oid, String orgLevel) {
          String orgChildKey = Constants.CACHE_ORG_CHILD_PREFIX + oid;
          if (this.redisTemplate.hasKey(orgChildKey)) {
               try {
                    return OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(orgChildKey), new TypeReference<List<OrgNameResultForm>>() {
                    });
               } catch (IOException e) {
                    log.error("根据oid所有下级组织,获取redis异常 key:" + orgChildKey, e);
               }
          }
          Set<Long> orgSet = userService.findOrgIdListByOwnerId(oid);
          List<Long> orgIds = new ArrayList<>(orgSet);
          orgIds.add(oid);
          return this.organizationMapper.findAllOrg(orgLevel + oid + "-",
                  orgIds, null, null);
     }

     /**
      * 查询用户是管理员的组织
      *
      * @param uid 用户id
      * @return List<Long>
      */
     public List<Long> findManageOrgIds(long uid, Long regionId) {
          // 查询用户为管理员的组织
          String userMgrKey = Constants.CACHE_USER_MGR_PREFIX + uid + "_" + regionId;
          if (this.redisTemplate.hasKey(userMgrKey)) {
               log.debug("获取用户管理组织列表缓存，userId：{}", uid);
               try {
                    return OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(userMgrKey), new TypeReference<List<Long>>() {
                    });
               } catch (IOException e) {
                    log.error("获取redis异常 key:" + userMgrKey, e);
               }
          }
          List<Integer> manageType = new ArrayList<>();
          manageType.add(Constants.ROLE_TYPE_DEFINED_USER);
          manageType.add(Constants.ROLE_TYPE_DEFINED_ROOT);
          return this.userOrgAndCorpMapper.findOrgIdByUserIdAndRoleType(uid, manageType, Constants.ROLE_BELONG_WX,
                  regionId);
     }

     /**
      * 查询用户的角色信息和菜单
      *
      * @param uid      用户id
      * @param oid      公众号关联的组织id
      * @param uoid     用户所在组织id
      * @param isManage 是否是管理员 1-是 0-否
      */
     public RoleMenuForm findRoleListByUser(long oid, long uoid, long uid, Integer isManage, Long regionId) {
          RoleMenuForm roleMenuForm = new RoleMenuForm();
          Date now = DateTime.now().toDate();
          userService.commonAddRole(oid, uid, regionId, now);
          // 查询用户角色
          List<Long> roleList = userService.getRoleList(uoid, uid, isManage, now, regionId);
          roleMenuForm.setRoleList(roleList);
          roleMenuForm.setMenuList(userService.getMenuForms(roleList, regionId));
          return roleMenuForm;
     }

     /**
      * 登录成功后 需要添加缓存的信息
      * 1.菜单对应url存入redis
      * 2.用户信息
      *
      * @param menuFormList 菜单form
      * @param tokenId      登录id
      */
     public void loginAfterTokenCache(List<MenuForm> menuFormList, String tokenId, Long regionId) {
          if (this.redisTemplate.hasKey(tokenId)) {
               putUserInfoToToken(tokenId, regionId);
               putRedisMenuUrl(menuFormList, tokenId);
          }
     }

     /**
      * 添加用户信息到token
      *
      * @param tokenId 登录tokenId
      */
     private void putUserInfoToToken(String tokenId, Long regionId) {
          // Token
          String token = this.redisTemplate.opsForValue().get(tokenId);
          if (StringUtils.isNotBlank(token)) {
               try {
                    TokenBase tokenBase = Utils.fromJson(token, TokenBase.class);
                    // 设置userInfoBase
                    UserInfoBase base = this.openService.userBaseInfo(tokenBase.getUserId(), tokenBase.getOid(), null
                            , regionId);
                    // 设置userLimit
                    List<UserInfoBase> limitList = this.openService.getUserLimit(tokenBase.getUserId(), null, regionId);
                    base.setUserLimitList(limitList);
                    tokenBase.setUserBaseInfo(base);
                    this.redisTemplate.opsForValue().set(tokenId, OBJECTMAPPER.writeValueAsString(tokenBase), 60 * 30, TimeUnit.SECONDS);
               } catch (Exception e) {
                    log.error("putUserInfoToToken error!{}", e.getMessage());
                    log.error("putUserInfoToToken error!", e);
               }

          }
     }

     /**
      * 菜单对应url存入redis
      *
      * @param menuFormList 菜单form
      * @param tokenId      登录token
      */
     private void putRedisMenuUrl(List<MenuForm> menuFormList, String tokenId) {
          userService.putRedisMenuUrl(menuFormList, tokenId);
     }
}
