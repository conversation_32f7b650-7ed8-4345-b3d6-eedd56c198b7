package com.goodsogood.ows.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.ApplicationConfigCacheKey;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.ApplicationConfigHsMapper;
import com.goodsogood.ows.mapper.ApplicationConfigMapper;
import com.goodsogood.ows.model.db.ApplicationConfigEntity;
import com.goodsogood.ows.model.db.ApplicationConfigHsEntity;
import com.goodsogood.ows.model.vo.ApplicationConfigForm;
import com.goodsogood.ows.model.vo.Result;
import com.netflix.appinfo.InstanceInfo;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.netflix.eureka.EurekaServiceInstance;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/4/13
 */
@Log4j2
@Service
public class ApplicationConfigService {

    private final Errors errors;
    private final ApplicationConfigMapper applicationConfigMapper;
    private final ApplicationConfigHsMapper applicationConfigHsMapper;
    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final DiscoveryClient discoveryClient;
    private final RestTemplate restTemplate;

    @Autowired
    public ApplicationConfigService(Errors errors,
                                    ApplicationConfigMapper applicationConfigMapper,
                                    ApplicationConfigHsMapper applicationConfigHsMapper,
                                    StringRedisTemplate redisTemplate,
                                    ObjectMapper objectMapper,
                                    DiscoveryClient discoveryClient) {
        this.errors = errors;
        this.applicationConfigMapper = applicationConfigMapper;
        this.applicationConfigHsMapper = applicationConfigHsMapper;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.discoveryClient = discoveryClient;
        this.restTemplate = new RestTemplate();
        restTemplate.setErrorHandler(new ClientExceptionHandler());
    }

    /**
     * 新增应用配置
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByUpload(MultipartFile file) {
        ApplicationConfigEntity configEntity = parseFileGetConfig(file);
        return save(configEntity);
    }

    /**
     * 修改应用配置
     *
     * @param file
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByUpload(MultipartFile file, String commit) {
        ApplicationConfigEntity configEntity = parseFileGetConfig(file);
        configEntity.setCommit(commit);
        return update(configEntity);
    }

    /**
     * 解析file获取config
     *
     * @param file
     * @return
     */
    private ApplicationConfigEntity parseFileGetConfig(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        fileName = StringUtils.substringBeforeLast(fileName, ".");
        String[] fileNameArr = fileName.split("~");
        if (fileNameArr.length != 2) {
            throw new ApiException("配置文件的命名规则必须为{application}~{label}.yml", new Result(errors, 5900, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        String application = fileNameArr[0];
        String label = fileNameArr[1];
        try {
            Yaml yaml = new Yaml();
            Object load = yaml.load(file.getInputStream());
            String content = objectMapper.writeValueAsString(load);
            return new ApplicationConfigEntity(application.toUpperCase(), label.toUpperCase(), content);
        } catch (Exception e) {
            log.error("解析yaml文件出错", e);
            throw new ApiException("解析yaml文件出错", new Result(errors, 5902, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 新增应用配置
     *
     * @param applicationConfigForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveByForm(ApplicationConfigForm applicationConfigForm) {
        ApplicationConfigEntity configEntity = parseFormGetConfig(applicationConfigForm);
        return save(configEntity);
    }

    /**
     * 修改应用配置
     *
     * @param applicationConfigForm
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByForm(ApplicationConfigForm applicationConfigForm) {
        ApplicationConfigEntity configEntity = parseFormGetConfig(applicationConfigForm);
        configEntity.setId(applicationConfigForm.getId());
        configEntity.setCommit(applicationConfigForm.getCommit());
        return update(configEntity);
    }

    /**
     * 解析表单参数获取config
     *
     * @param applicationConfigForm
     * @return
     */
    public ApplicationConfigEntity parseFormGetConfig(ApplicationConfigForm applicationConfigForm) {
        try {
            Yaml yaml = new Yaml();
            Object load = yaml.load(applicationConfigForm.getContent());
            String content = objectMapper.writeValueAsString(load);
            String application = applicationConfigForm.getApplication();
            if (StringUtils.isNotBlank(application)) {
                application = application.toUpperCase();
            }
            String label = applicationConfigForm.getLabel();
            if (StringUtils.isNotBlank(label)) {
                label = label.toUpperCase();
            }
            return new ApplicationConfigEntity(application, label, content);
        } catch (Exception e) {
            log.error("解析yaml文件出错", e);
            throw new ApiException("解析yaml文件出错", new Result(errors, 5902, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 保存配置
     *
     * @param applicationConfigEntity
     * @return
     */
    private boolean save(ApplicationConfigEntity applicationConfigEntity) {
        ApplicationConfigEntity old = getApplicationConfig(applicationConfigEntity.getApplication(), applicationConfigEntity.getLabel());
        if (old != null) {
            log.error("应用配置已存在，application = {}，label = {}", applicationConfigEntity.getApplication(), applicationConfigEntity.getLabel());
            throw new ApiException("应用配置已存在", new Result(errors, 5901, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        applicationConfigEntity.setCreateTime(new Date());
        return applicationConfigMapper.insert(applicationConfigEntity) > 0;
    }

    /**
     * 修改配置
     *
     * @param applicationConfigEntity
     * @return
     */
    private boolean update(ApplicationConfigEntity applicationConfigEntity) {
        ApplicationConfigEntity old = null;
        if (applicationConfigEntity.getId() != null) {
            old = applicationConfigMapper.selectByPrimaryKey(applicationConfigEntity.getId());
        } else {
            if (StringUtils.isNotBlank(applicationConfigEntity.getApplication()) && StringUtils.isNotBlank(applicationConfigEntity.getLabel())) {
//                ApplicationConfigEntity condition = new ApplicationConfigEntity();
//                condition.setApplication(applicationConfigEntity.getApplication());
//                condition.setLabel(applicationConfigEntity.getLabel());
//                Example example =new Example(ApplicationConfigEntity.class);
//                Example.Criteria criteria = example
//                        .createCriteria()
//                        .andEqualTo("label", applicationConfigEntity.getLabel())
//                        .andEqualTo("application", applicationConfigEntity.getApplication());
                old = getApplicationConfig(applicationConfigEntity.getApplication(), applicationConfigEntity.getLabel());
            }
        }
        if (old == null) {
            throw new ApiException("应用配置不存在", new Result(errors, 5904, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        ApplicationConfigEntity newApplicationConfig = new ApplicationConfigEntity();
        newApplicationConfig.setId(old.getId());
        newApplicationConfig.setContent(applicationConfigEntity.getContent());
        if (StringUtils.isBlank(applicationConfigEntity.getCommit())) {
            newApplicationConfig.setCommit("本次更新未填写commit");
        } else {
            newApplicationConfig.setCommit(applicationConfigEntity.getCommit());
        }
        newApplicationConfig.setUpdateTime(new Date());
        int result = applicationConfigMapper.updateByPrimaryKeySelective(newApplicationConfig);
        if (result <= 0) {
            throw new ApiException("应用配置不存在", new Result(errors, 5904, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        ApplicationConfigHsEntity applicationConfigHsEntity = new ApplicationConfigHsEntity(
                old.getId(),
                old.getApplication(),
                old.getLabel(),
                old.getContent(),
                old.getCommit()
        );
        applicationConfigHsEntity.setCreateTime(new Date());
        if (applicationConfigHsMapper.insert(applicationConfigHsEntity) > 0) {
            modifyCleanCache(old.getApplication(), old.getLabel());
//            try {
//                refresh(null);
//            } catch (Exception e) {
//                log.error("通知其他内部应用更新缓存失败", e);
//            }
        }
        return true;
    }

    /**
     * 获取应用配置
     *
     * @param application 应用名
     * @param label       标签
     * @return
     */
    public ApplicationConfigEntity getApplicationConfig(String application, String label) {
        application = application.toUpperCase();
        label = label.toUpperCase();
        String key = String.format(ApplicationConfigCacheKey.CONFIG_KEY, application, label);
        if (redisTemplate.hasKey(key)) {
            String cache = redisTemplate.opsForValue().get(key);
            try {
                return objectMapper.readValue(cache, ApplicationConfigEntity.class);
            } catch (IOException e) {
                log.error("获取缓存失败，key = {}", key, e);
            }
        }
        ApplicationConfigEntity condition = new ApplicationConfigEntity();
        condition.setApplication(application);
        condition.setLabel(label);
        ApplicationConfigEntity applicationConfigEntity = applicationConfigMapper.selectOne(condition);
        if (applicationConfigEntity == null) {
            return null;
        }
        try {
            String cache = objectMapper.writeValueAsString(applicationConfigEntity);
            redisTemplate.opsForValue().set(key, cache, ApplicationConfigCacheKey.EXPIRE, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("写入缓存失败，key = {}", key, e);
        }
        return applicationConfigEntity;
    }

    /**
     * 修改数据后删除缓存
     *
     * @param application
     * @param label
     */
    public void modifyCleanCache(String application, String label) {
        String key = String.format(ApplicationConfigCacheKey.CONFIG_KEY, application, label);
        redisTemplate.delete(key);
    }

    /**
     * 刷新缓存
     *
     * @param application
     * @return
     */
    public Map<String, Map<String, String>> refresh(String application) {
        // key = service，value = [服务实例（ip），调用结果]
        Map<String, Map<String, String>> map = new HashMap<>();
        String msg = "调用结果：[{result}]，消息：[{msg}]";
        List<String> services = discoveryClient.getServices();
        for (String service : services) {
            // 如果传入了application就是指定应用刷新，否则为全量刷新
            if (StringUtils.isNotBlank(application) && !StringUtils.equalsIgnoreCase(application, service)) {
                break;
            }
            Map<String, String> msgMap = new HashMap<>();
            List<ServiceInstance> instances = discoveryClient.getInstances(service);
            if (instances == null || instances.isEmpty()) {
                String emptyMsg = "注册表中没有发现任何可用实例";
                msgMap.put("nothing", emptyMsg);
                map.put(service, msgMap);
                continue;
            }
            for (ServiceInstance instance : instances) {
                EurekaServiceInstance eurekaServiceInstance = (EurekaServiceInstance) instance;
                InstanceInfo instanceInfo = eurekaServiceInstance.getInstanceInfo();
                String ip = instanceInfo.getIPAddr() + ":" + instanceInfo.getPort();
                String url = "http://" + ip + "/app/config/manual/refresh";
                try {
                    ResponseEntity<Boolean> responseEntity = restTemplate.getForEntity(url, Boolean.class);
                    if (responseEntity.getBody()) {
                        String successMsg = msg.replace("{ip}", ip).replace("{result}", "true").replace("{msg}", "调用成功");
                        msgMap.put(ip, successMsg);
                    } else {
                        String failedMsg = msg.replace("{ip}", ip).replace("{result}", "false").replace("{msg}", "调用失败，请查看业务方日志");
                        msgMap.put(ip, failedMsg);
                    }
                } catch (Exception e) {
                    log.error("调用配置刷新接口失败，service：[{}]，ip：[{}]", service, ip, e);
                    String error = msg.replace("{ip}", ip).replace("{result}", "false").replace("{msg}", e.getMessage());
                    msgMap.put(ip, error);
                }
            }
            map.put(service, msgMap);
        }
        return map;
    }
}
