package com.goodsogood.ows.service

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.configuration.DynamicFieldConfig
import com.goodsogood.ows.configuration.MyMongoTemplate
import com.goodsogood.ows.configuration.OrgTypeConfig
import com.goodsogood.ows.configuration.TestOrgConfig
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.BranchHighlightMapper
import com.goodsogood.ows.mapper.FieldValueMapper
import com.goodsogood.ows.mapper.OrgPeriodMapper
import com.goodsogood.ows.mapper.OrganizationMapper
import com.goodsogood.ows.model.db.BranchHighlightEntity
import com.goodsogood.ows.model.db.FieldValueEntity
import com.goodsogood.ows.model.db.OrganizationEntity
import com.goodsogood.ows.model.mongo.Org
import com.goodsogood.ows.model.vo.HomeResultVo
import com.goodsogood.ows.model.vo.ListResultVo
import com.goodsogood.ows.model.vo.MapOrgDetailVo
import com.goodsogood.ows.model.vo.MapResultVo
import com.goodsogood.ows.model.vo.Result
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example

/**
 * 首页服务层
 * <AUTHOR>
 * @createTime 2023年02月08日 10:39:00
 */
@Service
class HomeService @Autowired constructor(
    val errors: Errors,
    val testOrgConfig: TestOrgConfig,
    val orgTypeConfig: OrgTypeConfig,
    val mongoTemplate: MyMongoTemplate,
    val orgPeriodMapper: OrgPeriodMapper,
    val fieldValueMapper: FieldValueMapper,
    val dynamicFieldConfig: DynamicFieldConfig,
    val organizationMapper: OrganizationMapper,
    val branchHighlightMapper: BranchHighlightMapper,
    val simpleApplicationConfigHelper: SimpleApplicationConfigHelper
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    @Value("\${exclude-org-ids}")
    val excludeUnitIds: String = "1294,5,2409,2417,1222,1255"

    val orgDependencyField = "org_dependency"

    fun index(header: HeaderHelper.SysHeader): HomeResultVo {
        val vo = HomeResultVo()
        // 党建地图展示一级党组织
        val firstLevelOrg = firstLevelOrg(header)
        vo.mapResult = firstLevelOrg.map {
            MapResultVo(
                orgId = it.organizationId,
                orgName = it.name,
                orgType = if (orgTypeConfig.branchChild.contains(it.orgTypeChild)) {
                    3
                } else if (orgTypeConfig.generalBranchChild.contains(it.orgTypeChild)) {
                    2
                } else if (orgTypeConfig.communistChild.contains(it.orgTypeChild)) {
                    1
                } else {
                    null
                },
                longitude = it.longitude,
                latitude = it.latitude
            )
        }
        // 党建地图右边列表
        val unitList = this.getUnitList(header)
        val orgMongoList = this.getOrgByMongo()
        val orgUnitList = this.organizationMapper.selectOrgUnitList()
        val orgUnitMap = orgUnitList.groupBy { it.ownerId }
        val orgMap = orgMongoList.groupBy { it.ownerId }
        val listResult = mutableListOf<ListResultVo>()
        unitList.forEach { unit ->
            // 封装基本数据
            val resultVo = ListResultVo(
                unitId = unit.organizationId,
                unitName = unit.name,
                unitShortName = unit.shortName
            )
            // 查询行政单位关联的党组织的所属地（区县级）
            val orgList = orgMap[unit.organizationId]
            if (!orgList.isNullOrEmpty()) {
                val adcodeList = mutableSetOf<String>()
                orgList.forEach { org ->
                    val orgDependencyList =
                        when (val any = org.fields[dynamicFieldConfig.fields?.get(orgDependencyField)]) {
                            is String? -> any?.split(",")
                            is List<*> -> any.map { it.toString() }
                            else -> mutableListOf()
                        } ?: mutableListOf()
                    if (orgDependencyList.size == 2) {
                        adcodeList.add(orgDependencyList[1])
                    }
                }
                resultVo.adcode = adcodeList
            }
            // 查询行政单位关联的顶级组织
            val firstOrgList = orgUnitMap[unit.organizationId]
            if (!firstOrgList.isNullOrEmpty()) {
                val orgTypeChild = firstOrgList[0].orgTypeChild
                if (orgTypeConfig.branchChild.contains(orgTypeChild)) {
                    resultVo.orgType = 3
                } else if (orgTypeConfig.generalBranchChild.contains(orgTypeChild)) {
                    resultVo.orgType = 2
                } else if (orgTypeConfig.communistChild.contains(orgTypeChild)) {
                    resultVo.orgType = 1
                }
            }
            listResult.add(resultVo)
        }
        vo.listResult = listResult.groupBy { it.orgType }.toMutableMap()
        vo.listResult.remove(0)
        return vo
    }

    fun mapOrgDetail(adcode: String? = null, unitId: Long? = null): List<MapOrgDetailVo> {
        if (adcode.isNullOrBlank() && unitId == null) {
            throw ApiException("传参错误", Result<Any>(errors, 5641, HttpStatus.INTERNAL_SERVER_ERROR.value(), "传参错误"))
        }
        val resultVo = mutableListOf<MapOrgDetailVo>()
        // 查询组织列表
        val orgList = if (unitId != null) {
            this.getOrgListByOwnerId(unitId)
        } else if (!adcode.isNullOrBlank()) {
            this.getOrgListByAdcode(adcode, dynamicFieldConfig.fields?.get(orgDependencyField))
        } else {
            mutableListOf()
        }
        if (orgList.isNotEmpty()) {
            val orgIds = orgList.map { it.organizationId }
            // 组织届次
            val secretaryMap =
                this.orgPeriodMapper.selectOrgSecretaryByOrgs(orgIds).filterNotNull().groupBy { it.orgId }
            // 组织风采
            val highlightMap = this.branchHighlightMapper.selectHighlightListByOrg(orgIds).groupBy { it.orgId }
            orgList.map { org ->
                val secretary = secretaryMap[org.organizationId]
                val highlights = highlightMap[org.organizationId]
                resultVo.add(
                    MapOrgDetailVo(
                        orgId = org.organizationId,
                        orgName = org.name,
                        orgSecretary = secretary?.get(0)?.name,
                        orgPeriod = secretary?.get(0)?.time,
                        orgStylePic = highlights?.get(0)?.url,
                        longitude = org.longitude,
                        latitude = org.latitude
                    )
                )
            }
        }
        return resultVo
    }

    private fun firstLevelOrg(header: HeaderHelper.SysHeader): List<OrganizationEntity> {
        val orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(header.regionId)
        val ex = Example(OrganizationEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("parentId", orgData.orgId)
        criteria.andEqualTo("status", Constants.YES)
        criteria.andEqualTo("orgType", Constants.ORG_TYPE_COMMUNIST)
        criteria.andIsNotNull("orgAddress")
        criteria.andNotIn("organizationId", testOrgConfig.testOrgIds(header.regionId))
        return this.organizationMapper.selectByExample(ex)
    }

    private fun getUnitList(header: HeaderHelper.SysHeader): List<OrganizationEntity> {
        val corpData = this.simpleApplicationConfigHelper.getCorpByRegionId(header.regionId)
        val ex = Example(OrganizationEntity::class.java)
        val criteria = ex.createCriteria()
        criteria.andEqualTo("parentId", corpData.orgId)
        criteria.andEqualTo("status", Constants.YES)
        criteria.andNotIn("organizationId", excludeUnitIds.split(","))
        return this.organizationMapper.selectByExample(ex)
    }

    private fun getOrgListByOwnerId(ownerId: Long): List<OrganizationEntity> {
        val ex = Example(OrganizationEntity::class.java)
        ex.createCriteria()
            .andEqualTo("status", Constants.YES)
            .andIn("orgTypeChild", this.orgTypeConfig.basicChild)
            .andEqualTo("ownerId", ownerId)
        return this.organizationMapper.selectByExample(ex)
    }

    private fun getOrgListByAdcode(adcode: String, fieldName: String?): List<OrganizationEntity> {
        val ex = Example(FieldValueEntity::class.java)
        ex.createCriteria()
            .andEqualTo("sourceType", 2)
            .andEqualTo("fieldName", fieldName)
            .andLike("fieldValue", "%$adcode%")
        val entities = this.fieldValueMapper.selectByExample(ex)
        return if (entities.isNotEmpty()) {
            val example = Example(OrganizationEntity::class.java)
            example.createCriteria()
                .andIn("organizationId", entities.map { it.sourceId })
                .andEqualTo("status", Constants.YES)
                .andIn("orgTypeChild", this.orgTypeConfig.basicChild)
            this.organizationMapper.selectByExample(example)
        } else {
            return mutableListOf()
        }
    }

    private fun getOrgByMongo(): List<Org> {
        val criteria = Criteria()
        criteria.andOperator(
            Criteria.where("ownerId").exists(true),
            Criteria.where("status").`is`(Constants.YES)
        )
        val query = Query(criteria)
        return this.mongoTemplate.find(query, Org::class.java)
    }
}