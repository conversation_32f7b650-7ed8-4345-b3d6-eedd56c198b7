package com.goodsogood.ows.service;

import cn.hutool.core.convert.Convert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.common.idUntils.CertNumberUtil;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.UserFieldEnum;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.*;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.mongo.Org;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.TagBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static java.lang.Long.valueOf;

/**
 * 组织用户服务类
 *
 * <AUTHOR> tc
 * @Date 2018/6/12
 */
@Service
@Log4j2
@EnableCaching
public class OrgUserService {

    /**
     * 用户中心服务器
     **/
    @Value("${tog-services.file-center}")
    @NotBlank
    private String serverFileCenter;

    /**
     * 组织管理员菜单
     */
    @Value("${org-role.managerMenuIds}")
    @NotBlank
    private String managerMenuIds;

    /**
     * 普通用户菜单
     */
    @Value("${org-role.userMenuIds}")
    @NotBlank
    private String userMenuIds;

    /**
     * 管理员角色类型
     */
    @Value("${role-type}")
    @NotBlank
    private String roleType;

    @Value("${temp-path}")
    @NotBlank
    private String tempPath;

    private static final List<String> CHOOSE_USER_FIELDS = Arrays.asList("userId", "orgId", "orgIds", "tree", "corpId", "page", "pagesize");

    private final MessagesConfig messagesConfig;
    private final ObjectMapper OBJECTMAPPER = new ObjectMapper();
    private final Errors errors;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final DepartmentMapper departmentMapper;
    private final UserDepartmentMapper userDepartmentMapper;
    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final UserRoleMapper userRoleMapper;
    private final StringRedisTemplate redisTemplate;
    private final RestTemplate restTemplate;
    private final OrgUserCommonService orgUserCommonService;
    private final BroadcastService broadcastService;
    private final CallbackService callbackService;
    private final UserOrgCacheCallService userOrgCacheCallService;
    private final RoleMenuMapper roleMenuMapper;
    private final OrganizationMapper organizationMapper;
    private final TagMapper tagMapper;
    private final UserThirdMapper userThirdMapper;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final SaasConfig saasConfig;
    private final VolunteerUserSyncService volunteerUserSyncService;
    private final UserOptionConfig userOptionConfig;
    private final SnapshotAsyncService snapshotAsyncService;
    private final OptionService optionService;
    private final UserMongoService userMongoService;
    private final FormService formService;
    private final OrgMongoService orgMongoService;
    private final JoinCommunityService joinCommunityService;
    private final EcsService ecsService;
    private final EcsConfig ecsConfig;
    private final OrgGroupMemberService orgGroupMemberService;
    private final UserHighlightService userHighlightService;
    private final DevelopConfig developConfig;
    private final LeaderMapper leaderMapper;
    private final PartyGroupUserMapper partyGroupUserMapper;
    private final TestOrgConfig testOrgConfig;
    private final SynchService synchService;
    private final MembershipMapper membershipMapper;

    @Autowired
    public OrgUserService(MessagesConfig messagesConfig, Errors errors, UserOrgAndCorpMapper userOrgAndCorpMapper,
                          DepartmentMapper departmentMapper, UserDepartmentMapper userDepartmentMapper,
                          UserMapper userMapper, RoleMapper roleMapper, UserRoleMapper userRoleMapper,
                          StringRedisTemplate redisTemplate, RestTemplate restTemplate,
                          OrgUserCommonService orgUserCommonService, BroadcastService broadcastService,
                          CallbackService callbackService, RoleMenuMapper roleMenuMapper, UserOrgCacheCallService userOrgCacheCallService,
                          OrganizationMapper organizationMapper, TagMapper tagMapper, UserThirdMapper userThirdMapper, SimpleApplicationConfigHelper simpleApplicationConfigHelper,
                          SaasConfig saasConfig, VolunteerUserSyncService volunteerUserSyncService, UserOptionConfig userOptionConfig, SnapshotAsyncService snapshotAsyncService,
                          OptionService optionService, UserMongoService userMongoService, FormService formService, OrgMongoService orgMongoService, JoinCommunityService joinCommunityService, EcsService ecsService, EcsConfig ecsConfig, OrgGroupMemberService orgGroupMemberService, UserHighlightService userHighlightService, DevelopConfig developConfig, LeaderMapper leaderMapper, PartyGroupUserMapper partyGroupUserMapper, TestOrgConfig testOrgConfig,
                          SynchService synchService, MembershipMapper membershipMapper) {
        this.messagesConfig = messagesConfig;
        this.errors = errors;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.departmentMapper = departmentMapper;
        this.userDepartmentMapper = userDepartmentMapper;
        this.userMapper = userMapper;
        this.roleMapper = roleMapper;
        this.userRoleMapper = userRoleMapper;
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
        this.orgUserCommonService = orgUserCommonService;
        this.broadcastService = broadcastService;
        this.callbackService = callbackService;
        this.userOrgCacheCallService = userOrgCacheCallService;
        this.roleMenuMapper = roleMenuMapper;
        this.organizationMapper = organizationMapper;
        this.tagMapper = tagMapper;
        this.userThirdMapper = userThirdMapper;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.saasConfig = saasConfig;
        this.volunteerUserSyncService = volunteerUserSyncService;
        this.userOptionConfig = userOptionConfig;
        this.snapshotAsyncService = snapshotAsyncService;
        this.optionService = optionService;
        this.userMongoService = userMongoService;
        this.formService = formService;
        this.orgMongoService = orgMongoService;
        this.joinCommunityService = joinCommunityService;
        this.ecsService = ecsService;
        this.ecsConfig = ecsConfig;
        this.orgGroupMemberService = orgGroupMemberService;
        this.userHighlightService = userHighlightService;
        this.developConfig = developConfig;
        this.leaderMapper = leaderMapper;
        this.partyGroupUserMapper = partyGroupUserMapper;
        this.testOrgConfig = testOrgConfig;
        this.synchService = synchService;
        this.membershipMapper = membershipMapper;
    }

    /**
     * 查询
     *
     * @param form       form
     * @param pageNumber pageNumber
     * @return page
     */
    public Page<UserOrgResultForm> getOrgUserList(OrgUserQueryForm form, PageNumber pageNumber, Long regionId) {
        //设置分页属性
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        return PageHelper.startPage(p, r).doSelectPage(() -> userOrgAndCorpMapper.findOrgUserList(form, regionId));
    }

    /**
     * 导出EXCEL查询
     *
     * @param form form
     * @return list
     */
    public List<UserOrgResultForm> getOrgUserListForExport(OrgUserQueryForm form, Long regionId) {
        return userOrgAndCorpMapper.findOrgUserList(form, regionId);
    }

    /**
     * 新增组织人员
     *
     * @param user       user
     * @param header     header
     * @param sourceType 1-手动新增 2-批量导入新增
     * @return long
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addOrgUser(OrgUserForm user, int sourceType, HeaderHelper.SysHeader header) {
        Long userId = user.getUserId();
        try {
            Long updUserId = header.getUserId();
            Long regionId = header.getRegionId();
            log.debug("新增党员-参数userForm[{}],userId[{}],sourceType[{}],regionId[{}]",
                    user, updUserId, sourceType, regionId);
            // 获取当前时间
            Date curTime = DateTime.now().toDate();
            UserEntity entity = new UserEntity();
            if (Objects.nonNull(userId)) {
                entity = this.userMapper.selectByPrimaryKey(user.getUserId());
            }
            entity.setName(user.getName());
            entity.setPhoneSecret(user.getPhoneSecret());
            entity.setPhone(user.getPhone());
            entity.setIsVerify(Constants.IS_VERIFY_YES);
            entity.setIsLose((null != user.getIsLose() && user.getIsLose() == Constants.IS_LOSE_YES) ?
                    Constants.IS_LOSE_YES :
                    Constants.IS_LOSE_NO);
            Integer gender = user.getGender();//设置性别
            // 工号 2022-04-01 烟草工号不允许页面修改
            //entity.setJobNumber(user.getJobNumber());
            // 身份证
            if (StringUtils.isNotBlank(user.getCertNumber())) {
                user.setCertType(Objects.nonNull(user.getCertType()) ? user.getCertType() : Constants.CERT_NUMBER_IDENTITY);
                if (Constants.CERT_NUMBER_IDENTITY == user.getCertType()) {
                    // 身份证号解密
                    String cn = NumEncryptUtils.decrypt(user.getCertNumber(), user.getCertNumberSecret());
                    // 如果性别为空，通过身份证获取
                    if (gender == null) {
                        gender = Utils.getGender(cn);
                    }
                    // 根据身份证计算年龄，截止当前年
                    entity.setAge(Utils.getAge(user.getCertNumber(), user.getCertNumberSecret()));
                }
                entity.setCertType(user.getCertType());
                entity.setCertNumberSecret(user.getCertNumberSecret());
                entity.setCertNumber(user.getCertNumber());
            }
            entity.setGender(gender);
            entity.setLastChangeUser(updUserId);
            // 民族、户籍类型、学历、籍贯、政治面貌
            entity.setEthnic(user.getEthnic());
            entity.setCensusType(user.getCensusType());
            entity.setEducation(user.getEducation());
            entity.setNativeProvince(user.getNativeProvince());
            entity.setNativeCity(user.getNativeCity());
            entity.setPoliticalType(user.getPoliticalType());
            // 入党日期
            entity.setJoiningTime(user.getJoiningTime());
            // 新增党员方式
            entity.setJoinType(Objects.isNull(user.getJoinType()) ? Constants.JOIN_TYPE_DEP : user.getJoinType());
            // 出生日期
            entity.setBirthday(user.getBirthday());
            // 住址
            entity.setAddress(user.getAddress());
            entity.setBirthPlace(user.getBirthPlace());
            entity.setIsFlow(user.getIsFlow());
            // 照片
            entity.setHeadUrl(user.getHeadUrl());
            entity.setSequence(user.getSequence());
            // 是否离退休
            List<String> retireCode = new ArrayList<>(Arrays.asList(this.userOptionConfig.getRetireCode().split(",")));
            Long positionCode = user.getPositionCode();
            if (positionCode != null && retireCode.contains(String.valueOf(positionCode))) {
                entity.setIsRetire(Constants.IS_RETIRE_YES);
            } else {
                entity.setIsRetire(Constants.IS_RETIRE_NO);
            }
            String position = user.getPosition();
            if (StringUtils.isBlank(position) && Objects.nonNull(positionCode)) {
                position = this.optionService.getValueByCode(String.valueOf(Constants.POSITION_CODE), positionCode);
            }
            entity.setPosition(position);
            entity.setStatus(Constants.STATUS_YES);
            if (null != userId) {
                // 2021-11-08 sj 新增党组织关系时，去掉人员和游客组织的关系
                if (Constants.IS_VERIFY_YES == entity.getIsVisitor()) {
                    // 新增游客组织关系数据
                    Example example = new Example(OrganizationEntity.class);
                    example.createCriteria().andEqualTo("orgTypeChild", Constants.ORG_TYPE_VISITOR_CHILD)
                            .andEqualTo("parentId", header.getOid())
                            .andEqualTo("status", Constants.YES);
                    OrganizationEntity visitorOrg = this.organizationMapper.selectOneByExample(example);
                    if (Objects.nonNull(visitorOrg) && Objects.nonNull(visitorOrg.getOrganizationId())) {
                        this.userOrgAndCorpMapper.deleteByUserId(Constants.DEP_TYPE_ORG, visitorOrg.getOrganizationId(), userId);
                        this.userRoleMapper.deleteAllByUserAndRole(userId, visitorOrg.getOrganizationId());
                    }
                }

                entity.setUserId(user.getUserId());
                entity.setUpdateTime(curTime);
                entity.setIsVisitor(Constants.IS_VISITOR_NO);
                // 修改
                this.userMapper.updateByPrimaryKeySelective(entity);
                userId = user.getUserId();
            } else {
                // 是否生成第三方数据ID
                if (this.saasConfig.getGenerateThirdId()) {
                    entity.setId(this.saasConfig.getThirdIdPrefix() +
                            UUID.randomUUID().toString().replaceAll("-", ""));
                }
                entity.setPassword(MD5Helper.getMD5(Constants.PASSWORD));
                entity.setCreateTime(curTime);
                entity.setUpdateTime(curTime);
                entity.setIsVisitor(Constants.STATUS_NO);
                // 新增用户调用ECS服务获取HMAC值
                if (this.ecsConfig.getRun()) {
                    String hmac = this.ecsService.hmacData(Constants.ENCODE_TYPE_HEX, Utils.toJson(entity));
                    entity.setHmac(hmac);
                }
                this.userMapper.insertSelective(entity);
                userId = entity.getUserId();
            }

            // 默认挂载在组织下root部门
            List<Long> depIdList = new ArrayList<>(1);
            DepartmentForm form = this.departmentMapper.getDepPid(null, user.getOrgId(), Constants.DEP_TYPE_ORG, Constants.IS_ROOT_YES);
            if (form != null && form.getDepartmentId() != null) {
                depIdList.add(form.getDepartmentId());
                this.userDepartmentMapper.batchInsert(depIdList, entity.getUserId(), Constants.DUTY_GENERAL);
            }

            // 构建用户组织关系数据
            UserOrgAndCorpEntity userOrgEntity = new UserOrgAndCorpEntity();
            userOrgEntity.setUserId(entity.getUserId());
            userOrgEntity.setOrganizationId(user.getOrgId());
            userOrgEntity.setScore(0);
            userOrgEntity.setJobGrade(user.getJobGrade());
            userOrgEntity.setCommunist(user.getCommunist());
            userOrgEntity.setYouthLeague(user.getYouthLeague());
            userOrgEntity.setUnionMember(user.getUnionMember());
            userOrgEntity.setWomenLeague(user.getWomenLeague());
            userOrgEntity.setLastChangeUser(updUserId);
            userOrgEntity.setRegionId(regionId);
            // 进入支部日期
            if (StringUtils.isNotBlank(user.getJoinPartyTime())) {
                userOrgEntity.setJoinPartyTime(Utils.stringToLocalDateTime(user.getJoinPartyTime(), "yyyy-MM-dd"));
            }
            userOrgEntity.setJoinPartyType(Objects.isNull(user.getJoinPartyType()) ? user.getJoinType() : user.getJoinPartyType());
            // 进入支部类型 发展党员时，进入支部类型默认为市内转入
            if (this.userOptionConfig.getJoinPartyType().equals(user.getJoinPartyType())) {
                userOrgEntity.setJoinPartyType(Integer.parseInt(this.userOptionConfig.getJoinType()));
            }
            userOrgEntity.setUpdateTime(curTime);
            userOrgEntity.setOldOrgName(user.getOldOrgName());
            userOrgEntity.setPositionCode(positionCode);
            userOrgEntity.setPosition(position);
            // 转正日期
            if (user.getJoiningPositiveTime() != null) {
                userOrgEntity.setJoiningTime(Utils.stringToLocalDateTime(user.getJoiningPositiveTime(), "yyyy-MM-dd"));
            }

            // 新增用户,组织关系转入该支部
            userOrgEntity.setTagId(this.orgUserCommonService.initUserOrgTag(userOrgEntity.getTagId(),
                    user.getOrgId(), entity.getUserId(), regionId, positionCode));
            userOrgEntity.setCreateTime(curTime);
            // 判断组织和用户是不是存在关系
            final int countByUserAndOrg = this.userOrgAndCorpMapper.selectCountByUserAndOrg(entity.getUserId(), user.getOrgId());
            if (countByUserAndOrg > 0) {
                throw new ApiException("新增人员失败，人员和组织已存在关系", new Result<>(errors, Global.Errors.SYSTEM_UNKNOWN, HttpStatus.NOT_FOUND.value()));
            }
            // 校验用户关系是否属于其他党组织
            Example cntEx = new Example(UserOrgAndCorpEntity.class);
            cntEx.createCriteria().andEqualTo("userId", entity.getUserId())
                    .andNotEqualTo("organizationId", user.getOrgId())
                    .andEqualTo("isEmployee", Constants.YES)
                    .andEqualTo("regionId", regionId);
            int cont = this.userOrgAndCorpMapper.selectCountByExample(cntEx);
            // 2021-04-15 sj 加入逻辑组织类型非党组织类型时，is_employee强制=0
            final OrganizationEntity orgEntity = this.organizationMapper.selectByPrimaryKey(user.getOrgId());
            if (!(Objects.equals(orgEntity.getOrgType(), Constants.ORG_TYPE_COMMUNIST))) {
                userOrgEntity.setIsEmployee(Constants.IS_EMPLOYEE_NO);
            } else if (cont > 0) {
                throw new ApiException("新增人员失败，已存在组织关系", new Result<>(errors, Global.Errors.SYSTEM_UNKNOWN, HttpStatus.NOT_FOUND.value()));
            } else {
                userOrgEntity.setIsEmployee(Constants.IS_EMPLOYEE);
                // 组织用户数量+1
                Example ex = new Example(OrganizationEntity.class);
                ex.createCriteria().andEqualTo("organizationId", user.getOrgId());
                OrganizationEntity numEntity = this.organizationMapper.selectOneByExample(ex);
                numEntity.setUserNum((numEntity.getUserNum() == null ? 0 : numEntity.getUserNum()) + 1);
                numEntity.setUpdateTime(curTime);
                numEntity.setLastChangeUser(updUserId);
                this.organizationMapper.updateByPrimaryKeySelective(numEntity);
            }
            this.userOrgAndCorpMapper.insertSelective(userOrgEntity);

            log.debug("新增党员-新增用户组织关系完成 userOrgEntity[{}]", userOrgEntity);
            // 根据regionId查询顶级党组织ID
            Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
            // 新增用户，默认权限-查询默认权限,区县默认权限改为挂载到顶级党组织ID上
            RoleEntity re = this.roleMapper.getRoleByType(Constants.ROLE_TYPE_DEFAULT, Constants.DEP_TYPE_ORG,
                    orgData.getOrgId(), Constants.MENU_BELONG_PC, regionId);
            if (re != null && re.getRoleId() != null) {
                // 新增用户设置权限
                UserRoleEntity ure = new UserRoleEntity();
                ure.setRoleId(re.getRoleId());
                ure.setUserId(entity.getUserId());
                ure.setOrganizationId(user.getOrgId());
                // 调用ECS服务获取HMAC值
                if (this.ecsConfig.getRun()) {
                    String hmac = this.ecsService.hmacData(Constants.ENCODE_TYPE_HEX, Utils.toJson(ure));
                    ure.setHmac(hmac);
                }
                this.userRoleMapper.insertSelective(ure);
                log.debug("新增组织人员时添加PC端默认权限成功 userId -> {}", entity.getUserId());
            }
            // 添加微信默认权限
            RoleEntity exRole = this.roleMapper.getRoleByType(Constants.ROLE_TYPE_DEFAULT, Constants.DEP_TYPE_ORG,
                    orgData.getOrgId(), Constants.MENU_BELONG_WX, regionId);
            if (exRole != null && exRole.getRoleId() != null) {
                UserRoleEntity wxEntity = new UserRoleEntity();
                wxEntity.setUserId(entity.getUserId());
                wxEntity.setRoleId(exRole.getRoleId());
                wxEntity.setOrganizationId(user.getOrgId());
                // 获取HMAC值
                if (this.ecsConfig.getRun()) {
                    String hmac = this.ecsService.hmacData(Constants.ENCODE_TYPE_HEX, Utils.toJson(wxEntity));
                    wxEntity.setHmac(hmac);
                }
                this.userRoleMapper.insertSelective(wxEntity);
                log.debug("新增组织人员时添加微信端默认权限成功 userId -> {}", entity.getUserId());
            }
            // 党员发展过程
            if (Arrays.asList(1, 17).contains(entity.getPoliticalType()) && Objects.nonNull(userOrgEntity.getJoiningTime())) { // 正式党员
                this.addHighlight(userId, Utils.localDateTimeToString(userOrgEntity.getJoiningTime()), Constants.USER_HIGHLIGHT_OFFICIAL, header);
            } else if (Arrays.asList(5, 18).contains(entity.getPoliticalType()) && StringUtils.isNotBlank(entity.getJoiningTime())) { // 预备党员
                this.addHighlight(userId, entity.getJoiningTime(), Constants.USER_HIGHLIGHT_PREP, header);
            }
            if (userOrgEntity.isEmployee == 1) {
                this.addHighlight(userId, DateUtils.toFormatDate(new Date(), "yyyy-MM-dd"), "加入" + orgEntity.getName(), header);
            }

            // 更新组织人员数
            this.orgUserCommonService.putOrgUserCount(user.getOrgId());
            entity = this.userMapper.selectByPrimaryKey(userId);
            UserOrgAndCorpEntity uoc = this.userOrgAndCorpMapper.findUserOrgByExample(Constants.IS_EMPLOYEE,
                    header.getOrgType(), entity.getUserId(), null, header.getRegionId());
            this.snapshotAsyncService.buildUserSnapshot(entity, null, uoc, null, curTime, orgEntity,
                    "新增");
            // 如果所属序列不为空，新增序列变更流水
            if (Objects.nonNull(user.getSequence())) {
                this.synchService.dealUserTag(userId, regionId, null, null, user.getSequence(), null);
            }
            // 调用学习系统-党组织非第二党支部
            if (this.getOrgTypeById(user.getOrgId()) &&
                    !(Constants.ORG_TYPE_CHILD_BRANCH_TYPE.equals(orgEntity.getOrgTypeChild()))) {
                // 调用用户组织信息变化回调接口
                UserChangeForm userChangeForm = this.commonUserChangeForm(entity.getUserId(), user.getOrgId(),
                        null, curTime, 2, regionId);
                userChangeForm.setIsRetire(entity.getIsRetire());
                this.callbackService.userOrgChangeCallback(userChangeForm, regionId);
                // 调用学习系统订阅接口
                this.broadcastService.commonBroadcast(entity, user.getOrgId(), String.valueOf(user.getOrgId()),
                        "1", regionId);
            }
            // 党小组
            if (Constants.ORG_TYPE_SMALL_GROUP.equals(orgEntity.getOrgTypeChild())) {
                int code = this.orgGroupMemberService.addOrgGroupMember(orgEntity, userId, header);
                if (code != 0) {
                    throw new ApiException("新增党小组成员出错", new Result<>(errors, Global.Errors.SYSTEM_UNKNOWN, HttpStatus.NOT_FOUND.value()));
                }
            }
        } catch (ApiException apiEx) {
            log.error("抛出错误", apiEx);
            throw apiEx;
        } catch (Exception e) {
            log.error("系统异常，新增组织人员失败! phone {},orgId {}", user.getPhone(), user.getOrgId(), e);
            return null;
        }

        return userId;
    }

    /**
     * 构造
     *
     * @param userId   userId
     * @param newOrgId newOrgId
     * @param oldOrgId oldOrgId
     * @param time     time
     * @param status   2-新用户 1-用户转换组织 3-用户删除组织
     * @return UserChangeForm
     */
    public UserChangeForm commonUserChangeForm(Long userId, Long newOrgId, Long oldOrgId,
                                               Date time, int status, Long regionId) {
        UserEntity ue;
        UserChangeForm resultForm = new UserChangeForm();
        resultForm.setUserId(userId);
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(Global.CACHE_USER_ID_PREFIX + userId))) {
            ue = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_USER_ID_PREFIX + userId), UserEntity.class);
        } else {
            Example example = new Example(UserEntity.class);
            example.createCriteria().andEqualTo("userId", userId)
                    .andEqualTo("status", Constants.YES);
            ue = this.userMapper.selectOneByExample(example);
        }
        if (null != ue && null != ue.getUserId()) {
            resultForm.setUserName(ue.getName());
            resultForm.setCertNumber(ue.getCertNumber());
            resultForm.setCertNumberSecret(ue.getCertNumberSecret());
            resultForm.setIsRetire((null != ue.getIsRetire() && Constants.IS_RETIRE_YES == ue.getIsRetire()) ?
                    Constants.IS_RETIRE_YES :
                    Constants.IS_RETIRE_NO);
            resultForm.setPhone(ue.getPhone());
            resultForm.setPhoneSecret(ue.getPhoneSecret());
        }
        resultForm.setOldOrgId(oldOrgId);
        resultForm.setStatus(status);
        resultForm.setTime(time);
        if (null != newOrgId) {
            OrganizationEntity newOrg = this.orgUserCommonService.getOrgById(newOrgId);
            resultForm.setNewOrgId(newOrgId);
            resultForm.setNewOrgName(null == newOrg ? "" : newOrg.getName());
            OrganizationEntity org = getParentIdByOrgId(newOrgId, regionId);
            if (null != org && null != org.getOrganizationId()) {
                resultForm.setNewOwnerId(org.getOrganizationId());
                resultForm.setNewOwnerName(org.getName());
            }
        }
        // 如果是新增和修改组织，则根据newOrgId查询标签列表，若是删除，则使用oldOrgId查询标签列表
        Example tuEx = new Example(UserOrgAndCorpEntity.class);
        Example.Criteria criteria = tuEx.createCriteria();
        criteria.andEqualTo("userId", userId)
                .andEqualTo("regionId", regionId)
                .andEqualTo("isEmployee", Constants.IS_EMPLOYEE);
        if (1 == status || 2 == status) {
            criteria.andEqualTo("organizationId", newOrgId);
        } else if (3 == status) {
            criteria.andEqualTo("organizationId", oldOrgId);
        }
        tuEx.setOrderByClause("update_time DESC");
        UserOrgAndCorpEntity userOrgAndCorpEntity = this.userOrgAndCorpMapper.selectOneByExample(tuEx);
        if (null != userOrgAndCorpEntity && !StringUtils.isBlank(userOrgAndCorpEntity.getTagId())) {
            List<TagBase> baseList = new ArrayList<>();
            for (String tagId : userOrgAndCorpEntity.getTagId().split(",")) {
                TagEntity tag = this.tagMapper.selectByPrimaryKey(valueOf(tagId));
                if (null != tag) {
                    TagBase base = new TagBase();
                    base.setTagType(tag.getTagType());
                    base.setTagId(tag.getTagId());
                    base.setTagName(tag.getName());
                    base.setOrgId(userOrgAndCorpEntity.getOrganizationId());
                    baseList.add(base);
                }
            }
            resultForm.setTagList(baseList);
        }

        return resultForm;
    }

    /**
     * 查询
     *
     * @param orgId orgId
     * @return org
     */
    private OrganizationEntity getParentIdByOrgId(long orgId, Long regionId) {
        // 根据regionID查询区县顶级组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        String orgKey = Global.CACHE_ORG_ID_PREFIX + orgId;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgKey))) {
            OrganizationEntity org = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
            // 返回上级组织为顶级党组织ID
            if (Objects.requireNonNull(org).getOrganizationId().equals(orgData.getOrgId())
                    || org.getParentId().equals(orgData.getOrgId())) {
                return org;
            } else {
                return getParentIdByOrgId(org.getParentId(), regionId);
            }
        } else {
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria().andEqualTo("organizationId", orgId)
                    .andEqualTo("status", Constants.YES);
            OrganizationEntity parentEntity = this.organizationMapper.selectOneByExample(example);
            if (parentEntity == null) {
                return null;
            } else {
                // 返回上级组织为顶级党组织ID
                if (parentEntity.getOrganizationId().equals(orgData.getOrgId())
                        || parentEntity.getParentId().equals(orgData.getOrgId())) {
                    return parentEntity;
                } else {
                    return getParentIdByOrgId(parentEntity.getParentId(), regionId);
                }
            }
        }
    }

    /**
     * 根据orgId查询orgType,若是 党组织，则返回 TRUE
     *
     * @param orgId orgId
     * @return Boolean
     */
    public boolean getOrgTypeById(Long orgId) {
        String orgRedisKey = Global.CACHE_ORG_ID_PREFIX + orgId;
        Integer orgType = null;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgRedisKey))) {
            OrganizationEntity org = Utils.fromJson(this.redisTemplate.opsForValue().get(orgRedisKey), OrganizationEntity.class);
            if (Objects.nonNull(org)) {
                orgType = org.getOrgType();
            }
        }
        if (Objects.isNull(orgType)) {
            Example orgEx = new Example(OrganizationEntity.class);
            orgEx.createCriteria().andEqualTo("organizationId", orgId)
                    .andEqualTo("status", Constants.YES);
            OrganizationEntity org = this.organizationMapper.selectOneByExample(orgEx);
            orgType = null == org ? null : org.getOrgType();
        }
        if (Constants.ORG_TYPE_COMMUNIST.equals(orgType)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
     * 修改用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean editOrgUser(OrgUserForm user, HeaderHelper.SysHeader header) {
        try {
            Date curTime = DateTime.now().toDate();
            UserEntity entity = this.userMapper.selectByPrimaryKey(user.getUserId());
            // 原来所属序列
            Integer sequenceBefore = entity.getSequence();
            entity.setName(user.getName());
            if (StringUtils.isNotBlank(user.getPhone())) {
                entity.setPhone(user.getPhone());
            }
            if (StringUtils.isNotBlank(user.getPhoneSecret())) {
                entity.setPhoneSecret(user.getPhoneSecret());
            }
            entity.setCertType(user.getCertType());
            if (StringUtils.isNotBlank(user.getCertNumber())) {
                entity.setCertNumber(user.getCertNumber());
            }
            if (StringUtils.isNotBlank(user.getCertNumberSecret())) {
                entity.setCertNumberSecret(user.getCertNumberSecret());
            }
            entity.setGender(user.getGender());
            entity.setLastChangeUser(header.getUserId());
            //民族、户籍类型、学历、籍贯、政治面貌
            entity.setEthnic(user.getEthnic());
            entity.setCensusType(user.getCensusType());
            entity.setEducation(user.getEducation());
            entity.setBirthday(user.getBirthday());
            entity.setNativeProvince(user.getNativeProvince());
            entity.setNativeCity(user.getNativeCity());
            Integer newPoliticalType = user.getPoliticalType();
            entity.setPoliticalType(newPoliticalType);
            entity.setJoiningTime(user.getJoiningTime());
            entity.setIsLose((Objects.nonNull(user.getIsLose()) && Constants.IS_LOSE_YES == user.getIsLose()) ? Constants.IS_LOSE_YES : Constants.IS_LOSE_NO);
            entity.setPosition(user.getPosition());
            //entity.setJobNumber(user.getJobNumber());
            // 当党员修改为非党员, 记录下原用户数据
            UserEntity oldUser = this.userMapper.selectByPrimaryKey(user.getUserId());
            // 获取标签列表
            List<TagBase> callBackTagList = new ArrayList<>();
            // 设置flag,判断标签是否修改, 是否需要调用党费系统
            boolean flag = false;
            // 标签所在组织
            Long orgByTag = 0L;
            // 党费标签费回调状态
            int ppmdStatus = 1;
            // 原政治面貌
            Integer oldPoliticalType = oldUser.getPoliticalType();
            // 现工作岗位
            Long positionCode = user.getPositionCode();
            // 原用户组织信息
            int isEmployee = header.getOrgType().equals(Constants.ORG_TYPE_COMMUNIST) ? Constants.IS_EMPLOYEE : 0;
            UserOrgAndCorpEntity uocEntities = this.userOrgAndCorpMapper.findUserOrgByExample(isEmployee,
                    header.getOrgType(), user.getUserId(), user.getOrgId(), header.getRegionId());
            if (uocEntities == null) {
                return false;
            }
            // 党员政治面貌
            List<String> politicalTypes = Arrays.asList(this.userOptionConfig.getPoliticalCode().split(","));
            // 离退休
            List<String> retireCodes = new ArrayList<>(Arrays.asList(this.userOptionConfig.getRetireCode().split(",")));
            // 从非离退休 -> 退休，回调党费先删除，再新增
            boolean isChangePosition = false;
            // 从离退休 -> 非离退休，回调党费先删除，在新增
            boolean isChangeRetire = false;
            if (null != oldPoliticalType && (politicalTypes.contains(String.valueOf(oldPoliticalType)))) {
                // 原政治面貌为 党员 -> 非党员
                if (newPoliticalType != null && (!politicalTypes.contains(String.valueOf(newPoliticalType)))) {
                    log.debug("编辑党员-用户[{}]组织关系数据[{}]", user.getUserId(), uocEntities);
                    this.buildCallbackTagList(callBackTagList, uocEntities);
                    orgByTag = uocEntities.getOrganizationId();
                    flag = true;
                    ppmdStatus = 3;
                } else {
                    // 是否离退休
                    log.debug("编辑党员-用户[{}]组织关系数据[{}]", user.getUserId(), uocEntities);
                    // 党员政治面貌不变，校验工作岗位，如果是沙区，则离退休党员也需要交纳党费
                    Long oldPositionCode = uocEntities.getPositionCode();
                    // 工作岗位从 非离退休 -> 离退休
                    if (!retireCodes.contains(String.valueOf(oldPositionCode)) &&
                            retireCodes.contains(String.valueOf(positionCode))) {
                        // 如果是离退休党员需要交纳党费，则需要先删除，再新增
                        if (this.saasConfig.getRegions().contains(header.getRegionId())) {
                            isChangePosition = true;
                        } else {
                            // 非沙区标签变化
                            flag = true;
                        }
                        this.buildRetireTagList(uocEntities, callBackTagList, user.getOrgId(), isChangePosition);
                        entity.setIsRetire(Constants.IS_RETIRE_YES);
                        orgByTag = uocEntities.getOrganizationId();
                        // 党费 3-删除
                        ppmdStatus = 3;
                    }
                    // 工作岗位从 离退休 -> 非离退休
                    if (retireCodes.contains(String.valueOf(oldPositionCode)) &&
                            !retireCodes.contains(String.valueOf(positionCode))) {
                        this.buildNoRetireTagList(uocEntities, callBackTagList, user.getOrgId(), header.getRegionId());
                        entity.setIsRetire(Constants.IS_RETIRE_NO);
                        orgByTag = uocEntities.getOrganizationId();
                        isChangeRetire = true;
                        flag = false;
                        // 党费 2-新增
                        ppmdStatus = 2;
                    }
                }
            } else {
                // 原政治面貌为 非党员 -> 党员
                if (null != oldPoliticalType && (!politicalTypes.contains(String.valueOf(oldPoliticalType)) &&
                        (newPoliticalType != null && (politicalTypes.contains(String.valueOf(newPoliticalType)))))) {
                    boolean isRetire = retireCodes.contains(String.valueOf(user.getPositionCode()));
                    // 校验工作岗位 如果是离退休党员，并且是沙区
                    if (!isRetire || this.saasConfig.getRegions().contains(header.getRegionId())) {
                        // 获取党费标签
                        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(header.getRegionId());
                        TagEntity payTag = this.getTagByExample(orgData.getOrgId(), header.getRegionId()
                        );
                        AtomicBoolean isPpmdTag = new AtomicBoolean(false);
                        String tagLength = uocEntities.getTagId();
                        List<String> tags = new ArrayList<>();
                        if (StringUtils.isNotBlank(tagLength)) {
                            tags = Arrays.asList(tagLength.split(","));
                            for (int i = tags.size() - 1; i >= 0; i--) {
                                String tagId = tags.get(i);
                                int tagType = this.tagMapper.getTagById(Long.parseLong(tagId));
                                if (tagType == Constants.TAG_TYPE_PAY) {
                                    isPpmdTag.set(true);
                                }
                            }
                            // 不存在党费标签，则添加党费标签
                            if (!isPpmdTag.get()) {
                                tags.add(String.valueOf(payTag.getTagId()));
                                TagBase base = this.getTagBase(user.getOrgId(), payTag.getTagId());
                                callBackTagList.add(base);
                            }
                        } else {
                            tags.add(String.valueOf(payTag.getTagId()));
                            TagBase base = this.getTagBase(user.getOrgId(), payTag.getTagId());
                            callBackTagList.add(base);
                            orgByTag = uocEntities.getOrganizationId();
                            flag = true;
                            // 党费 2-新增
                            ppmdStatus = 2;
                        }
                        String tagsLenth = String.join(",", tags);
                        if (tagLength == null || tagLength.length() != tagsLenth.length()) {
                            this.userOrgAndCorpMapper.updateTagIdByUserAndOrg(uocEntities.getUserId(),
                                    uocEntities.getOrganizationId(), tagsLenth);
                            orgByTag = uocEntities.getOrganizationId();
                            flag = true;
                            // 党费 2-新增
                            ppmdStatus = 2;
                        }
                    }
                }
            }
            entity.setUpdateTime(curTime);
            entity.setIsVerify(Constants.IS_VERIFY_YES);
            entity.setIsEdit(Constants.IS_EDIT_NO);
            // 住址
            entity.setAddress(user.getAddress());
            entity.setBirthPlace(user.getBirthPlace());
            entity.setIsFlow(user.getIsFlow());
            // 照片
            entity.setHeadUrl(user.getHeadUrl());
            entity.setSequence(user.getSequence());
            // 更新hmac值
            if (this.ecsConfig.getRun()) {
                String hmac = this.ecsService.hmacData(Constants.ENCODE_TYPE_HEX, Utils.toJson(entity));
                entity.setHmac(hmac);
            }
            // 修改组织人员基本信息
            this.userMapper.updateByPrimaryKey(entity);

            // 更新组织用户补充信息
            UserOrgAndCorpEntity ue = new UserOrgAndCorpEntity();
            ue.setOldOrgName(user.getOldOrgName());
            ue.setOrganizationId(user.getOrgId());
            ue.setJobGrade(user.getJobGrade());
            ue.setPosition(user.getPosition());
            ue.setCommunist(user.getCommunist());
            ue.setYouthLeague(user.getYouthLeague());
            ue.setUnionMember(user.getUnionMember());
            ue.setWomenLeague(user.getWomenLeague());
            ue.setUserId(user.getUserId());
            ue.setLastChangeUser(header.getUserId());
            ue.setUpdateTime(new Date());
            ue.setPositionCode(positionCode);
            String position = user.getPosition();
            if (StringUtils.isBlank(position) && Objects.nonNull(positionCode)) {
                position = this.optionService.getValueByCode(String.valueOf(Constants.POSITION_CODE), positionCode);
            }
            ue.setPosition(position);
            ue.setJoinPartyType(user.getJoinPartyType());
            ue.setOldOrgName(user.getOldOrgName());
            if (StringUtils.isNotBlank(user.getJoinPartyTime())) {
                ue.setJoinPartyTime(Utils.stringToLocalDateTime(user.getJoinPartyTime(), "yyyy-MM-dd"));
            }
            if (StringUtils.isNotBlank(user.getJoiningPositiveTime())) {
                ue.setJoiningTime(Utils.stringToLocalDateTime(user.getJoiningPositiveTime(), "yyyy-MM-dd"));
            }
            this.userOrgAndCorpMapper.updateByUserIdAndOrgId(ue);

            // 党员发展过程
            if (Arrays.asList(5, 18).contains(oldUser.getPoliticalType()) &&
                    Arrays.asList(1, 17).contains(entity.getPoliticalType()) && Objects.nonNull(ue.getJoiningTime())) { // 正式党员
                this.addHighlight(user.getUserId(), Utils.localDateTimeToString(ue.getJoiningTime()), Constants.USER_HIGHLIGHT_OFFICIAL, header);
            } else if (!Objects.equals(oldUser.getPoliticalType(), entity.getPoliticalType()) &&
                    (!Arrays.asList(1, 17).contains(entity.getPoliticalType())) &&
                    Arrays.asList(5, 18).contains(entity.getPoliticalType()) && Objects.nonNull(entity.getJoiningTime())) { // 预备党员
                this.addHighlight(user.getUserId(), entity.getJoiningTime(), Constants.USER_HIGHLIGHT_PREP, header);
            } /*else if (16 == entity.getPoliticalType()) { // 发展党员
                this.addHighlight(userId, null, "成为发展党员", header);
            } else if (15 == entity.getPoliticalType()) { // 积极分子
                this.addHighlight(userId, null, "成为入党积极分子", header);
            }*/

            try {
                // 根据用户ID查询用户信息
                UserEntity userEntity = this.userMapper.selectByPrimaryKey(user.getUserId());
                // 调用用户组织信息变化回调接口
                UserChangeForm userChangeForm = this.commonUserChangeForm(user.getUserId(), orgByTag,
                        orgByTag, new Date(), ppmdStatus, header.getRegionId());
                // 判断标签是否修改, 是否需要调用党费系统
                if (flag) {
                    // 封装标签列表
                    if (CollectionUtils.isEmpty(userChangeForm.getTagList())) {
                        userChangeForm.setTagList(callBackTagList);
                    }
                    this.callbackService.userOrgChangeCallback(userChangeForm, header.getRegionId());
                }
                // 如果从非退休变成退休，则党费回调先删除，再新增
                if (isChangePosition) {
                    // 删除原党费信息
                    userChangeForm.setIsRetire(Constants.IS_RETIRE_NO);
                    userChangeForm.setStatus(3);
                    this.callbackService.userOrgChangeCallback(userChangeForm, header.getRegionId());
                    UserChangeForm addForm = new UserChangeForm();
                    BeanUtils.copyProperties(userChangeForm, addForm);
                    // 再新增党费信息
                    addForm.setIsRetire(Constants.IS_RETIRE_YES);
                    addForm.setStatus(2);
                    this.callbackService.userOrgChangeCallback(addForm, header.getRegionId());
                }
                // 从离退休 -> 非离退休，党费先删除，再新增
                if (isChangeRetire) {
                    // 删除原党费信息
                    userChangeForm.setIsRetire(Constants.IS_RETIRE_YES);
                    userChangeForm.setStatus(3);
                    this.callbackService.userOrgChangeCallback(userChangeForm, header.getRegionId());
                    UserChangeForm addForm = new UserChangeForm();
                    BeanUtils.copyProperties(userChangeForm, addForm);
                    // 再新增党费信息
                    addForm.setIsRetire(Constants.IS_RETIRE_NO);
                    addForm.setStatus(2);
                    this.callbackService.userOrgChangeCallback(addForm, header.getRegionId());
                }
                OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(user.getOrgId());
                // 处理快照历史数据
                UserOrgAndCorpEntity uoc = this.userOrgAndCorpMapper.findUserOrgByExample(Constants.IS_EMPLOYEE,
                        header.getOrgType(), userEntity.getUserId(), null, header.getRegionId());
                log.debug("快照数据参数uoc[{}],oldUoc[{}]", uoc, uocEntities);
                // 处理所属序列变更流水，所属序列有变化
                if (Objects.nonNull(sequenceBefore) && !sequenceBefore.equals(userEntity.getSequence())) {
                    this.synchService.dealUserTag(userEntity.getUserId(), header.getRegionId(), null, null, userEntity.getSequence(), sequenceBefore);
                }
                if (Objects.nonNull(uoc)) {
                    this.snapshotAsyncService.buildUserSnapshot(userEntity, oldUser, uoc, uocEntities, curTime, org,
                            "修改");
                }
            } catch (Exception e) {
                log.error("编辑党员-修改组织用户时调用学习系统异常:" + e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("编辑党员-系统异常，修改组织人员信息失败:" + e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 处理用户过后的后续操作
     *
     * @param userId       用户ID
     * @param orgId        组织ID
     * @param dynamicField 动态字段
     * @param dealType     操作类型 1-新增， 2-修改
     * @param header       请求头
     */
    public void afterDealWithUser(Long userId, Long orgId, Map<String, Object> dynamicField,
                                  int dealType, boolean isBatch, HeaderHelper.SysHeader header) {
        try {
            // 刷新缓存
            UserEntity entity = this.userMapper.selectByPrimaryKey(userId);
            // 刷新组织缓存
            OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(orgId);
            //保存到MongoDB
            this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), entity, 1);
            this.userOrgCacheCallService.flushOrgInfo(org, 1);
            this.userMongoService.conversionUserSync(userId, dynamicField, isBatch, header);
            this.orgMongoService.conversionOrg(org, null, header);
            // 处理志愿者数据
            this.callbackService.sycInitVolunteer(entity, header.getRegionId(), Constants.STATUS_YES, null);
            // 如果是第二党支部新增用户，则同步成为第二党支部志愿服务队志愿者
            if (dealType == 1) {
                if (Constants.ORG_TYPE_CHILD_BRANCH_TYPE.equals(org.getOrgTypeChild())) {
                    log.debug("新增第二党支部处理志愿者orgId[{}],user[{}]", orgId, userId);
                    this.volunteerUserSyncService.syncUserOfVolunteer(orgId, header.getRegionId());
                }
            }
            // 调用学习系统订阅接口
            if (this.getOrgTypeById(orgId) &&
                    !(Constants.ORG_TYPE_CHILD_BRANCH_TYPE.equals(org.getOrgTypeChild()))) {
                // 调用学习系统订阅接口
                this.broadcastService.commonBroadcast(entity, orgId, Convert.toStr(orgId), Convert.toStr(dealType)
                        , header.getRegionId());
            }
        } catch (Exception e) {
            log.error("处理完用户数据后续操作报错 -> userId:[{}] orgId:[{}], 异常 -> ", userId, orgId, e);
        }
    }

    /**
     * 党员 -> 非党员
     *
     * @param callBackTagList tagList
     * @param uocEntity       uocEntity
     */
    private void buildCallbackTagList(List<TagBase> callBackTagList, UserOrgAndCorpEntity uocEntity) {
        // 判断组织里面存在多标签
        String eTagId = uocEntity.getTagId();
        if (StringUtils.isNotBlank(eTagId)) {
            List<String> list = new ArrayList<>(Arrays.asList(eTagId.split(",")));
            for (int i = list.size() - 1; i >= 0; i--) {
                // 判断标签是否是党费标签, 若是，删除党费标签
                String s = list.get(i);
                int tagType = this.tagMapper.getTagById(Long.parseLong(s));
                if (tagType == Constants.TAG_TYPE_PAY) {
                    list.remove(s);
                    // 封装回调标签列表，党费回调状态为 3-删除
                    TagBase tagBase = this.getTagBase(uocEntity.getOrganizationId(), Long.parseLong(s));
                    callBackTagList.add(tagBase);
                }
            }
            // 如果标签长度发生变化，则需要更新数据库
            String newTag = String.join(",", list);
            if (eTagId.length() != newTag.length()) {
                this.userOrgAndCorpMapper.updateTagIdByUserAndOrg(uocEntity.getUserId(), uocEntity.getOrganizationId(), newTag);
            }
        }
    }

    /**
     * 工作岗位从 非离退休 -> 离退休
     *
     * @param uoc             uoc
     * @param callBackTagList callbackTagList
     * @param orgId           orgId
     */
    private void buildRetireTagList(UserOrgAndCorpEntity uoc, List<TagBase> callBackTagList, Long orgId, boolean flag) {
        List<String> tags = new ArrayList<>(Collections.singletonList(uoc.getTagId()));
        // 如果工作岗位从 非离退休 -> 离退休
        if (uoc.getTagId() != null) {
            for (int i = tags.size() - 1; i >= 0; i--) {
                String tagId = tags.get(i);
                int tagType = this.tagMapper.getTagById(Long.parseLong(tagId));
                if (!flag && tagType == Constants.TAG_TYPE_PAY) {
                    tags.remove(tagId);
                }
                // 封装回调标签列表 党费回调状态为 3-删除
                TagBase tagBase = this.getTagBase(orgId, Long.parseLong(tagId));
                callBackTagList.add(tagBase);
            }
            // 如果标签长度发生变化，则需要更新数据库
            String newTag = String.join(",", tags);
            if (uoc.getTagId().length() != newTag.length()) {
                this.userOrgAndCorpMapper.updateTagIdByUserAndOrg(uoc.getUserId(),
                        uoc.getOrganizationId(), newTag);
            }
        }
    }

    /**
     * 工作岗位从 离退休 -> 非离退休
     *
     * @param uoc             uoc
     * @param callBackTagList callBackTagList
     * @param orgId           orgId
     */
    private void buildNoRetireTagList(UserOrgAndCorpEntity uoc, List<TagBase> callBackTagList, Long orgId, Long regionId) {
        List<String> tags = new ArrayList<>();
        // 如果工作岗位从 离退休 -> 非离退休
        boolean isPpmdTag = false;
        // 根据regionID查询区县顶级组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        // 查询党费标签
        TagEntity tagEntity = this.getTagByExample(orgData.getOrgId(), regionId);
        if (uoc.getTagId() != null) {
            tags = new ArrayList<>(Collections.singletonList(uoc.getTagId()));
            for (int i = tags.size() - 1; i >= 0; i--) {
                String tagId = tags.get(i);
                int tagType = this.tagMapper.getTagById(Long.parseLong(tagId));
                if (tagType == Constants.TAG_TYPE_PAY) {
                    isPpmdTag = true;
                }
            }
            // 如果不存在党费标签
            if (!isPpmdTag) {
                tags.add(String.valueOf(tagEntity.getTagId()));
                // 封装回调标签列表 党费回调状态为 2-新增
                tags.add(String.valueOf(tagEntity.getTagId()));
                TagBase tagBase = new TagBase();
                tagBase.setTagId(tagEntity.getTagId());
                tagBase.setTagType(tagEntity.getTagType());
                tagBase.setTagName(tagEntity.getName());
                tagBase.setOrgId(orgId);
                callBackTagList.add(tagBase);
            }
        } else {
            // 没有任何标签,添加党费标签
            tags.add(String.valueOf(tagEntity.getTagId()));
        }
        // 如果标签长度发生变化，则需要更新数据库
        String newTag = String.join(",", tags);
        if (uoc.getTagId() == null || uoc.getTagId().length() != newTag.length()) {
            this.userOrgAndCorpMapper.updateTagIdByUserAndOrg(uoc.getUserId(),
                    uoc.getOrganizationId(), newTag);
        }
    }

    /**
     * 根据条件查询标签
     *
     * @param orgId    orgId
     * @param regionId regionId
     */
    private TagEntity getTagByExample(Long orgId, Long regionId) {
        Example example = new Example(TagEntity.class);
        example.createCriteria().andEqualTo("status", Constants.STATUS_YES)
                .andEqualTo("organizationId", orgId)
                .andEqualTo("tagType", Constants.TAG_TYPE_PAY)
                .andEqualTo("regionId", regionId);
        return this.tagMapper.selectOneByExample(example);
    }

    /**
     * @param orgId orgId
     * @param tagId tagId
     * @return TagBase
     */
    private TagBase getTagBase(Long orgId, Long tagId) {
        // 封装回调标签列表，党费回调状态为 3-删除
        TagBase tagBase = new TagBase();
        TagEntity tagEntity = this.tagMapper.selectByPrimaryKey(tagId);
        if (tagEntity != null) {
            tagBase.setTagId(tagEntity.getTagId());
            tagBase.setTagType(tagEntity.getTagType());
            tagBase.setTagName(tagEntity.getName());
            tagBase.setOrgId(orgId);
        }
        return tagBase;
    }


    /**
     * 删除党员
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean delOrgUser(Long userId, Long orgId, Long regionId, Integer leaveType,
                              HeaderHelper.SysHeader header, Integer type) {
        try {
            // 原用户信息
            Date curTime = DateTime.now().toDate();
            UserEntity oldUser = this.userMapper.selectByPrimaryKey(userId);
            //组织信息
            OrganizationEntity organizationEntity = organizationMapper.selectByPrimaryKey(orgId);
            // 查询用户所在党委
            List<Long> idList = new ArrayList<>();
            idList.add(userId);
            Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
            List<UserInfoBase> partyOrgList = this.userOrgAndCorpMapper.findUserPartyByList(Constants.YES, Constants.YES, idList, regionId,
                    orgData.getOrgId());
            // 该用户组织关系是否在该组织
            Example example = new Example(UserOrgAndCorpEntity.class);
            example.createCriteria().andEqualTo("userId", userId)
                    .andEqualTo("organizationId", orgId)
                    .andEqualTo("isEmployee", Constants.STATUS_YES)
                    .andEqualTo("regionId", regionId);
            int count = this.userOrgAndCorpMapper.selectCountByExample(example);
            // 组织关系不属于该组织
            UserChangeForm form = new UserChangeForm();
            if (count > 0) {
                // 构造用户组织信息变化调用回调接口参数
                form = this.commonUserChangeForm(userId, null, orgId, new Date(), 3, regionId);
            }
            // 删除组织用户补充信息
            this.userOrgAndCorpMapper.deleteByUserId(Constants.DEP_TYPE_ORG, orgId, userId);
            // 解除用户在组织里的部门绑定关系
            List<Long> orgIdList = new ArrayList<>(1);
            orgIdList.add(orgId);
            List<Long> depIdList = departmentMapper.selectDepIdsByOrgIds(orgIdList);
            if (!CollectionUtils.isEmpty(depIdList)) {
                this.userDepartmentMapper.batchDelete(depIdList, userId);
            }
            // 解除用户在组织里的权限绑定关系
            this.userRoleMapper.deleteByOrgIdAndUserId(orgId, userId, null, null);
            // 校验用户是否还存在组织关系,用户存在多区县情况,则不需要添加regionId过滤
            Example ex = new Example(UserOrgAndCorpEntity.class);
            ex.createCriteria().andEqualTo("userId", userId)
                    .andEqualTo("regionId", regionId);
            int cnt = this.userOrgAndCorpMapper.selectCountByExample(ex);
            UserEntity userEntity = this.userMapper.selectByPrimaryKey(userId);
            if (cnt <= 0) {
                // 更新用户状态为2，并且清空手机号码，身份证号码，如果是组织关系转接，则不清空手机号码
                if (this.userOptionConfig.getLeaveTypes().contains(leaveType)) {
                    UserEntity updateEntity = new UserEntity();
                    updateEntity.setStatus(Constants.STATUS_NO);
                    updateEntity.setUserId(userId);
                    updateEntity.setLeaveType(leaveType);
                    updateEntity.setUpdateTime(curTime);
                    this.userMapper.updateByPrimaryKeySelective(updateEntity);
                } else {
                    this.userMapper.updateUserById(userId, leaveType);
                }
                // 删除用户openId
                Example thirdEx = new Example(UserThirdEntity.class);
                thirdEx.createCriteria().andEqualTo("userId", userId)
                        .andEqualTo("oId", orgData.getOrgId());
                UserThirdEntity ute = this.userThirdMapper.selectOneByExample(thirdEx);
                this.userThirdMapper.deleteByExample(thirdEx);
                if (ute != null) {
                    // 删除openID缓存
                    this.userOrgCacheCallService.flushOpenIdSingle(ute.getThToken());
                }
                // 如果是预备党员删除，则发展党员终止发展
                if (Objects.nonNull(oldUser.getPoliticalType())
                        && Constants.COMMUNIST_PROBATIONARY_PARTY_MEMBER == oldUser.getPoliticalType() &&
                        type == 1) {
                    RecusalForm recusalForm = new RecusalForm();
                    recusalForm.setUserId(userId);
                    recusalForm.setOrgId(orgId);
                    recusalForm.setCancelRemark(this.developConfig.getDeleteUser());
                    log.debug("删除人员-同步发展党员删除用户 param:[{}] header:[{}] type:[{}]", recusalForm, header, type);
                    this.userOrgCacheCallService.asyncDevelop(recusalForm, header, type);
                }
                // 领导班子设置删除
                leaderMapper.deleteLeaderByUserId(userId, header.getUserId());
                // 党组人员删除
                partyGroupUserMapper.delPartyGroupUserByUserId(userId, header.getUserId());
            } else {
                // 刷新缓存
                this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
            }
            OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(orgId);
            if (count > 0) {
                // 删除缓存
                this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 2);
                // 更新组织人员数
                this.orgUserCommonService.putOrgUserCount(orgId);
                // 回调各模块
                this.callbackService.userOrgChangeCallback(form, regionId);
                // 回调学习系统
                this.broadcastService.commonBroadcast(userEntity, orgId, String.valueOf(orgId), "3", regionId);
                // 处理快照历史数据
                UserEntity updateUser = new UserEntity();
                BeanUtils.copyProperties(oldUser, updateUser);
                updateUser.setStatus(Constants.STATUS_NO);
                UserOrgAndCorpEntity uoc = this.userOrgAndCorpMapper.findUserOrgByExample(Constants.IS_EMPLOYEE,
                        org.getOrgType(), updateUser.getUserId(), org.getOrganizationId(), org.getRegionId());
                this.snapshotAsyncService.buildUserSnapshot(updateUser, oldUser, uoc, uoc, curTime, org, "删除");
                // 处理志愿者数据
                List<String> orgs = new ArrayList<>();
                if (!CollectionUtils.isEmpty(partyOrgList)) {
                    UserInfoBase base = partyOrgList.get(0);
                    orgs.add(String.valueOf(base.getPartyOrgId()));
                    orgs.add(base.getPartyOrgName());
                }
                this.callbackService.sycInitVolunteer(userEntity, regionId, Constants.STATUS_NO, orgs);
                this.userMongoService.remove(userId);
            }
            // 刷新组织缓存
            this.userOrgCacheCallService.flushOrgInfo(orgId, 1);
            // 删除MongoDB数据
            this.userMongoService.conversionUserSync(userId, null, false, header);
            // 删除进小区人员
            joinCommunityService.deleteByUserOrg(header.getRegionId(), orgId, userId, header.getUserId());
            // 党小组
            if (Constants.ORG_TYPE_SMALL_GROUP.equals(org.getOrgTypeChild())) {
                int code = this.orgGroupMemberService.deleteOrgGroupMember(userId, orgId);
                if (code != 0) {
                    throw new ApiException("删除党小组成员出错", new Result<>(errors, Global.Errors.SYSTEM_UNKNOWN, HttpStatus.NOT_FOUND.value()));
                }
            }
            //将删除人员信息添加到党籍管理中
            MembershipEntity membershipEntity = new MembershipEntity();
            membershipEntity.setUserId(userId);
            membershipEntity.setOrgId(orgId);
            membershipEntity.setName(oldUser.getName());
            membershipEntity.setPoliticalType(oldUser.getPoliticalType());
            membershipEntity.setLeaveType(leaveType);
            membershipEntity.setOrgName(organizationEntity.getName());
            membershipEntity.setOrgLevel(organizationEntity.getOrgLevel());
            membershipEntity.setCreateUser(header.getUserId());
            membershipEntity.setCreateTime(LocalDateTime.now());
            membershipEntity.setJobNumber(oldUser.getJobNumber());
            membershipEntity.setPosition(oldUser.getPosition());
            membershipEntity.setEmail(oldUser.getEmail());
            membershipEntity.setGender(oldUser.getGender());
            membershipEntity.setAge(oldUser.getAge());
            membershipEntity.setBirthday(oldUser.getBirthday());
            membershipEntity.setCensusType(oldUser.getCensusType());
            membershipEntity.setNationality(oldUser.getNationality());
            membershipEntity.setEntryDate(oldUser.getEntryDate());
            membershipEntity.setMarriage(oldUser.getMarriage());
            membershipEntity.setEducation(oldUser.getEducation());
            membershipEntity.setEthnic(oldUser.getEthnic());
            membershipEntity.setNativeProvince(oldUser.getNativeProvince());
            membershipEntity.setNativeCity(oldUser.getNativeCity());
            membershipEntity.setJoiningTime(oldUser.getJoiningTime());
            membershipEntity.setAddress(oldUser.getAddress());
            membershipEntity.setTitle(oldUser.getTitle());
            membershipEntity.setHandSign(oldUser.getHandSign());
            membershipEntity.setHeadUrl(oldUser.getHeadUrl());
            membershipEntity.setSequence(oldUser.getSequence());
            membershipEntity.setIsFlow(oldUser.getIsFlow());
            membershipEntity.setCertType(oldUser.getCertType());
            addMembership(membershipEntity);
        } catch (Exception e) {
            log.error("系统异常，删除组织人员信息失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    /**
     * @param oid oid
     * @return string
     */
    public String getNameByOid(Long oid) {
        return this.userOrgAndCorpMapper.getName(Constants.DEP_TYPE_ORG, oid);
    }

    /**
     * 导出excel
     *
     * @param orgUserList orgUserList
     * @param cName       cName
     * @return Workbook
     */
    public Workbook creatExcel(List<UserOrgResultForm> orgUserList, String cName) {
        //生成
        Map<String, List<List<String>>> data = new HashMap<>(1, 1);
        List<List<String>> itemList = new ArrayList<>(orgUserList.size());

        List<String> header = new ArrayList<>();
        header.add("姓名");
        header.add("电话");
        header.add("证件号");

        itemList.add(header);

        orgUserList.forEach((item) -> {
            List<String> row = new ArrayList<>(8);
            row.add(item.getName());
            row.add(item.getPhone());
            row.add(item.getCertNumber());
            itemList.add(row);
        });
        data.put(cName, itemList);

        String fileName = cName + "." + ExcelUtils.XLS_SUFFIX;
        //生成excel文件
        Workbook wb = null;
        try {
            wb = ExcelUtils.generateExcel(fileName, data);
        } catch (Exception e) {
            log.error("生成组织人员excel失败! errorMasage {}", e.getMessage(), e);
        }
        return wb;
    }


    /**
     * 上传组织员工数据
     */
    public ResponseEntity<Result<?>> uploadOrgUser(String fileId, String path, OpenService openService) {
        log.debug("<上传组织员工数据> --> 开始 fileId:{}", fileId);
        Long stime1 = System.currentTimeMillis();

        String resultKey = UUID.randomUUID().toString().toLowerCase();
        //调用异步处理
        Long stime = System.currentTimeMillis();
        orgUserCommonService.asynDisposeExcel(resultKey, fileId, path, this.redisTemplate, serverFileCenter, restTemplate, openService, errors);
        Long etime = System.currentTimeMillis();
        log.debug("<上传组织员工数据>  异步处理用时  --> fileId:{}   耗时:{}", fileId, etime - stime);

        OrgUserExportAsynReForm re = new OrgUserExportAsynReForm(resultKey);

        Long etime1 = System.currentTimeMillis();
        log.debug("<上传组织员工数据> --> 结束 fileId:{} 耗时{}", fileId, etime1 - stime1);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    /**
     * @Author: tc
     * @Description 获取异步批量上传组织人员结果
     * @Date 17:24 2018/7/13
     */
    public ResponseEntity<Result<?>> getOperKey(String resultKey) {
        try {
            OrgUserExportReForm ouerf;
            if (Boolean.TRUE.equals(redisTemplate.hasKey(resultKey))) {
                ouerf = OBJECTMAPPER.readValue(redisTemplate.opsForValue().get(resultKey), new TypeReference<OrgUserExportReForm>() {
                });
            } else {
                ouerf = new OrgUserExportReForm(0, "解析中", "", 0);
            }
            return new ResponseEntity<>(new Result<>(ouerf, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("<获取异步批量上传组织人员结果> -> errorMessage:{}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(errors, 193, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * @param operKey  operKey
     * @param page     page
     * @param pageSize pageSize
     * @return ResponseEntity
     */
    public ResponseEntity<Result<?>> findImportData(String operKey, Integer page, Integer pageSize) {

        List<OrgUserExportForm> dataList = new ArrayList<>();
        try {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(operKey))) {
                //去redis获取缓存的数据
                List<OrgUserForm> oufList = OBJECTMAPPER.readValue(redisTemplate.opsForValue().get(operKey), new TypeReference<List<OrgUserForm>>() {
                });
                //计算分页
                int startIndex = (page - 1) * pageSize;
                int endIndex = Math.min(page * pageSize, oufList.size());
                oufList.subList(startIndex, endIndex).forEach(ouf -> {
                    OrgUserExportForm ouef = new OrgUserExportForm();
                    BeanUtils.copyProperties(ouf, ouef);
                    ouef.setErrorMsg("");
                    ouef.setStateTxt("准备导入");
                    dataList.add(ouef);
                });
            } else {
                log.info("上传的数据已失效，请重新上传组织会员数据！");
                return new ResponseEntity<>(new Result<>(errors, 175, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("<查询组织人员待导入信息失败> -> errorMessage:{}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(errors, 189, HttpStatus.OK.value()), HttpStatus.OK);
        }

        //组装返回结果
        Integer pageCount = dataList.size() % pageSize != 0 ? dataList.size() / pageSize + 1 : dataList.size() / pageSize;
        OrgUserImportDataReForm re = new OrgUserImportDataReForm(operKey, dataList.size(), page, pageSize, pageCount, dataList);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }


    /**
     * 导入组织员工，并返回导入失败的信息
     *
     * @param operKey operKey
     * @return ResponseEntity
     */
    public ResponseEntity<Result<?>> importOrgUser(String operKey, Long orgId, HeaderHelper.SysHeader header
            , OpenService ignoredOpenService, Integer page, Integer pageSize) {
        log.debug("<批量导入组织人员> --> 开始 operKey:{} orgId:{} page:{} pageSize:{}", operKey, orgId, page, pageSize);
        Long stime4 = System.currentTimeMillis();
        List<OrgUserExportErrorForm> list = new ArrayList<>(5);//失败数据集合
        int succeedCount;
        int pageCount;
        //完成标记
        int finish = 0;
        try {
            if (Boolean.TRUE.equals(redisTemplate.hasKey(operKey))) {
                //去redis获取缓存的数据
                log.debug("<批量导入组织人员> 去redis获取缓存的数据 --> 开始");
                Long stime1 = System.currentTimeMillis();
                List<OrgUserForm> oufList = OBJECTMAPPER.readValue(redisTemplate.opsForValue().get(operKey), new TypeReference<List<OrgUserForm>>() {
                });
                Long etime1 = System.currentTimeMillis();
                log.debug("<批量导入组织人员> 去redis获取缓存的数据 --> 结束   耗时:{}ms", etime1 - stime1);

                //计算分页
                int startIndex = (page - 1) * pageSize;
                int endIndex = Math.min(page * pageSize, oufList.size());
                List<OrgUserForm> oufPageList = oufList.subList(startIndex, endIndex);

                //自定义ForkJoinPool线程池
                ForkJoinPool myPool = new ForkJoinPool(30);
                myPool.submit(() ->
                        oufPageList.parallelStream().forEach(ouf -> {
                            log.debug("<批量导入组织人员> 处理缓存里的数据 name:{} phone:{} --> 开始", ouf.getName(), ouf.getPhone());
                            Long stime2 = System.currentTimeMillis();
                            try {
                                String dataDisposeFlag;//数据处理标记
                                String phn = ouf.getPhone();
                                String cnr = ouf.getCertNumber();
                                ouf.setOrgId(orgId);//设置组织编号
                                ouf.setCertType(Constants.CERT_NUMBER_IDENTITY);//导入的默认使用的身份证号码

                                if (null == phn) {//手机号码为空
                                    log.debug("<批量导入组织人员> 手机号码为空！name {} phone {} certNumber{}", ouf.getName(), phn, cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportPhone()));
                                    return;
                                }
                                if (null == ouf.getName()) {//用户姓名为空
                                    log.debug("<批量导入组织人员> 用户姓名为空！name {} phone {} certNumber{}", ouf.getName(), phn, cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportName()));
                                    return;
                                }
                                if (null == cnr) {//证件号码为空
                                    log.debug("<批量导入组织人员> 证件号码为空！name {} phone {} certNumber{}", ouf.getName(), phn, cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportCertNumber()));
                                    return;
                                }

                                // 改为在上传解析时检查实名认证  2018.07.18
                                if (ouf.getCheckRealName() != 1) {
                                    log.debug("<批量导入组织人员> 身份证实名认证失败 -> name:{}  certNumber:{}", ouf.getName(), cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, ouf.getCheckRealNameMsg()));
                                    return;
                                }

                                // 姓名文本长度限制
                                if (ouf.getName().length() > 30) {//用户姓名超过30个字
                                    log.debug("<批量导入组织人员> 用户姓名超过30个字！name {} phone {} certNumber{}", ouf.getName(), phn, cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportNameLength()));
                                    return;
                                }

                                // 验证通过姓名、身份证、电话是否能获取到已存在用户
                                String p = NumEncryptUtils.encrypt(phn, 3);//手机号加密
                                // 证件号加密，兼容12371数据加密方式
                                List<String> certNumbers = new ArrayList<>();
                                String c1 = NumEncryptUtils.encrypt(cnr, Constants.CERT_NUMBER_IDENTITY);
                                String c2 = CertNumberUtil.encrypt(cnr);//12371身份证加密方法
                                certNumbers.add(c1);
                                certNumbers.add(c2);
                                UserForm uf = userMapper.exportCheck(ouf.getName(), null, certNumbers);
                                int existPhone = userMapper.checkPhone(p, null);//判断手机号是否已存在于用户表
                                if (uf != null && uf.getUserId() != null) {
                                    UserForm ufp = userMapper.exportCheck(ouf.getName(), p, certNumbers);
                                    if (ufp != null && ufp.getUserId() != null) {//身份证、姓名、手机号在数据库中有对应数据匹配，且数据完全匹配，则直接导入的数据覆盖原有数据信息
                                        // 判断用户是否已存在于要导入的组织中
                                        UserForm user = userMapper.addCheckPhone(p, Constants.DEP_TYPE_ORG, ouf.getOrgId());
                                        if (user != null && user.getUserId() != null) {//导入的组织人员存在于当前组织，更新基本信息并更新组织附加信息
                                            ouf.setUserId(ufp.getUserId());
                                            dataDisposeFlag = "updateAll";
                                        } else {
                                            // 导入的组织人员不存在于当前组织，更新基本信息并添加新的组织附加信息
                                            ouf.setUserId(ufp.getUserId());
                                            ouf.setFlag("2");//修改
                                            dataDisposeFlag = "updateAndAdd";
                                        }
                                    } else {//表示手机号并不匹配
                                        //判断手机号是否已经存在
                                        if (existPhone > 0) {//存在就提示手机号已存在
                                            log.debug("<批量导入组织人员> 该手机号码已经存在，提示 用户已存在 -> phone:{}", phn);
                                            list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportRepetitionPhone()));
                                            return;
                                        } else {//手机号不存在则根据身份证和姓名做更新操作
                                            dataDisposeFlag = "updateAll";
                                            ouf.setUserId(uf.getUserId());
                                        }
                                    }
                                } else {//身份证、姓名不存在，如果手机号存在了就提示手机号已存在
                                    //判断手机号是否已经存在
                                    if (existPhone > 0) {//存在就提示手机号已存在
                                        log.debug("<批量导入组织人员> 该手机号码已经存在，提示 用户已存在 -> phone:{}", phn);
                                        list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportRepetitionPhone()));
                                        return;
                                    } else {//身份证、姓名、手机都不存在的情况下直接新增

                                        ouf.setFlag("1");//新增
                                        dataDisposeFlag = "addAll";
                                    }
                                }

                                //验证通过后，设置电话和身份证的密文和脱敏文本
                                ouf.setPhone(NumEncryptUtils.encrypt(phn, 3));
                                ouf.setPhoneSecret(NumEncryptUtils.numSecret(phn, 3));
                                ouf.setCertNumber(NumEncryptUtils.encrypt(cnr, Constants.CERT_NUMBER_IDENTITY));
                                ouf.setCertNumberSecret(NumEncryptUtils.numSecret(cnr, Constants.CERT_NUMBER_IDENTITY));

                                //数据入库
                                boolean bl;
                                if ("addAll".equals(dataDisposeFlag) || "updateAndAdd".equals(dataDisposeFlag)) {
                                    bl = Objects.nonNull(addOrgUser(ouf, Constants.STATUS_NO, header));
                                } else {
                                    bl = editOrgUser(ouf, header);
                                }
                                if (!bl) {
                                    log.debug("<批量导入组织人员> 入库失败 phone {} certNumber:{}", phn, cnr);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportDBError()));
                                }
                            } catch (Exception e) {
                                try {
                                    String p = NumEncryptUtils.decrypt(ouf.getPhone(), ouf.getPhoneSecret());
                                    String c = NumEncryptUtils.decrypt(ouf.getCertNumber(), ouf.getCertNumberSecret());
                                    log.error("<批量导入组织人员> 系统报错 phone {} certNumber:{} errorMessage:{}", p, c, e.getMessage(), e);
                                    list.add(createOrgUserExportErrorForm(ouf, messagesConfig.getImportSystemError(), p, c));
                                } catch (Exception e1) {
                                    log.error("<批量导入组织人员> 添加错误信息，转化手机号码和身份证号码为明文报错！ errorMessage:{}", e.getMessage(), e);
                                }
                            }
                            Long etime2 = System.currentTimeMillis();
                            log.debug("<批量导入组织人员> 处理缓存里的数据 name:{} phone:{} --> 结束  耗时:{}ms", ouf.getName(), ouf.getPhone(), etime2 - stime2);
                        })
                ).get();


                //计算导入成功数量
                succeedCount = oufPageList.size() - list.size();
                //按照当前的pageSize计算总页数
                pageCount = oufList.size() % pageSize != 0 ? oufList.size() / pageSize + 1 : oufList.size() / pageSize;
                //计算是不是最后一次导入
                if (endIndex >= oufList.size()) {
                    finish = 1;
                }
                //更新组织人员数量
                if (oufList.size() > 0 && finish == 1) {
                    log.debug("<批量导入组织人员> 更新组织人员数量 orgId:{} --> 开始  ", orgId);
                    Long stime3 = System.currentTimeMillis();
                    orgUserCommonService.putOrgUserCount(orgId);
                    Long etime3 = System.currentTimeMillis();
                    log.debug("<批量导入组织人员> 更新组织人员数量 orgId:{} --> 结束  耗时:{}ms", orgId, etime3 - stime3);
                }
            } else {
                log.info("上传的数据已失效，请重新上传组织会员数据！");
                return new ResponseEntity<>(new Result<>(errors, 175, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("<批量导入组织人员报错> -> errorMessage:{}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(errors, 183, HttpStatus.OK.value()), HttpStatus.OK);
        }

        //组装返回结果
        OrgUserExportErrorReForm re = new OrgUserExportErrorReForm(succeedCount, page, pageSize, pageCount, finish, list);

        Long etime4 = System.currentTimeMillis();
        log.debug("<批量导入组织人员> --> 结束 operKey:{} orgId:{} page:{} pageSize:{} 耗时:{}ms", operKey, orgId, page, pageSize, etime4 - stime4);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

    /**
     * 批量导入失败后，单独提交
     *
     * @param header        header
     * @param orgId         orgId
     * @param name          name
     * @param phone         phone
     * @param certType      certType
     * @param certNumber    certNumber
     * @param politicalType politicalType
     * @param jobGrade      jobGrade
     * @param position      position
     * @param communist     communist
     * @param youthLeague   youthLeague
     * @param unionMember   unionMember
     * @param womenLeague   womenLeague
     * @param openService   operService
     * @return ResponseEntity
     */
    public ResponseEntity<Result<?>> submitData(HeaderHelper.SysHeader header, Long orgId, String name, String phone,
                                                Integer certType, String certNumber, Integer politicalType, Integer jobGrade,
                                                String position, Integer communist, Integer youthLeague, Integer unionMember,
                                                Integer womenLeague, String joiningTime, OpenService openService) {
        //检查身份证格式
        if (!Utils.checkCertNumber(Constants.CERT_NUMBER_IDENTITY, certNumber)) {
            log.debug("身份证号码格式错误 -> certNumber:{}", certNumber);
            return new ResponseEntity<>(new Result<>(errors, 149, HttpStatus.OK.value()), HttpStatus.OK);
        }
        OrgUserForm orgUser = new OrgUserForm();
        orgUser.setOrgId(orgId);
        orgUser.setName(name);
        orgUser.setCertType(certType);
        orgUser.setPoliticalType(politicalType);
        orgUser.setJobGrade(jobGrade);
        orgUser.setPosition(position);
        orgUser.setCommunist(communist);
        orgUser.setYouthLeague(youthLeague);
        orgUser.setUnionMember(unionMember);
        orgUser.setWomenLeague(womenLeague);
        orgUser.setJoiningTime(joiningTime);//入党日期

        String dataDisposeFlag;//数据处理标记
        //验证通过姓名、身份证、电话是否能获取到已存在用户
        String p = NumEncryptUtils.encrypt(phone, 3);//手机号加密
        //证件号加密，兼容12371数据加密方式
        List<String> certNumbers = new ArrayList<>();
        String c1 = NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY);
        String c2 = CertNumberUtil.encrypt(certNumber);//12371身份证加密方法
        certNumbers.add(c1);
        certNumbers.add(c2);
        UserForm uf = userMapper.exportCheck(name, null, certNumbers);
        int existPhone = userMapper.checkPhone(p, null);//判断手机号是否已存在于用户表
        if (uf != null && uf.getUserId() != null) {
            UserForm ufp = userMapper.exportCheck(name, p, certNumbers);
            if (ufp != null && ufp.getUserId() != null) {//身份证、姓名、手机号在数据库中有对应数据匹配，且数据完全匹配，则直接导入的数据覆盖原有数据信息
                //判断用户是否已存在于要导入的组织中
                UserForm user = userMapper.addCheckPhone(p, Constants.DEP_TYPE_ORG, orgId);
                if (user != null && user.getUserId() != null) {//导入的组织人员存在于当前组织，更新基本信息并更新组织附加信息
                    dataDisposeFlag = "updateAll";
                    orgUser.setUserId(ufp.getUserId());
                } else {//导入的组织人员不存在于当前组织，更新基本信息并添加新的组织附加信息
                    orgUser.setUserId(ufp.getUserId());
                    orgUser.setFlag("2");//修改
                    dataDisposeFlag = "updateAndAdd";
                }
            } else {//表示手机号并不匹配
                //判断手机号是否已经存在
                if (existPhone > 0) {//存在就提示手机号已存在
                    log.debug("<单独提交导入组织人员> 该手机号码已经存在，提示 用户已存在 -> phone:{}", phone);
                    return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
                } else {//手机号不存在则根据身份证和姓名做更新操作
                    dataDisposeFlag = "updateAll";
                    orgUser.setUserId(uf.getUserId());
                }
            }
        } else {//身份证、姓名不存在，如果手机号存在了就提示手机号已存在
            //判断手机号是否已经存在
            if (existPhone > 0) {//存在就提示手机号已存在
                log.debug("<批量导入组织人员> 该手机号码已经存在，提示 用户已存在 -> phone:{}", phone);
                return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
            } else {//身份证、姓名、手机都不存在的情况下直接新增
                orgUser.setFlag("1");//新增
                dataDisposeFlag = "addAll";
            }
        }

        if ("addAll".equals(dataDisposeFlag)) {
            //身份证实名认证
            String validateStr = openService.validateCertNumber(certNumber, name);
            Map rsMap = Utils.fromJson(validateStr, Map.class);
            if (rsMap == null || String.valueOf(rsMap.get(Constants.CERT_NUMBER_RS_CODE)).equals(Constants.CERT_NUMBER_RS_ERROR)) {
                log.debug("身份证错误，请确认身份证信息是否正确 -> name:{}  certNumber:{}", name, certNumber);
                return new ResponseEntity<>(new Result<>(errors, 151, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }

        //验证通过后，设置电话和身份证的密文和脱敏文本
        orgUser.setPhone(NumEncryptUtils.encrypt(phone, 3));
        orgUser.setPhoneSecret(NumEncryptUtils.numSecret(phone, 3));
        orgUser.setCertNumber(NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY));
        orgUser.setCertNumberSecret(NumEncryptUtils.numSecret(certNumber, Constants.CERT_NUMBER_IDENTITY));

        //数据入库
        boolean bl;
        if ("addAll".equals(dataDisposeFlag) || "updateAndAdd".equals(dataDisposeFlag)) {
            bl = Objects.nonNull(addOrgUser(orgUser, Constants.STATUS_NO, header));
        } else {
            bl = editOrgUser(orgUser, header);
        }

        if (!bl) {
            log.debug("<导入失败后单独提交人员信息> 系统异常，新增组织人员失败！phone : " + phone);
            return new ResponseEntity<>(new Result<>(errors, 134, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            //更新组织人员数
            orgUserCommonService.putOrgUserCount(orgId);
        }
        log.debug("<导入失败后单独提交人员信息> 新增组织人员完成");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * @param orgIds orgIds
     * @param page   page
     * @return page
     */
    public Page<OrgUserForm> selectOrgUserInfo(String orgIds, PageNumber page, Long regionId) {
        //设置分页属性
        int p = Preconditions.checkNotNull(page.getPage());
        int r = Preconditions.checkNotNull(page.getRows());
        List<Long> orgIdList = Arrays.stream(orgIds.split(","))
                .map(Long::valueOf).collect(Collectors.toList());
        return PageHelper.startPage(p, r).doSelectPage(() -> userOrgAndCorpMapper.selectOrgUserInfoByOrgIds(orgIdList, regionId));
    }


    /**
     * 根据组织编号和用户编号 查询组织用户信息
     *
     * @param oId    oId
     * @param userId userId
     * @return UserForm
     */
    public UserForm orgUserInfo(long oId, long userId, Long regionId) {

        // 查询用户组织属性
        Example exam = new Example(UserOrgAndCorpEntity.class);
        exam.createCriteria().andEqualTo("userId", userId)
                .andEqualTo("organizationId", oId)
                .andEqualTo("regionId", regionId);
        UserOrgAndCorpEntity orgInfo = this.userOrgAndCorpMapper.selectOneByExample(exam);
        log.debug("查询结果 -> [{}]", orgInfo);
        // 判断用户是否在该组织下
        UserForm form = new UserForm();
        if (orgInfo != null && orgInfo.getUserId() != null) {
            // 查询用户基本属性
            Example example = new Example(UserEntity.class);
            example.createCriteria().andEqualTo("userId", userId);
            UserEntity ue = this.userMapper.selectOneByExample(example);

            // 基本属性
            form.setUserId(ue.getUserId());
            form.setName(ue.getName());
            form.setPhone(ue.getPhoneSecret());//返回脱敏的文本
            form.setCertType(ue.getCertType());//证件类型
            form.setCertNumber(ue.getCertNumberSecret());//证件号 返回脱敏的文本
            form.setEthnic(ue.getEthnic());//民族
            form.setCensusType(ue.getCensusType());//户籍类型
            form.setEducation(ue.getEducation());//学历
            form.setNativeProvince(ue.getNativeProvince());//籍贯一级区域
            form.setNativeCity(ue.getNativeCity());//籍贯二级区域
            form.setGender(ue.getGender());//性别
            form.setPoliticalType(ue.getPoliticalType());//政治面貌
            form.setIsLose(ue.getIsLose());// 是否失联 1-是 2-否
            // 入党时间
            form.setJoiningTime(ue.getJoiningTime());
            // 转正时间
            if (null != orgInfo.getJoiningTime()) {
                form.setJoiningPostiveTime(Utils.localDateTimeToString(orgInfo.getJoiningTime()));
            }
            form.setBirthday(ue.getBirthday());
            // 前端展示脱敏值
            form.setCertNumber(ue.getCertNumberSecret());
            form.setPhone(ue.getPhoneSecret());
            form.setIsBind(Constants.IS_BIND_NO);
            form.setJobNumber(ue.getJobNumber());
            // 住址
            form.setAddress(ue.getAddress());
            // 流动党员
            form.setIsFlow(ue.getIsFlow());
            // 职位
            form.setTitle(ue.getTitle());
            // 头像
            form.setHeadUrl(ue.getHeadUrl());
            form.setSequence(ue.getSequence());
            // 手写签名
            form.setHandSign(ue.getHandSign());
            // 是否绑定
            Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
            Example ex = new Example(UserThirdEntity.class);
            ex.createCriteria().andEqualTo("userId", ue.getUserId())
                    .andEqualTo("oId", orgData.getOrgId());
            UserThirdEntity ute = this.userThirdMapper.selectOneByExample(ex);
            if (ute != null) {
                form.setIsBind(Constants.IS_BIND_YES);
            }
            form.setJoinPartyType(orgInfo.getJoinPartyType());
            if (null != orgInfo.getJoinPartyTime()) {
                form.setJoinPartyTime(Utils.localDateTimeToString(orgInfo.getJoinPartyTime()));
            }
            form.setOldOrgName(orgInfo.getOldOrgName());
            form.setJoinType(ue.getJoinType());

            // 组织属性
            form.setPositionCode(orgInfo.getPositionCode());
            form.setEmail(orgInfo.getEmail());
            form.setEntryDate(orgInfo.getEntryDate());
            form.setTagId(orgInfo.getTagId());
            form.setJobGrade(orgInfo.getJobGrade());//技术等级
            form.setCommunist(orgInfo.getCommunist());//中共党员
            form.setYouthLeague(orgInfo.getYouthLeague());//共青团
            form.setUnionMember(orgInfo.getUnionMember());//工会会员
            form.setWomenLeague(orgInfo.getWomenLeague());//妇联

        }

        return form;
    }

    /**
     * 身份证姓名重复验证
     *
     * @param certNumber 身份证号，明文号码
     * @param userId     用户编号
     * @return int
     */
    public int checkCertNumber(String certNumber, Long userId, String name) {
        return checkIdCard(certNumber, userId, name);
    }

    /**
     * 身份证姓名重复验证
     *
     * @param idCard 身份证号，明文号码
     * @param userId 用户编号
     * @return int
     */
    private int checkIdCard(String idCard, Long userId, String name) {
        String certNumber = NumEncryptUtils.encrypt(idCard, Constants.CERT_NUMBER_IDENTITY);//加密
        return userMapper.idCardCheck(certNumber, userId, name);
    }

    /**
     * @Author: tc
     * @Description 验证身份证实名和是否存在
     * @Date 9:29 2018/7/13
     */
    public ResponseEntity<Result<?>> checkNameIdCard(String idCard, String name, Long userId, String idCardRepetitionError, OpenService openService) {
        OpenForm openForm = new OpenForm();
        try {
            // 身份证实名认证
            String validateStr = openService.validateCertNumber(idCard, name);
            Map rsMap = Utils.fromJson(validateStr, Map.class);
            if (rsMap == null || String.valueOf(rsMap.get(Constants.CERT_NUMBER_RS_CODE)).equals(Constants.CERT_NUMBER_RS_ERROR)) {
                log.debug("<身份证实名验证及重复检查>  身份证实名信息错误 -> name:{}  idCard:{}", name, idCard);
                openForm = Utils.fromJson(validateStr, OpenForm.class);
            } else {
                // 身份证姓名重复验证
                int exist = checkIdCard(idCard, userId, name);
                if (exist > 0) {
                    log.debug("<身份证实名验证及重复检查>  身份证已存在 -> name:{}  idCard:{}", name, idCard);
                    openForm.setCode(-1);
                    openForm.setMessage(idCardRepetitionError);
                } else {
                    openForm.setCode(0);
                    openForm.setMessage("验证通过");
                }
            }
        } catch (Exception e) {
            log.error("<身份证实名验证及重复检查>  系统报错！ -> name:{}  idCard:{} errorMessage:{} ", name, idCard, e.getMessage(), e);
            throw new ApiException("身份证实名验证及重复检查失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "身份证实名验证及重复检查失败"));
        }
        return new ResponseEntity<>(new Result<>(openForm, errors), HttpStatus.OK);
    }

    /**
     * 用户中心在修改用户名以后，广播到其他模块的订阅接口中
     */
    public void userChangeNameCallback(Long uid, String name, Long regionId) {
        orgUserCommonService.callbackDoGet("user_change_name_callback", uid, "name",
                name, regionId);
    }

    /**
     * 用户中心在修改用户电话以后，广播到其他模块的订阅接口中
     */
    public void userChangePhoneCallback(Long uid, String phone, Long regionId) {
        orgUserCommonService.callbackDoGet("user_change_phone_callback", uid,
                "phone", phone, regionId);
    }

    /**
     * 用户修改身份证回调
     *
     * @param userId     userId
     * @param certNumber certNumber
     * @param regionId   regionId
     */
    public void userChangeIdcardCallback(Long userId, String certNumber, Long regionId) {
        orgUserCommonService.userChangeIdcardCallback(userId, "user_change_idcard_callback",
                certNumber, "id_card", regionId);
    }

    /**
     * 用户修改政治面貌回调
     *
     * @param userId        userId
     * @param politicalType politicalType
     * @param regionId      regionId
     */
    public void userChangePoliticalTypeCallBack(Long userId, Integer politicalType, Long regionId) {
        orgUserCommonService.callbackDoGet("user_change_political_type_callback", userId,
                "political_type", String.valueOf(politicalType), regionId);
    }

    /**
     * 构建导入错误信息实体
     *
     * @param ouf ouf
     * @param msg msg
     */
    private OrgUserExportErrorForm createOrgUserExportErrorForm(OrgUserForm ouf, String msg) {
        return createOrgUserExportErrorForm(ouf, msg, null, null);
    }

    /**
     * @param ouf        ouf
     * @param msg        msg
     * @param phone      phone
     * @param certNumber certNumber
     */
    private OrgUserExportErrorForm createOrgUserExportErrorForm(OrgUserForm ouf, String msg, String phone, String certNumber) {
        return new OrgUserExportErrorForm(ouf.getName(), phone != null ? phone : ouf.getPhone(), ouf.getCertType(), certNumber != null ? certNumber : ouf.getCertNumber(), msg,
                ouf.getPoliticalType(), ouf.getJobGrade(), ouf.getPosition(), ouf.getCommunist(), ouf.getYouthLeague(),
                ouf.getUnionMember(), ouf.getWomenLeague());
    }

    /**
     * 移交管理员
     *
     * @param form   form
     * @param header header
     */
    @Transactional(rollbackFor = Throwable.class)
    public int transferManager(TransferManagerForm form, HeaderHelper.SysHeader header) {

        // 校验操作用户是否已经是管理员
        int count = this.userOrgAndCorpMapper.checkRoleType(header.getUserId(), header.getOid(), Constants.ROLE_TYPE_DEFINED_ROOT);
        if (count <= 0) {
            return 212;
        }
        // 校验该用户是否已经是管理员
        int cnt = this.userOrgAndCorpMapper.checkRoleType(form.getUserId(), header.getOid(), Constants.ROLE_TYPE_DEFINED_ROOT);
        if (cnt > 0) {
            return 209;
        }
        // 校验用户信息是否完整
        Example userEx = new Example(UserEntity.class);
        userEx.createCriteria().andEqualTo("userId", form.getUserId());
        UserEntity ue = this.userMapper.selectOneByExample(userEx);
        if (ue == null) {
            return 210;
        } else if (StringUtils.isBlank(ue.getName())
                || StringUtils.isBlank(ue.getCertNumber())
                || StringUtils.isBlank(ue.getPhone())) {
            return 211;
        }
        // 设置权限
        // 1-更改原管理员权限为普通权限
        Example roleEx = new Example(RoleEntity.class);
        roleEx.createCriteria().andEqualTo("organizationId", header.getOid());
        List<RoleEntity> roleList = this.roleMapper.selectByExample(roleEx);
        // 管理员权限ID
        long managerRoleId = 1L;
        // 普通用户权限ID
        long userRoleId = 1L;
        if (roleList != null && !roleList.isEmpty()) {
            for (RoleEntity re : roleList) {
                if (re.getRoleType() == Constants.ROLE_TYPE_DEFINED_ROOT) {
                    managerRoleId = re.getRoleId();
                }
                if (re.getRoleType() == Constants.ROLE_TYPE_DEFAULT) {
                    userRoleId = re.getRoleId();
                }
            }
        } else {
            // 新增组织权限
            Date curDate = new Date();
            RoleEntity mgrRole = new RoleEntity();
            mgrRole.setName("默认管理员");
            mgrRole.setRoleType(Constants.ROLE_TYPE_DEFINED_ROOT);
            mgrRole.setOrgType(Constants.DEP_TYPE_ORG);
            mgrRole.setOrganizationId(header.getOid());
            mgrRole.setStatus(Constants.STATUS_YES);
            mgrRole.setCreateTime(curDate);
            mgrRole.setUpdateTime(curDate);
            this.roleMapper.insertSelective(mgrRole);
            managerRoleId = mgrRole.getRoleId();
            // 新增权限菜单列表
            roleMenuMapper.batchInsert(Arrays.asList(userMenuIds.split(",")), managerRoleId, header.getRegionId());
            // 新增普通用户权限
            RoleEntity userRole = new RoleEntity();
            userRole.setName("个人用户");
            userRole.setRoleType(Constants.ROLE_TYPE_DEFAULT);
            userRole.setOrgType(Constants.DEP_TYPE_ORG);
            userRole.setOrganizationId(header.getOid());
            userRole.setStatus(Constants.STATUS_YES);
            userRole.setCreateTime(curDate);
            userRole.setUpdateTime(curDate);
            this.roleMapper.insertSelective(userRole);
            userRoleId = userRole.getRoleId();
            // 新增权限菜单列表
            roleMenuMapper.batchInsert(Arrays.asList(managerMenuIds.split(",")), userRoleId, header.getRegionId());
        }
        // 更改原管理员权限
        UserRoleEntity ure = new UserRoleEntity();
        ure.setUserId(header.getUserId());
        ure.setRoleId(managerRoleId);
        ure.setOrganizationId(header.getOid());
        this.userRoleMapper.delete(ure);
        ure.setRoleId(userRoleId);
        this.userRoleMapper.insertSelective(ure);
        // 修改新用户权限
        ure.setUserId(form.getUserId());
        ure.setRoleId(userRoleId);
        this.userRoleMapper.delete(ure);
        ure.setRoleId(managerRoleId);
        this.userRoleMapper.insertSelective(ure);
        // 更改原用户为可以修改状态
        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(header.getUserId());
        userEntity.setIsEdit(Constants.IS_ROOT_YES);
        this.userMapper.updateByPrimaryKeySelective(userEntity);
        // 更改管理员用户为不可修改状态
        userEntity.setUserId(form.getUserId());
        userEntity.setIsEdit(Constants.IS_EDIT_NO);
        this.userMapper.updateByPrimaryKeySelective(userEntity);
        return 1;
    }

    /**
     * 移交管理员校验
     *
     * @param user_id userId
     * @param header  header
     */
    public int transManagerCheck(Long user_id, HeaderHelper.SysHeader header) {

        // 校验操作用户是否已经是管理员
        int count = this.userOrgAndCorpMapper.checkRoleType(header.getUserId(), header.getOid(), Constants.ROLE_TYPE_DEFINED_ROOT);
        if (count <= 0) {
            return 212;
        }
        // 校验该用户是否已经是管理员
        int cnt = this.userOrgAndCorpMapper.checkRoleType(user_id, header.getOid(), Constants.ROLE_TYPE_DEFINED_ROOT);
        if (cnt > 0) {
            return 209;
        }
        return 1;
    }

    /**
     * 更新用户组织标签
     *
     * @param form form
     */
    public ResponseEntity<Result<?>> updateUserOrgTag(UpdateUserOrgTagForm form, Long regionId) {
        log.debug("更新组织标签, form -> [{}]", form);
        // 设置回调状态 默认为2（新增），3为删除
        int callBackStatus = 2;
        // 查询t_user_org_corp, 获取userorgcorp
        Example e = new Example(UserOrgAndCorpEntity.class);
        e.createCriteria().andEqualTo("userId", form.getUserId()).andEqualTo("organizationId", form.getOrgId());
        List<UserOrgAndCorpEntity> userOrgAndCorpEntities = this.userOrgAndCorpMapper.selectByExample(e);
        if (userOrgAndCorpEntities.size() != 1) {
            log.debug("查询t_user_org_corp表异常, 查询条数 -> [{}], 数据 -> [{}]", userOrgAndCorpEntities.size(), userOrgAndCorpEntities);
            return new ResponseEntity<>(new Result<>(errors, 901, HttpStatus.OK.value()), HttpStatus.OK);
        }
        UserOrgAndCorpEntity entity = userOrgAndCorpEntities.get(0);
        // 获取原标签ID
        String oldTagIds = entity.getTagId();
        List<String> oldTags = new ArrayList<>();
        if (StringUtils.isNotBlank(oldTagIds)) {
            oldTags = new ArrayList<>(Arrays.asList(oldTagIds.split(",")));
        }
        // 获取新标签列表
        List<TagBase> callBackTagList = new ArrayList<>();
        List<UserOrgTagForm> newTags = form.getTags();
        for (UserOrgTagForm tagForm : newTags) {
            String tagId = tagForm.getTagId().toString();
            Integer status = tagForm.getStatus();
            // 判断是否勾选状态
            if (status == Constants.YES) {
                //TODO 当前逻辑只会存在党费标签, 若是其它标签需要拓展接口
                if (this.orgUserCommonService.isCommunist(form.getUserId())) {
                    // 判断是否在其他党组织内，已存在党费标签
                    List<UserOrgAndCorpEntity> UocEntities =
                            this.userOrgAndCorpMapper.selectUserOrgCorpByUserId(form.getUserId(),
                                    Constants.ORG_TYPE_COMMUNIST, regionId);
                    // 判断是否在t_user_org_corp存在数据
                    if (UocEntities.size() > 0) {
                        log.debug("用户[{}]在t_user_org_corp存在数据[{}]条", form.getUserId(), UocEntities.size());
                        // 循环判断多组织
                        for (UserOrgAndCorpEntity e1 : UocEntities) {
                            if (!e1.getOrganizationId().equals(form.getOrgId())) {
                                // 判断组织里面存在多标签
                                String eTagId = e1.getTagId();
                                log.debug("当前人员[{}]在党组织[{}]下的标签为[{}]", form.getUserId(), e1.getOrganizationId(), eTagId);
                                if (StringUtils.isNotBlank(eTagId)) {
                                    String[] str = eTagId.split(",");
                                    for (String s : str) {
                                        // 判断标签是否是党费标签, 若是，则本次不新增
                                        int tag = this.tagMapper.getTagById(Long.parseLong(s));
                                        if (tag == Constants.TAG_TYPE_PAY) {
                                            log.debug("当前人员[{}]在党组织[{}]下已存在党费标签[{}]", form.getUserId(), e1.getOrganizationId(), s);
                                            return new ResponseEntity<>(new Result<>(errors, 902, HttpStatus.OK.value()), HttpStatus.OK);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // 勾选状态
                    // 判断是否在数据中已存在保存标签ID
                    if (oldTagIds == null || !oldTagIds.contains(tagId)) {
                        // 不存在，则新添加新原数据中, 已存在，则不作任何操作
                        oldTags.add(tagId);
                    }
                }
            } else {
                // 取消勾选状态
                // 判断是否在数据中已存在保存标签ID
                if (oldTagIds != null && oldTagIds.contains(tagId)) {
                    // 存在, 则需要删除原列表中的标签ID, 不存在，则不作任何操作
                    oldTags.remove(tagId);
                }
                callBackStatus = 3;
            }
            // 封装回调标签列表
            TagEntity tagEntity = this.tagMapper.selectByPrimaryKey(tagForm.getTagId());
            if (tagEntity != null) {
                TagBase tagBase = new TagBase();
                tagBase.setTagType(tagEntity.getTagType());
                tagBase.setOrgId(form.getOrgId());
                tagBase.setTagName(tagEntity.getName());
                tagBase.setTagId(tagEntity.getTagId());
                callBackTagList.add(tagBase);
            }
        }
        // 循环判断完成后, 需要保存数据
        String newTag = String.join(",", oldTags);
        if ((StringUtils.isBlank(oldTagIds) && StringUtils.isNotBlank(newTag)) || (StringUtils.isNotBlank(oldTagIds) && !oldTagIds.equals(newTag))) {
            this.userOrgAndCorpMapper.updateTagIdByUserAndOrg(form.getUserId(), form.getOrgId(), ((newTag.length() == 0) ? null : newTag));
            int isRetire = 2;
            // 调用广播接口
            UserChangeForm userChangeForm = this.commonUserChangeForm(form.getUserId(), form.getOrgId(),
                    form.getOrgId(), new Date(), callBackStatus, regionId);
            // 用户是否是离退休党员
            List<String> retireCode = new ArrayList<>(Arrays.asList(this.userOptionConfig.getRetireCode().split(",")));
            if (!StringUtils.isBlank(String.valueOf(entity.getPositionCode()))
                    && retireCode.contains(String.valueOf(entity.getPositionCode()))) {
                isRetire = 1;
            }
            userChangeForm.setIsRetire(isRetire);
            // 封装标签列表
            if (userChangeForm.getTagList().size() == 0) {
                userChangeForm.setTagList(callBackTagList);
            }
            this.callbackService.userOrgChangeCallback(userChangeForm, regionId);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询所有用户-领导班子
     *
     * @param pageNumber 页码
     * @param param      参数
     * @param type       type
     * @param orgType    组织类型
     * @param regionId   区县ID
     */
    public Page<OrgUserInfoResultForm> findAllUser(PageNumber pageNumber, String param,
                                                   Integer type, Integer orgType, Long regionId) {
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        return PageHelper.startPage(p, r).doSelectPage(() -> this.organizationMapper.findAllUser(param, type,
                orgType, regionId));
    }

    /**
     * 根据用户ID列表查询用户信息
     *
     * @param form   用户ID列表
     * @param header 请求头
     */
    public Map<String, Object> findUserByIdList(UserIdForm form, HeaderHelper.SysHeader header) {
        Long regionId = header.getRegionId();
        Integer orgType = header.getOrgType();
        Map<String, Object> resultMap = new HashMap<>();
        int isEmployee = Constants.ORG_TYPE_COMMUNIST.equals(orgType) ? 1 : 0;
        // 查询全部用户-默认分页
        if (null != form.getOrgId()
                && null == form.getUserOrgId()
                && CollectionUtils.isEmpty(form.getIdList())) {
            PageNumber pageNumber = new PageNumber(form.getPageNum(), form.getPageSize());
            Page<UserInfoBase> pageList = PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows()).doSelectPage(() ->
                    this.userThirdMapper.findAllUserByOrgId(form.getOrgId(), null, Constants.STATUS_YES, isEmployee,
                            form.getType(), regionId));
            resultMap.put("result", pageList);
        } else if (null != form.getOrgId()
                && null != form.getUserOrgId()
                && CollectionUtils.isEmpty(form.getIdList())) {
            // 根据组织ID和公众号所在组织ID查询用户
            if (Constants.STATUS_YES == form.getIsPage()) {
                // 分页
                PageNumber pageNumber = new PageNumber(form.getPageNum(), form.getPageSize());
                Page<UserInfoBase> pageList = PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows()).doSelectPage(() ->
                        this.userThirdMapper.findAllUserByOrgId(form.getOrgId(),
                                form.getUserOrgId(), Constants.STATUS_YES, isEmployee, form.getType(), regionId));
                resultMap.put("result", pageList);
            } else {
                // 不分页
                List<UserInfoBase> resultList = this.userThirdMapper.findAllUserByOrgId(form.getOrgId(),
                        form.getUserOrgId(), Constants.STATUS_YES, isEmployee, form.getType(), regionId);
                resultMap.put("result", resultList);
            }
        } else if (null != form.getOrgId()
                && null == form.getUserOrgId()
                && !CollectionUtils.isEmpty(form.getIdList())) {
            List<UserInfoBase> resultList = new ArrayList<>();
            // 根据公众号组织ID和用户列表查询用户
            for (Long userId : form.getIdList()) {// 获取用户缓存信息
                String userKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(userKey)) && (null == form.getType() || form.getType() == 0)) {
                    UserInfoBase base = Utils.fromJson(this.redisTemplate.opsForValue().get(userKey), UserInfoBase.class);
                    if (Objects.nonNull(base)) {
                        if (StringUtils.isNotBlank(base.getPhone())) {
                            base.setPhone(NumEncryptUtils.encrypt(base.getPhone(), 2));
                        }
                        if (StringUtils.isNotBlank(base.getCertNumber())) {
                            base.setCertNumber(NumEncryptUtils.encrypt(base.getCertNumber(), 1));
                        }
                        if (form.getType() != null && form.getType() == 1) {
                            if (StringUtils.isNotBlank(base.getContactNumber())) {
                                base.setPhone(NumEncryptUtils.encrypt(base.getContactNumber(), 2));
                                base.setPhoneSecret(base.getContactNumberSecret());
                            }
                        }
                        base.setName(base.getUserName());
                        resultList.add(base);
                    }
                } else {
                    // 根据用户ID查询用户信息
                    UserInfoBase targetBase = new UserInfoBase();
                    Example example = new Example(UserEntity.class);
                    example.createCriteria().andEqualTo("userId", userId).andEqualTo("status", Constants.STATUS_YES);
                    UserEntity userEntity = this.userMapper.selectOneByExample(example);
                    if (null == userEntity) {
                        userEntity = new UserEntity();
                        userEntity.setUserId(userId);
                        BeanUtils.copyProperties(userEntity, targetBase);
                        targetBase.setName(userEntity.getName());
                        resultList.add(targetBase);
                    } else {
                        // 根据userId查询openId
                        Example ex = new Example(UserThirdEntity.class);
                        ex.createCriteria().andEqualTo("userId", userEntity.getUserId()).andEqualTo("oId", form.getOrgId());
                        List<UserThirdEntity> openIdList = this.userThirdMapper.selectByExample(ex);
                        if (!ListUtils.isEmpty(openIdList)) {
                            for (UserThirdEntity userThirdEntity : openIdList) {
                                UserInfoBase base = new UserInfoBase();
                                BeanUtils.copyProperties(userEntity, base);
                                if (form.getType() != null && form.getType() == 1) {
                                    if (StringUtils.isNotBlank(userEntity.getContactNumber())) {
                                        base.setPhone(userEntity.getContactNumber());
                                        base.setPhoneSecret(userEntity.getContactNumberSecret());
                                    }
                                }
                                base.setOpenId(userThirdEntity.getThToken());
                                base.setName(userEntity.getName());
                                resultList.add(base);
                            }
                        } else {
                            // 没有openId
                            BeanUtils.copyProperties(userEntity, targetBase);
                            if (form.getType() != null && form.getType() == 1) {
                                if (StringUtils.isNotBlank(userEntity.getContactNumber())) {
                                    targetBase.setPhone(userEntity.getContactNumber());
                                    targetBase.setPhoneSecret(userEntity.getContactNumberSecret());
                                }
                            }
                            targetBase.setName(userEntity.getName());
                            resultList.add(targetBase);
                        }
                    }
                }
            }
            resultMap.put("result", resultList);
        }
        resultMap.computeIfAbsent("result", k -> new ArrayList<>());
        return resultMap;
    }

    /**
     * 根据组织查询管理员列表
     *
     * @param form 查询参数
     */
    public Map<String, Object> findOrgMgrByList(OrgMgrForm form, Long regionId) {
        Map<String, Object> resultMap = new HashMap<>();
        // 管理员角色类型
        String[] roleTypes = this.roleType.split(",");
        List<Integer> roleTypeList = new ArrayList<>(roleTypes.length);
        for (String roleType : roleTypes) {
            roleTypeList.add(Integer.parseInt(roleType));
        }
        // 是否分页查询
        if (Constants.YES == form.getIsPage()) {
            PageNumber pageNumber = new PageNumber(form.getPageNum(), form.getPageSize());
            Page<UserInfoBase> pageList = PageHelper.startPage(pageNumber.getPage(), pageNumber.getRows()).doSelectPage(() ->
                    this.organizationMapper.findOrgMgrByList(form.getIdList(), Constants.STATUS_YES, roleTypeList, regionId));
            resultMap.put("result", pageList);
        } else {
            List<UserInfoBase> resultList = this.organizationMapper.findOrgMgrByList(form.getIdList(),
                    Constants.STATUS_YES, roleTypeList, regionId);
            resultMap.put("result", resultList);
        }
        return resultMap;
    }

    /**
     * 成员管理查询人员
     *
     * @param queryMap   queryMap
     * @param pageNumber pageNumber
     * @return page
     */
    public Page<Map<String, Object>> getUserList(Map<String, Object> queryMap, PageNumber pageNumber, HeaderHelper.SysHeader header) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        queryMap.put(UserFieldEnum.ORG_REGION_ID.getKey(), header.getRegionId());
        // 组织类型
        Object o = queryMap.get(FieldNameConfig.ORG_TYPE);
        Integer orgType = header.getOrgType();
        if (Objects.nonNull(o) && StringUtils.isNotBlank(Convert.toStr(o))) {
            orgType = Convert.toInt(o);
        }
        queryMap.put(UserFieldEnum.ORG_TYPE.getKey(), orgType);
        // 判断组织类型，如果选择组织为党小组，is_employee=0, 其他is_employee=1
        final Object orgIdObj = queryMap.get(UserFieldEnum.ORG_ID.getKey());
        final Object onlyManager = queryMap.get(UserFieldEnum.ONLY_MANAGER.getKey());
        if (Objects.nonNull(orgIdObj)) {
            final Long orgId = Convert.toLong(orgIdObj);
            final Org org = this.orgMongoService.getOrgById(orgId);
            if (Objects.nonNull(org)) {
                if (!org.getOrgTypeChild().equals(Constants.ORG_TYPE_SMALL_GROUP)) {
                    if (!Convert.toInt(onlyManager, 0).equals(1)) {
                        queryMap.put(UserFieldEnum.IS_EMPLOYEE.getKey(), Constants.IS_EMPLOYEE);
                    }
                }
            }
        }
        Page<Map<String, Object>> userList = this.userMongoService.getUserList(queryMap, 1, 1, pageNumber, header, false);
        log.debug("查询人员数据完成, 当前时间 -> [{}]", stopWatch.toString());
        stopWatch.stop();
        return userList;
    }

    /**
     * 导出成员列表
     *
     * @param queryMap queryMap
     */
    @Async("asyncGenerateExcelExecutor")
    public void exportUserList(Map<String, Object> queryMap, String tableName, String uuid, String redisRepeatKey, HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 组织类型
        Object o = queryMap.get(FieldNameConfig.ORG_TYPE);
        Integer orgType = header.getOrgType();
        if (Objects.nonNull(o) && StringUtils.isNotBlank(Convert.toStr(o))) {
            orgType = Convert.toInt(o);
        }
        List<TableConfigForm> formList = this.formService.tableConfigList(1, 2, orgType, header.getRegionId());

        List<String> tableHeader = new ArrayList<>();
        formList.forEach(form -> tableHeader.add(form.getAlias()));

        List<List<String>> itemList = new ArrayList<>();
        itemList.add(tableHeader);

        int pages = 100;
        for (int i = 1; i <= pages; i++) {
            PageNumber pageNumber = new PageNumber(i, 1000);
            Page<Map<String, Object>> pageData = this.userMongoService.getUserList(queryMap, 1, 2, pageNumber, header, false);
            if (i == 1) {
                pages = pageData.getPages();
                RateUtils.build(Convert.toInt(pageData.getTotal()), uuid);
            }
            pageData.forEach(item -> {
                List<String> row = new ArrayList<>();
                formList.forEach(form -> row.add(Convert.toStr(item.get(form.getFieldName()))));
                itemList.add(row);
                RateUtils.auto(uuid);
            });
        }

        AsyncFileDownUtils.asyncGenerateExcel(this.redisTemplate, this.restTemplate, serverFileCenter, errors, headers, itemList,
                tableName, tempPath, uuid, redisRepeatKey);

    }

    public Object downloadUserList(HttpServletResponse ignoredResponse, String uuid, String redisRepeatKey) {
        return AsyncFileDownUtils.asyncDownFile(this.errors, this.redisTemplate, uuid, redisRepeatKey);
    }

    /**
     * 人员选择器的人员查询接口
     *
     * @param form form
     * @return page
     */
    public Page<UserOrgResultForm> getUserByCondition(ChooseUserQueryForm form, PageNumber pageNumber, Long regionId, String version) {
        // 如果是V3版本，则查询所有党员数据
        if (StringUtils.isNotBlank(version) && "v3".equalsIgnoreCase(version)) {
            Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
            List<Long> orgIds = Collections.singletonList(orgData.getOrgId());
            form.setOrgIds(orgIds);
        }
        Page<UserOrgResultForm> list;
        setEmployee(form);
        try {
            //替换证件号码和电话为密文
            if (form.getCertNumber() != null) {
                form.setCertNumber(NumEncryptUtils.encrypt(form.getCertNumber(), Constants.CERT_NUMBER_IDENTITY));
            }
            if (form.getPhone() != null) {
                form.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
            }
            //籍贯分隔
            String nativePlace = form.getNativePlace();
            if (org.apache.commons.lang.StringUtils.isNotBlank(nativePlace)) {
                String replace = nativePlace.replace("，", ",");
                List<String> placeList = Arrays.asList(replace.split(","));
                form.setPlaceList(placeList);
            }
            //设置分页属性
            int p = Preconditions.checkNotNull(pageNumber.getPage());
            int r = Preconditions.checkNotNull(pageNumber.getRows());
            list = PageHelper.startPage(p, r).doSelectPage(() -> userOrgAndCorpMapper.findUserByCondition(form, regionId));
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
            throw new ApiException("查询组织人员列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
        return list;
    }

    /**
     * @param form form
     * @title 设置employee
     * <AUTHOR>
     * @updateTime 2022/4/7 10:59 AM
     */
    private void setEmployee(ChooseUserQueryForm form) {
        final Class<? extends ChooseUserQueryForm> formClass = form.getClass();
        final Field[] fields = formClass.getDeclaredFields();
        for (Field field : fields) {
            try {
                //打开私有访问
                field.setAccessible(true);
                //获取属性
                String name = field.getName();
                if (!CHOOSE_USER_FIELDS.contains(name)) {
                    //获取属性值
                    final Object object = field.get(form);
                    if (Objects.nonNull(object)) {
                        log.debug("字段" + name + "的值：" + object);
                        form.setIsEmployee(Constants.IS_EMPLOYEE);
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("ChooseUserQueryForm reflex error", e);
            }
        }
    }

    /**
     * 人员选择器的人员查询接口
     *
     * @param queryMap queryMap
     * @return page
     */
    public Page<Map<String, Object>> getUserByCondition(Map<String, Object> queryMap, HeaderHelper.SysHeader header) {
        Page<Map<String, Object>> list = new Page<>();
        try {
            queryMap.put(UserFieldEnum.ORG_REGION_ID.getKey(), header.getRegionId());
            queryMap.put(UserFieldEnum.ORG_TYPE.getKey(), header.getOrgType());

            String certNumber = Convert.toStr(queryMap.get(FieldNameConfig.USER_CERT_NUMBER));
            String phone = Convert.toStr(queryMap.get(FieldNameConfig.USER_PHONE));
            //替换证件号码和电话为密文
            if (StringUtils.isNotBlank(certNumber)) {
                queryMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY));
            }
            if (StringUtils.isNotBlank(phone)) {
                queryMap.put(FieldNameConfig.USER_PHONE, NumEncryptUtils.encrypt(phone, Constants.PHONE_IDENTITY));
            }
            //籍贯分隔
            String nativePlace = Convert.toStr(queryMap.get(UserFieldEnum.NATIVE_PLACE.getKey()));
            if (StringUtils.isNotBlank(nativePlace)) {
                String replace = nativePlace.replace("，", ",");
                List<String> placeList = Arrays.asList(replace.split(","));
                queryMap.put(UserFieldEnum.NATIVE_PLACE.getKey(), placeList);
            }
            int page = queryMap.get(FieldNameConfig.PAGE) == null ? 1 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE));
            int pageSize = queryMap.get(FieldNameConfig.PAGE_SIZE) == null ? 10 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE_SIZE));
            queryMap.remove(FieldNameConfig.PAGE);
            queryMap.remove(FieldNameConfig.PAGE_SIZE);
            list = this.userMongoService.getUserList(queryMap, 1, 3, new PageNumber(page, pageSize), header, false);
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
        }
        return list;
    }

    /**
     * 通过Excel导入获取用户信息
     *
     * @param form form
     */
    public Map<String, Object> findUserByImport(BatchUserInfoForm form, Integer orgType, Long regionId) {
        Map<String, Object> resultMap = new HashMap<>();
        List<UserInfoBase> baseList = new ArrayList<>();
        BatchUserInfoForm resultForm = new BatchUserInfoForm();
        for (UserInfoBase userInfoBase : form.getDataList()) {
            // 根据姓名和身份证查询是否存在该用户
            Example example = new Example(UserEntity.class);
            example.createCriteria().andEqualTo("certNumber", NumEncryptUtils.encrypt(userInfoBase.getCertNumber(), 1))
                    .andEqualTo("name", userInfoBase.getName())
                    .andEqualTo("status", Constants.STATUS_YES);
            UserEntity userEntity = this.userMapper.selectOneByExample(example);
            if (userEntity != null && userEntity.getUserId() != null) {
                // 校验电话号码是否存在
                example.clear();
                example.createCriteria().andEqualTo("phone", NumEncryptUtils.encrypt(userInfoBase.getPhone(), 2))
                        .andNotEqualTo("userId", userEntity.getUserId());
                int cnt = this.userMapper.selectCountByExample(example);
                if (cnt > 0) {
                    resultMap.put("code", 5601);
                    resultMap.put("name", userInfoBase.getName());
                    resultMap.put("phone", userInfoBase.getPhone());
                    break;
                } else {
                    // 查询用户所在组织
                    List<UserOrgResultForm> list = this.userOrgAndCorpMapper.findUserById(Constants.STATUS_YES, orgType,
                            userEntity.getUserId(), Constants.YES, regionId);
                    if (!CollectionUtils.isEmpty(list)) {
                        UserInfoBase base = new UserInfoBase();
                        base.setUserId(userEntity.getUserId());
                        base.setName(userEntity.getName());
                        base.setPhone(userEntity.getPhoneSecret());
                        base.setCertNumber(userEntity.getCertNumberSecret());
                        base.setOrgId(list.get(0).getOrgId());
                        base.setOrgName(list.get(0).getOrgName());
                        baseList.add(base);
                    } else {
                        resultMap.put("code", 5602);
                        resultMap.put("name", userInfoBase.getName());
                        break;
                    }
                    resultMap.put("code", 1);
                    resultForm.setDataList(baseList);
                    resultMap.put("form", resultForm);
                }
            } else {
                resultMap.put("code", 5603);
                resultMap.put("name", userInfoBase.getName());
                resultMap.put("cert", userInfoBase.getCertNumber());
                break;
            }
        }
        return resultMap;
    }

    /**
     * 根据组织查询组织内党员
     *
     * @param form     查询参数
     * @param page     页码
     * @param pageSize 每页数量
     * @param regionId 区县ID
     * @return List<UserPartyResultForm>
     */
    public List<UserPartyResultForm> findPartyUserByWhere(UserPartyQueryForm form, Integer page, Integer pageSize,
                                                          Long regionId) {
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> this.userOrgAndCorpMapper.findPartyUserByWhere(form,
                Constants.YES,
                Constants.STATUS_YES,
                Constants.YES, this.userOptionConfig.getPoliticalCode(), regionId));
    }

    public OrgUserForm getOrgUserByMap(Map<String, Object> userMap) {
        OrgUserForm userForm = new OrgUserForm();
        userForm.setUserId(this.getLongByMap(userMap, FieldNameConfig.USER_ID));
        userForm.setName(this.getStrByMap(userMap, FieldNameConfig.USER_NAME));
        userForm.setPhone(this.getStrByMap(userMap, FieldNameConfig.USER_PHONE));
        userForm.setPhoneSecret(this.getStrByMap(userMap, FieldNameConfig.USER_PHONE_SECRET));
        userForm.setCertType(this.getIntByMap(userMap, FieldNameConfig.USER_CERT_TYPE));
        userForm.setCertNumber(this.getStrByMap(userMap, FieldNameConfig.USER_CERT_NUMBER));
        userForm.setCertNumberSecret(this.getStrByMap(userMap, FieldNameConfig.USER_CERT_NUMBER_SECRET));
        userForm.setGender(this.getIntByMap(userMap, FieldNameConfig.USER_GENDER));
        userForm.setOrgId(this.getLongByMap(userMap, FieldNameConfig.USER_ORG_ID));
        userForm.setEthnic(this.getIntByMap(userMap, FieldNameConfig.USER_ETHNIC));
        userForm.setCensusType(this.getIntByMap(userMap, FieldNameConfig.USER_CENSUS_TYPE));
        userForm.setEducation(this.getIntByMap(userMap, FieldNameConfig.USER_EDUCATION));
        userForm.setPoliticalType(this.getIntByMap(userMap, FieldNameConfig.USER_POLITICAL_TYPE));
        userForm.setJobGrade(this.getIntByMap(userMap, FieldNameConfig.USER_JOB_GRADE));
        userForm.setPosition(this.getStrByMap(userMap, FieldNameConfig.USER_POSITION));
        userForm.setPositionCode(this.getLongByMap(userMap, FieldNameConfig.USER_POSITION_CODE));
        userForm.setCommunist(this.getIntByMap(userMap, FieldNameConfig.USER_COMMUNIST));
        userForm.setYouthLeague(this.getIntByMap(userMap, FieldNameConfig.USER_YOUTH_LEAGUE));
        userForm.setUnionMember(this.getIntByMap(userMap, FieldNameConfig.USER_UNION_MEMBER));
        userForm.setWomenLeague(this.getIntByMap(userMap, FieldNameConfig.USER_WOMEN_LEAGUE));
        userForm.setJoiningTime(this.getStrByMap(userMap, FieldNameConfig.USER_JOINING_TIME));
        userForm.setIsBind(this.getIntByMap(userMap, FieldNameConfig.USER_IS_BIND));
        userForm.setOrgName(this.getStrByMap(userMap, FieldNameConfig.USER_ORG_NAME));
        userForm.setOldOrgName(this.getStrByMap(userMap, FieldNameConfig.USER_OLD_ORG_NAME));
        userForm.setJoiningPositiveTime(this.getStrByMap(userMap, FieldNameConfig.USER_JOINING_POSITIVE_TIME));
        userForm.setIsLose(this.getIntByMap(userMap, FieldNameConfig.USER_IS_LOSE));
        userForm.setJoinPartyType(this.getIntByMap(userMap, FieldNameConfig.USER_JOIN_PARTY_TYPE));
        userForm.setJoinType(this.getIntByMap(userMap, FieldNameConfig.USER_JOIN_TYPE));
        userForm.setJoinPartyTime(this.getStrByMap(userMap, FieldNameConfig.USER_JOIN_PARTY_TIME));
        userForm.setBirthday(this.getStrByMap(userMap, FieldNameConfig.USER_BIRTHDAY));
        userForm.setNativeProvince(this.getIntByMap(userMap, FieldNameConfig.USER_NATIVE_PROVINCE));
        userForm.setNativeCity(this.getIntByMap(userMap, FieldNameConfig.USER_NATIVE_CITY));
        userForm.setFlag(this.getStrByMap(userMap, "flag"));
        userForm.setJobNumber(this.getStrByMap(userMap, FieldNameConfig.USER_JOB_NUMBER));
        userForm.setAddress(this.getStrByMap(userMap, FieldNameConfig.ADDRESS));
        userForm.setHeadUrl(this.getStrByMap(userMap, FieldNameConfig.HEAD_URL));
        userForm.setIsFlow(this.getIntByMap(userMap, FieldNameConfig.USER_IS_FLOW));
        userForm.setSequence(this.getIntByMap(userMap, FieldNameConfig.USER_SEQUENCE));
        return userForm;
    }

    private String getStrByMap(Map<String, Object> userMap, String key) {
        return Convert.toStr(userMap.get(key));
    }

    private Integer getIntByMap(Map<String, Object> userMap, String key) {
        return Convert.toInt(userMap.get(key));
    }

    private Long getLongByMap(Map<String, Object> userMap, String key) {
        return Convert.toLong(userMap.get(key));
    }

    private void addHighlight(Long userId, String time, String desc, HeaderHelper.SysHeader header) {
        UserHighlightForm form = new UserHighlightForm();
        form.setType(1);
        form.setUserId(userId);
        form.setNodeTime(time);
        form.setDesc(desc);
        this.userHighlightService.addUserHighlight(form, header);
    }

    /**
     * 通过Excel导入获取用户信息
     *
     * @param baseList baseList
     */
    public BatchResultForm findUserByNewImport(List<UserInfoBase> baseList, Integer orgType, Long regionId) {
        BatchResultForm result = new BatchResultForm();
        List<UserInfoBase> successList = new ArrayList<>();
        List<BatchForm> failList = new ArrayList<>();
        for (UserInfoBase userInfoBase : baseList) {
            // 如果身份证为空，则根据用户姓名和电话号码查询用户
            if (StringUtils.isBlank(userInfoBase.getCertNumber())) {
                Example example = new Example(UserEntity.class);
                example.createCriteria().andEqualTo("phone", NumEncryptUtils.encrypt(userInfoBase.getPhone(), 2))
                        .andEqualTo("name", userInfoBase.getName())
                        .andEqualTo("status", Constants.STATUS_YES);
                UserEntity userEntity = this.userMapper.selectOneByExample(example);
                if (Objects.isNull(userEntity)) {
                    BatchForm failForm = new BatchForm();
                    failForm.setMsg("人员不存在");
                    failForm.setPhone(userInfoBase.getPhone());
                    failForm.setName(userInfoBase.getName());
                    failList.add(failForm);
                    continue;
                }
                List<UserOrgResultForm> list = this.userOrgAndCorpMapper.findUserById(Constants.STATUS_YES, orgType,
                        userEntity.getUserId(), Constants.YES, regionId);
                if (!CollectionUtils.isEmpty(list)) {
                    UserInfoBase base = new UserInfoBase();
                    base.setUserId(userEntity.getUserId());
                    base.setName(userEntity.getName());
                    base.setPhone(userEntity.getPhoneSecret());
                    base.setCertNumber(userEntity.getCertNumberSecret());
                    base.setOrgId(list.get(0).getOrgId());
                    base.setOrgName(list.get(0).getOrgName());
                    successList.add(base);
                } else {
                    BatchForm failForm = new BatchForm();
                    failForm.setMsg("人员不是组织成员");
                    failForm.setPhone(userInfoBase.getPhone());
                    failForm.setName(userInfoBase.getName());
                    failList.add(failForm);
                }
            } else {
                // 根据姓名和身份证查询是否存在该用户
                Example example = new Example(UserEntity.class);
                example.createCriteria().andEqualTo("certNumber", NumEncryptUtils.encrypt(userInfoBase.getCertNumber(), 1))
                        .andEqualTo("name", userInfoBase.getName())
                        .andEqualTo("status", Constants.STATUS_YES);
                UserEntity userEntity = this.userMapper.selectOneByExample(example);
                if (userEntity != null && userEntity.getUserId() != null) {
                    // 校验电话号码是否存在
                    example.clear();
                    example.createCriteria().andEqualTo("phone", NumEncryptUtils.encrypt(userInfoBase.getPhone(), 2))
                            .andNotEqualTo("userId", userEntity.getUserId());
                    int cnt = this.userMapper.selectCountByExample(example);
                    if (cnt > 0) {
                        BatchForm failForm = new BatchForm();
                        failForm.setMsg("电话号码已被其他用户使用");
                        failForm.setPhone(userInfoBase.getPhone());
                        failForm.setName(userInfoBase.getName());
                        failList.add(failForm);
                    } else {
                        // 查询用户所在组织
                        List<UserOrgResultForm> list = this.userOrgAndCorpMapper.findUserById(Constants.STATUS_YES, orgType,
                                userEntity.getUserId(), Constants.YES, regionId);
                        if (!CollectionUtils.isEmpty(list)) {
                            UserInfoBase base = new UserInfoBase();
                            base.setUserId(userEntity.getUserId());
                            base.setName(userEntity.getName());
                            base.setPhone(userEntity.getPhoneSecret());
                            base.setCertNumber(userEntity.getCertNumberSecret());
                            base.setOrgId(list.get(0).getOrgId());
                            base.setOrgName(list.get(0).getOrgName());
                            successList.add(base);
                        } else {
                            BatchForm failForm = new BatchForm();
                            failForm.setMsg("人员不是组织成员");
                            failForm.setPhone(userInfoBase.getPhone());
                            failForm.setName(userInfoBase.getName());
                            failList.add(failForm);
                        }
                    }
                } else {
                    BatchForm failForm = new BatchForm();
                    failForm.setMsg("人员不存在");
                    failForm.setPhone(userInfoBase.getPhone());
                    failForm.setName(userInfoBase.getName());
                    failList.add(failForm);
                }
            }
        }
        result.setSuccessList(successList);
        result.setFailList(failList);
        return result;
    }

    /**
     * 查询-注意这里加了缓存后，page的属性获取不到，不能加。
     *
     * @param regionId regionId
     * @return page
     */
    public Page<ScoreUserOrgInfo> findUserOrgInfo(Long regionId, int page, int pageSize) {
        List<Long> testOrg = testOrgConfig.testOrgIds(regionId);
        return PageHelper.startPage(page, pageSize).doSelectPage(() -> userMapper.findUserOrgInfo(regionId, testOrg));
    }

    public List<UserInfoBase> findUserPlaintext(HeaderHelper.SysHeader header, Long orgId) {
        List<UserInfoBase> result = this.userMapper.findUserPlaintext(header.getRegionId(), orgId);
        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(base -> {
                // 解密，生成明文
                try {
                    String phonePlaintext = NumEncryptUtils.decrypt(base.getPhone(), base.getPhoneSecret());
                    base.setPhone(phonePlaintext);
                    String certPlaintext = NumEncryptUtils.decrypt(base.getCertNumber(), base.getCertNumberSecret());
                    base.setCertNumber(certPlaintext);
                } catch (Exception e) {
                    log.error("解密失败[{}]", e.getMessage(), e);
                }
            });
        }
        return result;
    }

    /**
     * 新增党籍管理
     */
    public void addMembership(MembershipEntity membershipEntity){
        membershipMapper.insert(membershipEntity);
    }

    /**
     * 查询党籍管理列表
     * @param headers
     * @param name
     * @param politicalType
     * @param leaveType
     * @param page
     * @param pageSize
     * @return
     */
    public List<MembershipEntity> findMembership(HttpHeaders headers, String name, Integer politicalType, Integer leaveType, Integer page, Integer pageSize) {
        Example example = new Example(MembershipEntity.class);
        example.selectProperties("membershipId","orgId","userId","name","politicalType","leaveType");
        example.setOrderByClause("create_time desc"); // 添加时间倒序排序
        Example.Criteria criteria = example.createCriteria();
        if (name!=null && !name.isEmpty()) {
            criteria.andLike("name", "%" + name + "%");
        }
        criteria.andEqualTo("leaveType",leaveType);
        if (politicalType != null) {
            if (politicalType != 0) {
                criteria.andEqualTo("politicalType", politicalType);
            } else{
                criteria.andIsNull("politicalType");
            }
        }
        return PageHelper.startPage(page,pageSize).doSelectPage(()->membershipMapper.selectByExample(example));
    }

    /**
     * 查询党籍管理详情
     * @param headers
     * @param userId
     * @return
     */
    public UserForm findMembershipDetails(HttpHeaders headers, Long userId) {
        Example example = new Example(MembershipEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId",userId);
        List<MembershipEntity> membershipEntities = membershipMapper.selectByExample(example);
        UserForm userForm = new UserForm();
        for (MembershipEntity membershipEntity : membershipEntities) {
            userForm.setUserId(userId);
            userForm.setPhone(null);
            userForm.setTagId(null);
            userForm.setGroupInfo(null);
            userForm.setName(membershipEntity.getName());
            userForm.setPhoneSecret(null);
            userForm.setCertType(membershipEntity.getCertType());
            userForm.setCertNumber(null);
            userForm.setCertNumberSecret(null);
            userForm.setJobNumber(membershipEntity.getJobNumber());
            userForm.setPosition(membershipEntity.getPosition());
            userForm.setEmail(membershipEntity.getEmail());
            userForm.setEntryDate(membershipEntity.getEntryDate());
            userForm.setGender(membershipEntity.getGender());
            userForm.setCensusType(membershipEntity.getCensusType());
            userForm.setNationality(membershipEntity.getNationality());
            userForm.setNativeProvince(membershipEntity.getNativeProvince());
            userForm.setNativeCity(membershipEntity.getNativeCity());
            userForm.setMarriage(membershipEntity.getMarriage());
            userForm.setEducation(membershipEntity.getEducation());
            userForm.setPoliticalType(membershipEntity.getPoliticalType());
            userForm.setEthnic(membershipEntity.getEthnic());
            userForm.setAddress(membershipEntity.getAddress());
            userForm.setDepartmentId(null);
            userForm.setDepartmentName(null);
            userForm.setDepName(null);
            userForm.setDepId(null);
            userForm.setJobGrade(null);
            userForm.setYouthLeague(null);
            userForm.setUnionMember(null);
            userForm.setWomenLeague(null);
            userForm.setIsLose(null);
            userForm.setPositionCode(null);
            userForm.setBirthday(membershipEntity.getBirthday());
            userForm.setJoinPartyTime(null);
            userForm.setJoiningPostiveTime(null);
            userForm.setJoinPartyType(null);
            userForm.setJoinType(null);
            userForm.setOldOrgId(null);
            userForm.setOldOrgName(null);
            userForm.setJoiningTime(membershipEntity.getJoiningTime());
            userForm.setPositiveJoinTime(null);
            userForm.setIsBind(null);
            userForm.setOrgId(membershipEntity.getOrgId());
            userForm.setOrgName(membershipEntity.getOrgName());
            userForm.setIsFlow(membershipEntity.getIsFlow());
            userForm.setTitle(membershipEntity.getTitle());
            userForm.setHeadUrl(membershipEntity.getHeadUrl());
            userForm.setHandSign(membershipEntity.getHandSign());
            userForm.setSequence(membershipEntity.getSequence());
        }
        return userForm;
    }
}
