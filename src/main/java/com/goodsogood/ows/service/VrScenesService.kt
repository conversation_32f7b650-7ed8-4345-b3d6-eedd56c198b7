package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.OrganizationMapper
import com.goodsogood.ows.mapper.VrScenesMapper
import com.goodsogood.ows.model.db.VrScenesEntity
import com.goodsogood.ows.model.vo.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import tk.mybatis.mapper.entity.Example
import java.time.LocalDateTime

@Service
class VrScenesService(@Autowired val errors: Errors,
                      @Autowired val vrScenesMapper: VrScenesMapper,
                      @Autowired val organizationMapper: OrganizationMapper,
                      @Autowired val pushExcellenCriterionService: PushExcellenCriterionService) {

    companion object {
        private const val SUCCESS = "操作成功"
    }

    val log: Logger = LoggerFactory.getLogger(VrScenesService::class.java)

    /**
     * scenesId
     */
    fun add(form: VrAddForm, headers: HttpHeaders) : String {
        log.debug("新增VR场景 -> $form")
        val myHeader = HeaderHelper.buildMyHeader(headers)
        // 暂时一个组织只能上传一个VR视频
        checkOrgVr(form.orgId)
        val entity = VrScenesEntity(
            orgId = form.orgId,
            name = form.name,
            description = form.description,
            cover = form.cover,
            url = form.url,
            status = 1,
            createTime = LocalDateTime.now(),
            createUser = myHeader.userId
        )
        vrScenesMapper.insert(entity)
        // 发起推优审批
        val org = organizationMapper.selectByPrimaryKey(form.orgId)
        pushExcellenCriterionService.addPushExcellen(null, mutableListOf(entity.scenesId), 4, form.orgId, org.name)
        return SUCCESS
    }


    fun addPositions(form: VrAddForm, headers: HttpHeaders) : Int {
        log.debug("新增VR场景 -> $form")
        val myHeader = HeaderHelper.buildMyHeader(headers)
        val entity = VrScenesEntity(
            orgId = form.orgId,
            name = form.name,
            description = form.description,
            cover = form.cover,
            url = form.url,
            status = 1,
            createTime = LocalDateTime.now(),
            createUser = myHeader.userId
        )
        vrScenesMapper.insert(entity)
        return vrScenesMapper.selectByEntity(form.orgId,form.name);
    }
    /**
     * 更新VR场景
     */
    fun update(form: VrUpdateForm, headers: HttpHeaders) : String {
        log.debug("更新VR场景 -> $form")
        val myHeader = HeaderHelper.buildMyHeader(headers)
        val entity = vrScenesMapper.selectByPrimaryKey(form.scenesId)
        if(entity != null) {
            val copy = entity.copy(
                name = form.name,
                description = form.description,
                cover = form.cover,
                url = form.url,
                status = 1, // 更新需要重新提起审批
                updateTime = LocalDateTime.now(),
                lastChangeUser = myHeader.userId
            )
            vrScenesMapper.updateByPrimaryKey(copy)
            // 发起推优审批
            val org = organizationMapper.selectByPrimaryKey(form.orgId)
            pushExcellenCriterionService.addPushExcellen(null, mutableListOf(entity.scenesId), 4, form.orgId, org.name)
        }
        return SUCCESS
    }

    /**
     * 删除VR场景
     */
    fun del(scenesId: Long, headers: HttpHeaders) : String {
        log.debug("删除VR场景 -> $scenesId")
        vrScenesMapper.deleteByPrimaryKey(scenesId)
        // 删除推优
        pushExcellenCriterionService.cancelPushExcellen(4, null, scenesId)
        return SUCCESS
    }

    /**
     * 获取VR场景
     */
    fun get(scenesId: Long) : VrUpdateForm {
        log.debug("获取VR场景 -> $scenesId")
        val entity = vrScenesMapper.selectByPrimaryKey(scenesId)
        val organizationEntity = organizationMapper.selectByPrimaryKey(entity?.orgId)
        return VrUpdateForm(entity?.scenesId,
                            entity?.orgId,
                            organizationEntity?.name,
                            entity?.name,
                            entity?.description,
                            entity?.cover,
                            entity?.url,
                            entity?.status)
    }

    /**
     * @title 根据组织ID查询VR
     * <AUTHOR>
     * @param
     * @updateTime 2022/3/30 11:29 AM
     * @return
     * @throws
     */
    fun getByOrg(orgId: Long) : VrUpdateForm {
        log.debug("根据组织ID查询VR -> $orgId")
        val form = VrUpdateForm()
        val example = Example(VrScenesEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("orgId", orgId)
        val list = vrScenesMapper.selectByExample(example)
        if (list.isNotEmpty()) {
            val entity = list[0]
            val organizationEntity = organizationMapper.selectByPrimaryKey(entity?.orgId)
            form.scenesId = entity.scenesId
            form.orgId = entity.orgId
            form.orgName = organizationEntity.name
            form.name = entity.name
            form.description = entity.description
            form.cover = entity.cover
            form.url = entity.url
            form.status = entity.status
        }
        return form
    }

    /**
     * 获取VR场景列表
     */
    fun list(form: VrQueryForm): Page<VrResultForm> {
        log.debug("获取VR场景列表 -> $form")
        return PageHelper.startPage<VrResultForm>(form.page, form.pageSize)
            .doSelectPage {
                vrScenesMapper.selectVRScenesByOrg(form.orgId)
            }
    }

    /**
     * 根据ids获取VR场景列表
     */
    fun listByIds(ids: List<Long>): List<VrResultForm> {
        log.debug("根据ids获取VR场景列表 -> $ids")
        return vrScenesMapper.selectVRByIds(ids)
    }

    /**
     * 获取VR场景统计数据
     */
    fun statisticalData(orgId: Long?) : StatisticalDataForm {
        log.debug("获取VR场景统计数据 -> $orgId")
        val orgNum = vrScenesMapper.getVrOrgNum(orgId)
        val vrNum = vrScenesMapper.getVrNum(orgId)
        return StatisticalDataForm(orgNum, vrNum)
    }

    private fun checkOrgVr(orgId: Long?, scenesId: Long? = null) {
        val example = Example(VrScenesEntity::class.java)
        val criteria = example.createCriteria()
        criteria.andEqualTo("orgId", orgId)
        if(scenesId != null) {
            criteria.andNotEqualTo("scenesId", scenesId)
        }
        val i = vrScenesMapper.selectCountByExample(example)
        if (i > 0) {
            throw ApiException("组织$orgId 已经上传过VR视频",
                Result<Any>(errors, 40100, HttpStatus.OK.value(), "已上传过VR视频")
            )
        }
    }

    /**
     * 更新VR状态
     * @param id 主键ID
     * @param status -1:取消审核  0:审核失败   1.待审核  2.审核成功  空：原始态
     */
    fun updateVRStatus(id: Long? = null, status: Int = 1, lastChangeUser: Long? = null) {
        log.debug("更新VR状态id: $id, 更新状态: $status")
        if (id != null) {
            val scenes = vrScenesMapper.selectByPrimaryKey(id)
            scenes.status = status
            scenes.updateTime = LocalDateTime.now()
            scenes.lastChangeUser = lastChangeUser
            vrScenesMapper.updateByPrimaryKey(scenes)
        }
    }
}