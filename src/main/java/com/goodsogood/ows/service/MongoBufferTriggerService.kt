package com.goodsogood.ows.service.dss

import com.github.phantomthief.collection.BufferTrigger
import com.github.phantomthief.collection.impl.MultiIntervalTriggerStrategy
import com.goodsogood.ows.model.mongo.MongoBasicBean
import com.goodsogood.ows.model.mongo.Org
import com.goodsogood.ows.model.mongo.User
import com.goodsogood.ows.service.MongoService
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy


@Service
class MongoBufferTriggerService(@Autowired val mongoService: MongoService) {

    val log: Logger = LoggerFactory.getLogger(MongoBufferTriggerService::class.java)

    lateinit var bufferTrigger: BufferTrigger<MongoBasicBean>

    @PostConstruct
    fun initBufferTrigger() {

        bufferTrigger = BufferTrigger.simple<MongoBasicBean, Set<MongoBasicBean>>()
                .triggerStrategy(MultiIntervalTriggerStrategy()
                        .on(5, TimeUnit.SECONDS, 1)
                        .on(3, TimeUnit.SECONDS, 50)
                        .on(1, TimeUnit.SECONDS, 200))
                .consumer(this::insert)
                .enableBackPressure()
                .maxBufferCount(100_000)
                .build()
    }

    fun insert(list: Collection<MongoBasicBean>) {
        log.debug("导入数据数量 -> [${list.size}]")
        val userList = mutableListOf<User>()
        val orgList = mutableListOf<Org>()
        list.forEach{
            when (it) {
                is User -> {
                    userList.add(it)
                }
                is Org -> {
                    orgList.add(it)
                }
                else -> log.error("传入的参数有问题! -> [$it]")
            }
        }
        if (userList.size > 0) {
            userList.forEach { user ->
                this.mongoService.insertOrUpdateData(User::class.java, user, mapOf("userId" to user.userId))
            }
        }
        if (orgList.size > 0) {
            orgList.forEach { org ->
                this.mongoService.insertOrUpdateData(Org::class.java, org, mapOf("organizationId" to org.organizationId))
            }
        }
        log.debug("导入数据数量 -> [${list.size}] 完成！")
    }

    fun addBuffer(any: MongoBasicBean) {
        this.bufferTrigger.enqueue(any)
    }

    @PreDestroy
    fun beforeDestroy() {
        this.bufferTrigger.manuallyDoTrigger()
    }
}