package com.goodsogood.ows.service;

import com.goodsogood.ows.mapper.FieldRuleMapper;
import com.goodsogood.ows.model.vo.FieldRuleForm;
import com.goodsogood.ows.model.vo.FieldRuleVo;
import com.goodsogood.ows.model.vo.QueryConfigForm;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @program: ows-user-center2
 * @description: 规则验证服务
 * @author: Mr.<PERSON><PERSON><PERSON>
 * @create: 2021-02-08 09:41
 **/
@Service
@Log4j2
public class FieldRuleService {

    private final FieldRuleMapper fieldRuleMapper;

    public FieldRuleService(FieldRuleMapper fieldRuleMapper){
        this.fieldRuleMapper = fieldRuleMapper;
    }

    /**
     * 根据验证规则id 返回字段验证信息
     * @return
     */
    public FieldRuleVo getFileRuleInfo(QueryConfigForm queryConfigForm){
        if(null==queryConfigForm.getFieldRuleId()){
            return null ;
        }
        FieldRuleForm fieldRule = fieldRuleMapper.findFieldRuleByKey(queryConfigForm.getFieldRuleId());
        return getFieRule(fieldRule,queryConfigForm);
    }

    /**
     * 进行单个规则处理
     * @param fieldRule
     * @return
     */
    private FieldRuleVo getFieRule(FieldRuleForm fieldRule,QueryConfigForm queryConfigForm){
        if(fieldRule==null){
            return null;
        }
        if(!StringUtils.isEmpty(fieldRule.getDateFormat())){
            queryConfigForm.getAttrMap().put("mode",fieldRule.getDateFormat());
        }
        //最小值
        if(!StringUtils.isEmpty(fieldRule.getMinValue())){
            queryConfigForm.getAttrMap().put("min",fieldRule.getMinValue());
        }
        //最大值
        if(!StringUtils.isEmpty(fieldRule.getMaxValue())){
            queryConfigForm.getAttrMap().put("max",fieldRule.getMaxValue());
        }
        FieldRuleVo rule = new FieldRuleVo();
        if(!StringUtils.isEmpty(fieldRule.getMinLength())){
            rule.setMin(fieldRule.getMinLength());
        }
        if(!StringUtils.isEmpty(fieldRule.getMaxLength())){
            rule.setMax(fieldRule.getMaxLength());
        }
        //正则表达式
        if(!StringUtils.isEmpty(fieldRule.getRegular())){
            rule.setPattern(fieldRule.getRegular());
            rule.setPatternMessage(fieldRule.getPatternMessage());
        }
        //消息提示
        rule.setRequired(fieldRule.getRequired()==1);
        if(!StringUtils.isEmpty(fieldRule.getMessage())){
            rule.setMessage(fieldRule.getMessage());
        }
        //前端验证方法信息
        if(!StringUtils.isEmpty(fieldRule.getFuncName())){
            rule.setFuncName(fieldRule.getFuncName());
        }
        //设置正则提示信息
        if(!StringUtils.isEmpty(fieldRule.getPatternMessage())){
            rule.setPatternMessage(fieldRule.getPatternMessage());
        }
        return rule;
    }

}
