package com.goodsogood.ows.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.github.pagehelper.Page;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.mongo.ThirdToken;
import com.goodsogood.ows.model.mongo.User;
import com.goodsogood.ows.model.mongo.UserOrg;
import com.goodsogood.ows.model.vo.*;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.util.*;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/7/6
 */

@Service
@Log4j2
public class JoinCommunityService {

    private final CommunityUserMapper communityUserMapper;
    private final UserService userService;
    private final Errors errors;
    private final OrganizationMapper organizationMapper;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final UserMongoService userMongoService;
    private final OpenService openService;
    private final OrgMongoService orgMongoService;
    private final UserOrgCacheCallService userOrgCacheCallService;
    private final SimpleApplicationConfigHelper applicationConfigHelper;
    private final OrgService orgService;
    private final RoleService roleService;
    private final UserMapper userMapper;
    private final MyMongoTemplate mongoTemplate;
    private final TagMapper tagMapper;

    @Autowired
    @Lazy
    public JoinCommunityService(CommunityUserMapper communityUserMapper,
                                UserService userService, Errors errors,
                                OrganizationMapper organizationMapper,
                                UserOrgAndCorpMapper userOrgAndCorpMapper, UserMongoService userMongoService,
                                OpenService openService, OrgMongoService orgMongoService,
                                UserOrgCacheCallService userOrgCacheCallService,
                                SimpleApplicationConfigHelper applicationConfigHelper,
                                OrgService orgService, RoleService roleService,
                                UserMapper userMapper, MyMongoTemplate mongoTemplate,
                                TagMapper tagMapper) {
        this.communityUserMapper = communityUserMapper;
        this.userService = userService;
        this.errors = errors;
        this.organizationMapper = organizationMapper;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.userMongoService = userMongoService;
        this.openService = openService;
        this.orgMongoService = orgMongoService;
        this.userOrgCacheCallService = userOrgCacheCallService;
        this.applicationConfigHelper = applicationConfigHelper;
        this.orgService = orgService;
        this.roleService = roleService;
        this.userMapper = userMapper;
        this.mongoTemplate = mongoTemplate;
        this.tagMapper = tagMapper;
    }

    public CommunityCheckForm check(HeaderHelper.SysHeader sysHeader, Long userId) throws Exception {
        CommunityCheckForm communityCheckForm = communityUserMapper.checkUser(sysHeader.getRegionId(), userId);
        if (null == communityCheckForm) {
            throw new ApiException("用户不存在", new Result<>(errors, 199, HttpStatus.OK.value()));
        }

        //如果是系统用户 如果没有在community_user表中产生过数据 则尝试再次获取用户表信息
        if (communityCheckForm.getIsVisitor().equals(2)
                && StringUtils.isBlank(communityCheckForm.getPhone())) {
            CommunityUserEntity orgInfoByUser = communityUserMapper.findOrgInfoByUser(userId, sysHeader.getRegionId(), sysHeader.getOrgType());
            UserEntity userEntity = userService.selectOne(userId);
            if (null != orgInfoByUser) {
                communityCheckForm.setOrgName(orgInfoByUser.getOrgName());
                communityCheckForm.setCompanyName(orgInfoByUser.getCompanyName());
                communityCheckForm.setAddress(orgInfoByUser.getAddress());
                communityCheckForm.setPhone(NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()));
            }
        }

        List<UserOrg> orgByUserRoleType = roleService.getOrgByUserRoleType(sysHeader.getRegionId(), sysHeader.getUserId(), Constants.ROLE_TYPE_COMMUNITY_ADMIN);
        if (!CollectionUtils.isEmpty(orgByUserRoleType)) {
            //与李兰玉沟通确定一个用户只会绑定一个进小区组织管理员
            if (orgByUserRoleType.size() > 1) {
                throw new ApiException("查询到管理了多个小区,请保证一个用户只管理一个小区!", new Result<>(errors, 33005, HttpStatus.OK.value()));
            }
            communityCheckForm.setMeIsAdmin(1);
            communityCheckForm.setManageOrgId(orgByUserRoleType.get(0).getOrgId());
            communityCheckForm.setManageOrgName(orgByUserRoleType.get(0).getOrgName());
        } else {
            communityCheckForm.setMeIsAdmin(2);
        }

        //如果是系统用户未绑定 则查出用户名和电话
        if (communityCheckForm.getIsVisitor().equals(2) &&
                (null == communityCheckForm.getJoinType() || communityCheckForm.getJoinType().equals(0))) {
            UserEntity userEntity = userService.selectOne(userId);
            communityCheckForm.setUserName(userEntity.getName());
            communityCheckForm.setPhone(NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()));
        }

        //如果存在加入的组织Id 则尝试查询用户标签
        if (null != communityCheckForm.getJoinOrgId()) {
            String tags = communityUserMapper.findUserTags(sysHeader.getRegionId(), userId, communityCheckForm.getJoinOrgId());
            if (StringUtils.isNotBlank(tags)) {
                Example tagExample = new Example(TagEntity.class);
                tagExample.createCriteria()
                        .andIn("tagId", Arrays.asList(tags.split(",")))
                        .andEqualTo("status", 1);
                tagExample.orderBy("seq").desc();
                tagExample.selectProperties("tagId", "name");
                List<TagEntity> tagEntities = tagMapper.selectByExample(tagExample);
                if (!CollectionUtils.isEmpty(tagEntities)) {
                    List<CommunityCheckForm.CommunityUserTagForm> tagForms = new ArrayList<>(tagEntities.size());
                    tagEntities.forEach(x -> tagForms.add(new CommunityCheckForm.CommunityUserTagForm(x.getTagId(), x.getName())));
                    communityCheckForm.setTags(tagForms);
                }
            }
        }

        //如果是管理员 如果没有加入小区 则把管理员加入进小区
        CommunityCheckForm communityCheckManageForm = communityUserMapper.checkUser(sysHeader.getRegionId(), sysHeader.getUserId());
        if (null != communityCheckManageForm
                && null == communityCheckManageForm.getJoinOrgId() && !CollectionUtils.isEmpty(orgByUserRoleType)) {
            joinPeople(sysHeader, sysHeader.getUserId(), 1, "");
            return check(sysHeader,userId);
//            Date date = new Date();
//            CommunityUserEntity communityUserEntity = new CommunityUserEntity();
//            communityUserEntity.setJoinType(1);
//            communityUserEntity.setJoinTime(date);
//            communityUserEntity.setJoinOrgId(orgByUserRoleType.get(0).getOrgId());
//            communityUserEntity.setJoinOrgName(orgByUserRoleType.get(0).getOrgName());
//            communityUserEntity.setAddress(communityCheckForm.getAddress());
//            communityUserEntity.setPhone(communityCheckForm.getPhone());
//            communityUserEntity.setRegionId(sysHeader.getRegionId());
//            communityUserEntity.setUserId(userId);
//            communityUserEntity.setUserName(communityCheckForm.getUserName());
//            communityUserEntity.setCreateUser(-998L);
//            communityUserEntity.setCreateTime(date);
//            communityUserEntity.setCheckUser(userId);
//            communityUserEntity.setStatus(1);
//            communityUserMapper.insert(communityUserEntity);
//
//            communityCheckForm.setJoinOrgId(communityCheckForm.getManageOrgId());
//            communityCheckForm.setJoinOrgName(communityCheckForm.getManageOrgName());
//            communityCheckForm.setJoinTime(date);
//            communityCheckForm.setJoinType(1);
        }

        return communityCheckForm;
    }

    public void getImg(HttpServletResponse response, HeaderHelper.SysHeader sysHeader) throws Exception {
        BufferedImage generate = QrCodeUtil.generate(sysHeader.getUserId().toString(), 300, 300);
        ImgUtil.write(generate, null, response.getOutputStream());
    }

    public void editVisitor(HeaderHelper.SysHeader sysHeader, CommunityEditForm editForm) {
        UserEntity userEntity = edit(sysHeader, editForm);
        //如果是游客就修改姓名
        userEntity.setName(editForm.getUserName());
        userEntity.setUpdateTime(new Date());
        userEntity.setLastChangeUser(sysHeader.getUserId());
        userMapper.updateByPrimaryKeySelective(userEntity);
        //修改mongo
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userEntity.getUserId()));
        Update update = new Update();
        update.set("name", userEntity.getName());
        mongoTemplate.updateFirst(query, update, User.class);
        //刷新缓存
        userOrgCacheCallService.flushUserInfo(sysHeader.getRegionId(), userEntity, 1);
    }

    @Transactional(rollbackFor = Exception.class)
    public UserEntity edit(HeaderHelper.SysHeader sysHeader, CommunityEditForm editForm) {
        //判断是否为游客 只有游客才能编辑
        UserEntity userEntity = userService.selectOne(sysHeader.getUserId());
        if (null == userEntity) {
            throw new ApiException("用户不存在", new Result<>(errors, 199, HttpStatus.OK.value()));
        }
        if (!userEntity.getIsVisitor().equals(1)) {
            throw new ApiException("只有游客才能编辑!", new Result<>(errors, 33003, HttpStatus.OK.value()));
        }
        Example example = new Example(CommunityUserEntity.class);
        example.createCriteria()
                .andEqualTo("regionId", sysHeader.getRegionId())
                .andEqualTo("userId", sysHeader.getUserId())
                .andEqualTo("status", 1);
        CommunityUserEntity communityUserEntity = communityUserMapper.selectOneByExample(example);
        Date date = new Date();
        //第一次编辑
        if (null == communityUserEntity) {
            communityUserEntity = new CommunityUserEntity();
            BeanUtil.copyProperties(editForm, communityUserEntity);
            communityUserEntity.setRegionId(sysHeader.getRegionId());
            communityUserEntity.setUserId(sysHeader.getUserId());
            communityUserEntity.setCreateUser(sysHeader.getUserId());
            communityUserEntity.setCreateTime(date);
            communityUserEntity.setStatus(1);
            communityUserEntity.setJoinType(0);
            communityUserMapper.insert(communityUserEntity);
        } else {
            BeanUtil.copyProperties(editForm, communityUserEntity);
            communityUserEntity.setChangeUser(sysHeader.getUserId());
            communityUserEntity.setChangeTime(date);
            communityUserMapper.updateByPrimaryKey(communityUserEntity);
        }
        return userEntity;
    }


    /**
     * 加入小区
     */
    public void joinPeople(HeaderHelper.SysHeader sysHeader, Long userId, Integer type, String address) throws Exception {
        UserOrg join = join(sysHeader, userId, type, address);
        //如果同意 则加入到组织小区中
        if (type.equals(1)) {
            insertOrgByUser(userId, join.getOrgId(), sysHeader);
        }
    }

    /**
     * @param userId  加入人userId
     * @param type    1:加入 2:退回
     * @param address 加入时管理员可以编辑用户的地址 如果不为空则覆盖
     */
    @Transactional(rollbackFor = Exception.class)
    public UserOrg join(HeaderHelper.SysHeader sysHeader, Long userId, Integer type, String address) throws Exception {
        List<UserOrg> orgByUserRoleType = roleService.getOrgByUserRoleType(sysHeader.getRegionId(), sysHeader.getUserId(), Constants.ROLE_TYPE_COMMUNITY_ADMIN);
        //是否含有权限
        if (CollectionUtils.isEmpty(orgByUserRoleType)) {
            throw new ApiException("您没有进小区管理权限!", new Result<>(errors, 33004, HttpStatus.OK.value()));
        }
        //与李兰玉沟通确定一个用户只会绑定一个进小区组织管理员
        if (orgByUserRoleType.size() > 1) {
            throw new ApiException("查询到管理了多个小区,请保证一个用户只管理一个小区!", new Result<>(errors, 33005, HttpStatus.OK.value()));
        }

        Long orgId = orgByUserRoleType.get(0).getOrgId();
        OrganizationEntity org = orgService.getOrgById(orgId, sysHeader.getRegionId());

        //验证用户
        CommunityCheckForm check = communityUserMapper.checkUser(sysHeader.getRegionId(), userId);
        UserEntity userEntity = userService.selectOne(userId);
        if (null == check || null == userEntity) {
            throw new ApiException("用户不存在,请填写您的信息!", new Result(errors, 33002, HttpStatus.OK.value()));
        }
        if (check.getJoinType().equals(1) || check.getJoinType().equals(2)) {
            throw new ApiException("该用户已加入小区,无法再加入其他小区!", new Result(errors, 33001, HttpStatus.OK.value()));
        }
        if (check.getIsVisitor().equals(2) && (StringUtils.isBlank(userEntity.getPhone()) ||
                StringUtils.isBlank(userEntity.getPhoneSecret()))) {
            throw new ApiException("手机号码不存在,请补填号码!", new Result(errors, 33006, HttpStatus.OK.value()));
        }

        Date date = new Date();
        Example communityExample = new Example(CommunityUserEntity.class);
        communityExample.createCriteria()
                .andEqualTo("regionId", sysHeader.getRegionId())
                .andEqualTo("userId", userId)
                .andEqualTo("status", 1);
        CommunityUserEntity communityUserEntity = communityUserMapper.selectOneByExample(communityExample);
        communityUserEntity = null == communityUserEntity ?
                communityUserMapper.findOrgInfoByUser(userId, sysHeader.getRegionId(), sysHeader.getOrgType()) : communityUserEntity;
        communityUserEntity.setJoinOrgId(org.getOrganizationId());
        communityUserEntity.setJoinOrgName(org.getName());

        //是否覆盖用户地址
        communityUserEntity.setAddress(StringUtils.isBlank(address) ? communityUserEntity.getAddress() : address);

        //先默认不通过
        communityUserEntity.setJoinType(3);

        //记录加入时间
        communityUserEntity.setJoinTime(date);
        //如果是系统用户
        if (check.getIsVisitor().equals(2)) {
            if (type.equals(1)) {
                communityUserEntity.setJoinType(1);
            }
            if (communityUserEntity.getCommunityId() == null) {
                communityUserEntity.setRegionId(sysHeader.getRegionId());
                communityUserEntity.setUserId(userId);
                communityUserEntity.setCreateUser(sysHeader.getUserId());
                communityUserEntity.setCreateTime(date);
                communityUserEntity.setCheckUser(sysHeader.getUserId());
                communityUserEntity.setStatus(1);
                communityUserEntity.setPhone(NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()));
                communityUserMapper.insert(communityUserEntity);
            } else {
                communityUserEntity.setChangeUser(sysHeader.getUserId());
                communityUserEntity.setChangeTime(date);
                communityUserMapper.updateByPrimaryKey(communityUserEntity);
            }
            //新增进小区表
        } else {//如果是游客
            if (type.equals(1)) {
                communityUserEntity.setJoinType(2);
            }
            communityUserEntity.setCheckUser(sysHeader.getUserId());
            communityUserEntity.setChangeUser(sysHeader.getUserId());
            communityUserEntity.setChangeTime(date);
            communityUserMapper.updateByPrimaryKey(communityUserEntity);
        }
        return orgByUserRoleType.get(0);
    }

    public CommunityUserListForm list(HeaderHelper.SysHeader sysHeader) {
        List<UserOrg> orgByUserRoleType = roleService.getOrgByUserRoleType(sysHeader.getRegionId(), sysHeader.getUserId(), Constants.ROLE_TYPE_COMMUNITY_ADMIN);
        //是否含有权限
        if (CollectionUtils.isEmpty(orgByUserRoleType)) {
            throw new ApiException("您没有进小区管理权限!", new Result<>(errors, 33004, HttpStatus.OK.value()));
        }
        //与李兰玉沟通确定一个用户只会绑定一个进小区组织管理员
        if (orgByUserRoleType.size() > 1) {
            throw new ApiException("查询到管理了多个小区,请保证一个用户只管理一个小区!", new Result<>(errors, 33005, HttpStatus.OK.value()));
        }
        Long orgId = orgByUserRoleType.get(0).getOrgId();
        String orgName = orgByUserRoleType.get(0).getOrgName();

        Map<String, Object> queryMap = new HashMap<>(1);
        queryMap.put("orgList.orgId", orgId);

        CommunityUserListForm communityUserListForm = new CommunityUserListForm();
        communityUserListForm.setOrgId(orgId);
        communityUserListForm.setOrgName(orgName);
        communityUserListForm.setList(communityUserMapper.selectValidUserByOrg(sysHeader.getRegionId(), orgId));
        Page<User> userListByWhere = userMongoService.getUserListByWhere(queryMap, 1, Integer.MAX_VALUE);
        if (!CollectionUtils.isEmpty(communityUserListForm.getList())
                && !CollectionUtils.isEmpty(userListByWhere)) {
            Region.OrgData orgData = this.applicationConfigHelper.getOrgByRegionId(sysHeader.getRegionId());
            Long oid = orgData.getOrgId();
            Map<Long, User> map = new HashMap<>(userListByWhere.size());
            userListByWhere.forEach(x -> {
                map.put(x.getUserId(), x);
            });
            communityUserListForm.getList().forEach(x -> {
                User user = map.get(x.getUserId());
                if (null != user) {
                    Optional<ThirdToken> firstOptional = user.getThirdList().stream().filter(third -> third.getThType().equals(Constants.TH_TOKEN_TYPE_WEIXIN) && third.getOId().equals(oid)).findFirst();
                    if (firstOptional.isPresent()) {
                        ThirdToken thirdToken = firstOptional.get();
                        x.setHeadUrl(thirdToken.getHead());
                    }
                }
            });
        }


        return communityUserListForm;
    }

    /**
     * 逻辑删除
     */
    public void deleteByUserOrg(Long regionId, Long orgId, Long userId, Long changeUserId) {
        communityUserMapper.deleteByUserOrg(regionId, orgId, userId, changeUserId);
    }

    /**
     * 切换人员状态 由退回为初始状态
     */
    public void reset(Long userId, HeaderHelper.SysHeader header) {
        communityUserMapper.reset(userId, header.getUserId(), header.getRegionId());
    }

    /**
     * 查询标签列表
     */
    public List<CommunityCheckForm.CommunityUserTagForm> tagList(Integer type, HeaderHelper.SysHeader sysHeader) {
        Example example = new Example(TagEntity.class);
        example.createCriteria()
                .andEqualTo("regionId", sysHeader.getRegionId())
                .andEqualTo("status", 1)
                .andEqualTo("tagType", type);
        example.orderBy("seq").desc();
        example.selectProperties("tagId", "name");
        List<TagEntity> tagEntities = tagMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(tagEntities)) {
            List<CommunityCheckForm.CommunityUserTagForm> result = new ArrayList<>(tagEntities.size());
            tagEntities.forEach(x -> result.add(new CommunityCheckForm.CommunityUserTagForm(x.getTagId(), x.getName())));
            return result;
        }
        return new ArrayList<>();
    }

    /**
     * 编辑标签
     */
    public void tagEdit(String ids, HeaderHelper.SysHeader sysHeader) {
        Example example = new Example(UserOrgAndCorpEntity.class);
        example.createCriteria()
                .andEqualTo("regionId", sysHeader.getRegionId())
                .andEqualTo("userId", sysHeader.getUserId())
                .andEqualTo("organizationId", sysHeader.getUoid());
        UserOrgAndCorpEntity userOrgAndCorpEntity = userOrgAndCorpMapper.selectOneByExample(example);
        if (null == userOrgAndCorpEntity) {
            throw new ApiException("进入小区后才能编辑个人特征", new Result<>(errors, 33007, HttpStatus.OK.value()));
        }
        userOrgAndCorpEntity.setTagId(ids);
        userOrgAndCorpEntity.setUpdateTime(new Date());
        userOrgAndCorpEntity.setLastChangeUser(sysHeader.getUserId());
        userOrgAndCorpMapper.updateByPrimaryKey(userOrgAndCorpEntity);
    }

    private void insertOrgByUser(Long userId, Long orgId, HeaderHelper.SysHeader sysHeader) {
        Example example = new Example(OrganizationEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("organizationId", orgId);
        criteria.andEqualTo("regionId", sysHeader.getRegionId());
        criteria.andEqualTo("status", Constants.STATUS_YES);
        int count = this.organizationMapper.selectCountByExample(example);
        //判断userOrgCorp是否已经加入了当前用户
        Example userOrgCorpExample = new Example(UserOrgAndCorpEntity.class);
        userOrgCorpExample.createCriteria()
                .andEqualTo("regionId", sysHeader.getRegionId())
                .andEqualTo("organizationId", orgId)
                .andEqualTo("userId", userId);
        //如果已经存在数据则不新增到userOrgCorp
        int i = userOrgAndCorpMapper.selectCountByExample(userOrgCorpExample);
        if (i > 0) {
            return;
        }
        if (count > 0) {
            UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
            entity.setUserId(userId);
            entity.setOrganizationId(orgId);
            entity.setRegionId(sysHeader.getRegionId());
            entity.setCreateTime(new Date());
            entity.setIsEmployee(Constants.NO);
            this.userOrgAndCorpMapper.insert(entity);
            FindOrgListForm form = new FindOrgListForm();
            form.setIdList(Collections.singletonList(orgId));
            form.setRegionId(sysHeader.getRegionId());
            // 同步新增人员Mongo
            this.userMongoService.conversionUser(userId, null, false, sysHeader);
            this.openService.updateOrgUserNum(form);
            this.orgMongoService.conversionOrg(orgId, null, sysHeader);
            this.userOrgCacheCallService.flushUserInfo(sysHeader.getRegionId(), userId, 1);
        } else {
            log.error("当前组织[{}]不在区县[{}]下 -> ", orgId, sysHeader.getRegionId());
            throw new ApiException("组织与区县不匹配", new Result<>(errors, 5630, HttpStatus.OK.value(), "组织与区县不匹配"));
        }
    }
}
