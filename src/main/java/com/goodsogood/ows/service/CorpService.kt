package com.goodsogood.ows.service

import com.github.pagehelper.Page
import com.github.pagehelper.PageHelper
import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.OrganizationMapper
import com.goodsogood.ows.model.db.OrganizationEntity
import com.goodsogood.ows.model.vo.OrgBaseInfo
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpHeaders
import org.springframework.stereotype.Service
import java.util.*

@Service
class CorpService @Autowired constructor(
    val error: Errors,
    val organizationMapper: OrganizationMapper,
    val simpleApplicationConfigHelper: SimpleApplicationConfigHelper
) {

    @Value("\${exclude-org-ids}")
    val excludeOrgIds: String = "1294,5,2409,2417,1222,1255"

    @Value("\${saas.region-label}")
    val label: String = "v4.0.0"

    private val log = LoggerFactory.getLogger(CorpService::class.java)

    /**
     * 获取所有单位
     */
    fun getCorpList(page: Int = 1, pageSize: Int = 10, headers: HttpHeaders): Page<OrgBaseInfo> {
        val header = HeaderHelper.buildMyHeader(headers)
        val corpData = simpleApplicationConfigHelper.getOrgByRegionId(label, header.regionId)
        val corpId = organizationMapper.selectByPrimaryKey(corpData.orgId)?.ownerId ?: -1
        return PageHelper.startPage<OrganizationEntity>(page, pageSize).doSelectPage {
            organizationMapper.getCorpList(
                corpId,
                Constants.CORP_TYPE_BRANCH_CHILD,
                excludeOrgIds.split(",").map { it.toLong() }.toMutableList()
            )
        }
    }
}