package com.goodsogood.ows.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.common.HttpUtils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.MyMongoTemplate;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.mapper.ComponentMapper;
import com.goodsogood.ows.mapper.ComponentOptionMapper;
import com.goodsogood.ows.model.db.ComponentEntity;
import com.goodsogood.ows.model.db.ComponentOptionEntity;
import com.goodsogood.ows.model.vo.*;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2019-07-23 16:53
 **/
@Service
@Log4j2
public class ComponentService {

    private final ComponentMapper componentMapper;
    private final ComponentOptionMapper componentOptionMapper;
    public static final Long createUser=168L;
    private final StringRedisTemplate redisTemplate;
    private final MyMongoTemplate myMongoTemplate;
    private final ComponentOptionService componentOptionService;
    private final Errors errors;
    ObjectMapper objectMapper = new ObjectMapper();



    @Autowired
    public ComponentService(ComponentMapper componentMapper,
                            ComponentOptionMapper componentOptionMapper,
                            StringRedisTemplate redisTemplate,
                            MyMongoTemplate myMongoTemplate, Errors errors,
                            ComponentOptionService componentOptionService) {
        this.componentMapper = componentMapper;
        this.componentOptionMapper=componentOptionMapper;
        this.redisTemplate = redisTemplate;
        this.myMongoTemplate = myMongoTemplate;
        this.errors=errors;
        this.componentOptionService=componentOptionService;
    }

    /**
     * 查询组件列表
     * @return
     */
    public List<ComponentEntity> getComponentList(){
        List<ComponentEntity> componentList = componentMapper.getComponentList();
        return  componentList;
    }

    /**
     *添加组件信息 返回组件Id,与组件下拉选项Id
     * @return
     */
    @Transactional
    public Map<String,Integer> add(ComponentForm componentForm, HttpServletRequest request) {
        ComponentEntity componentEntity = new ComponentEntity();
        componentEntity.setName(componentForm.getName());
        List<ComponentEntity> isExistComponentName = componentMapper.select(componentEntity);
        if(!CollectionUtils.isEmpty(isExistComponentName)){
            throw new ApiException("组件名称已经存在，请重新命名！",
                    new Result<>(errors, 21001, HttpStatus.OK.value()));
        }
        try {
            ComponentOptionEntity componentOptionEntity=new ComponentOptionEntity();
            componentOptionEntity.setCreateTime(new Date());
            componentOptionEntity.setComponentParentId(0L);
            componentOptionEntity.setStatus(1);
            componentOptionEntity.setLevel("0");
            componentOptionEntity.setName("root");
            componentOptionEntity.setSeq(0);
            componentOptionEntity.setCreateUser(HttpUtils.getUid(request, errors));
            componentOptionEntity.setCreateTime(DateTime.now().toDate());
            componentOptionMapper.insert(componentOptionEntity);
            Integer componentOptionId =componentOptionEntity.getComponentOptionId().intValue();

            componentEntity = new ComponentEntity();
            BeanUtils.copyProperties(componentForm,componentEntity);
            componentEntity.setCreateUser(HttpUtils.getUid(request, errors));
            componentEntity.setCreateTime(DateTime.now().toDate());
            componentEntity.setStatus(1);

            componentEntity.setComponentOptionId(componentOptionId.longValue());
            componentMapper.insert(componentEntity);
            Integer componentId = componentEntity.getComponentId().intValue();

            Map<String, Integer> map = new HashMap<>();
            map.put("option_id", componentOptionId);
            map.put("component_id", componentId);
            return  map;
        }catch (Exception ex){
            log.error("添加组件信息发生异常-ex-{}",ex);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new ApiException("添加组库失败", new Result<>(errors, 21002, HttpStatus.OK.value()));
        }
    }

    /**
     * 添加初始化组库信息
     * @return
     */
    @Transactional
    public Map<String,Long> addInitComponent(InitComponentForm initComponentForm) {
        ComponentEntity componentEntity = new ComponentEntity();
        componentEntity.setName(initComponentForm.getName());
        List<ComponentEntity> isExistComponentName = componentMapper.select(componentEntity);
        //验证名称是否相同
        if(!CollectionUtils.isEmpty(isExistComponentName)){
            throwParamApiException("组件名称已经存在，请重新命名！");
        }
        Example example = new Example(ComponentEntity.class);
        example.createCriteria().andEqualTo("optionCode", initComponentForm.getOptionCode());
        List<ComponentEntity> componentEntities = componentMapper.selectByExample(example);
        if(!CollectionUtils.isEmpty(componentEntities)){
            throwParamApiException("optionCode 已经存在，不能进行多次添加");
        }
        try {
            ComponentOptionEntity componentOptionEntity=new ComponentOptionEntity();
            componentOptionEntity.setCreateTime(new Date());
            componentOptionEntity.setComponentParentId(0L);
            componentOptionEntity.setStatus(1);
            componentOptionEntity.setLevel("0");
            componentOptionEntity.setName("root");
            componentOptionEntity.setSeq(0);
            componentOptionEntity.setCreateUser(createUser);
            componentOptionMapper.insert(componentOptionEntity);
            Long componentOptionId =componentOptionEntity.getComponentOptionId();

            componentEntity = new ComponentEntity();
            BeanUtils.copyProperties(initComponentForm,componentEntity);
            componentEntity.setCreateTime(new Date());
            componentEntity.setCreateUser(createUser);
            componentEntity.setStatus(1);
            componentEntity.setCode(initComponentForm.getCode());
            componentEntity.setOptionCode(initComponentForm.getOptionCode()+"");
            componentEntity.setComponentOptionId(componentOptionId.longValue());
            componentMapper.insert(componentEntity);
            Long componentId = componentEntity.getComponentId();
            //拉取数据
            pullComponentData(componentId);
            //添加数据刷新redis缓存
            refreshComponent(componentId,0);
            Map<String, Long> map = new HashMap<>();
            map.put("option_id", componentOptionId);
            map.put("component_id", componentId);
            return  map;
        }catch (Exception ex){
            log.error("添加组件信息发生异常-ex-{}",ex);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new ApiException("添加组库失败", new Result<>(errors, 21002, HttpStatus.OK.value()));
        }
    }

    /**
     * 编辑组件信息
     * @param componentForm
     * @param request
     * @return
     */
    public Integer edit(ComponentForm componentForm, HttpServletRequest request) {
        ComponentEntity componentEntity = new ComponentEntity();
        componentEntity.setName(componentForm.getName());
        componentEntity.setComponentId(componentForm.getComponentId());
        Integer isExistComponentName = componentMapper.selectIsExistComponent(componentEntity);
        if(isExistComponentName>0){
            throw new ApiException("组件名称已经存在，请重新命名！",
                    new Result<>(errors, 21001, HttpStatus.OK.value()));
        }
        Integer componentCode = componentOptionService.getComponentCode(componentForm.getComponentId());
        if(componentCode!=1){
            if(null!=componentForm.getLevel()){
                throwParamApiException("系统初始化组件不能修改层级关系！");
            }
        }
        BeanUtils.copyProperties(componentForm,componentEntity);
        componentEntity.setLastChangeUser(HttpUtils.getUid(request, errors));
        componentEntity.setUpdateTime(new Date());
        refreshComponent(componentForm.getComponentId(),0);
        //自定义
        if(componentCode==1) {
            return componentMapper.editComponent(componentEntity);
        }else {
            //2.3字典组库以及区域选择，不能更新等级，因为这些都初始化完成的
            return componentMapper.editDicComponent(componentEntity);
        }
    }

    public Integer del(ComponentForm componentForm, HttpServletRequest request) {
        ComponentEntity component = componentMapper.selectByPrimaryKey(componentForm.getComponentId());
        Integer componentCode = componentOptionService.getComponentCode(componentForm.getComponentId());
        if(componentCode!=1){
            throwParamApiException("初始化的组库信息是不能进行删除！");
        }
        ComponentEntity componentEntity = new ComponentEntity();
        BeanUtils.copyProperties(componentForm,componentEntity);
        componentEntity.setLastChangeUser(HttpUtils.getUid(request, errors));
        return componentMapper.delComponent(componentEntity);

    }


    /**
     * 得到自定义的选项列表 系统初始化的是不能进行编辑
     * @param componentId
     * @return
     */
    public ComponentFormList getDetailComponent(Long componentId) {
        valComponent(componentId);
        ComponentFormList componentList = componentMapper.getComponentDetail(componentId);
        return  componentList;
    }

    /**
     * 验证组件
     */
    private ComponentEntity  valComponent(Long componentId){
        ComponentEntity componentEntity = new ComponentEntity();
        componentEntity.setComponentId(componentId.longValue());
        ComponentEntity componentIsExistEntity = componentMapper.selectOne(componentEntity);
        if(null==componentIsExistEntity){
            throw new ApiException("组库信息不存在", new Result<>(errors, 21006, HttpStatus.OK.value()));
        }

        return componentIsExistEntity;
    }

    /**
     * 刷新组件信息
     * @param componentId
     * @param componentType
     */
    public List<DictionaryForm> refreshComponent(Long componentId,Integer componentType) {
        ComponentEntity componentEntity = valComponent(componentId);
        List<DictionaryForm> dictionaryForms=null;
        ComponentFormList componentFormList = getDetailComponent(componentId);
        Integer componentCode = componentOptionService.getComponentCode(componentId.longValue());
        //自定义
        if(componentCode==1) {
            dictionaryForms = componentOptionMapper.refreshCustomComponent(
                    componentEntity.getComponentOptionId(),componentFormList.getLevel(),componentCode);
        //系统初始化
        }else if(componentCode==2) {
            //在等级为1 情况存在code 与op_key 相等情况
            if(componentEntity.getLevel()==1){
                dictionaryForms = componentOptionMapper.refreshCustomComponentOneLevel(
                        Long.valueOf(componentEntity.getOptionCode()),
                        componentFormList.getLevel(), componentCode);
            }else {
                dictionaryForms = componentOptionMapper.refreshCustomComponent(
                        Long.valueOf(componentEntity.getOptionCode()),
                        componentFormList.getLevel(), componentCode);
            }
        }
        // 省市区域组库 查询t_dicitem_area数据
        else if(componentCode==3) {
            dictionaryForms = componentOptionMapper.refreshCustomComponent(
                    componentEntity.getComponentOptionId(),componentFormList.getLevel(),componentCode);
        }
        //这里面对单选框单独处理或者其它选项进行处理
        if(componentType==3){
            Example example = new Example(ComponentOptionEntity.class);
            //重写数据
            dictionaryForms = dictionaryForms.get(0).getOptionList();
            assert dictionaryForms != null;
            dictionaryForms.forEach(item->{
                example.clear();
                example.createCriteria().andEqualTo("componentOptionId", item.getOpValue());
                ComponentOptionEntity componentOptionEntity = componentOptionMapper.selectOneByExample(example);
                if(null!=componentOptionEntity){
                    item.setOpValue(""+componentOptionEntity.getOpKey());
                    item.setSeq(null);
                    item.setOptionList(null);
                }
            });
        }
        //这里刷新到redis
        refreshRedis(dictionaryForms,componentId);
        return  dictionaryForms;
    }

    /**
     * 刷新文件列表缓存
     */
    public void refreshRedis( List<DictionaryForm> dictionaryForms,Long componentId) {
        String redisKey= Config.REFRESH_COMPONENT+"_"+componentId;
        try {
             redisTemplate.opsForValue().set(redisKey, objectMapper.writeValueAsString(dictionaryForms));
             redisTemplate.expire(redisKey, Config.COMPONENT_EXPIRE_DAY, TimeUnit.DAYS);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }


    /**
     * 缓存组库选项
     * @param componentId
     */
    public List<DictionaryForm> getComponentOptionByCache(Long componentId)  {
        String redisKey=Config.REFRESH_COMPONENT+"_"+componentId;
        String redisValue = redisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isEmpty(redisValue)){
          return   refreshComponent(componentId,0);
        }
        List<DictionaryForm> list = null;
        try {
                list = objectMapper.readValue(redisTemplate.opsForValue().get(redisKey),
                        new TypeReference<List<DictionaryForm>>() {});
        } catch (IOException e) {
            e.printStackTrace();
        }
        return  list;
    }


    /**
     *根据主库Id,以及code 反向查询code 的层级关系
     * @param componentId
     */
    public SingleComponentOption getComponentOptionLevel(Long componentId,Long code)  {
        List<DictionaryForm> componentOptionByCache = getComponentOptionByCache(componentId);
        DictionaryForm dictionaryForm  = getDictionaryForm(componentOptionByCache, code);
        SingleComponentOption singleComponentOption = new SingleComponentOption();
        if(dictionaryForm!=null){
            singleComponentOption.setCode(dictionaryForm.getOpKey());
            singleComponentOption.setValue(""+dictionaryForm.getOpValue());
            ComponentEntity componentEntity = componentMapper.selectByPrimaryKey(componentId);
            //如果主库只有一层 不存在多级code
            if(componentEntity.getLevel()==1) {
               return   singleComponentOption;
            }else {
                Integer componentCode = componentOptionService.getComponentCode(componentId.longValue());
                return  componentOptionMapper.selectParentOption(componentCode,code);
            }
        }
        return  null;
    }

    /**
     * 从组库选项中得到 得到某个具体选项内容
     * @return
     */
    private   DictionaryForm   getDictionaryForm(List<DictionaryForm> componentOptionByCache,Long code){
        DictionaryForm dictionaryForm=null;
         for(int i=0;i<componentOptionByCache.size();i++){
             dictionaryForm = componentOptionByCache.get(i);
            if(dictionaryForm.getOpKey().equals(code+"")){
               return dictionaryForm;
            }else {
                if(!CollectionUtils.isEmpty(dictionaryForm.getOptionList())) {
                    dictionaryForm = getDictionaryForm(dictionaryForm.getOptionList(), code);
                    if(dictionaryForm.getOpKey().equals(code+"")){
                        return dictionaryForm;
                    }
                }
            }
        }
        return  dictionaryForm;
    }




    /**
     * 拉取组件信息存入t_component_option 表中
     * @param componentId
     * @return
     */
    @Transactional
    public void  pullComponentData(Long componentId) {
        ComponentEntity componentEntity = valComponent(componentId);
        if(componentEntity==null) {
            throw new ApiException("根据组件Id未查询到组件信息", new Result<>
                    (errors, 165, HttpStatus.OK.value(), "根据组件Id未查询到组件信息"));
        }
        if(componentEntity.getIsPull ()!=null && componentEntity.getIsPull()==1){
            throw new ApiException("组件已经初始化,无须再次进行数据拉取!");
        }
        List<DictionaryForm> dictionaryForms=null;
        List<ComponentOptionEntity> componentOptionEntities=new ArrayList<>();
        //只拉取系统生成的组件信息 自定义不用拉取信息
        if(componentEntity.getCode()==1) {
            Integer optionCode =Integer.valueOf(componentEntity.getOptionCode());
            //说明是拉取省市信息,如果这里到时候出现类似区域信息 还会修改代码 因为表已经存在变化,其它都在t_option表中
            if (  optionCode == -1 && optionCode<0 ){
                dictionaryForms = componentOptionMapper.
                        refreshAreaComponent(componentEntity.getOptionCode(),1,0);
                traversingDictionaryForms(componentOptionEntities,dictionaryForms,componentEntity,3);
            }else {
                //当多级时候调用方法
                if(componentEntity.getLevel()==1){
                    dictionaryForms = componentOptionMapper.
                            refreshSystemComponentOneLevel(componentEntity.getOptionCode(), 0);
                }else {
                    dictionaryForms = componentOptionMapper.
                            refreshSystemComponent(componentEntity.getOptionCode(), 0);
                }
                traversingDictionaryForms(componentOptionEntities,dictionaryForms,componentEntity,2);
            }
        }
        try {
            if (!CollectionUtils.isEmpty(componentOptionEntities)) {
                componentOptionMapper.insertList(componentOptionEntities);
                componentEntity.setIsPull(1);
                componentMapper.editComponentPullData(componentEntity);
            }

        }catch (Exception ex){
            log.error("拉取组件信息发生异常-ex-{}",ex);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new ApiException("拉取组件信息发生失败");
        }
    }

    /**
     * 遍历表单内容
     * systemCode编码：2.字典表数据 3.省市区域
     */
    private  void  traversingDictionaryForms(List<ComponentOptionEntity> componentOptionEntities,
                                             List<DictionaryForm> dictionaryForms,
                                             ComponentEntity componentEntity,
                                             Integer systemCode){
        if(!CollectionUtils.isEmpty(dictionaryForms)){
            dictionaryForms.forEach(item->{
                ComponentOptionEntity componentOptionEntity=new ComponentOptionEntity();
                //第一个层次没有上级Id
                if(systemCode==2) {
                    componentOptionEntity.setOpKey(item.getOpValue());
                }else if(systemCode==3) {
                    if(null!=item.getParentId()) {
                        componentOptionEntity.setComponentParentId(Long.valueOf(item.getParentId()));
                    }
                }
                componentOptionEntity.setLevel(item.getLevel().toString());
                if(null !=item.getSeq()) {
                    componentOptionEntity.setSeq(item.getSeq());
                }
                componentOptionEntity.setName(item.getOpKey());
                componentOptionEntity.setStatus(1);
                componentOptionEntity.setSource(systemCode);
                componentOptionEntity.setCode(Long.valueOf(item.getCode()));
                componentOptionEntity.setCreateTime(new Date());
                componentOptionEntity.setCreateUser(createUser);
                List<DictionaryForm> optionList = item.getOptionList();
                componentOptionEntities.add(componentOptionEntity);
                if(!CollectionUtils.isEmpty(optionList)){
                    traversingDictionaryForms(componentOptionEntities,optionList,componentEntity,systemCode);
                }
            });
        }
    }

    /**
     * 抛出参数异常信息
     * @param msg
     */
    private  void  throwParamApiException(String msg){
        throw new ApiException(msg ,new Result<>(errors, 200000, HttpStatus.OK.value(),msg));
    }


}