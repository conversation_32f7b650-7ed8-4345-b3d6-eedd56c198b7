package com.goodsogood.ows.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.FieldConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.FieldMapper;
import com.goodsogood.ows.mapper.FieldRuleMapper;
import com.goodsogood.ows.mapper.QueryConfigMapper;
import com.goodsogood.ows.mapper.TableConfigMapper;
import com.goodsogood.ows.model.db.FieldEntity;
import com.goodsogood.ows.model.db.FieldRuleEntity;
import com.goodsogood.ows.model.db.QueryConfigEntity;
import com.goodsogood.ows.model.db.TableConfigEntity;
import com.goodsogood.ows.model.vo.*;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 表单服务
 *
 * <AUTHOR>
 * @date 2020-08-18 13:56
 * @since 3.0.1
 **/
@Service
@Log4j2
public class FormService {
     private final static String FIELD_NAME = "field_";
     private final QueryConfigMapper queryConfigMapper;
     private final TableConfigMapper tableConfigMapper;
     private final FieldMapper fieldMapper;
     private final ComponentService componentService;
     private final OrganizationTreeService organizationTreeService;
     private final FieldRuleService fieldRuleService;
     private final FieldRuleMapper fieldRuleMapper;
     private final FieldConfig fieldConfig;
     private final ObjectMapper objectMapper = new ObjectMapper();
    private final Errors errors;

    @Autowired
     public FormService(QueryConfigMapper queryConfigMapper,
                        TableConfigMapper tableConfigMapper,
                        FieldMapper fieldMapper, ComponentService componentService,
                        OrganizationTreeService organizationTreeService,
                        FieldRuleService fieldRuleService,
                        FieldRuleMapper fieldRuleMapper,
                        FieldConfig fieldConfig,
                        Errors errors) {
          this.queryConfigMapper = queryConfigMapper;
          this.tableConfigMapper = tableConfigMapper;
          this.fieldMapper = fieldMapper;
          this.componentService = componentService;
          this.organizationTreeService = organizationTreeService;
          this.fieldRuleService = fieldRuleService;
          this.fieldRuleMapper = fieldRuleMapper;
        this.fieldConfig = fieldConfig;
        this.errors = errors;
    }

     /**
      * 查询框结构查询
      * @param own 1-用户 2-组织
      * @return
      */
     public List<QueryConfigForm> queryConfigList(Integer own, HeaderHelper.SysHeader header,
                                                  Integer level, Integer opType) {
         Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                 header.getRegionId()).getTreeId();
         List<QueryConfigForm> resultList = this.queryConfigMapper.queryConfigList(own, Constants.STATUS_YES,
                  orgTreeId,level);
         if (!CollectionUtils.isEmpty(resultList)) {
               resultList.forEach(queryConfigForm -> {
                   if (queryConfigForm.getComponentId() != null && opType==1 &&
                           queryConfigForm.getComponentId()>0) {
                         // 根据组件ID获取组件数据
                         List<DictionaryForm> dicList =this.componentService.
                                 getComponentOptionByCache(queryConfigForm.getComponentId());
                         if (!CollectionUtils.isEmpty(dicList)) {
                              queryConfigForm.setComponents(dicList);
                         }
                    }
               });
         }
         //加入属性
         resultList.forEach(item->{
             if(!CollectionUtils.isEmpty(item.getComponents())){
                 item.getAttrMap().put("options",item.getComponents());
             }
             //设置关联字段关系
             if(!StringUtils.isEmpty(item.getAssociateName())){
                 item.getAttrMap().put("match", Arrays.asList(item.getAssociateName().split(",")) );
             }
             if(null!=item.getAttr()){
                 handlerAttrForm(item.getAttrMap(), item.getAttr());
                 //得到是否显示字段 重写field里面显示
                 if(null!=item.getAttr().getIsShow()) {
                     item.setIsShow(item.getAttr().getIsShow());
                 }
                 //重写组织类型
                 if(!StringUtils.isEmpty(item.getAttr().getComponentType())) {
                     item.setComponentType(item.getAttr().getComponentType());
                 }
             }
             //设置布局规则信息
             if(!StringUtils.isEmpty(item.getLayout())) {
                 item.setLayout(item.getLayout());
             }
             //设置condition
             if(null!=item.getCondition()){
                 item.setCondition(item.getCondition());
             }
         });
         return resultList;
     }

    /**
     * 处理表单数据
     */
    private void  handlerAttrForm(Map<String, Object> attrMap,AttrForm attr){
        final Class aClass = attr.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        Arrays.stream(declaredFields).forEach(item->{
            String filedName = item.getName();
            item.setAccessible(true);
            try {
                Object result = item.get(attr);
                if(null!=result){
                    attrMap.put(filedName, result);
                }
            } catch (Exception e) {
               log.error("解决失败，处理字段信息,error-Msg={}",e.getMessage());
            }
        });
    }
     /**
      * 表格表头列表查询
      * @param own 1-成员管理 2-组织 3-用户选择器
      * @param type own=1时，1=成员管理查询，2=成员管理下载，3=人员选择器
      * @return
      */
     public List<TableConfigForm> tableConfigList(Integer own, Integer type, Integer orgType, Long regionId) {
         Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(orgType,
                 regionId).getTreeId();
         List<TableConfigForm> collect = this.tableConfigMapper.
                 tableConfigList(own, type, orgTreeId, Constants.STATUS_YES)
                 .stream().sorted(Comparator.comparing(TableConfigForm::getTableOrder))
                 .collect(Collectors.toList());
         //如果attr里面有属性字段 就首先获取
         collect.forEach(item->{
            if(null!=item.getAttrTable()&&!StringUtils.isEmpty(item.getAttrTable().getIsShow())){
                item.setShow(item.getAttrTable().getIsShow());
            }
         });
         return collect;
     }

     /**
      * 用户/组织表单列表查询
      *
      * @param own 1-用户 2-组织
      * @return
      */
     public List<QueryConfigForm> fieldList(Integer own,HeaderHelper.SysHeader header,
                                            Integer opType,Integer type) {
         Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                 header.getRegionId()).getTreeId();
         List<QueryConfigForm> resultList = this.fieldMapper.fieldList(own, Constants.STATUS_YES, orgTreeId,type);
          if (!CollectionUtils.isEmpty(resultList)) {
               resultList.forEach(queryConfigForm -> {
                    if (queryConfigForm.getComponentId() != null && opType==1 &&
                            queryConfigForm.getComponentId()>0) {
                         List<DictionaryForm> dicList = this.componentService.
                                 getComponentOptionByCache(queryConfigForm.getComponentId());
                         if (!CollectionUtils.isEmpty(dicList)) {
                              queryConfigForm.setComponents(dicList);
                         }
                    }
               });
          }
         //加入属性
         resultList.forEach(item->{
                 if(!CollectionUtils.isEmpty(item.getComponents())){
                     item.getAttrMap().put("options",item.getComponents());
                 }
                 if(item.getComponentType().equals(10)){
                     item.getAttrMap().put("onCheckPhone","onCheckUserPhone");
                 }
                 if (null != item.getAttr()) {
                     handlerAttrForm(item.getAttrMap(), item.getAttr());
                 }
                 //验证规则
                 FieldRuleVo fileRuleInfo = fieldRuleService.getFileRuleInfo(item);
                 item.setFieldRule(fileRuleInfo);
                 //设置关联字段关系
                if(!StringUtils.isEmpty(item.getAssociateName())){
                    item.getAttrMap().put("match", Arrays.asList(item.getAssociateName().split(",")) );
                }
                //设置布局规则信息
                if(!StringUtils.isEmpty(item.getLayout())) {
                     item.setLayout(item.getLayout());
                }
                //设置condition
                if(null!=item.getCondition()){
                    item.setCondition(item.getCondition());
                }
         });
         //如果opType=2 就要过滤属性字段
         if(opType==2) {
             List<QueryConfigForm> collect = resultList.stream().filter(item -> {
                 return (!fieldConfig.getName().contains(item.getFieldName()));
             }).collect(Collectors.toList());
           //再次过滤字段org_id 并且 componentType 的所在支部这个字段
           return  collect.stream().filter(item->!(item.getFieldName()
                     .equals("org_id")&&item.getComponentType()==8)).collect(Collectors.toList());
         }
         return resultList;
     }


    /**
     * 查询框结构设置
      * @param own
     * @param header
     * @return
     */
    public Integer queryConfigSet(QueryConfigEntity queryConfig,Integer own,
                                  HeaderHelper.SysHeader header) throws JsonProcessingException {
        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                header.getRegionId()).getTreeId();
        queryConfig.setOwn(own);
        queryConfig.setCreateUser(header.getUserId());
        queryConfig.setCreateTime(new Date());
        queryConfig.setOrgTreeId(orgTreeId);
        queryConfig.setLayout(queryConfig.getLayout());
        queryConfig.setDefaultValue(queryConfig.getDefaultValue());
        queryConfig.setCondition(queryConfig.getCondition());
        return  queryConfigMapper.insert(queryConfig);
    }

    /**
     * 表格表头列表设置
     * @param tableConfig
     * @param own
     * @param header
     * @return
     */
    public Integer tableConfigSet(TableConfigEntity tableConfig,Integer own, HeaderHelper.SysHeader header) {
        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                header.getRegionId()).getTreeId();
        tableConfig.setOwn(own);
        tableConfig.setCreateUser(header.getUserId());
        tableConfig.setCreateTime(new Date());
        tableConfig.setOrgTreeId(orgTreeId);
        return  tableConfigMapper.insert(tableConfig);
    }

    /**
     * 字段 更新
     * @param own
     * @param header
     * @param listForm
     */
    @Transactional(rollbackFor = Exception.class)
    public void fieldListUpdate(Integer own, Integer type,HeaderHelper.SysHeader header,
                                LinkedList<QueryConfigForm> listForm) throws JsonProcessingException {
        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                header.getRegionId()).getTreeId();
        //得到所有key 动态字段 并且是可以删除的字段才能删除
        Map<String, String> mapKey = listForm.stream().
                filter(item -> (null!=item.getCanDel()&&item.getCanDel()==1))
                .collect(Collectors.toMap(QueryConfigForm::getFieldName, QueryConfigForm::getFieldName));
        //删除非默认字段 并且标识可以删除
        fieldMapper.delNotDefaultField(own, type, orgTreeId);
        fieldMapper.delField(own, type, orgTreeId);
        for(int i=0;i<listForm.size();i++){
            QueryConfigForm item = listForm.get(i);
            Integer fromOrder=i+1;
            //如果是默认字段或者动态字段表示不能删除就改变顺序 canDel为2表示不能删除
            if(null!=item.getCanDel()&&item.getCanDel()==2){
                fieldMapper.updateFieldOrder(item.getFieldId(), fromOrder,
                        objectMapper.writeValueAsString(item.getLayout()),item.getQueryType());
            }else {
                FieldRuleVo fieldRule = item.getFieldRule();
                FieldRuleEntity fieldRuleEntity = getFieldRule(fieldRule);
                FieldEntity fieldEntity = new FieldEntity();
                //验证attrMap 有没有数据
                if(item.getAttrMap().size()>0){
                    AttrForm attrFrom = getAttrFrom(item.getAttrMap());
                    fieldEntity.setAttr(attrFrom);
                    //最小值
                    if(!StringUtils.isEmpty(item.getAttrMap().get("min"))){
                        double min = Double.parseDouble(item.getAttrMap().get("min").toString());
                        fieldRuleEntity.setMinValue(min);
                    }
                    //最大值
                    if(!StringUtils.isEmpty(item.getAttrMap().get("max"))){
                        double max = Double.parseDouble(item.getAttrMap().get("max").toString());
                        fieldRuleEntity.setMaxValue(max);
                    }
                }
                //写入规则数据
                Long fileRuleId = null;
                //只有验证的时候才能写t_field_rule 这个表
                if(fieldRuleEntity!=null&&fieldRuleEntity.getRequired()>0){
                    //写入规则信息
                    fieldRuleEntity.setCreateTime(new Date());
                    //fieldRuleEntity.setRuleType("2");
                    fieldRuleEntity.setCreateUser(header.getUserId());
                    fieldRuleMapper.insertUseGeneratedKeys(fieldRuleEntity);
                    fileRuleId = fieldRuleEntity.getFieldRuleId();
                }
                fieldEntity.setOwn(own);
                fieldEntity.setCreateTime(new Date());
                fieldEntity.setCreateUser(header.getUserId());
                fieldEntity.setFieldRuleId(fileRuleId);
                fieldEntity.setIsDefault(Constants.IS_DEFAULT_NO);
                //不是手动加入的字段 动态字段都是可以删除的
                fieldEntity.setCanDel(1);
                fieldEntity.setStatus(Constants.YES);
                BeanUtil.copyProperties(item,fieldEntity,
                        CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                fieldEntity.setFieldId(null);
                fieldEntity.setIsShow(item.getIsShow()?1:0);
                fieldEntity.setOrgTreeId(orgTreeId);
                fieldEntity.setFormOrder(fromOrder);
                fieldEntity.setType("1");
                fieldEntity.setQueryType(item.getQueryType());
                String fieldName="";
                //如果customKey有值 就一定新增加
                if(!StringUtils.isEmpty(item.getCustomKey())){
                    if(fieldMapper.valSameField(own,type,orgTreeId,item.getCustomKey())>0){
                        throw new ApiException("自定义key重复",
                                new Result<>(errors, 31006, HttpStatus.OK.value()));
                    }
                    fieldEntity.setFieldName(item.getCustomKey());
                    //并且这个是不能删除的
                    fieldEntity.setCanDel(2);
                    fieldEntity.setFieldId(item.getFieldId());
                    this.fieldMapper.insert(fieldEntity);
                }else {
                    if (!StringUtils.isEmpty(item.getFieldName()) && item.getFieldName().startsWith("field_")) {
                        fieldName = item.getFieldName();
                        fieldEntity.setFieldId(item.getFieldId());
                        this.fieldMapper.insert(fieldEntity);
                    } else {
                        this.fieldMapper.insertUseGeneratedKeys(fieldEntity);
                        fieldName = "field_" + fieldEntity.getFieldId();
                    }
                }
                //重新设置字段名称 与 字段主键信息
                fieldEntity.setFieldName(fieldName);
                mapKey.put(item.getFieldName(), fieldName);
                fieldMapper.updateByPrimaryKey(fieldEntity);
                //重置condition
                resetCondition(mapKey);
            }
        }
    }

    /**
     * 按照数据库最大ID生成字段名
     *
     * @return
     */
    public String getFieldName() {
        Long maxId = this.fieldMapper.getMaxId();
        maxId = (null != maxId ? maxId + 1 : 1);
        return FIELD_NAME + maxId;
    }


    //把规则信息转成验证实体
    private FieldRuleEntity getFieldRule(FieldRuleVo fieldRule){
        if(null==fieldRule){
            return null;
        }
        if("这是必填".equals(fieldRule.getMessage())){
            System.out.println(fieldRule.getMessage());
        }
        FieldRuleEntity fieldRuleEntity = new FieldRuleEntity();
        //复制不为空的字段
        BeanUtil.copyProperties(fieldRule,fieldRuleEntity,
                CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
        //重置require
        fieldRuleEntity.setRequired(null!=fieldRule.getRequired()&&fieldRule.getRequired()?1:0);
        //正则表达式
        if(!StringUtils.isEmpty(fieldRule.getPattern())){
            fieldRuleEntity.setRegular(fieldRule.getPattern());
        }
        //正则表达式提示信息
        if(!StringUtils.isEmpty(fieldRule.getPatternMessage())){
            fieldRuleEntity.setPatternMessage(fieldRule.getPatternMessage());
        }
        //重置最小长度
        if(!StringUtils.isEmpty(fieldRule.getMin())){
            fieldRuleEntity.setMinLength(fieldRule.getMin());
        }
        //重置最大1长度
        if(!StringUtils.isEmpty(fieldRule.getMax())){
            fieldRuleEntity.setMaxLength(fieldRule.getMax());
        }
        return fieldRuleEntity;
    }

    /**
     * 就是把attrMap 里面的数据放入attr里面
     * @param attrMap
     * @return
     */
    private AttrForm getAttrFrom(Map<String, Object> attrMap){
        AttrForm attr = new AttrForm();
        final Class<? extends AttrForm> aClass = attr.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        attrMap.forEach((k,v)->{
            Arrays.stream(declaredFields).forEach(item->{
                String fieldName = item.getName();
                if(!k.equals(fieldName)){
                    return;
                }
                try {
                    Field f = aClass.getDeclaredField(fieldName);
                    f.setAccessible(true);
                    //给变量赋值
                    f.set(attr, v);
                } catch (Exception e) {
                    log.error("getAttrFrom，处理字段信息,error-Msg={}",e.getMessage());
                }
            });
        });
        return attr;
    }


    /**
     * 更新表头
     * @param own
     * @param header
     * @param list
     * @return
     */
//    @Transactional(rollbackFor = Exception.class)
//    public Integer tableConfigUpdate(Integer own, HeaderHelper.SysHeader header,
//                                     LinkedList<TableConfigForm> list,Integer type) {
//        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
//                header.getRegionId()).getTreeId();
//        //先查询出来这个查询所有查询框配置信息
//        List<Long> queryConfigIds = tableConfigMapper.getTableConfigIds(own, type, orgTreeId);
//        //先删除数据
//        for(int i=1;i<list.size();i++) {
//            TableConfigForm it = list.get(i);
//            Integer fromOrder=i+1;
//            //如果字段默认字段 不能删除 只能更改字段信息及配置信息
//            if(it.getIsDefault()!=null&&it.getIsDefault()==1){
//                TableConfigEntity tableConfig= tableConfigMapper.selectByPrimaryKey(it.getTableConfigId());
//                if(tableConfig==null){
//                    continue;
//                }
//                tableConfig.setWidth(it.getWidth());
//                tableConfig.setAlign(it.getAlign());
//                tableConfig.setLastChangeUser(header.getUserId());
//                tableConfig.setUpdateTime(new Date());
//                tableConfigMapper.updateByPrimaryKey(tableConfig);
//                queryConfigIds.remove(it.getTableConfigId());
//                continue;
//            }
//            TableConfigEntity tableConfigEntity = new TableConfigEntity();
//            BeanUtils.copyProperties(it,tableConfigEntity);
//            //生成规则信息
//            FieldRuleVo fieldRule = it.getFieldRule();
//            FieldRuleEntity fieldRuleEntity = getFieldRule(fieldRule);
//            //写入规则数据
//            Long fileRuleId = null;
//            if(fieldRuleEntity!=null){
//                //写入规则信息
//                fieldRuleEntity.setCreateTime(new Date());
//                fieldRuleEntity.setRuleType("2");
//                fieldRuleEntity.setCreateUser(header.getUserId());
//                fieldRuleMapper.insertUseGeneratedKeys(fieldRuleEntity);
//                fileRuleId = fieldRuleEntity.getFieldRuleId();
//            }
//            //写入字段信息
//            FieldEntity fieldEntity = new FieldEntity();
//            fieldEntity.setOwn(own);
//            fieldEntity.setCreateTime(new Date());
//            fieldEntity.setCreateUser(header.getUserId());
//            fieldEntity.setFieldRuleId(fileRuleId);
//            fieldEntity.setStatus(1);
//            BeanUtil.copyProperties(it,fieldEntity,
//                    CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
//            //fieldEntity.setFieldName(this.getFieldName());
//            fieldEntity.setFieldId(null);
//            fieldEntity.setOrgTreeId(orgTreeId);
//            fieldEntity.setFormOrder(fromOrder);
//            fieldMapper.insertUseGeneratedKeys(fieldEntity);
//
//            //重新设置字段名称 与 字段主键信息
//            fieldEntity.setFieldName("field_" + fieldEntity.getFieldId());
//            fieldMapper.updateByPrimaryKey(fieldEntity);
//
//            //生成tableConfig数据
//            tableConfigEntity.setOwn(own);
//            tableConfigEntity.setType(type);
//            tableConfigEntity.setFieldId(fieldEntity.getFieldId());
//            tableConfigEntity.setOrgTreeId(orgTreeId);
//            tableConfigEntity.setCreateTime(new Date());
//            tableConfigEntity.setCreateUser(header.getUserId());
//            tableConfigEntity.setStatus(1);
//            tableConfigEntity.setTableOrder(fromOrder);
//            tableConfigMapper.insert(tableConfigEntity);
//        }
//        //删除动态字段配置信息
//        if(!CollectionUtils.isEmpty(queryConfigIds)) {
//            fieldMapper.delNotDefaultFieldList(queryConfigIds);
//            //删除查询配置信息
//            tableConfigMapper.deleteField(queryConfigIds);
//        }
//        return 1;
//    }

    /**
     * 更新表头
     * @param own
     * @param header
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer tableConfigUpdate(Integer own, HeaderHelper.SysHeader header,
                                     LinkedList<TableConfigForm> list,Integer type) {
        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                header.getRegionId()).getTreeId();
        //不删除 隐藏字段因为隐藏字段是初始化的字段
        tableConfigMapper.delTableConfigIds(own, type, orgTreeId);
        //先删除数据
        for(int i=0;i<list.size();i++) {
            TableConfigForm it = list.get(i);
            TableConfigEntity tableConfigEntity = new TableConfigEntity();
            //生成tableConfig数据
            tableConfigEntity.setOwn(own);
            tableConfigEntity.setType(type);
            tableConfigEntity.setFieldId(it.getFieldId());
            tableConfigEntity.setOrgTreeId(orgTreeId);
            tableConfigEntity.setCreateTime(new Date());
            tableConfigEntity.setCreateUser(header.getUserId());
            tableConfigEntity.setStatus(1);
            tableConfigEntity.setTableOrder(i+1);
            tableConfigEntity.setWidth(it.getWidth());
            tableConfigEntity.setAlign(it.getAlign());
            AttrForm attrForm = new AttrForm();
            if(null!=it.getAttrTable())
            BeanUtils.copyProperties(it.getAttrTable(),attrForm);
            //如果隐藏的表头要隐藏
            if(null!=it.isShow()){
                attrForm.setIsShow(it.isShow());
                tableConfigEntity.setAttr(attrForm);
            }
            tableConfigMapper.insert(tableConfigEntity);
        }
        return 1;
    }
//    /**
//     * 更新查询框
//     * @param own
//     * @param header
//     * @param level
//     * @param list
//     * @return
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public Integer queryConfigUpdate(Integer own, HeaderHelper.SysHeader header,
//                                    Integer level, List<QueryConfigForm> list) {
//
//        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
//                header.getRegionId()).getTreeId();
//        //先查询出来这个查询所有查询框配置信息
//        List<Long> queryConfigIds = queryConfigMapper.getQueryConfigIds(own, level, orgTreeId);
//        //得到所有key
//        Map<String, String> mapKey = list.stream().
//                filter(item -> (item.getIsDefault() == null||item.getCanDel()==1))
//                .collect(Collectors.toMap(QueryConfigForm::getFieldName, QueryConfigForm::getFieldName));
//        //先按顺序新增
//        for(int i=0;i<list.size();i++) {
//            QueryConfigForm it = list.get(i);
//            Integer queryOrder=i+1;
//            //如果字段默认字段 修改顺序
//            if(it.getCanDel()==2){
//                QueryConfigEntity queryConfig= queryConfigMapper.selectByPrimaryKey(it.getQueryConfigId());
//                if(queryConfig==null){
//                    continue;
//                }
//                queryConfig.setLastChangeUser(header.getUserId());
//                queryConfig.setUpdateTime(new Date());
//                queryConfig.setLayout(it.getLayout());
//                queryConfig.setDefaultValue(it.getDefaultValue());
//                queryConfig.setCondition(it.getCondition());
//                //验证attrMap 有没有数据
//                if(it.getAttrMap().size()>0){
//                    AttrForm attrFrom = getAttrFrom(it.getAttrMap());
//                    queryConfig.setAttr(attrFrom);
//                }
//                queryConfigMapper.updateByPrimaryKey(queryConfig);
//                queryConfigIds.remove(it.getQueryConfigId());
//                continue;
//            }
//            QueryConfigEntity queryConfigEntity = new QueryConfigEntity();
//            BeanUtils.copyProperties(it,queryConfigEntity);
//            //生成规则信息
//            FieldRuleVo fieldRule = it.getFieldRule();
//            FieldRuleEntity fieldRuleEntity = getFieldRule(fieldRule);
//            Long fileRuleId = null;
//            if(fieldRuleEntity!=null){
//                //写入规则信息
//                fieldRuleEntity.setCreateTime(new Date());
//                fieldRuleEntity.setRuleType("2");
//                fieldRuleEntity.setCreateUser(header.getUserId());
//                fieldRuleMapper.insertUseGeneratedKeys(fieldRuleEntity);
//                fileRuleId = fieldRuleEntity.getFieldRuleId();
//            }
//            //写入字段信息
//            FieldEntity fieldEntity = new FieldEntity();
//            fieldEntity.setOwn(own);
//            fieldEntity.setCreateTime(new Date());
//            fieldEntity.setCreateUser(header.getUserId());
//            fieldEntity.setFieldRuleId(fileRuleId);
//            fieldEntity.setIsDefault(Constants.IS_DEFAULT_NO);
//            fieldEntity.setStatus(Constants.YES);
//            BeanUtil.copyProperties(it,fieldEntity,
//                    CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
//            //重置新的属性
//            fieldEntity.setFieldName(this.getFieldName());
//            fieldEntity.setFieldId(null);
//            fieldEntity.setOrgTreeId(orgTreeId);
//            fieldEntity.setFormOrder(queryOrder);
//            fieldMapper.insertUseGeneratedKeys(fieldEntity);
//            //重新设置字段名称 与 字段主键信息
//            String fieldName = "field_" + fieldEntity.getFieldId();
//            //重新设置字段名称 与 字段主键信息
//            fieldEntity.setFieldName(fieldName);
//            if(!it.getFieldName().startsWith("field_")) {
//                mapKey.put(it.getFieldName(), fieldName);
//            }
//            fieldMapper.updateByPrimaryKey(fieldEntity);
//            queryConfigEntity.setQueryOrder(i+1);
//            queryConfigEntity.setOwn(own);
//            queryConfigEntity.setLevel(level);
//            queryConfigEntity.setStatus(1);
//            queryConfigEntity.setCreateTime(new Date());
//            queryConfigEntity.setCreateUser(header.getUserId());
//            queryConfigEntity.setOrgTreeId(orgTreeId);
//            queryConfigMapper.insert(queryConfigEntity);
//        }
//        //删除动态字段配置信息
//        if(!CollectionUtils.isEmpty(queryConfigIds)) {
//            fieldMapper.delNotDefaultFieldList(queryConfigIds);
//            //删除查询配置信息
//            queryConfigMapper.deleteField(queryConfigIds);
//        }
//        //重置condition
//        resetCondition(mapKey);
//        return 1;
//    }

    /**
     * 更新查询框
     * @param own
     * @param header
     * @param level
     * @param list
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer queryConfigUpdate(Integer own, HeaderHelper.SysHeader header,
                                     Integer level, List<QueryConfigForm> list) {
        Long orgTreeId = organizationTreeService.getOrgTreeByTreeType(header.getOrgType(),
                header.getRegionId()).getTreeId();
        //先查询出来这个查询所有查询框配置信息
        queryConfigMapper.delQueryConfigIds(own, level, orgTreeId);
        //先按顺序新增
        for(int i=0;i<list.size();i++) {
            QueryConfigForm it = list.get(i);
            QueryConfigEntity queryConfigEntity = new QueryConfigEntity();
            BeanUtils.copyProperties(it,queryConfigEntity);
            queryConfigEntity.setQueryOrder(i+1);
            queryConfigEntity.setOwn(own);
            queryConfigEntity.setLevel(level);
            queryConfigEntity.setStatus(1);
            queryConfigEntity.setCreateTime(new Date());
            queryConfigEntity.setCreateUser(header.getUserId());
            queryConfigEntity.setOrgTreeId(orgTreeId);
            queryConfigEntity.setQueryConfigId(null);
            //验证attrMap 有没有数据
            if(it.getAttrMap().size()>0){
                AttrForm attrFrom = getAttrFrom(it.getAttrMap());
                if(!StringUtils.isEmpty(it.getComponentType())){
                    attrFrom.setComponentType(it.getComponentType());
                }
                queryConfigEntity.setAttr(attrFrom);
            }
            queryConfigMapper.insert(queryConfigEntity);
        }
        return 1;
    }

    /**
     * 重置condition 中的key
     * @param mapKey
     */
    private void resetCondition(Map<String, String> mapKey){
        //处理完成以后更新condition 中的key
        if(mapKey.size()>0){
            ArrayList<String> listFieldName = new ArrayList<>(mapKey.values());
            Example example = new Example(FieldEntity.class);
            example.selectProperties("condition","fieldId");
            example.createCriteria().andIn("fieldName", listFieldName);
            List<FieldEntity> fieldEntities = fieldMapper.selectByExample(example);
            if(!CollectionUtils.isEmpty(fieldEntities)){
                fieldEntities.forEach(item->{
                    if(!CollectionUtils.isEmpty(item.getCondition())){
                        List<ConditionForm> condition = item.getCondition();
                        condition.forEach(it->{
                            if(mapKey.containsKey(it.getKey())){
                                it.setKey(mapKey.get(it.getKey()));
                            }
                        });
                        example.clear();
                        example.createCriteria().andEqualTo("fieldId", item.getFieldId());
                        fieldMapper.updateByExampleSelective(item, example);
                    }
                });
            }
        }
    }
}

