package com.goodsogood.ows.service;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.VolunteerTeamEntity;
import com.goodsogood.ows.model.vo.VolunteerUserFindForm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-23 16:46
 * @since 3.0.0
 **/
@Service
@Log4j2
public class VolunteerCacheService {

     private final StringRedisTemplate redisTemplate;

     @Autowired
     public VolunteerCacheService(StringRedisTemplate redisTemplate) {
          this.redisTemplate = redisTemplate;
     }

     /**
      * 刷新志愿团体缓存
      *
      * @param entity 参数
      * @param type   1-刷新 2-删除
      */
     @Async("flushCacheExecutor")
     public void flushVolunteerTeamCache(VolunteerTeamEntity entity, Integer type) {
          String redisKey = Constants.CACHE_VOL_TEAM_PREFIX + entity.getVolunteerTeamId();
          String orgToTeamKey = Constants.CACHE_ORG_TO_TEAM_PREFIX + entity.getOrganizationId();
          List<String> keyList = new ArrayList<>();
          keyList.add(redisKey);
          keyList.add(orgToTeamKey);
          if (type == Constants.OPER_TYPE_DEL) {
               this.redisTemplate.delete(keyList);
          } else if (type == Constants.OPER_TYPE_ADD) {
               this.redisTemplate.delete(keyList);
               this.redisTemplate.opsForValue().set(redisKey, Utils.toJson(entity),
                       Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
               this.redisTemplate.opsForValue().set(orgToTeamKey, Utils.toJson(entity),
                       Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
          }
     }

     /**
      * 刷新志愿者缓存
      * 如果删除志愿者 需要先删除缓存再删除数据
      *
      * @param byId 志愿者实体
      * @param type 1-刷新 2-删除
      */
     @Async("flushCacheExecutor")
     public void flushVolunteerUserCache(VolunteerUserFindForm byId, Integer type) {
          String redisKey = Constants.CACHE_VOL_USER_PREFIX + byId.getVolunteerUserId();
          if (type == Constants.OPER_TYPE_DEL) {
               this.redisTemplate.delete(redisKey);
          } else if (type == Constants.OPER_TYPE_ADD) {
               this.redisTemplate.delete(redisKey);
               this.redisTemplate.opsForValue().set(redisKey, Utils.toJson(byId),
                       Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
          }
     }

     /**
      * 仅刷新组织信息缓存
      *
      * @param org  组织实体
      * @param flag 操作类型 1-删除
      */
     @Async("flushCacheExecutor")
     @Transactional
     public void flushOrgSingle(OrganizationEntity org, Integer... flag) {
          String curTime = Utils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
          StopWatch stopWatch = new StopWatch();
          stopWatch.start();
          log.debug("仅刷新组织信息缓存,orgId:[{}],开始时间：[{}]", org.getOrganizationId(), curTime);
          String orgKey = Global.CACHE_ORG_ID_PREFIX + org.getOrganizationId();
          if (this.redisTemplate.hasKey(orgKey)) {
               this.redisTemplate.delete(orgKey);
          }
          redisTemplate.opsForValue().set(orgKey, Utils.toJson(org),
                  Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
          log.debug("组织信息缓存：orgId:[{}], 刷新成功！耗时：[{}]", org.getOrganizationId(), stopWatch.toString());
          stopWatch.stop();
     }
}
