package com.goodsogood.ows.service

import com.goodsogood.ows.common.Constants
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.config.SimpleApplicationConfigHelper
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.mapper.OrganizationMapper
import com.goodsogood.ows.mapper.UserOrgAndCorpMapper
import com.goodsogood.ows.model.db.OrganizationEntity
import com.goodsogood.ows.model.db.UserOrgAndCorpEntity
import com.goodsogood.ows.model.vo.CloudOrgForm
import com.goodsogood.ows.model.vo.Result
import org.joda.time.DateTime
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.ObjectUtils
import tk.mybatis.mapper.entity.Example

@Service
class CloudOrgService @Autowired constructor(
    val error: Errors,
    val organizationMapper: OrganizationMapper,
    val userOrgAndCorpMapper: UserOrgAndCorpMapper,
    val simpleApplicationConfigHelper: SimpleApplicationConfigHelper,
    val userOrgCacheCallService: UserOrgCacheCallService,
    val userMongoService: UserMongoService,
    val orgMongoService: OrgMongoService,
) {

    /**
     * 新增云上党支部
     */
    @Transactional(rollbackFor = [Exception::class])
    fun addOrg(form: CloudOrgForm, header: HeaderHelper.SysHeader): Long {
        // 校验名称是否重复
        val example = Example(OrganizationEntity::class.java)
        example.createCriteria().andEqualTo("name", form.orgName)
            .andEqualTo("status", Constants.STATUS_YES)
            .andEqualTo("orgType", Constants.ORG_TYPE_ECP)
        val cnt = this.organizationMapper.selectCountByExample(example)
        if (cnt > 0) {
            throw ApiException(
                "支部名称已经存在，请重新输入.",
                Result<Any>(error, 35004, HttpStatus.OK.value())
            )
        }
        val regionData = this.simpleApplicationConfigHelper.getRegionByRegionId(header.regionId)
        val topCloudOrgId = regionData.ecpData.orgId
        val curDate = DateTime.now().toDate()
        val org = OrganizationEntity()
        org.parentId = topCloudOrgId
        org.name = form.orgName
        org.regionId = header.regionId
        org.orgType = Constants.ORG_TYPE_ECP
        org.orgTypeChild = Constants.ORG_TYPE_ECP_CHILD
        org.orgCreateTime = curDate
        org.status = Constants.STATUS_YES
        org.orgLevel = "-0-1-".plus(topCloudOrgId).plus("-")
        org.activate = Constants.ACTIVATE_STATUS_YES
        org.ownerTree = Constants.OWNER_TREE_CHILD
        org.isRetire = Constants.IS_RETIRE_NO
        org.isFlow = Constants.STATUS_NO
        org.lastChangeUser = header.userId
        org.updateTime = curDate
        org.createTime = curDate
        this.organizationMapper.insertSelective(org)
        // 刷新MongoDB
        orgMongoService.conversionOrg(org.organizationId, null, header)
        return org.organizationId
    }

    /**
     * 编辑云上党支部
     */
    @Transactional(rollbackFor = [Exception::class])
    fun updateOrg(form: CloudOrgForm, header: HeaderHelper.SysHeader): Long {
        if (ObjectUtils.isEmpty(form.orgId)) {
            throw ApiException(
                "编辑失败!未传递org_id",
                Result<Any>(error, 5630, HttpStatus.OK.value(),"请传递组织id")
            )
        }
        val example = Example(OrganizationEntity::class.java)
        example.createCriteria().andEqualTo("organizationId", form.orgId)
        val org = OrganizationEntity()
        org.name = form.orgName
        organizationMapper.updateByExampleSelective(org, example)
        //刷新缓存
        val newOrg = organizationMapper.selectOneByExample(example)
        this.userOrgCacheCallService.flushOrgInfo(newOrg, 1)
        //刷新MongoDB
        orgMongoService.conversionOrg(form.orgId, null, header)
        return newOrg.organizationId
    }

    /**
     * 删除云区党支部
     */
    @Transactional(rollbackFor = [Exception::class])
    fun deleteOrg(form: CloudOrgForm, header: HeaderHelper.SysHeader) {
        val curDate = DateTime.now().toDate()
        val updateOrg = OrganizationEntity()
        updateOrg.status = Constants.STATUS_NO
        updateOrg.updateTime = curDate
        updateOrg.lastChangeUser = header.userId
        updateOrg.regionId = header.regionId
        val example = Example(OrganizationEntity::class.java)
        example.createCriteria().andEqualTo("organizationId", form.orgId)
        this.organizationMapper.updateByExampleSelective(updateOrg, example)
        // 刷新MongoDB
        orgMongoService.conversionOrg(form.orgId, null, header)
    }

    /**
     * 加入云区党支部
     */
    @Transactional(rollbackFor = [Exception::class])
    fun addUser(form: CloudOrgForm, header: HeaderHelper.SysHeader) {
        // 校验是否重复加入
        val example = Example(UserOrgAndCorpEntity::class.java)
        example.createCriteria().andEqualTo("userId", form.userId)
            .andEqualTo("organizationId", form.orgId)
        val cnt = this.userOrgAndCorpMapper.selectCountByExample(example)
        if (cnt > 0) {
            throw ApiException(
                "请勿重复加入同一个支部",
                Result<Any>(error, 35005, HttpStatus.OK.value())
            )
        }
        val curDate = DateTime.now().toDate()
        val userOrgCorpEntity = UserOrgAndCorpEntity()
        userOrgCorpEntity.userId = form.userId
        userOrgCorpEntity.organizationId = form.orgId
        userOrgCorpEntity.regionId = header.regionId
        userOrgCorpEntity.isEmployee = Constants.IS_EMPLOYEE_NO
        userOrgCorpEntity.updateTime = curDate
        userOrgCorpEntity.createTime = curDate
        userOrgCorpEntity.lastChangeUser = header.userId
        this.userOrgAndCorpMapper.insertSelective(userOrgCorpEntity)
        // 更新云区支部人员数量
        this.organizationMapper.updateUserNumByWhere(form.orgId, header.userId)
        // 刷新缓存
        this.userOrgCacheCallService.flushOrgInfo(form.orgId, Constants.STATUS_YES)
        // 刷新mongoDB
        userMongoService.conversionUserSync(form.userId, null, false, header)
    }
}