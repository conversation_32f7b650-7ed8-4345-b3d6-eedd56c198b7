package com.goodsogood.ows.service;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.SaasConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.*;
import com.goodsogood.ows.model.db.*;
import com.goodsogood.ows.model.mongo.ThirdToken;
import com.goodsogood.ows.model.mongo.User;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.TokenBase;
import com.google.common.base.Preconditions;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2018-03-21
 */
@Service
@Log4j2
public class UserService {

    @Value("${ppmd-tag-name}")
    private String ppmdTagName;

    private final String phoneRegexp = "^1\\d{10}$";

    private final Set<Long> resultSet = new HashSet<>();
    private final ObjectMapper OBJECTMAPPER = new ObjectMapper();

    private final UserMapper userMapper;
    private final UserDepartmentMapper userDepartmentMapper;
    private final UserRoleMapper userRoleMapper;
    private final RoleMapper roleMapper;
    private final RoleMenuMapper roleMenuMapper;
    private final DepartmentMapper departmentMapper;
    private final MenuMapper menuMapper;
    private final MenuUrlMapper menuUrlMapper;
    private final UserOrgAndCorpMapper userOrgAndCorpMapper;
    private final OrganizationMapper organizationMapper;
    private final CorporationMapper corporationMapper;
    private final StringRedisTemplate redisTemplate;
    private final Errors errors;
    private final UserThirdMapper userThirdMapper;
    private final OpenService openService;
    private final OrgTransInfoMapper orgTransInfoMapper;
    private final OrgTransUserMapper orgTransUserMapper;
    private final LoginLogService loginLogService;
    private final UserLoginLogMapper userLoginLogMapper;
    private final UserOrgCacheCallService userOrgCacheCallService;
    private final TagMapper tagMapper;
    private final OptionService optionService;
    private final BroadcastService broadcastService;
    private final GroupMapper groupMapper;
    private final UserGroupMapper userGroupMapper;
    private final OfficialAccountService officialAccountService;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final SynchService synchService;
    private final SaasConfig saasConfig;
    private final UserMongoService userMongoService;

//    private final ExaminationService examinationService;
    private final MessageService messageService;
    private final OrgGroupMemberService orgGroupMemberService;


    @Value("${org-role.wxMenuIds}")
    private String wxMenuIds;

    @Value("${org-role.wxManagerMenuIds}")
    private String wxManagerMenuIds;

    @Value("${init-org-user.tag_type}")
    private String initTagId;

    @Value("${user-option.retireCode}")
    private String userOptionRetireCode;

    @Value("${user-option.politicalCode}")
    private String politicalCode;

    @Value("${party-push-scheduler.dataSize}")
    private Integer dataSize;

    @Value("${party-push-scheduler.birthTemplateId}")
    @NotNull
    private Long birthTemplateId;

    @Value("${party-push-scheduler.joinTemplateId}")
    @NotNull
    private Long joinTemplateId;

    @Value("${party-push-scheduler.channelType}")
    @NotNull
    private Byte channelType;

    @Value("${party-push-scheduler.source}")
    @NotNull
    private String source;

    @Value("${party-push-scheduler.pushModel}")
    @NotNull
    private Byte pushModel;

    @Value("${evel-tag-type}")
    @NotNull
    private Integer evalTagType;

    // 默认一页大小
    private static final Integer PAGE_SIZE = 5;

    @Autowired
    public UserService(UserMapper userMapper, UserDepartmentMapper userDepartmentMapper, UserRoleMapper userRoleMapper, RoleMapper roleMapper, RoleMenuMapper roleMenuMapper, DepartmentMapper departmentMapper, MenuMapper menuMapper, MenuUrlMapper menuUrlMapper, UserOrgAndCorpMapper userOrgAndCorpMapper, OrganizationMapper organizationMapper, CorporationMapper corporationMapper, StringRedisTemplate redisTemplate, Errors errors, UserThirdMapper userThirdMapper, OpenService openService, OrgTransInfoMapper orgTransInfoMapper, OrgTransUserMapper orgTransUserMapper, LoginLogService loginLogService, UserLoginLogMapper userLoginLogMapper, UserOrgCacheCallService userOrgCacheCallService, TagMapper tagMapper, OptionService optionService, BroadcastService broadcastService, GroupMapper groupMapper, UserGroupMapper userGroupMapper, OfficialAccountService officialAccountService, SimpleApplicationConfigHelper simpleApplicationConfigHelper, SynchService synchService, SaasConfig saasConfig, UserMongoService userMongoService, MessageService messageService, OrgGroupMemberService orgGroupMemberService) {
        this.userMapper = userMapper;
        this.userDepartmentMapper = userDepartmentMapper;
        this.userRoleMapper = userRoleMapper;
        this.roleMapper = roleMapper;
        this.roleMenuMapper = roleMenuMapper;
        this.departmentMapper = departmentMapper;
        this.menuMapper = menuMapper;
        this.menuUrlMapper = menuUrlMapper;
        this.userOrgAndCorpMapper = userOrgAndCorpMapper;
        this.organizationMapper = organizationMapper;
        this.corporationMapper = corporationMapper;
        this.redisTemplate = redisTemplate;
        this.errors = errors;
        this.userThirdMapper = userThirdMapper;
        this.openService = openService;
        this.orgTransInfoMapper = orgTransInfoMapper;
        this.orgTransUserMapper = orgTransUserMapper;
        this.loginLogService = loginLogService;
        this.userLoginLogMapper = userLoginLogMapper;
        this.userOrgCacheCallService = userOrgCacheCallService;
        this.tagMapper = tagMapper;
        this.optionService = optionService;
        this.broadcastService = broadcastService;
        this.groupMapper = groupMapper;
        this.userGroupMapper = userGroupMapper;
        this.officialAccountService = officialAccountService;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.synchService = synchService;
        this.saasConfig = saasConfig;
        this.userMongoService = userMongoService;
        this.messageService = messageService;
        this.orgGroupMemberService = orgGroupMemberService;
    }

    /**
     * 新增用户部门信息
     *
     * @param entity
     *
     * @return
     */
    @Transactional
    public void insertUserDepartment(UserDepartmentEntity entity) {
        this.userDepartmentMapper.insert(entity);
        // 更新部门员工数量
        this.departmentMapper.addStaffNumber(entity.getDepartmentId(), 1);
    }

    /**
     * 更新用户部门信息
     *
     * @param entity
     *
     * @return
     */
    @Transactional
    public int updateUserDepartment(UserDepartmentEntity entity) {
        return this.userDepartmentMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 新增用户
     *
     * @param userEntity
     *
     * @return
     */
    @Transactional
    public boolean addUser(UserEntity userEntity, UserForm user, int type, long oId, Long regionId) {
        try {
            userEntity.setAge(Utils.getAge(userEntity.getCertNumber(), userEntity.getCertNumberSecret()));

            //身份证号脱敏
            userEntity.setCertNumberSecret(NumEncryptUtils.numSecret(userEntity.getCertNumber(), 1));
            //身份证号加密
            userEntity.setCertNumber(NumEncryptUtils.encrypt(userEntity.getCertNumber(), 1));
            //电话脱敏
            userEntity.setPhoneSecret(NumEncryptUtils.numSecret(userEntity.getPhone(), 2));
            //电话加密
            userEntity.setPhone(NumEncryptUtils.encrypt(userEntity.getPhone(), 2));
            // 是否生成第三方数据ID
            if (this.saasConfig.getGenerateThirdId()) {
                userEntity.setId(this.saasConfig.getThirdIdPrefix() + UUID.randomUUID().toString().replaceAll("-", ""));
            }
            this.userMapper.addUser(userEntity);
            // 查询root节点ID isRoot=1
            List<Long> rootList = new ArrayList<>();
            DepartmentForm form = this.departmentMapper.getDepPid(null, oId, type, Constants.IS_ROOT_YES);
            rootList.add(form.getDepartmentId());
            if (user.getDep() != null && user.getDep().size() > 0) {
                // 用户新增成功,新增用户和部门关系记录
                this.userDepartmentMapper.batchInsert(user.getDep(), userEntity.getUserId(), Constants.DUTY_GENERAL);
                log.debug("新增用户和部门关系完成 -> departmentId:{}", user.getDep());
                // 根据部门ID查询上级部门ID
                Set<Long> allSet = new HashSet<>();
                for (Long depId : user.getDep()) {
                    Set<Long> set = getDepList(depId, type, oId);
                    allSet.addAll(set);
                }
                List<Long> allList = new ArrayList<>(allSet);
                // 部门以及所有上级部门员工数量+1
                this.departmentMapper.batchUpdateStaffNumber(allList, 1, Constants.STATUS_NOT_DEL);
                log.debug("部门员工数量+1完成 -> departmentId:{}", user.getDep());
            } else {
                // 默认用户挂root部门
                this.userDepartmentMapper.batchInsert(rootList, userEntity.getUserId(), Constants.DUTY_GENERAL);
            }
            // root部门员工数量+1
            this.departmentMapper.batchUpdateStaffNumber(rootList, 1, Constants.STATUS_NOT_DEL);
            // 单位员工数量+1
            if (type == Constants.DEP_TYPE_CORP) {
                this.corporationMapper.updateStaffNumberById(1, oId);
            }
            // 新增用户单位、组织关系表
            UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
            entity.setUserId(userEntity.getUserId());
            if (type == Constants.DEP_TYPE_CORP) {
                entity.setCorporationId(oId);
            } else if (type == Constants.DEP_TYPE_ORG) {
                entity.setOrganizationId(oId);
            }
            entity.setScore(0);
            entity.setTagId(initUserOrgTag(user.getTagId()));
            this.userOrgAndCorpMapper.insert(entity);
            log.debug("新增用户单位、组织关系信息完成 -> userId:{}", entity.getUserId());
            // 新增用户，默认权限
            RoleEntity re = this.roleMapper.getRoleByType(Constants.ROLE_TYPE_DEFAULT, type, oId, Constants.MENU_BELONG_PC, regionId);
            if (re != null && re.getRoleId() != null) {
                UserRoleEntity ure = new UserRoleEntity();
                ure.setRoleId(re.getRoleId());
                ure.setUserId(userEntity.getUserId());
                if (Constants.DEP_TYPE_CORP == type) {
                    ure.setCorporationId(oId);
                } else if (Constants.DEP_TYPE_ORG == type) {
                    ure.setOrganizationId(oId);
                }
                this.userRoleMapper.insert(ure);
                log.debug("新增用户时添加默认权限成功 userId -> {}", userEntity.getUserId());
            }

            // 刷新缓存 tc2018.08.17**/
            // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24**/
            userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
        } catch (Exception e) {
            log.error("系统异常，新增员工失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * @param depId
     * @param type
     * @param oId
     *
     * @return
     */
    private DepParentForm depIdList(Long depId, int type, Long oId) {
        DepParentForm result = new DepParentForm();
        DepartmentForm de = this.departmentMapper.getDepPid(depId, oId, type, Constants.IS_ROOT_NO);
        resultSet.add(depId);
        if (de != null && de.getParentId() != null) {
            result.setDepId(depId);
            result.setParent(depIdList(de.getParentId(), type, oId));
        }
        return result;
    }

    /**
     * @param depId
     * @param type
     * @param oId
     *
     * @return
     */
    public Set<Long> getDepList(Long depId, int type, Long oId) {
        resultSet.clear();
        depIdList(depId, type, oId);
        return resultSet;
    }

    /**
     * 修改用户信息
     *
     * @return
     *
     * @description 2-其他组织存在，回显用户基本数据，修改信息
     */
    @Transactional
    public boolean editUser(UserForm form, HeaderHelper.SysHeader header) {
        try {
            long oId = header.getOid();
            int type = header.getType();
            Long regionId = header.getRegionId();
            List<Long> depId = form.getDep();
            // 查询root部门（is_root = 1）
            DepForm df = this.departmentMapper.findRootId(oId, type);
            UserEntity entity = new UserEntity();
            entity.setUserId(form.getUserId());
            entity.setName(form.getName());
            entity.setPhone(form.getPhone());
            entity.setPhoneSecret(form.getPhoneSecret());
            entity.setCertType(form.getCertType());
            entity.setCertNumber(form.getCertNumber());
            entity.setCertNumberSecret(form.getCertNumberSecret());
            entity.setGender(form.getGender());
            entity.setCensusType(form.getCensusType());
            entity.setNationality(form.getNationality());
            entity.setMarriage(form.getMarriage());
            entity.setEducation(form.getEducation());
            entity.setPoliticalType(form.getPoliticalType());
            entity.setEthnic(form.getEthnic());
            entity.setLastChangeUser(header.getUserId());
            entity.setUpdateTime(new Date());
            // 其他组织存在用户信息时挂载到当前组织
            if ("2".equals(form.getFlag())) {
                // 修改信息
                this.userMapper.updateByPrimaryKeySelective(entity);
                // 新增用户和当前单位或组织关系
                UserOrgAndCorpEntity ucEntity = new UserOrgAndCorpEntity();
                ucEntity.setUserId(form.getUserId());
                ucEntity.setTagId(form.getTagId());
                ucEntity.setEmail(form.getEmail());
                ucEntity.setPosition(form.getPosition());
                ucEntity.setJobNumber(form.getJobNumber());
                ucEntity.setEntryDate(form.getEntryDate());
                if (Constants.DEP_TYPE_CORP == header.getType()) {
                    ucEntity.setCorporationId(oId);
                } else if (Constants.DEP_TYPE_ORG == header.getType()) {
                    ucEntity.setOrganizationId(oId);
                }
                this.userOrgAndCorpMapper.insertSelective(ucEntity);
                // 新增用户默认角色
                RoleEntity role = this.roleMapper.getRoleByType(Constants.ROLE_TYPE_DEFAULT, type, oId, Constants.MENU_BELONG_PC, regionId);
                UserRoleEntity ure = new UserRoleEntity();
                ure.setUserId(form.getUserId());
                ure.setRoleId(role.getRoleId());
                if (Constants.DEP_TYPE_CORP == header.getType()) {
                    ure.setCorporationId(oId);
                } else if (Constants.DEP_TYPE_ORG == header.getType()) {
                    ure.setOrganizationId(oId);
                }
                this.userRoleMapper.insertSelective(ure);
                // 新增用户部门关系，若没有选择部门则挂载到root部门
                if (depId == null || depId.isEmpty()) {
                    UserDepartmentEntity ude = new UserDepartmentEntity();
                    ude.setDuty(Constants.DUTY_GENERAL);
                    ude.setUserId(form.getUserId());
                    ude.setDepartmentId(df.getDepartmentId());
                    this.userDepartmentMapper.insertSelective(ude);
                    // root部门员工数量+1
                    List<Long> rootList = new ArrayList<>();
                    rootList.add(df.getDepartmentId());
                    this.departmentMapper.batchUpdateStaffNumber(rootList, 1, Constants.STATUS_NOT_DEL);
                } else {
                    // 用户新增成功,新增用户和部门关系记录
                    this.userDepartmentMapper.batchInsert(depId, form.getUserId(), Constants.DUTY_GENERAL);
                    // 根据部门ID查询上级部门ID
                    Set<Long> allSet = new HashSet<>();
                    for (Long dId : depId) {
                        Set<Long> set = getDepList(dId, type, oId);
                        allSet.addAll(set);
                    }
                    List<Long> allList = new ArrayList<>(allSet);
                    // 部门以及所有上级部门员工数量+1
                    this.departmentMapper.batchUpdateStaffNumber(allList, 1, Constants.STATUS_NOT_DEL);
                }
                // 单位员工数量+1
                if (type == Constants.DEP_TYPE_CORP) {
                    this.corporationMapper.updateStaffNumberById(1, oId);
                }
                return true;
            }

            // 根据用户ID查询所属部门
            List<Long> depList = this.userDepartmentMapper.getDepIdByUserId(form.getUserId(), Constants.DUTY_GENERAL, type, oId);
            if (depList != null && !depList.isEmpty()) {
                // 批量删除原用户部门信息
                this.userDepartmentMapper.batchDeleteByUserId(depList, form.getUserId(), Constants.DUTY_GENERAL);
            }
            // 更新部门人数
            if (depList != null && depId != null && !depList.isEmpty() && !depId.isEmpty()) {
                List<Long> plusList = new ArrayList<>(depId);
                List<Long> minusList = new ArrayList<>();
                for (Long id : depList) {
                    if (!depId.contains(id)) {
                        minusList.add(id);
                    } else {
                        plusList.remove(id);
                    }
                }
                if (!plusList.isEmpty()) {
                    // 查询部门所有上级部门
                    List<Long> pList = getParentList(plusList, type, oId);
                    pList.remove(df.getDepartmentId());
                    if (!pList.isEmpty()) {
                        Set<Long> set = new HashSet<>(pList);
                        set.addAll(plusList);
                        List<Long> newList = new ArrayList<>(set);
                        newList.remove(df.getDepartmentId());
                        List<Long> rList = getRepeatId(newList, depList);
                        if (rList != null) {
                            for (Long id : rList) {
                                newList.remove(id);
                            }
                        }
                        if (!newList.isEmpty()) {
                            this.departmentMapper.batchUpdateStaffNumber(newList, 1, Constants.STATUS_NOT_DEL);
                        }
                    }
                }
                if (!minusList.isEmpty()) {
                    // 查询部门所有上级部门
                    List<Long> pList = getParentList(minusList, type, oId);
                    pList.remove(df.getDepartmentId());
                    if (!pList.isEmpty()) {
                        Set<Long> set = new HashSet<>(pList);
                        set.addAll(minusList);
                        List<Long> mList = new ArrayList<>(set);
                        mList.remove(df.getDepartmentId());
                        List<Long> rList = getRepeatId(mList, depId);
                        if (rList != null) {
                            for (Long id : rList) {
                                mList.remove(id);
                            }
                        }
                        this.departmentMapper.batchUpdateStaffNumber(mList, -1, Constants.STATUS_NOT_DEL);
                    }
                    Set<Long> s = new HashSet<>();
                    s.addAll(pList);
                    s.addAll(minusList);
                    List<Long> l = new ArrayList<>(s);
                    List<Long> updateList = new ArrayList<>();
                    for (Long id : l) {
                        int no = this.departmentMapper.getStaffNumber(id);
                        if (no == 0) {
                            updateList.add(id);
                        }
                    }
                    if (!updateList.isEmpty()) {
                        this.departmentMapper.batchUpdateStaffNumber(updateList, 0, Constants.STATUS_YES);
                    }
                }
            } else if ((depList == null || depList.isEmpty()) && depId != null && !depId.isEmpty()) {
                // 查询部门所有上级部门
                List<Long> pList = getParentList(depId, type, oId);
                // 更新所有上级部门员工数量
                pList.remove(df.getDepartmentId());
                this.departmentMapper.batchUpdateStaffNumber(pList, 1, Constants.STATUS_NOT_DEL);
            } else if ((depId == null || depId.isEmpty()) && depList != null && !depList.isEmpty()) {
                // 查询部门所有上级部门
                List<Long> pList = getParentList(depList, type, oId);
                pList.remove(df.getDepartmentId());
                if (!pList.isEmpty()) {
                    // 更新所有上级部门员工数量
                    this.departmentMapper.batchUpdateStaffNumber(pList, -1, Constants.STATUS_NOT_DEL);
                }
                List<Long> updateList = new ArrayList<>();
                for (Long id : pList) {
                    int no = this.departmentMapper.getStaffNumber(id);
                    if (no == 0) {
                        updateList.add(id);
                    }
                }
                if (!updateList.isEmpty()) {
                    this.departmentMapper.batchUpdateStaffNumber(updateList, 0, Constants.STATUS_YES);
                }
            }
            if (depId != null && !depId.isEmpty()) {
                // 批量新增用户部门信息
                this.userDepartmentMapper.batchInsert(depId, form.getUserId(), Constants.DUTY_GENERAL);
            } else {
                // 批量新增用户root部门信息
                List<Long> rootList = new ArrayList<>();
                rootList.add(df.getDepartmentId());
                this.userDepartmentMapper.batchInsert(rootList, form.getUserId(), Constants.DUTY_GENERAL);
            }
            // 更新用户信息
            if (form.getCertNumber() != null) {
                entity.setAge(Utils.getAge(form.getCertNumber(), form.getCertNumberSecret()));
            }
            this.userMapper.updateByPrimaryKeySelective(entity);
            // 更新用户标签
            UserOrgAndCorpEntity ue = new UserOrgAndCorpEntity();
            ue.setTagId(form.getTagId());
            ue.setUserId(form.getUserId());
            List<UserOrgAndCorpEntity> list = new ArrayList<>();
            list.add(ue);
            this.userOrgAndCorpMapper.batchUpdateTag(list, type, oId);
            // 刷新缓存 tc2018.08.17**/
            // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24**/
            userOrgCacheCallService.flushUserInfo(header.getRegionId(), entity, 1);
        } catch (Exception e) {
            log.error("系统异常，修改员工失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * @param list1
     * @param list2
     *
     * @return
     */
    public List<Long> getRepeatId(List<Long> list1, List<Long> list2) {
        List<Long> list = new ArrayList<>();
        for (Long id : list1) {
            if (list2.contains(id)) {
                list.add(id);
            }
        }
        return list.isEmpty() ? null : list;
    }

    /**
     * 根据id查询所有上级部门ID
     *
     * @param list
     * @param type
     * @param oId
     *
     * @return
     */
    private List<Long> getParentList(List<Long> list, int type, long oId) {
        Set<Long> aSet = new HashSet<>();
        for (Long id : list) {
            Set<Long> set = getDepList(id, type, oId);
            aSet.addAll(set);
        }
        return new ArrayList<>(aSet);
    }

    /**
     * 根据条件修改用户信息
     *
     * @param userEntity
     *
     * @return
     */
    @Transactional
    public int updateUserByWhere(UserEntity userEntity) {
        return this.userMapper.updatePwdByPhone(userEntity);
    }

    /**
     * 修改手机号码
     *
     * @param userEntity
     *
     * @return
     */
    @Transactional
    public int changePhoneById(UserEntity userEntity) {
        return this.userMapper.changePhoneById(userEntity);
    }

    /**
     * 离职-清空用户和单位或组织以及部门关系，转入中转站
     *
     * @param userEntity
     *
     * @return
     */
    @Transactional
    public String leave(UserEntity userEntity, int type, Long oId) {
        try {
            Example example = new Example(UserDepartmentEntity.class);
            example.createCriteria().andEqualTo("userId", userEntity.getUserId()).andEqualTo("duty", Constants.DUTY_LEADER);
            List<UserDepartmentEntity> existList = this.userDepartmentMapper.selectByExample(example);
            if (existList != null && !existList.isEmpty()) {
                return "exist";
            }
            // 查询root部门（is_root = 1）
            DepartmentEntity depEntity = this.departmentMapper.getRootDepId(oId, type, Constants.IS_ROOT_YES);
            // 查询用户所在部门
            List<Long> idList = this.userDepartmentMapper.getDepIdByUserId(userEntity.getUserId(), Constants.DUTY_GENERAL, type, oId);
            // 查询部门的所有上级
            Set<Long> allSet = new HashSet<>();
            for (Long id : idList) {
                Set<Long> set = getDepList(id, type, oId);
                allSet.addAll(set);
            }
            List<Long> allList = new ArrayList<>(allSet);
            if (depEntity != null && depEntity.getDepartmentId() != null && !allList.contains(depEntity.getDepartmentId())) {
                allList.add(depEntity.getDepartmentId());
            }
            // 部门以及所有上级部门员工数量-1
            this.departmentMapper.batchUpdateStaffNumber(allList, -1, Constants.STATUS_NOT_DEL);
            // 清空用户单位或组织关系
            this.userOrgAndCorpMapper.deleteByUserId(type, oId, userEntity.getUserId());
            // 清空用户部门关系
            this.userDepartmentMapper.deleteByUserId(userEntity.getUserId());
            // 清空用户角色关系
            this.userRoleMapper.deleteByUserId(userEntity.getUserId());
            UserEntity ue = this.userMapper.selectByPrimaryKey(userEntity);

            // 离职人员挂载到当前组织或者上级组织中转站
            // 校验当前组织是否存在中转站
            Example transExample = new Example(OrgTransInfoEntity.class);
            transExample.createCriteria().andEqualTo("organizationId", oId).andEqualTo("status", 0);
            int exist = this.orgTransInfoMapper.selectCountByExample(transExample);

            OrgTransUserEntity orgTransUserEntity = new OrgTransUserEntity();
            orgTransUserEntity.setUserId(userEntity.getUserId());
            // 添加用户到上级组织中转站
            if (exist > 0) {
                orgTransUserEntity.setOrganizationId(oId);
            } else {
                Example pExample = new Example(OrganizationEntity.class);
                pExample.createCriteria().andEqualTo("organizationId", oId);
                OrganizationEntity oe = this.organizationMapper.selectOneByExample(pExample);
                orgTransUserEntity.setOrganizationId(oe.getParentId());
            }
            this.orgTransUserMapper.insertSelective(orgTransUserEntity);

            return ue.getName();
        } catch (Exception e) {
            log.error("系统异常，离职操作失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return "";
        }
    }

    /**
     * 查询部门人员信息
     *
     * @param pageNumber
     * @param name
     * @param
     *
     * @return
     */
    public Page<UserForm> getUser(PageNumber pageNumber, String name, long oId, int type, Long depId) {
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        Page<UserForm> resultForm = PageHelper.startPage(p, r).doSelectPage(() -> userDepartmentMapper.getDepUser(StringUtils.isBlank(name) ? "" : "%" + name + "%", oId, type, depId));
        if (depId != null) {
            for (UserForm form : resultForm) {
                List<String> depName = new ArrayList<>();
                List<Long> depIds = new ArrayList<>();
                depIds.add(form.getDepartmentId());
                depName.add(form.getDepartmentName());
                form.setDepName(depName);
                form.setDepId(depIds);
                form.setDepartmentId(null);
                form.setDepartmentName(null);
            }
        } else {
            for (UserForm uf : resultForm) {
                List<String> depName = new ArrayList<>();
                List<Long> depIds = new ArrayList<>();
                List<DepartmentEntity> depList = this.userDepartmentMapper.findDepUser(uf.getUserId(), type, oId);
                if (depList != null && !depList.isEmpty()) {
                    for (DepartmentEntity de : depList) {
                        depName.add(de.getName());
                        depIds.add(de.getDepartmentId());
                    }
                }
                uf.setDepName(depName);
                uf.setDepId(depIds);
                uf.setDepartmentId(null);
                uf.setDepartmentName(null);
            }
        }
        return resultForm;
    }

    /**
     * 查询部门管理员
     *
     * @param did
     *
     * @return
     */
    public List<UserForm> getDepManager(long did, int duty) {
        return this.userDepartmentMapper.getDepManager(did, duty);
    }

    /**
     * 取消(删除)部门管理员
     *
     * @param userId
     * @param depId
     * @param duty
     *
     * @return
     */
    @Transactional
    public int updateDepManager(long userId, long depId, int duty) {
        UserDepartmentEntity entity = new UserDepartmentEntity();
        entity.setDuty(Constants.DUTY_MANAGER);
        entity.setDepartmentId(depId);
        entity.setUserId(userId);
        return this.userDepartmentMapper.delete(entity);
    }

    /**
     * 新增部门管理员
     *
     * @param userId
     * @param depId
     *
     * @return
     */
    @Transactional
    public int addDepManager(long userId, long depId) {
        // 查询该员工是否是部门管理员
        UserDepartmentEntity ude = new UserDepartmentEntity();
        ude.setUserId(userId);
        ude.setDepartmentId(depId);
        ude.setDuty(Constants.DUTY_LEADER);
        // 是否是超级管理员
        int i = this.userDepartmentMapper.selectCount(ude);
        if (i == 1) {
            return 9;
        }
        // 是否是管理员
        ude.setDuty(Constants.DUTY_MANAGER);
        int cnt = this.userDepartmentMapper.selectCount(ude);
        if (cnt == 0) {
            // 新增管理员
            this.userDepartmentMapper.insert(ude);
        } else {
            return 2;
        }
        return 1;
    }


    /**
     * 系统管理员首页-查询
     *
     * @param map
     *
     * @return
     */
    public Page<SysUserForm> getSysList(PageNumber pageNumber, ConcurrentMap<String, Object> map) {
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        // 超级管理员权限
        int isSystemManager = isSystemManger((long) map.get("userId"));
        map.put("sys", isSystemManager);
        Page<SysUserForm> sysUserForm = PageHelper.startPage(p, r).doSelectPage(() -> this.userRoleMapper.getSysList(map));

        for (SysUserForm form : sysUserForm.getResult()) {
            int duty = form.getDuty();
            if (duty == Constants.DUTY_MANAGER) {
                form.setDutyName(Constants.DUTY_MANAGER_NAME);
            } else if (duty == Constants.DUTY_LEADER) {
                form.setDutyName(Constants.DUTY_LEADER_NAME);
            }
        }
        return sysUserForm;
    }

    /**
     * 新建管理员账号-首页
     *
     * @return
     */
    public SysForm getSys(int depType, long oId) {
        SysForm sysForm = new SysForm();
        // 查询部门信息
        List<DepartmentForm> depList = this.departmentMapper.getAllDep(depType, oId, Constants.STATUS_YES);
        // 查询菜单信息
        // List<MenuForm> menuList = this.menuMapper.getAllMenu(Constants.STATUS_YES);
        sysForm.setDep(depList);
        // sysForm.setMenu(menuList);
        sysForm.setMenu(getMenuChildren("0"));
        return sysForm;
    }

    /**
     * 新建，修改管理员账号
     *
     * @param form
     */
    @Transactional
    public boolean addSys(SysAddForm form, HeaderHelper.SysHeader header) {
        Integer type = header.getType();
        Long oId = header.getOid();
        Long regionId = header.getRegionId();

        try {
            // 查询用户所在部门
            List<Long> existList = this.userDepartmentMapper.getDepIdByUserId(form.getUserId(), Constants.DUTY_GENERAL, type, oId);
            // 如果是全单位
            if (form.getScope() == Constants.SCOPE_ALL) {
                // 查询单位下所有部门
                List<Long> depList = this.departmentMapper.getAllDepartmentId(type, oId);
                if (depList != null && !depList.isEmpty()) {
                    // 清空已有部门人员关系
                    this.userDepartmentMapper.batchDelete(depList, form.getUserId());
                }
                // 查询root部门（is_root = 1）
                DepartmentEntity depEntity = this.departmentMapper.getRootDepId(oId, type, Constants.IS_ROOT_YES);
                if (depEntity != null && depEntity.getDepartmentId() != null) {
                    // 新增部门人员关系
                    List<Long> paramList = new ArrayList<>();
                    paramList.add(depEntity.getDepartmentId());
                    // this.userDepartmentMapper.batchInsert(paramList, form.getUserId(), Constants.DUTY_MANAGER);
                    this.userDepartmentMapper.batchInsert(paramList, form.getUserId(), Constants.DUTY_LEADER);
                }
            } else if (form.getScope() == Constants.SCOPE_PART) {
                // 清空已有部门人员关系
                this.userDepartmentMapper.deleteByUserDuty(form.getUserId(), Constants.DUTY_MANAGER);
                // 新增部门人员关系
                this.userDepartmentMapper.batchInsert(form.getDep(), form.getUserId(), Constants.DUTY_MANAGER);
            }
            if (existList != null && !existList.isEmpty()) {
                this.userDepartmentMapper.batchInsert(existList, form.getUserId(), Constants.DUTY_GENERAL);
            }

            // 根据roleId判断是否是新增
            Long roleId = form.getRoleId();
            if (roleId == -1) {
                // 根据userId查询原有角色信息
                UserOrgAndCorpEntity ue = new UserOrgAndCorpEntity();
                ue.setUserId(form.getUserId());
                if (type == Constants.DEP_TYPE_ORG) {
                    ue.setOrganizationId(oId);
                } else if (type == Constants.DEP_TYPE_CORP) {
                    ue.setCorporationId(oId);
                }
                // 每个用户只有一个角色权限
                List<Long> rList = this.userRoleMapper.getRoleList(ue);
                for (Long rId : rList) {
                    // 根据roleId查询roleType
                    int roleType = this.roleMapper.getRoleTypeByRoleId(rId);
                    // 如果是默认权限，则不允许删除
                    if (roleType != Constants.ROLE_TYPE_DEFAULT) {
                        // 删除原有角色信息
                        RoleEntity re = new RoleEntity();
                        re.setRoleId(rId);
                        this.roleMapper.deleteByPrimaryKey(re);
                        // 删除原有角色菜单信息
                        this.roleMenuMapper.deleteMenuByRoleId(rId);
                    }
                    // 删除原有用户角色信息
                    UserRoleEntity ure = new UserRoleEntity();
                    ure.setCorporationId(oId);
                    ure.setOrganizationId(oId);
                    ure.setUserId(form.getUserId());
                    ure.setRoleId(rId);
                    List<UserRoleEntity> l = new ArrayList<>();
                    l.add(ure);
                    this.userRoleMapper.batchDelete(l, type);
                }

                // 新增角色
                RoleEntity roleEntity = new RoleEntity();
                roleEntity.setName("自定义角色");
                roleEntity.setRoleType(Constants.ROLE_TYPE_DEFINED_USER);
                roleEntity.setStatus(Constants.STATUS_YES);
                roleEntity.setLastChangeUser(form.getUserId());
                roleEntity.setOrgType(type);
                if (Constants.DEP_TYPE_CORP == type) {
                    roleEntity.setCorporationId(oId);
                } else if (Constants.DEP_TYPE_ORG == type) {
                    roleEntity.setOrganizationId(oId);
                }
                this.roleMapper.insertRole(roleEntity);
                // 新增用户角色信息
                List<UserRoleEntity> roleList = new ArrayList<>();
                UserRoleEntity entity = new UserRoleEntity();
                entity.setRoleId(roleEntity.getRoleId());
                entity.setUserId(form.getUserId());
                entity.setOrganizationId(oId);
                entity.setCorporationId(oId);
                roleList.add(entity);
                this.userRoleMapper.batchInsert(roleList, type);
                // 修改-根据角色ID删除原有菜单列表
                this.roleMenuMapper.deleteMenuByRoleId(roleEntity.getRoleId());
                // 新增角色菜单信息
                this.roleMenuMapper.batchInsert(form.getMenu(), roleEntity.getRoleId(), regionId);
            } else {
                // 修改-根据角色ID删除原有菜单列表
                this.roleMenuMapper.deleteMenuByRoleId(form.getRoleId());
                // 新增角色菜单信息
                this.roleMenuMapper.batchInsert(form.getMenu(), form.getRoleId(), regionId);
            }
        } catch (Exception e) {
            log.error("新建，修改管理员账号,操作失败 userId:[" + form.getUserId(), e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * 移除管理
     *
     * @param userId
     *
     * @return
     */
    @Transactional
    public boolean cancel(long userId, int type, long oId, long regionId) {
        try {
            Example example = new Example(UserDepartmentEntity.class);
            example.createCriteria().andEqualTo("userId", userId).andEqualTo("duty", Constants.DUTY_MANAGER);
            this.userDepartmentMapper.deleteByExample(example);
            // 查询默认权限
            RoleEntity roleEntity = this.roleMapper.getRoleByType(Constants.ROLE_TYPE_DEFAULT, type, oId, Constants.MENU_BELONG_PC, regionId);
            //更新用户权限为默认权限
            UserRoleEntity ure = new UserRoleEntity();
            ure.setRoleId(roleEntity.getRoleId());
            ure.setUserId(userId);
            if (Constants.DEP_TYPE_CORP == type) {
                ure.setCorporationId(oId);
            } else if (Constants.DEP_TYPE_ORG == type) {
                ure.setOrganizationId(oId);
            }
            this.userRoleMapper.updateByPrimaryKeySelective(ure);
        } catch (Exception e) {
            log.error("系统异常，移除管理失败 userId:[" + userId, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * 移交管理
     *
     * @param id
     * @param nId
     *
     * @return
     */
    @Transactional
    public int change(long id, long nId, int type, long oId) {
        try {
            // 查询root部门ID
            DepartmentForm form = this.departmentMapper.getDepPid(null, oId, type, Constants.IS_ROOT_YES);
            UserDepartmentEntity delEntity = new UserDepartmentEntity();
            delEntity.setDepartmentId(form.getDepartmentId());
            delEntity.setUserId(id);
            delEntity.setDuty(Constants.DUTY_LEADER);
            this.userDepartmentMapper.delete(delEntity);
            // 校验该用户是否是超级管理员
            Example example = new Example(UserDepartmentEntity.class);
            example.createCriteria().andEqualTo("userId", nId).andEqualTo("departmentId", form.getDepartmentId()).andEqualTo("duty", Constants.DUTY_LEADER);
            UserDepartmentEntity existEntity = this.userDepartmentMapper.selectOneByExample(example);
            if (existEntity != null && existEntity.getUserId() != null) {
                return 2;
            } else {
                UserDepartmentEntity insertEntity = new UserDepartmentEntity();
                insertEntity.setDuty(Constants.DUTY_LEADER);
                insertEntity.setUserId(nId);
                insertEntity.setDepartmentId(form.getDepartmentId());
                this.userDepartmentMapper.insert(insertEntity);
            }

            /*UserDepartmentEntity entity = new UserDepartmentEntity();
            entity.setUserId(id);
            entity.setDuty(Constants.DUTY_GENERAL);
            this.userDepartmentMapper.updateByPrimaryKeySelective(entity);
            entity = new UserDepartmentEntity();
            entity.setUserId(nId);
            entity.setDuty(Constants.DUTY_LEADER);
            this.userDepartmentMapper.updateByPrimaryKeySelective(entity);*/

        } catch (Exception e) {
            log.error("系统异常，移交管理失败 -> id:{},nId:{}", id, nId, e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 9;
        }
        return 1;
    }

    /**
     * 人员管理-查询
     *
     * @param form
     *
     * @return
     */
    public Page<UserResultForm> getUserList(PageNumber pageNumber, UserQueryForm form, int type, long oId, long userId) {
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        int flag = isSystemManger(userId);
        return PageHelper.startPage(p, r).doSelectPage(() -> this.userMapper.getUserList(form, type, oId, flag));
    }

    /**
     * 人员管理-权限分配-查询
     *
     * @return
     */
    public UserRoleForm allotList(int type, long oId) {
        // 查询角色
        List<RoleForm> roleList = this.roleMapper.getAllRole(Constants.STATUS_YES, type, oId);
        // 查询菜单
        // List<MenuForm> menuList = this.menuMapper.getAllMenu(Constants.STATUS_YES);
        UserRoleForm form = new UserRoleForm();
        form.setRole(roleList);
        form.setMenu(getMenuChildren("0"));
        // form.setMenu(menuList);
        return form;
    }

    /**
     * 树状结构菜单
     *
     * @param menuId
     *
     * @return
     */
    private List<MenuChildrenForm> getMenuChildren(String menuId) {
        // 查询菜单
        List<MenuForm> menuList = this.menuMapper.getChildrenMenu(Constants.STATUS_YES, menuId);
        List<MenuChildrenForm> result = new ArrayList<>();
        if (menuList != null && !menuList.isEmpty()) {
            for (MenuForm menu : menuList) {
                MenuChildrenForm resultForm = new MenuChildrenForm();
                resultForm.setMenuId(menu.getMenuId());
                resultForm.setParentId(menu.getParentId());
                resultForm.setName(menu.getName());
                resultForm.setChildren(getMenuChildren(menu.getMenuId()));
                result.add(resultForm);
            }
        }
        return result;
    }

    /**
     * 人员管理-权限分配-提交
     *
     * @param type
     * @param oId
     * @param form
     */
    @Transactional
    public boolean updateUserRole(long userId, int type, long oId, long regionId, RoleAllotForm form) {
        try {
            // 根据原roleId查询menuList，如果菜单列表有改动，原角色不变，用户再新增角色 role_type=3 用户自定义权限
            List<String> oldMenuList = this.roleMenuMapper.menuList(form.getRoleId());
            // 根据roleId查询roleType 1-默认权限 2-其他权限 3-自定义权限 9-超级权限
            int roleType = this.roleMapper.getRoleTypeByRoleId(form.getRoleId());
            List<String> menuList = form.getMenu();
            if (oldMenuList.size() != menuList.size()) {
                // 菜单列表有改动，新增角色
                commonUpdate(userId, form, type, oId, roleType, regionId, oldMenuList);
            } else {
                String flag = "1";
                for (String newMenu : menuList) {
                    if (oldMenuList.contains(newMenu)) {
                        continue;
                    } else {
                        flag = "0";
                        break;
                    }
                }
                if ("0".equals(flag)) {
                    // 菜单列表有改动，新增角色
                    commonUpdate(userId, form, type, oId, roleType, regionId, oldMenuList);
                } else {
                    // 删除原有用户角色信息
                    List<UserRoleEntity> roleList = new ArrayList<>();
                    UserRoleEntity entity = new UserRoleEntity();
                    entity.setUserId(form.getUserId());
                    entity.setOrganizationId(oId);
                    entity.setCorporationId(oId);
                    roleList.add(entity);
                    this.userRoleMapper.batchDelete(roleList, type);
                    // 新增用户角色信息
                    entity.setRoleId(form.getRoleId());
                    roleList.clear();
                    roleList.add(entity);
                    this.userRoleMapper.batchInsert(roleList, type);
                }
            }
        } catch (Exception e) {
            log.error("权限分配提交失败,roleId[" + form.getRoleId() + "]", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * @param userId
     * @param form
     * @param type
     * @param oId
     */
    private void commonUpdate(long userId, RoleAllotForm form, int type, long oId, int roleType, long regionId, List<String> oldMenuList) {
        if (roleType != 3) {
            List<UserRoleEntity> roleList;
            // 菜单列表有改动，新增角色
            RoleEntity roleEntity = new RoleEntity();
            roleEntity.setName("自定义角色");
            roleEntity.setRoleType(Constants.ROLE_TYPE_DEFINED_USER);
            roleEntity.setStatus(Constants.STATUS_YES);
            roleEntity.setLastChangeUser(userId);
            roleEntity.setOrgType(type);
            if (Constants.DEP_TYPE_CORP == type) {
                roleEntity.setCorporationId(oId);
            } else if (Constants.DEP_TYPE_ORG == type) {
                roleEntity.setOrganizationId(oId);
            }
            this.roleMapper.insertRole(roleEntity);

            // 根据用户ID查询原有角色信息
            UserRoleEntity userRoleEntity = new UserRoleEntity();
            userRoleEntity.setUserId(form.getUserId());
            if (Constants.DEP_TYPE_CORP == type) {
                userRoleEntity.setCorporationId(oId);
            } else if (Constants.DEP_TYPE_ORG == type) {
                userRoleEntity.setOrganizationId(oId);
            }
            roleList = this.userRoleMapper.select(userRoleEntity);
            if (roleList != null && !roleList.isEmpty()) {
                this.userRoleMapper.batchDelete(roleList, type);
            }

            // 删除原用户角色信息关系
            UserRoleEntity ure = new UserRoleEntity();
            ure.setRoleId(form.getRoleId());
            ure.setUserId(form.getUserId());
            ure.setOrganizationId(oId);
            ure.setCorporationId(oId);
            roleList.clear();
            roleList.add(ure);
            this.userRoleMapper.batchDelete(roleList, type);

            // 新增用户角色信息
            UserRoleEntity entity = new UserRoleEntity();
            entity.setRoleId(roleEntity.getRoleId());
            entity.setUserId(form.getUserId());
            entity.setOrganizationId(oId);
            entity.setCorporationId(oId);
            roleList.clear();
            roleList.add(entity);
            this.userRoleMapper.batchInsert(roleList, type);
            // 删除原有角色菜单信息
            this.roleMenuMapper.deleteMenuByRoleId(form.getRoleId());
            // 新增角色菜单信息
            this.roleMenuMapper.batchInsert(form.getMenu(), roleEntity.getRoleId(), regionId);
        } else {
            // 删除原有角色菜单信息
            this.roleMenuMapper.batchDelete(oldMenuList, form.getRoleId());
            // 新增角色菜单信息
            this.roleMenuMapper.batchInsert(form.getMenu(), form.getRoleId(), regionId);
        }

    }

    /**
     * 根据身份证号查询用户
     *
     * @param certNumber
     * @param password
     *
     * @return
     */
    public UserEntity checkByCertNumber(String certNumber, String password) {
        Example e = new Example(UserEntity.class);
        e.createCriteria().andEqualTo("certNumber", NumEncryptUtils.encrypt(certNumber, 1)).andEqualTo("password", password).andEqualTo("status", Constants.YES);
        return this.userMapper.selectOneByExample(e);
    }

    /**
     * 查询登录日志
     *
     * @param userId
     *
     * @return
     */
    public List<UserLoginLogEntity> getLoginLog(long userId) {
        Example example = new Example(UserLoginLogEntity.class);
        example.createCriteria().andEqualTo("loginStatus", Constants.STATUS_YES).andEqualTo("loginId", userId);
        return this.userLoginLogMapper.selectByExample(example);
    }

    /**
     * 首次登录
     *
     * @param userId
     *
     * @return
     */
    public ConcurrentMap<String, Object> firstLogin(long userId, String ip, String source, String userName, String phone, String certNumber, int isVerify, String phoneSecret, Long regionId) throws IOException {
        // 根据区县ID查询顶级党组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        ConcurrentMap<String, Object> resultMap = new ConcurrentHashMap<>();
        List<LoginResultForm> resultList = new ArrayList<>();
        LoginResultForm resultForm = new LoginResultForm();
        resultForm.setUserName(userName);
        TokenBase tokenBase = new TokenBase();
        // 登录时间
        Date now = DateTime.now().toDate();
        tokenBase.setLoginTime(now);
        tokenBase.setUserId(userId);
        // 从redis缓存获取用户所在组织
        String baseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        // 用户所在组织列表
        List<Long> orgList = new ArrayList<>();
        // 存在
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(baseKey))) {
            log.debug("登录时获取用户缓存信息,baseKey:{}", baseKey);
            UserInfoBase base = Utils.fromJson(this.redisTemplate.opsForValue().get(baseKey), UserInfoBase.class);
            if (Objects.nonNull(base)) {
                orgList = base.getOrgIdList();
            }
        } else {
            // 根据用户ID查询组织单位信息
            List<UserOrgAndCorpEntity> list = this.userOrgAndCorpMapper.getUserOrgList(userId, Constants.ACTIVATE_STATUS_YES, Constants.STATUS_YES);
            if (list != null && !list.isEmpty()) {
                for (UserOrgAndCorpEntity uoce : list) {
                    orgList.add(uoce.getOrganizationId());
                }
            }
            // 刷新用户缓存
            UserEntity userEntity = new UserEntity();
            userEntity.setUserId(userId);
            userEntity = this.userMapper.selectByPrimaryKey(userEntity);
            this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
            log.debug("登录时刷新用户缓存信息，baseKey:{}", baseKey);
        }
        if (orgList != null && !orgList.isEmpty()) {
            // 如果只有一个单位或组织
            if (orgList.size() == 1) {
                // 获取用户角色缓存信息
                String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId;
                List<MenuForm> menuList = new ArrayList<>();
                List<Long> roleList = new ArrayList<>();
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(roleKey))) {
                    log.debug("登录时获取用户角色缓存信息 rolekey:{}", roleKey);
                    List<UserRoleEntity> list = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(roleKey), new TypeReference<List<UserRoleEntity>>() {
                    });
                    for (UserRoleEntity ure : list) {
                        if (ure.getOrganizationId().intValue() == orgList.get(0).intValue()) {
                            roleList.add(ure.getRoleId());
                        }
                    }
                } else {
                    log.debug("登录时获取用户角色缓存信息 rolekey不存在");
                    UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
                    entity.setOrganizationId(orgList.get(0));
                    entity.setUserId(userId);
                    roleList = this.userRoleMapper.getRoleList(entity);
                    // log.debug("登录时获取用户角色信息 userId:{},roleList:{}", userId, roleList);
                }
                String tokenId = UUID.randomUUID().toString();
                resultForm.setToken(tokenId);
                log.debug("登录时设置用户的权限缓存 userId:{},roleList{}", userId, roleList);
                if (!roleList.isEmpty()) {
                    menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
                    // 根据菜单查询菜单对应接口地址列表
                    List<String> menuIdList = new ArrayList<>();
                    for (MenuForm menuForm : menuList) {
                        menuIdList.add(menuForm.getMenuId());
                    }
                    if (!menuIdList.isEmpty()) {
                        List<String> redisList = this.menuUrlMapper.getLinkUrlList(menuIdList, Constants.STATUS_YES);
                        if (redisList != null && !redisList.isEmpty()) {
                            // 菜单接口存入redis
                            log.debug("登录时，用户权限菜单存入redis tokenId:{},linkUrl:{}", tokenId, redisList);
                            this.redisTemplate.opsForValue().set(Constants.LOGIN_PREFIX_M + tokenId, Utils.toJson(redisList), 30, TimeUnit.MINUTES);
                        }
                    }
                }

                // 获取组织缓存信息
                String orgKey = Global.CACHE_ORG_ID_PREFIX + orgList.get(0);
                OrganizationEntity organizationEntity = new OrganizationEntity();
                // 存在
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgKey))) {
                    log.debug("登录获取组织缓存信息 orgKey:{}", orgKey);
                    organizationEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
                } else {
                    // 不存在
                    organizationEntity.setOrganizationId(orgList.get(0));
                    organizationEntity = this.organizationMapper.selectByPrimaryKey(organizationEntity);
                    // 组织信息存入缓存
                    List<OptionEntity> list;
                    if (Boolean.TRUE.equals(this.redisTemplate.hasKey(Constants.ORG_TYPE_OPT_CODE))) {
                        list = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(Constants.ORG_TYPE_OPT_CODE), new TypeReference<List<OptionEntity>>() {
                        });
                    } else {
                        list = this.optionService.getList(Constants.ORG_TYPE_OPT_CODE);
                        this.redisTemplate.opsForValue().set(Constants.ORG_TYPE_OPT_CODE, OBJECTMAPPER.writeValueAsString(list), 30, TimeUnit.DAYS);
                    }
                    for (OptionEntity oe : list) {
                        if (oe.getOpKey().intValue() == organizationEntity.getOrgType()) {
                            organizationEntity.setOrgTypeName(oe.getOpValue());
                        }
                    }
                    this.redisTemplate.opsForValue().set(orgKey, Utils.toJson(organizationEntity), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                    log.debug("登录用户所在组织存入缓存 orgKey:{}", orgKey);
                }
                resultForm.setOwnerTree(organizationEntity.getOwnerTree());
                resultForm.setOId(organizationEntity.getOrganizationId());
                // 如果是3-市级机关，则是1-工委维度
                if (organizationEntity.getOrganizationId().equals(orgData.getOrgId())) {
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_1);
                } else if (organizationEntity.getParentId().equals(orgData.getOrgId())) {
                    // 如果上级组织是3，则是2-党委维度
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_2);
                } else {
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_3);
                }
                resultForm.setOrgType(organizationEntity.getOrgType());
                resultForm.setOrgTypeChild(organizationEntity.getOrgTypeChild());
                resultForm.setType(Constants.DEP_TYPE_ORG);
                resultForm.setName(organizationEntity.getName());
                resultForm.setManager(getName(organizationEntity.getOrganizationId(), Constants.DEP_TYPE_ORG));
                // 组织员工数量
                resultForm.setStaffNumber(organizationEntity.getUserNum());
                // 查询组织标签信息
                String tags = this.userOrgAndCorpMapper.findTag(userId, Constants.DEP_TYPE_ORG, organizationEntity.getOrganizationId());
                resultForm.setTagId(tags);
                // 根据oid查询root部门和部门名称
                Example depExample = new Example(DepartmentEntity.class);
                depExample.createCriteria().andEqualTo("isRoot", Constants.IS_ROOT_YES).andEqualTo("organizationId", organizationEntity.getOrganizationId());
                DepartmentEntity de = this.departmentMapper.selectOneByExample(depExample);
                if (de != null && de.getDepartmentId() != null) {
                    resultForm.setDepId(de.getDepartmentId());
                    resultForm.setDepName(de.getName());
                }
                tokenBase.setType(Constants.DEP_TYPE_ORG);
                List<Long> orgIdList = new ArrayList<>();
                orgIdList.add(organizationEntity.getOrganizationId());
                tokenBase.setOid(organizationEntity.getOrganizationId());
                tokenBase.setIdList(orgIdList);

                resultMap.put("activate", organizationEntity.getActivate());
                resultForm.setMenu(menuList);
                resultForm.setUserId(userId);
                resultList.add(resultForm);

                resultMap.put("token", tokenBase);
                resultMap.put("flag", "1");

                // 调用积分中心接口
                String openId = getOpenIdByUserId(userId, orgIdList.get(0));
                log.debug("调用积分中心接口，参数：userId:{},phone:{},certNumber:{},openId:{},name:{},phoneSecret:{}", userId, phone, certNumber, openId, userName, phoneSecret);
                this.openService.userLoginCallback(userId, phone, certNumber, openId, isVerify, userName, phoneSecret, -999L);
                // 新增登录日志
                insertLog(userId, now, ip, source, tokenId);

            } else {
                moreOrgAndCorp(orgList, userId, resultList, resultMap);
            }
        } else {
            resultMap.put("flag", "3");
        }
        resultMap.put("list", resultList);
        return resultMap;
    }

    /**
     * @param userId
     *
     * @return
     */
    public String getNameByUserId(Long userId) {
        Example example = new Example(UserEntity.class);
        example.selectProperties("name");
        example.createCriteria().andEqualTo("userId", userId);
        UserEntity ue = this.userMapper.selectOneByExample(example);
        if (ue == null) {
            return "";
        }
        return ue.getName();
    }

    /**
     * 根据userId查询openId
     *
     * @param userId
     *
     * @return
     */
    public String getOpenIdByUserId(Long userId, Long oId) {
        Example example = new Example(UserThirdEntity.class);
        example.selectProperties("thToken");
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("oId", oId);
        UserThirdEntity ute = this.userThirdMapper.selectOneByExample(example);
        return ute == null ? "" : ute.getThToken();
    }

    /**
     * @param oId
     *
     * @return
     */
    private String getName(Long oId, int depType) {
        // 查询root节点部门ID
        DepartmentEntity dep = this.departmentMapper.getRootId(oId, depType, Constants.IS_ROOT_YES);
        String name = "";
        if (dep != null && dep.getDepartmentId() != null) {
            // 根据部门ID查询超级管理员ID
            List<UserDepartmentEntity> uList = this.userDepartmentMapper.getRootUserId(dep.getDepartmentId(), Constants.DUTY_LEADER);
            if (uList != null && !uList.isEmpty()) {
                for (UserDepartmentEntity ude : uList) {
                    UserEntity ue = new UserEntity();
                    ue.setUserId(ude.getUserId());
                    ue = this.userMapper.selectByPrimaryKey(ue);
                    name += ue.getName() + ";";
                }
            }
        }
        return "".equals(name) ? "" : name.substring(0, name.length() - 1);
    }

    /**
     * @param oId
     * @param type
     *
     * @return
     */
    private int getStaffNumber(Long oId, int type) {
        // 组织员工数量
        List<DepartmentEntity> depList = this.departmentMapper.getDepList(oId, type);
        int staffNumber = 0;
        if (depList != null && !depList.isEmpty()) {
            for (DepartmentEntity de : depList) {
                staffNumber += de.getStaffNumber();
            }
        }
        return staffNumber;
    }

    /**
     * 第二次登录
     *
     * @param userId
     *
     * @return
     */
    public ConcurrentMap<String, Object> againLogin(long userId, String tokenId, String ip, String source, String userName, long oId, String phone, String certNumber, int isVerify, String phoneSecret, Long regionId) throws IOException {

        ConcurrentMap<String, Object> resultMap = new ConcurrentHashMap<>();
        List<LoginResultForm> resultList = new ArrayList<>();
        LoginResultForm resultForm = new LoginResultForm();
        // 根据oid查询root部门和部门名称
        Example depExample = new Example(DepartmentEntity.class);
        depExample.createCriteria().andEqualTo("isRoot", Constants.IS_ROOT_YES).andEqualTo("organizationId", oId);
        DepartmentEntity de = this.departmentMapper.selectOneByExample(depExample);
        if (de != null && de.getDepartmentId() != null) {
            resultForm.setDepId(de.getDepartmentId());
            resultForm.setDepName(de.getName());
        }
        resultForm.setUserName(userName);
        // 登录时间
        Date now = DateTime.now().toDate();
        // 从缓存获取用户信息
        String baseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        List<Long> orgList = new ArrayList<>();
        if (this.redisTemplate.hasKey(baseKey)) {
            log.debug("登录时获取用户缓存信息 baseKey:{}", baseKey);
            UserInfoBase userInfoBase = Utils.fromJson(this.redisTemplate.opsForValue().get(baseKey), UserInfoBase.class);
            orgList = userInfoBase.getOrgIdList();
        } else {
            // 根据用户ID查询组织单位信息
            Example e = new Example(UserOrgAndCorpEntity.class);
            e.createCriteria().andEqualTo("userId", userId);
            List<UserOrgAndCorpEntity> list = this.userOrgAndCorpMapper.selectByExample(e);
            if (list != null && !list.isEmpty()) {
                for (UserOrgAndCorpEntity userOrgAndCorpEntity : list) {
                    orgList.add(userOrgAndCorpEntity.getOrganizationId());
                }
            }
            // 刷新用户缓存
            log.debug("登录时刷新用户缓存 baseKey:{}", baseKey);
            UserEntity userEntity = new UserEntity();
            userEntity.setUserId(userId);
            userEntity = this.userMapper.selectByPrimaryKey(userEntity);
            this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
        }

        if (!orgList.isEmpty()) {
            // 如果只有一个单位或组织
            if (orgList.size() == 1) {
                // 从缓存获取用户角色信息
                String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId;
                List<MenuForm> menuList = new ArrayList<>();
                List<Long> roleList = new ArrayList<>();
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(roleKey))) {
                    log.debug("登录时获取用户角色缓存信息 roleKey:{}", roleKey);
                    List<UserRoleEntity> list = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(roleKey), new TypeReference<List<UserRoleEntity>>() {
                    });
                    for (UserRoleEntity role : list) {
                        if (role.getOrganizationId().intValue() == orgList.get(0).intValue()) {
                            roleList.add(role.getRoleId());
                        }
                    }
                } else {
                    log.debug("登录时获取用户角色缓存信息 roleKey 不存在");
                    UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
                    entity.setUserId(userId);
                    entity.setOrganizationId(orgList.get(0));
                    roleList = this.userRoleMapper.getRoleList(entity);
                }
                log.debug("登录时获取用户角色信息 userId:{},roleList:{}", userId, roleList);
                if (!roleList.isEmpty()) {
                    menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
                    // 刷新用户菜单权限
                    String redisMenuKey = Constants.LOGIN_PREFIX_M + tokenId;
                    if (this.redisTemplate.hasKey(redisMenuKey)) {
                        this.redisTemplate.delete(redisMenuKey);
                    }
                    List<String> menuIdList = new ArrayList<>();
                    for (MenuForm form : menuList) {
                        menuIdList.add(form.getMenuId());
                    }
                    // TOKEN过期时间
                    long outTime = this.redisTemplate.getExpire(redisMenuKey, TimeUnit.SECONDS);
                    if (!menuIdList.isEmpty()) {
                        List<String> redisList = this.menuUrlMapper.getLinkUrlList(menuIdList, Constants.STATUS_YES);
                        if (redisList != null && !redisList.isEmpty()) {
                            // 菜单接口存入redis
                            log.debug("登录时，用户权限菜单存入redis tokenId:{},linkUrl:{}", tokenId, redisList);
                            this.redisTemplate.opsForValue().set(redisMenuKey, Utils.toJson(redisList), outTime, TimeUnit.SECONDS);
                        }
                    }
                }
                // 从缓存获取组织信息
                String orgKey = Global.CACHE_ORG_ID_PREFIX + orgList.get(0);
                OrganizationEntity organizationEntity = new OrganizationEntity();
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(orgKey))) {
                    log.debug("登录时获取组织信息缓存,orgKey:{}", orgKey);
                    organizationEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
                } else {
                    log.debug("登录时刷新组织信息缓存,orgKey:{}", orgKey);
                    organizationEntity.setOrganizationId(orgList.get(0));
                    organizationEntity = this.organizationMapper.selectByPrimaryKey(organizationEntity);
                    // 刷新组织缓存
                    this.redisTemplate.opsForValue().set(orgKey, Utils.toJson(organizationEntity), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                }
                resultForm.setOId(organizationEntity.getOrganizationId());
                // 根据区县ID查询顶级党组织ID
                Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
                // 如果是3-市级机关，则是1-工委维度
                if (organizationEntity.getParentId().equals(orgData.getOrgId())) {
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_2);
                } else if (orgData.getOrgId().equals(organizationEntity.getOrganizationId())) {
                    // 如果上级组织是3，则是2-党委维度
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_1);
                } else {
                    resultForm.setLevel(Constants.ORG_COUNT_LEVEL_3);
                }
                resultForm.setOrgType(organizationEntity.getOrgType());
                resultForm.setType(Constants.DEP_TYPE_ORG);
                resultForm.setName(organizationEntity.getName());
                resultForm.setManager(getName(organizationEntity.getOrganizationId(), Constants.DEP_TYPE_ORG));
                // 组织员工数量
                resultForm.setStaffNumber(organizationEntity.getUserNum());
                List<Long> orgIdList = new ArrayList<>();
                orgIdList.add(organizationEntity.getOrganizationId());
                // 查询组织标签信息
                String tags = this.userOrgAndCorpMapper.findTag(userId, Constants.DEP_TYPE_ORG, organizationEntity.getOrganizationId());
                resultForm.setTagId(tags);
                // 根据oid查询root部门和部门名称
                Example dex = new Example(DepartmentEntity.class);
                dex.createCriteria().andEqualTo("isRoot", Constants.IS_ROOT_YES).andEqualTo("organizationId", organizationEntity.getOrganizationId());
                DepartmentEntity departmentEntity = this.departmentMapper.selectOneByExample(dex);
                if (departmentEntity != null && departmentEntity.getDepartmentId() != null) {
                    resultForm.setDepId(departmentEntity.getDepartmentId());
                    resultForm.setDepName(departmentEntity.getName());
                }
                resultForm.setMenu(menuList);
                resultForm.setUserId(userId);
                resultForm.setToken(tokenId);
                resultList.add(resultForm);
                resultMap.put("flag", "1");

                // 调用积分中心接口
                String openId = getOpenIdByUserId(userId, orgList.get(0));
                log.debug("调用积分中心接口，参数：userId:{},phone:{},certNumber:{},openId:{},name:{},phoneSecret:{}", userId, phone, certNumber, openId, userName, phoneSecret);
                this.openService.userLoginCallback(userId, phone, certNumber, openId, isVerify, userName, phoneSecret, -999L);
                // 新增登录日志
                insertLog(userId, now, ip, source, tokenId);
            } else {
                moreOrgAndCorp(orgList, userId, resultList, resultMap);
            }
            resultMap.put("list", resultList);
        } else {
            resultMap.put("flag", "3");
        }
        return resultMap;
    }

    /**
     * 新增登录日志
     */
    @Async
    @Transactional
    public void insertLog(Long userId, Date now, String ip, String source, String tokenId) {
        // 新增登录日志
        log.debug("新增登录日志 userId:{},date:{}", userId, now);
        UserLoginLogEntity loginLogEntity = new UserLoginLogEntity();
        loginLogEntity.setLoginId(userId);
        loginLogEntity.setLoginDate(now);
        loginLogEntity.setLoginIp(ip);
        loginLogEntity.setLoginStatus(Constants.STATUS_YES);
        loginLogEntity.setSource(StringUtils.isBlank(source) ? null : source);
        loginLogEntity.setToken(tokenId);
        loginLogEntity.setRegionId(86L);
        this.userLoginLogMapper.insert(loginLogEntity);
    }

    /**
     * @param list
     * @param userId
     * @param resultList
     * @param resultMap
     */
    private void moreOrgAndCorp(List<Long> list, long userId, List<LoginResultForm> resultList, ConcurrentMap<String, Object> resultMap) {
        // 存在多个单位或组织信息时，返回单位组织信息列表
        if (list != null && !list.isEmpty()) {
            // 获取redis缓存信息
            List<LoginResultForm> orgResultList = new ArrayList<>();
            for (Long oid : list) {
                // redisKey
                String orgKey = Global.CACHE_ORG_ID_PREFIX + oid;
                if (this.redisTemplate.hasKey(orgKey)) {
                    log.debug("登录用户存在多个组织时，通过缓存获取组织信息 orgKey:{}", orgKey);
                    OrganizationEntity oe = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
                    LoginResultForm lrf = new LoginResultForm();
                    lrf.setOId(oe.getOrganizationId());
                    lrf.setName(oe.getName());
                    lrf.setType(Constants.DEP_TYPE_ORG);
                    lrf.setUserId(userId);
                    orgResultList.add(lrf);
                } else {
                    // 不存在,组织信息存入redis
                    OrganizationEntity orgEntity = new OrganizationEntity();
                    orgEntity.setOrganizationId(oid);
                    orgEntity = this.organizationMapper.selectByPrimaryKey(orgEntity);
                    this.redisTemplate.opsForValue().set(orgKey, Utils.toJson(orgEntity), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                    log.debug("登录用户存在多组织时，组织信息存入缓存 orgKey:{}", orgKey);
                    LoginResultForm loginForm = new LoginResultForm();
                    loginForm.setOId(orgEntity.getOrganizationId());
                    loginForm.setName(orgEntity.getName());
                    loginForm.setType(Constants.DEP_TYPE_ORG);
                    loginForm.setUserId(userId);
                    orgResultList.add(loginForm);
                }
            }
            resultMap.put("flag", "2");
            resultList.addAll(orgResultList);
        }
    }

    /**
     * 登录用户存在多个单位或组织
     *
     * @param userId
     * @param type
     * @param oid
     *
     * @return
     */
    public ConcurrentMap<String, Object> chooseOne(String tokenId, long userId, int type, long oid, HttpServletRequest request, Date now, Long regionId) throws Exception {
        ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
        map.put("status", Constants.YES);
        // 通过缓存获取组织信息
        String orgKey = Global.CACHE_ORG_ID_PREFIX + oid;
        OrganizationEntity oe;
        // 存在
        if (this.redisTemplate.hasKey(orgKey)) {
            log.debug("登录用户存在多组织时，获取组织缓存信息 orgKey:{}", orgKey);
            oe = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
            if (Constants.STATUS_NO == oe.getStatus()) {
                map.put("status", oe.getStatus());
                return map;
            }
        } else {
            // 校验组织是否已激活
            log.debug("登录用户存在多组织时,刷新组织缓存信息 orgKey:{}", orgKey);
            Example example = new Example(OrganizationEntity.class);
            example.createCriteria().andEqualTo("organizationId", oid);
            oe = this.organizationMapper.selectOneByExample(example);
            if (Constants.STATUS_NO == oe.getStatus()) {
                map.put("status", oe.getStatus());
                return map;
            }
            // 刷新组织信息缓存
            this.redisTemplate.opsForValue().set(orgKey, Utils.toJson(oe), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
        }
        if (oe.getOrganizationId() != null) {
            map.put("activate", oe.getActivate());
        }
        if ("-1".equals(tokenId)) {
            tokenId = UUID.randomUUID().toString();
            map.put("tokenId", tokenId);
        }
        LoginResultForm form = new LoginResultForm();
        form.setOwnerTree(oe.getOwnerTree());
        // 从缓存获取用户角色信息
        String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId;
        List<MenuForm> menuList = new ArrayList<>();
        List<Long> roleList = new ArrayList<>();
        if (this.redisTemplate.hasKey(roleKey)) {
            log.debug("登录时从缓存获取用户角色信息 roleKey:{}", roleKey);
            List<UserRoleEntity> userRoleEntityList = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(roleKey), new TypeReference<List<UserRoleEntity>>() {
            });
            for (UserRoleEntity userRoleEntity : userRoleEntityList) {
                if (userRoleEntity.getOrganizationId().intValue() == oid) {
                    roleList.add(userRoleEntity.getRoleId());
                }
            }
        } else {
            // 查询角色菜单信息
            UserOrgAndCorpEntity ue = new UserOrgAndCorpEntity();
            ue.setUserId(userId);
            ue.setOrganizationId(oid);
            roleList = this.userRoleMapper.getRoleList(ue);
            log.debug("登录时从数据库获取用户角色信息 userId:{},roleList:{}", userId, roleList);
        }
        if (!roleList.isEmpty()) {
            menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
            // 操作用户权限对应接口缓存
            String redisKey = Constants.LOGIN_PREFIX_M + tokenId;
            long outTime = 1800L;
            if (Boolean.TRUE.equals(this.redisTemplate.hasKey(redisKey))) {
                this.redisTemplate.delete(redisKey);
                outTime = this.redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
            }
            List<String> menus = new ArrayList<>();
            for (MenuForm menu : menuList) {
                menus.add(menu.getMenuId());
            }
            if (!menus.isEmpty()) {
                List<String> redisList = this.menuUrlMapper.getLinkUrlList(menus, Constants.STATUS_YES);
                if (redisList != null && !redisList.isEmpty()) {
                    this.redisTemplate.opsForValue().set(redisKey, Utils.toJson(redisList), outTime, TimeUnit.SECONDS);
                    log.debug("登录时，用户权限菜单存入redis tokenId:{},linkUrl:{}", tokenId, redisList);
                }
            }
        }
        form.setMenu(menuList);
        form.setUserId(userId);
        // 从缓存获取用户信息
        String userKey = Global.CACHE_USER_ID_PREFIX + userId;
        UserEntity nameEntity = new UserEntity();
        if (this.redisTemplate.hasKey(userKey)) {
            log.debug("登录用户存在多个组织时，获取用户缓存信息 userKey:{}", userKey);
            nameEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(userKey), UserEntity.class);
        } else {
            log.debug("登录用户存在多个组织时，刷新用户缓存信息 userKey:{}", userKey);
            nameEntity.setUserId(userId);
            nameEntity = this.userMapper.selectByPrimaryKey(nameEntity);
            // 刷新用户信息缓存
            this.userOrgCacheCallService.flushUserInfo(regionId, nameEntity, 1);
        }
        form.setUserName(nameEntity.getName());
        form.setToken(tokenId);
        form.setType(type);
        form.setOId(oid);
        // 根据区县ID查询顶级党组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        // 设置组织统计维度
        if (orgData.getOrgId().equals(oe.getParentId())) {
            // 2-党委维度
            form.setLevel(Constants.ORG_COUNT_LEVEL_2);
        } else if (orgData.getOrgId().equals(oe.getOrganizationId())) {
            // 1-工委维度
            form.setLevel(Constants.ORG_COUNT_LEVEL_1);
        } else {
            // 3-支部维度
            form.setLevel(Constants.ORG_COUNT_LEVEL_3);
        }
        // 通过缓存获取组织名称
        form.setName(oe.getName());
        // 设置组织类型
        form.setOrgType(oe.getOrgType());
        form.setOrgTypeChild(oe.getOrgTypeChild());
        // 根据oid查询root部门和部门名称
        Example depExample = new Example(DepartmentEntity.class);
        depExample.createCriteria().andEqualTo("isRoot", Constants.IS_ROOT_YES).andEqualTo("organizationId", oid);
        DepartmentEntity de = this.departmentMapper.selectOneByExample(depExample);
        if (de != null && de.getDepartmentId() != null) {
            form.setDepId(de.getDepartmentId());
            form.setDepName(de.getName());
        }
        // 查询组织标签信息
        String tags = this.userOrgAndCorpMapper.findTag(userId, type, oid);
        form.setTagId(tags);
        // 调用积分中心接口
        String phone = nameEntity.getPhone();
        if (!StringUtils.isBlank(nameEntity.getPhoneSecret())) {
            phone = NumEncryptUtils.decrypt(nameEntity.getPhone(), nameEntity.getPhoneSecret());
        }
        String certNumber = nameEntity.getCertNumber();
        if (!StringUtils.isBlank(nameEntity.getCertNumberSecret())) {
            certNumber = NumEncryptUtils.decrypt(nameEntity.getCertNumber(), nameEntity.getCertNumberSecret());
        }
        String openId = getOpenIdByUserId(userId, oid);
        log.debug("调用积分中心接口，参数：userId:{},phone:{},certNumber:{},openId:{},name:{},phoneSecret:{}", userId, phone, certNumber, openId, nameEntity.getName(), nameEntity.getPhoneSecret());
        this.openService.userLoginCallback(userId, phone, certNumber, openId, nameEntity.getIsVerify(), nameEntity.getName(), nameEntity.getPhoneSecret(), -999L);
        // 新增登录日志
        insertLog(userId, now, request.getRemoteAddr(), request.getHeader("_os"), tokenId);

        map.put("form", form);
        return map;
    }

    /**
     * 根据电话号码查询用户信息
     *
     * @param phone
     *
     * @return
     */
    public UserInfoBase getUserByPhone(String phone, long userId, int type, long oId, String str) {

        UserInfoBase base = new UserInfoBase();
        Example e = new Example(UserEntity.class);
        if ("1".equals(str)) {
            e.createCriteria().andEqualTo("phone", phone);
        } else if ("2".equals(str)) {
            e.createCriteria().andEqualTo("certNumber", phone);
        }
        UserEntity entity = this.userMapper.selectOneByExample(e);
        if (entity != null) {
            base.setEducation(entity.getEducation());
            base.setEthnic(entity.getEthnic());
            base.setGender(entity.getGender());
            //查询用户tagList
            String tagId = this.userOrgAndCorpMapper.findTag(userId, type, oId);
            if (!StringUtils.isBlank(tagId)) {
                List<Long> idList = new ArrayList<>();
                for (String tag : tagId.split(",")) {
                    idList.add(Long.parseLong(tag));
                }
                base.setTags(idList);
            }
            base.setAge(entity.getAge());
        }
        return base;
    }

    /**
     * 监测手机号重复
     *
     * @param phone
     *
     * @return
     */
    public int checkPhone(String phone, Long userId) {
        return this.userMapper.checkPhone(phone, userId);
    }

    /**
     * @param phone
     * @param userId
     *
     * @return
     */
    public UserEntity checkExistPhone(String phone, Long userId) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("phoneSecret", phone);
        return this.userMapper.selectOneByExample(example);
    }

    /**
     * 用户新增时监测手机号重复
     *
     * @param phone
     *
     * @return
     */
    public UserForm addCheckPhone(String phone, int type, long oId) {
        return this.userMapper.addCheckPhone(phone, type, oId);
    }

    /**
     * 监测手机号重复
     *
     * @param phone
     *
     * @return
     */
    public int isExist(String phone, long userId) {
        return this.userMapper.isExist(phone, userId);
    }

    /**
     * 系统管理员-编辑
     *
     * @param userId
     *
     * @return
     */
    public SysEditForm editManager(int type, long oId, long userId, long regionId) {

        // 查询人员为管理员的部门信息
        List<DepartmentForm> depList = this.userDepartmentMapper.getUserDep(userId);
        // 查询所有部门
        List<DepartmentForm> allList = this.departmentMapper.getAllDep(type, oId, Constants.STATUS_YES);

        // 查询人员所有权限
        UserOrgAndCorpEntity e = new UserOrgAndCorpEntity();
        e.setUserId(userId);
        if (type == Constants.DEP_TYPE_CORP) {
            e.setCorporationId(oId);
        } else if (type == Constants.DEP_TYPE_ORG) {
            e.setOrganizationId(oId);
        }
        List<Long> roleList = this.userRoleMapper.getRoleList(e);
        List<MenuForm> menuList = new ArrayList<>();
        if (ArrayUtils.isNotEmpty(roleList.toArray())) {
            // 根据角色ID查询menu信息
            menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
        }
        SysEditForm form = new SysEditForm();
        form.setUserId(userId);
        form.setMenu(menuList);
        //form.setMenu(getChildren(menuList,"0"));
        // 查询root部门（is_root = 1）
        DepartmentEntity depEntity = this.departmentMapper.getRootDepId(oId, type, Constants.IS_ROOT_YES);
        if (depEntity != null && depEntity.getDepartmentId() != null) {
            List<UserDepartmentEntity> udeList = this.userDepartmentMapper.getListByAll(userId, depEntity.getDepartmentId(), Constants.DUTY_MANAGER);
            if (ArrayUtils.isNotEmpty(udeList.toArray())) {
                form.setScope(Constants.SCOPE_ALL);
                form.setDep(allList);
            } else {
                form.setScope(Constants.SCOPE_PART);
                form.setDep(depList);
            }
        }
        return form;
    }

    /**
     * list转换成树结构
     *
     * @param list
     * @param menuId
     *
     * @return
     */
    private List<MenuChildrenForm> getChildren(List<MenuForm> list, String menuId) {
        List<MenuChildrenForm> result = new ArrayList<>();
        List<MenuForm> newList = new ArrayList<>();
        for (MenuForm form : list) {
            if (form.getParentId().equals(menuId)) {
                newList.add(form);
            }
        }
        if (newList != null && !newList.isEmpty()) {
            for (MenuForm form : newList) {
                MenuChildrenForm cf = new MenuChildrenForm();
                cf.setMenuId(form.getMenuId());
                cf.setParentId(form.getParentId());
                cf.setName(form.getName());
                cf.setChildren(getChildren(list, form.getMenuId()));
                result.add(cf);
            }
        }
        return result;
    }

    /**
     * 增加用户积分
     *
     * @param type
     * @param oId
     * @param score
     * @param userId
     *
     * @return
     */
    @Transactional
    public int updateScore(int type, long oId, int score, long userId) {
        int flag = this.userOrgAndCorpMapper.updateScore(type, oId, score, userId);
        int result = -1;
        if (flag > 0) {
            result = this.userOrgAndCorpMapper.getScore(type, oId, userId);
        } else {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }

    /**
     * 人员管理-权限分配-编辑
     *
     * @param roleId
     * @param userId
     * @param type
     * @param oId
     *
     * @return
     */
    public RoleResultForm editUserRole(long roleId, long userId, int type, long oId, long regionId) {

        RoleResultForm resultForm = new RoleResultForm();
        List<Long> roleList = new ArrayList<>();
        roleList.add(roleId);
        UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
        entity.setUserId(userId);
        if (type == Constants.DEP_TYPE_CORP) {
            entity.setCorporationId(oId);
        } else if (type == Constants.DEP_TYPE_ORG) {
            entity.setOrganizationId(oId);
        }
        List<MenuForm> menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
        resultForm.setUserId(userId);
        resultForm.setRoleId(roleId);
        resultForm.setMenu(menuList);
        return resultForm;
    }

    /**
     * 登录校验用户是否存在
     *
     * @param phone
     * @param password
     *
     * @return
     */
    public UserEntity check(String phone, String password) {
        Example e = new Example(UserEntity.class);
        //获取电话密文
        String encryptPhone = NumEncryptUtils.encrypt(phone, 2);
        e.createCriteria().andEqualTo("phone", encryptPhone).andEqualTo("password", password);
        return this.userMapper.selectOneByExample(e);
    }

    /**
     * 根据主键查询
     *
     * @param userId
     *
     * @return
     */
    public UserEntity selectOne(long userId) {
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(Global.CACHE_USER_ID_PREFIX + userId))) {
            return Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_USER_ID_PREFIX + userId), UserEntity.class);
        } else {
            Example example = new Example(UserEntity.class);
            example.createCriteria().andEqualTo("userId", userId);
            return this.userMapper.selectOneByExample(example);
        }
    }


    /**
     * 新增用户单位、组织信息
     *
     * @param type
     * @param oId
     * @param userId
     *
     * @return
     */
    @Transactional
    public void saveOrgAndCorp(int type, long oId, long userId) {
        UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
        entity.setUserId(userId);
        if (type == Constants.DEP_TYPE_CORP) {
            entity.setCorporationId(oId);
        } else if (type == Constants.DEP_TYPE_ORG) {
            entity.setOrganizationId(oId);
        }
        entity.setScore(0);
        this.userOrgAndCorpMapper.insert(entity);
    }

    /**
     * 根据userId查询用户信息
     *
     * @param userId
     *
     * @return
     */
    public UserForm userInfo(long userId, int type, long oId) throws Exception {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", userId);
        UserEntity ue = this.userMapper.selectOneByExample(example);
        //查询用户组织属性
        Example exam = new Example(UserOrgAndCorpEntity.class);
        if (Constants.DEP_TYPE_CORP == type) {
            exam.createCriteria().andEqualTo("userId", userId).andEqualTo("corporationId", oId);
        } else if (Constants.DEP_TYPE_ORG == type) {
            exam.createCriteria().andEqualTo("userId", userId).andEqualTo("organizationId", oId);
        }
        UserOrgAndCorpEntity orgInfo = this.userOrgAndCorpMapper.selectOneByExample(exam);
        UserForm form = new UserForm();
        form.setUserId(ue.getUserId());
        form.setName(ue.getName());
        form.setPhone(NumEncryptUtils.decrypt(ue.getPhone(), ue.getPhoneSecret()));
        form.setCertType(ue.getCertType());
        form.setCertNumber(NumEncryptUtils.decrypt(ue.getCertNumber(), ue.getCertNumberSecret()));
        if (orgInfo != null && orgInfo.getUserId() != null) {
            form.setJobNumber(orgInfo.getJobNumber());
            form.setPosition(orgInfo.getPosition());
            form.setEmail(orgInfo.getEmail());
            form.setEntryDate(orgInfo.getEntryDate());
            form.setTagId(orgInfo.getTagId());
            form.setJobGrade(orgInfo.getJobGrade());//职级
            form.setCommunist(orgInfo.getCommunist());//党组织
            form.setYouthLeague(orgInfo.getYouthLeague());//团组织
            form.setUnionMember(orgInfo.getUnionMember());//工会组织
            form.setWomenLeague(orgInfo.getWomenLeague());//妇女组织
        }
        // 根据userId查询部门信息
        List<DepartmentEntity> list = this.userDepartmentMapper.getDepNameByUserId(userId, type, oId, Constants.DUTY_MANAGER);
        List<Long> depId = new ArrayList<>();
        for (DepartmentEntity de : list) {
            depId.add(de.getDepartmentId());
        }
        form.setDep(depId);
        form.setGender(ue.getGender());
        form.setCensusType(ue.getCensusType());
        form.setNationality(ue.getNationality());
        form.setMarriage(ue.getMarriage());
        form.setEducation(ue.getEducation());
        form.setPoliticalType(ue.getPoliticalType());
        return form;
    }

    /**
     * @param entity
     *
     * @return
     */
    private long registUser(UserEntity entity) {
        this.userMapper.registUser(entity);
        return entity.getUserId();
    }

    /**
     * 未实名用户注册
     *
     * @param form
     * @param type
     * @param oId
     *
     * @return
     */
    @Transactional
    public int regist(RegistForm form, int type, long oId, Long regionId) {
        try {
            Date now = new Date();
            // 校验手机号
            int flag = checkPhone(NumEncryptUtils.encrypt(form.getPhone(), 2), null);
            if (flag > 0) {
                return 1;
            }
            UserEntity entity = new UserEntity();
            entity.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
            entity.setPhoneSecret(NumEncryptUtils.numSecret(form.getPhone(), 2));
            entity.setPassword(form.getPassword());
            entity.setName("未实名用户");
            entity.setStatus(Constants.STATUS_YES);
            entity.setUpdateTime(now);
            entity.setCreateTime(now);
            long userId = registUser(entity);
            // 查询root部门（is_root = 1）
            DepForm df = this.departmentMapper.findRootId(oId, type);
            UserDepartmentEntity ude = new UserDepartmentEntity();
            ude.setDuty(Constants.DUTY_GENERAL);
            ude.setUserId(userId);
            ude.setDepartmentId(df.getDepartmentId());
            this.userDepartmentMapper.insertSelective(ude);
            // 新增用户单位组织关系
            UserOrgAndCorpEntity uoc = new UserOrgAndCorpEntity();
            uoc.setUserId(userId);
            if (Constants.DEP_TYPE_CORP == type) {
                uoc.setCorporationId(oId);
            } else if (Constants.DEP_TYPE_ORG == type) {
                uoc.setOrganizationId(oId);
            }
            this.userOrgAndCorpMapper.insertSelective(uoc);
            // 刷新缓存 tc2018.08.17**/
            // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24**/
            userOrgCacheCallService.flushUserInfo(regionId, entity, 1);
        } catch (Exception e) {
            log.error("系统异常，注册失败", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return 9;
        }
        return 1;
    }


    /**
     * 绑定信息
     *
     * @param form
     * @param userId
     *
     * @return
     */
    public boolean bind(BindForm form, Long userId) {
        try {
            UserEntity entity = new UserEntity();
            entity.setUserId(userId);
            entity.setCertNumber(form.getCertNumber());
            entity.setName(form.getName());
            entity.setUpdateTime(DateTime.now().toDate());
            this.userMapper.updateByPrimaryKeySelective(entity);
        } catch (Exception e) {
            log.error("系统异常，未实名用户绑定信息失败,userId[" + userId + "]", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        return true;
    }

    /**
     * 根据用户ID查询MENU
     *
     * @param userId
     * @param type
     * @param oId
     *
     * @return
     */
    public List<MenuForm> userMenu(Long userId, int type, Long oId, Long regionId) {
        UserOrgAndCorpEntity uc = new UserOrgAndCorpEntity();
        if (type == Constants.DEP_TYPE_CORP) {
            uc.setCorporationId(oId);
        } else if (type == Constants.DEP_TYPE_ORG) {
            uc.setOrganizationId(oId);
        }
        uc.setUserId(userId);
        List<Long> roleList = this.userRoleMapper.getRoleList(uc);
        return this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, regionId);
    }

    /**
     * 未实名用户登录
     *
     * @param request
     * @param form
     * @param header
     * @param oper    接口调用来自哪儿
     *
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public ResponseEntity<Result<?>> guestLogin(HttpServletRequest request, GuestForm form, HeaderHelper.SysHeader header, String oper, Long selOrgId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        String openid = "";
        String ip = Utils.getRemoteHost(request);
        // 1、ios，2、android，3、windows，4、macOS，5、unix/linux'
        String source = request.getHeader("_os");

        //2019-01-09 huang_kangjie 用户基本信息，直接由调用方法提供，如果没有获取到基本信息，则通过数据查询
        UserEntity userEntity = form.getUserEntity();
        if (userEntity == null) {
            // 如果是绑定，则用身份证，其他则用手机号
            if (Constants.OPER_BIND.equals(oper)) { // 绑定
                userEntity = checkByCertNumber(form.getCertNumber(), form.getPassword());
            } else if (Constants.OPER_PHONE.equals(oper)) { // 登录
                userEntity = check(form.getPhone(), form.getPassword());
            }
        }

        try {
            if (userEntity == null) {
                log.debug("未实名用户登录，账号或密码错误 -> phone:{},password:{}", form.getPhone(), form.getPassword());
                return new ResponseEntity<>(new Result<>(errors, 118, HttpStatus.OK.value()), HttpStatus.OK);
            } else {
                GuestResultForm resultForm = new GuestResultForm();
                // 公众号所在组织ID
                Long oid = header.getOid();
                // 登录时间
                Date now = DateTime.now().toDate();
                // 获取header里的token
                String tokenId = header.getToken();
                resultForm.setType(header.getType());
                resultForm.setUserId(userEntity.getUserId());
                List<Long> orgIdList = new ArrayList<>();
                // 用户所在组织
                List<GuestOrgResultForm> userOrgList = getOidList(userEntity, header.getType(), header.getRegionId());
                if (CollectionUtils.isEmpty(userOrgList)) {
                    log.debug("用户不是组织成员或者所在组织未激活 -> phone:{},password:{}", form.getPhone(), form.getPassword());
                    return new ResponseEntity<>(new Result<>(errors, 122, HttpStatus.OK.value()), HttpStatus.OK);
                }
                for (GuestOrgResultForm orgForm : userOrgList) {
                    orgIdList.add(orgForm.getOrgId());
                }
                // 如果是登录则校验是否存在权限
                if (Constants.OPER_PHONE.equalsIgnoreCase(oper)) {
                    // 校验用户权限 2019-06-17
                    log.debug("校验用户是否存在公众号权限参数 userId:{}, oid:{},orgIdList:{}", userEntity.getUserId(), oid, orgIdList);
                    boolean isPass = this.officialAccountService.checkOrgPermission(oid, orgIdList);
                    if (!isPass) {
                        log.debug("用户无权限操作 -> userId:{},phone:{},password:{},orgId:{}", userEntity.getUserId(), form.getPhone(), form.getPassword(), oid);
                        return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                }
                // 校验用户是否有公众号所在组织的权限
                OrganizationEntity orgEntity;
                String orgKey = Global.CACHE_ORG_ID_PREFIX + oid;
                if (!this.redisTemplate.hasKey(orgKey)) {
                    Example orgEx = new Example(OrganizationEntity.class);
                    orgEx.createCriteria().andEqualTo("organizationId", oid);
                    orgEntity = this.organizationMapper.selectOneByExample(orgEx);
                } else {
                    orgEntity = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
                }
                if (orgEntity == null) {
                    log.debug("微信公众号所在组织不存在 -> phone:{},password:{},orgId:{}", form.getPhone(), form.getPassword(), oid);
                    return new ResponseEntity<>(new Result<>(errors, 219, HttpStatus.OK.value(), "微信公众号所属"), HttpStatus.OK);
                } else {
                    // 校验用户所在组织是否存在
                    if (null != selOrgId) {
                        if (!this.redisTemplate.hasKey(Global.CACHE_ORG_ID_PREFIX + selOrgId)) {
                            OrganizationEntity existOrg = this.organizationMapper.selectByPrimaryKey(selOrgId);
                            if (null == existOrg || null == existOrg.getOrganizationId()) {
                                log.debug("用户所在组织不存在 -> phone:{},password:{},orgId:{}", form.getPhone(), form.getPassword(), oid);
                                return new ResponseEntity<>(new Result<>(errors, 219, HttpStatus.OK.value(), "用户所属"), HttpStatus.OK);
                            }
                        }
                    }
                    // 读取缓存-组织的所有下级组织列表
                    List<OrgNameResultForm> orgList;
                    String orgChildKey = Constants.CACHE_ORG_CHILD_PREFIX + oid;
                    if (this.redisTemplate.hasKey(orgChildKey)) {
                        orgList = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(orgChildKey), new TypeReference<List<OrgNameResultForm>>() {
                        });
                    } else {
                        OrganizationEntity entity = new OrganizationEntity();
                        entity.setOrganizationId(oid);
                        entity.setRegionId(header.getRegionId());
                        orgList = this.openService.getChildList(entity);
                    }
                    boolean b = Boolean.TRUE;
                    for (OrgNameResultForm orgForm : orgList) {
                        for (Long orgId : orgIdList) {
                            if (orgForm.getOrgId().equals(orgId)) {
                                b = Boolean.FALSE;
                                break;
                            }
                        }
                        if (!b) {
                            break;
                        }
                    }
                    if (b) {
                        log.debug("用户无权限操作 -> userId:{},phone:{},password:{},orgId:{}", userEntity.getUserId(), form.getPhone(), form.getPassword(), oid);
                        return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                    List<Long> childList = new ArrayList<>();
                    // 设置idList：用户所在组织必须在公众号组织的所有下级中,包含公众号所在组织
                    for (OrgNameResultForm of : orgList) {
                        for (Long id : orgIdList) {
                            if (of.getOrgId().equals(id)) {
                                childList.add(id);
                            }
                        }
                    }
                    resultForm.setIdList(childList);
                }

                // 根据userId查询所在组织列表-过滤非党组织
                List<GuestOrgResultForm> newList = new ArrayList<>();
                for (GuestOrgResultForm memOrg : userOrgList) {
                    if (Constants.ORG_TYPE_COMMUNIST.equals(memOrg.getOrgType())) {
                        newList.add(memOrg);
                    }
                }
                resultForm.setOrgList(newList);
                if (newList.isEmpty()) {
                    resultForm.setSelOrgId(selOrgId);
                    resultForm.setIsManage(Constants.NO);
                } else {
                    selOrgId = null == selOrgId ? newList.get(0).getOrgId() : selOrgId;
                    resultForm.setSelOrgId(selOrgId);
                    // 查询用户为管理员的组织
                    String userMgrKey = Constants.CACHE_USER_MGR_PREFIX + userEntity.getUserId() + "_" + header.getRegionId();
                    List<Long> manageOrgIds;
                    if (this.redisTemplate.hasKey(userMgrKey)) {
                        log.debug("获取用户管理组织列表缓存，userId：{}", userEntity.getUserId());
                        manageOrgIds = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(userMgrKey), new TypeReference<List<Long>>() {
                        });
                    } else {
                        List<Integer> manageType = new ArrayList<>();
                        manageType.add(Constants.ROLE_TYPE_DEFINED_USER);
                        manageType.add(Constants.ROLE_TYPE_DEFINED_ROOT);
                        manageOrgIds = this.userOrgAndCorpMapper.findOrgIdByUserIdAndRoleType(userEntity.getUserId(), manageType, Constants.ROLE_BELONG_WX, header.getRegionId());
                    }
                    if (null != manageOrgIds && manageOrgIds.contains(selOrgId)) {
                        resultForm.setIsManage(Constants.YES);
                    } else {
                        resultForm.setIsManage(Constants.NO);
                    }
                }

                // 根据userId和oid查询openId
                if (this.redisTemplate.hasKey(Global.CACHE_BASE_USER_ID_PREFIX + userEntity.getUserId() + "_" + header.getRegionId())) {
                    UserInfoBase base = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_BASE_USER_ID_PREFIX + userEntity.getUserId() + "_" + header.getRegionId()), UserInfoBase.class);
                    if (base != null) {
                        openid = base.getOpenId();
                        resultForm.setWxOpenId(openid);
                    }
                }
                if (StringUtils.isBlank(openid)) {
                    Example ex = new Example(UserThirdEntity.class);
                    ex.selectProperties("thToken", "nickName");
                    ex.createCriteria().andEqualTo("userId", userEntity.getUserId()).andEqualTo("oId", oid);
                    ex.orderBy("createTime").desc();
                    UserThirdEntity ute = null;
                    List<UserThirdEntity> uteList = this.userThirdMapper.selectByExample(ex);
                    if (CollectionUtils.isNotEmpty(uteList)) {
                        ute = uteList.get(0);
                    }
                    if (ute != null && !StringUtils.isBlank(ute.getThToken())) {
                        openid = ute.getThToken();
                        resultForm.setWxOpenId(ute.getThToken());
                    }
                }
                // 校验用户信息是否完整
                if (StringUtils.isBlank(userEntity.getName()) || StringUtils.isBlank(userEntity.getCertNumber()) || StringUtils.isBlank(userEntity.getPhone())) {
                    return new ResponseEntity<>(new Result<>(errors, 200, HttpStatus.OK.value()), HttpStatus.OK);
                }
                resultForm.setOId(oid);
                resultForm.setName(userEntity.getName());
                if (userEntity.getIsVerify() == Constants.IS_VERIFY_YES) {
                    resultForm.setCertNumber(NumEncryptUtils.decrypt(userEntity.getCertNumber(), userEntity.getCertNumberSecret()));
                } else {
                    resultForm.setCertNumber(userEntity.getCertNumber());
                }
                resultForm.setCertNumberEncryption(userEntity.getCertNumber());
                if (!StringUtils.isBlank(userEntity.getPhone()) && !StringUtils.isBlank(userEntity.getPhoneSecret())) {
                    resultForm.setPhone(NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()));
                    resultForm.setPhoneEncryption(userEntity.getPhone());
                }
                // 设置默认值
                resultForm.setJoinDays(0);
                resultForm.setJoinYears(0);
                // 新增返回入党时间
                String userKey = Global.CACHE_USER_ID_PREFIX + userEntity.getUserId();
                // 获取当前年
                Calendar cal = Calendar.getInstance();
                int curYear = cal.get(Calendar.YEAR);
                if (this.redisTemplate.hasKey(userKey)) {
                    UserEntity ue = Utils.fromJson(this.redisTemplate.opsForValue().get(userKey), UserEntity.class);
                    if (!StringUtils.isBlank(ue.getJoiningTime())) {
                        int joinYear = Integer.parseInt(ue.getJoiningTime().substring(0, 4));
                        String month = ue.getJoiningTime().substring(4);
                        if (month.length() < 7) {
                            month = month.concat(" 00:00:00");
                        }
                        int years = Math.abs(curYear - joinYear);
                        int days = Utils.dayCompare(curYear + month, Utils.dateToString(now, "yyyy-MM-dd HH:mm:ss"));
                        if (days < 0) {
                            days = Utils.dayCompare((curYear - 1) + month, Utils.dateToString(now, "yyyy-MM-dd HH:mm:ss"));
                            --years;
                        }
                        // 获取入党年份
                        resultForm.setJoinTime(ue.getJoiningTime());
                        resultForm.setJoinYears(years);
                        resultForm.setJoinDays(days);
                    }
                } else {
                    Example example = new Example(UserEntity.class);
                    example.createCriteria().andEqualTo("userId", userEntity.getUserId());
                    UserEntity ue = this.userMapper.selectOneByExample(example);
                    if (null != ue && !StringUtils.isBlank(ue.getJoiningTime())) {
                        String month = ue.getJoiningTime().substring(4);
                        if (month.length() < 7) {
                            month = month.concat(" 00:00:00");
                        }
                        int joinYear = Integer.parseInt(ue.getJoiningTime().substring(0, 4));
                        int years = curYear - joinYear;
                        int days = Utils.dayCompare(String.valueOf(curYear).concat(month), Utils.dateToString(now, "yyyy-MM-dd HH:mm:ss"));
                        if (days < 0) {
                            --years;
                            days = Utils.dayCompare((curYear - 1) + month, Utils.dateToString(now, "yyyy-MM-dd HH:mm:ss"));
                        }
                        resultForm.setJoinTime(ue.getJoiningTime());
                        resultForm.setJoinYears(years);
                        resultForm.setJoinDays(days);
                    }
                }

                if ("-1".equals(tokenId)) {
                    // 生成token
                    tokenId = UUID.randomUUID().toString();
                    // 插入日志
                    this.loginLogService.guestLoginLog(userEntity, ip, source, tokenId, now);
                    // 构造token信息，压入redis
                    this.loginLogService.buildTokenBase(tokenId, userEntity.getUserId(), oid, now, header.getType(), header.getRegionId());
                    // 登录成功，返回
                    resultForm.setToken(tokenId);
                    // 校验公众号所在组织是否存在微信端权限
                    this.commonAddRole(oid, header.getUserId(), header.getRegionId(), now);
                    // 校验组织是否存在微信端权限
                    this.commonCheck(resultForm, now, userEntity.getUserId(), header.getRegionId());
                    // 第一次登录，菜单对应url存入redis
                    this.putRedisMenuUrl(resultForm.getMenu(), tokenId);
                    // 广播到积分中心
                    this.commonLoginCallback(userEntity, openid, oid);
                } else {
                    // 校验redis里token是否过期
                    if (this.redisTemplate.hasKey(tokenId)) {
                        // 登录 插入日志
                        this.loginLogService.guestLoginLog(userEntity, ip, source, tokenId, now);
                        // 登录成功，返回
                        resultForm.setToken(tokenId);
                        // 校验组织是否存在微信端权限
                        this.commonCheck(resultForm, now, userEntity.getUserId(), header.getRegionId());
                        // 广播到积分中心
                        this.commonLoginCallback(userEntity, openid, oid);
                    } else {
                        // 不存在,重新登录,前端设置token为初始值"-1"
                        log.debug("未实名用户登录超时，请重新登录 -> phone:{}", form.getPhone());
                        return new ResponseEntity<>(new Result<>(errors, 119, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                }
                // 如果是绑定，则同步用户信息到学习系统
                if (!StringUtils.isBlank(form.getSource()) && "frombind".equalsIgnoreCase(form.getSource())) {
                    UserEntity entity = this.userMapper.selectByPrimaryKey(userEntity.getUserId());
                    // 用户所在组织
                    if (entity != null && !StringUtils.isBlank(entity.getId())) {
                        Example example = new Example(UserOrgAndCorpEntity.class);
                        example.createCriteria().andEqualTo("userId", userEntity.getUserId()).andEqualTo("isEmployee", Constants.STATUS_YES);
                        example.setOrderByClause("update_time DESC");
                        List<UserOrgAndCorpEntity> userOrgAndCorpEntities = this.userOrgAndCorpMapper.selectByExample(example);
                        if (CollectionUtils.isNotEmpty(userOrgAndCorpEntities)) {
                            Long orgId = userOrgAndCorpEntities.get(0).getOrganizationId();
                            this.broadcastService.commonBroadcast(entity, orgId, String.valueOf(orgId), "2", header.getRegionId());
                        }
                    }
                }
                log.debug("guestLogin耗时：{}", stopWatch.toString());
                stopWatch.stop();
                return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换失败:{}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(errors, 120, HttpStatus.OK.value()), HttpStatus.OK);
        } catch (NullPointerException n) {
            log.error("获取缓存失败:{}", n.getMessage(), n);
            return new ResponseEntity<>(new Result<>(errors, 121, HttpStatus.OK.value()), HttpStatus.OK);
        } catch (Exception e) {
            log.error("guestLogin失败：{}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(errors, 186, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 菜单对应url存入redis
     *
     * @param menuFormList 菜单form
     * @param tokenId      登录token
     */
    void putRedisMenuUrl(List<MenuForm> menuFormList, String tokenId) {
        if (menuFormList != null && !menuFormList.isEmpty()) {
            String redisKey = Constants.LOGIN_PREFIX_M + tokenId;
            List<String> menuIdList = new ArrayList<>();
            for (MenuForm menuForm : menuFormList) {
                menuIdList.add(menuForm.getMenuId());
            }
            if (!menuIdList.isEmpty()) {
                List<String> urlList = this.menuUrlMapper.getLinkUrlList(menuIdList, Constants.STATUS_YES);
                if (urlList != null && !urlList.isEmpty()) {
                    log.debug("微信端登录，菜单对应url存入redis --> redisKey:{},urlList:{}", redisKey, urlList);
                    this.redisTemplate.opsForValue().set(redisKey, Utils.toJson(urlList), 30, TimeUnit.MINUTES);
                }
            }
        }
    }

    /**
     * @param orgId
     *
     * @return
     */
    public Set<Long> findOrgIdListByOwnerId(long orgId) {
        Set<Long> set = new HashSet<>();
        List<Long> idList = this.organizationMapper.getIdList(orgId);
        set.addAll(idList);
        if (CollectionUtils.isNotEmpty(idList)) {
            for (Long id : idList) {
                set.addAll(findOrgIdListByOwnerId(id));
            }
        }
        return set;
    }

    /**
     * @param orgId
     *
     * @return
     */
    private Set<OrgChildForm> findOrgListByOwnerId(long orgId, Long regionId) {
        Set<OrgChildForm> set = new HashSet<>();
        List<OrgChildForm> idList = this.organizationMapper.getFormList(orgId, regionId);
        set.addAll(idList);
        if (idList != null && !idList.isEmpty()) {
            for (OrgChildForm form : idList) {
                set.addAll(findOrgListByOwnerId(form.getOrgId(), regionId));
            }
        }
        return set;
    }

    /**
     * @param userEntity
     * @param oid        公众号所在组织ID
     */
    private void commonLoginCallback(UserEntity userEntity, String openId, Long oid) throws Exception {
        // 广播到积分中心
        log.debug("调用积分中心接口，参数：userId:{},phone:{},certNumber:{},openId:{},phoneSecret:{}", userEntity.getUserId(), userEntity.getPhone(), userEntity.getCertNumber(), openId, userEntity.getPhoneSecret());
        this.openService.userLoginCallback(userEntity.getUserId(), userEntity.getPhone(), userEntity.getCertNumber(), openId, userEntity.getIsVerify(), userEntity.getName(), userEntity.getPhoneSecret(), oid);
    }

    /**
     * @param resultForm
     * @param now
     */
    private void commonCheck(GuestResultForm resultForm, Date now, Long userId, Long regionId) {
        // 用户所在组织列表
        Long selOrgId = resultForm.getSelOrgId();
        List<Long> roleList = getRoleList(selOrgId, userId, resultForm.getIsManage(), now, regionId);
        // 根据角色查询菜单
        List<MenuForm> resultList = getMenuForms(roleList, regionId);
        resultForm.setMenu(resultList);
    }

    /**
     * 查询菜单
     *
     * @param roleList 角色
     *
     * @return List<MenuForm>
     *
     * @throws IOException
     */
    public List<MenuForm> getMenuForms(List<Long> roleList, Long regionId) {
        List<MenuForm> resultList = new ArrayList<>();
        if (!roleList.isEmpty()) {
            Set<MenuForm> set = new HashSet<>();
            for (Long roleId : roleList) {
                String roleRedisKey = Constants.CACHE_USER_ROLE_PREFIX + roleId;
                if (Boolean.TRUE.equals(this.redisTemplate.hasKey(roleRedisKey))) {
                    try {
                        set.addAll(OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(roleRedisKey), new TypeReference<List<MenuForm>>() {
                        }));
                    } catch (IOException e) {
                        log.error("从redis获取角色对应的菜单异常 roleId:" + roleId, e);
                        List<Long> oList = new ArrayList<>();
                        oList.add(roleId);
                        List<MenuForm> mList = this.roleMenuMapper.getMenuByRole(oList, Constants.MENU_BELONG_WX, regionId);
                        this.redisTemplate.opsForValue().set(roleRedisKey, Utils.toJson(mList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                        set.addAll(mList);
                    }
                } else {
                    List<Long> oList = new ArrayList<>();
                    oList.add(roleId);
                    List<MenuForm> mList = this.roleMenuMapper.getMenuByRole(oList, Constants.MENU_BELONG_WX, regionId);
                    this.redisTemplate.opsForValue().set(roleRedisKey, Utils.toJson(mList), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
                    set.addAll(mList);
                }
            }
            if (set.isEmpty()) {
                resultList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_WX, regionId);
            } else {
                resultList = new ArrayList<>(set);
            }
        }
        return resultList;
    }

    /**
     * 查询用户角色
     *
     * @param now      Date
     * @param userId
     * @param selOrgId
     *
     * @return
     */
    public List<Long> getRoleList(Long selOrgId, Long userId, Integer isManage, Date now, Long regionId) {
        List<Long> roleList = new ArrayList<>();
        List<RoleEntity> roleEntityList = this.commonAddRole(selOrgId, userId, regionId, now);
        // 校验用户是否存在微信端管理权限
        UserRoleEntity ure = this.userRoleMapper.checkExistRole(selOrgId, userId, Constants.ROLE_TYPE_DEFINED_ROOT, Constants.ROLE_BELONG_WX);
        // 校验用户是否存在该组织其他权限
        List<UserRoleEntity> otherRoleList = this.userRoleMapper.checkExistOtherRole(selOrgId, userId, Constants.ROLE_TYPE_DEFINED, Constants.ROLE_BELONG_WX);
        if (null == ure) {
            // 如果是管理员
            if (null != isManage && Constants.YES == isManage) {
                // 新增用户普通权限
                UserRoleEntity newRole = new UserRoleEntity();
                newRole.setOrganizationId(selOrgId);
                newRole.setUserId(userId);
                for (RoleEntity role : roleEntityList) {
                    if (Constants.ROLE_TYPE_DEFINED_ROOT == role.getRoleType()) {
                        newRole.setRoleId(role.getRoleId());
                    }
                }
                this.userRoleMapper.insertSelective(newRole);
                roleList.add(newRole.getRoleId());
            } else {
                // 校验用户是否存在微信端普通权限
                UserRoleEntity userRole = this.userRoleMapper.checkExistRole(selOrgId, userId, Constants.ROLE_TYPE_DEFAULT, Constants.ROLE_BELONG_WX);
                if (null == userRole) {
                    // 新增用户普通权限
                    UserRoleEntity newRole = new UserRoleEntity();
                    newRole.setOrganizationId(selOrgId);
                    newRole.setUserId(userId);
                    for (RoleEntity role : roleEntityList) {
                        if (Constants.ROLE_TYPE_DEFAULT == role.getRoleType()) {
                            newRole.setRoleId(role.getRoleId());
                        }
                    }
                    this.userRoleMapper.insertSelective(newRole);
                    roleList.add(newRole.getRoleId());
                } else {
                    roleList.add(userRole.getRoleId());
                }
            }
            // 刷新用户缓存
            this.userOrgCacheCallService.flushUserInfo(regionId, userId, 1);
        } else {
            roleList.add(ure.getRoleId());
        }
        // 用户拥有组织其他权限
        if (CollectionUtils.isNotEmpty(otherRoleList)) {
            otherRoleList.forEach(userRoleEntity -> roleList.add(userRoleEntity.getRoleId()));
        }
        return roleList;
    }

    /**
     * 校验组织是否拥有微信权限
     *
     * @param orgId
     * @param userId
     * @param now
     */
    public List<RoleEntity> commonAddRole(Long orgId, Long userId, Long regionId, Date now) {
        List<RoleEntity> resultList = new ArrayList<>();
        // 校验组织是否存在微信端权限(管理权限和普通权限)
        Example example = new Example(RoleEntity.class);
        example.createCriteria().andEqualTo("status", Constants.STATUS_YES).andEqualTo("roleType", Constants.ROLE_TYPE_DEFAULT).andEqualTo("orgType", Constants.DEP_TYPE_ORG).andEqualTo("organizationId", Constants.IS_ROOT_YES).andEqualTo("belong", Constants.MENU_BELONG_WX);
        RoleEntity wxEntity = this.roleMapper.selectOneByExample(example);
        if (null == wxEntity) {
            // 组织不存在微信端权限，则新增
            RoleEntity roleEntity = new RoleEntity();
            roleEntity.setBelong(Constants.MENU_BELONG_WX);
            roleEntity.setOrganizationId(orgId);
            roleEntity.setOrgType(Constants.DEP_TYPE_ORG);
            roleEntity.setRoleType(Constants.ROLE_TYPE_DEFAULT);
            roleEntity.setName("微信端普通权限");
            roleEntity.setCreateTime(now);
            roleEntity.setUpdateTime(now);
            roleEntity.setStatus(Constants.STATUS_YES);
            roleEntity.setLastChangeUser(userId);
            this.roleMapper.insertRole(roleEntity);
            // 新增微信端权限对应菜单
            if (roleEntity.getRoleId() != null && roleEntity.getRoleId() > 0) {
                this.roleMenuMapper.batchInsert(Arrays.asList(wxMenuIds.split(",")), roleEntity.getRoleId(), regionId);
            }
            resultList.add(roleEntity);
        } else {
            resultList.add(wxEntity);
        }
        // 管理员权限
        example.clear();
        example.createCriteria().andEqualTo("status", Constants.STATUS_YES).andEqualTo("orgType", Constants.DEP_TYPE_ORG).andEqualTo("roleType", Constants.ROLE_TYPE_DEFINED_ROOT).andEqualTo("organizationId", Constants.IS_ROOT_YES).andEqualTo("belong", Constants.MENU_BELONG_WX);
        RoleEntity mgrEntity = this.roleMapper.selectOneByExample(example);
        if (null == mgrEntity) {
            // 组织不存在微信端权限，则新增
            RoleEntity roleEntity = new RoleEntity();
            roleEntity.setBelong(Constants.MENU_BELONG_WX);
            roleEntity.setOrganizationId(orgId);
            roleEntity.setOrgType(Constants.DEP_TYPE_ORG);
            roleEntity.setRoleType(Constants.ROLE_TYPE_DEFINED_ROOT);
            roleEntity.setName("微信端管理权限");
            roleEntity.setCreateTime(now);
            roleEntity.setUpdateTime(now);
            roleEntity.setStatus(Constants.STATUS_YES);
            roleEntity.setLastChangeUser(userId);
            this.roleMapper.insertRole(roleEntity);
            // 新增微信端权限对应菜单
            if (roleEntity.getRoleId() != null && roleEntity.getRoleId() > 0) {
                this.roleMenuMapper.batchInsert(Arrays.asList(wxManagerMenuIds.split(",")), roleEntity.getRoleId(), regionId);
            }
            resultList.add(roleEntity);
        } else {
            resultList.add(mgrEntity);
        }
        return resultList;
    }

    /**
     * 获取人员的组织列表
     *
     * @param userEntity
     *
     * @return
     */
    public List<GuestOrgResultForm> getOidList(UserEntity userEntity, int type, Long regionId) throws IOException {
        if (this.redisTemplate.hasKey(Global.CACHE_BASE_USER_ID_PREFIX + userEntity.getUserId() + "_" + regionId)) {
            UserInfoBase base = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_BASE_USER_ID_PREFIX + userEntity.getUserId() + "_" + regionId), UserInfoBase.class);
            if (base == null) {
                List<GuestOrgResultForm> list = this.userOrgAndCorpMapper.getOrgListByUserId(Constants.STATUS_YES, Constants.ACTIVATE_STATUS_YES, userEntity.getUserId());
                for (GuestOrgResultForm from : list) {
                    // 设置组织统计维度
                    setLevel(from, regionId);
                }
                return list;
            } else {
                List<GuestOrgResultForm> resultList = new ArrayList<>();
                List<Long> idList = base.getOrgIdList();
                for (Long id : idList) {
                    GuestOrgResultForm resultForm = new GuestOrgResultForm();
                    resultForm.setOrgId(id);
                    OrganizationEntity redisOrg = Utils.fromJson(this.redisTemplate.opsForValue().get(Global.CACHE_ORG_ID_PREFIX + id), OrganizationEntity.class);
                    if (redisOrg != null && redisOrg.getOrganizationId() != null) {
                        resultForm.setParentId(redisOrg.getParentId());
                        resultForm.setOrgName(redisOrg.getName());
                        resultForm.setOrgType(redisOrg.getOrgType());
                        resultForm.setOrgTypeChild(redisOrg.getOrgTypeChild());
                        // 设置组织统计维度
                        setLevel(resultForm, regionId);
                    } else {
                        OrganizationEntity oe = this.organizationMapper.selectByPrimaryKey(id);
                        if (!StringUtils.isBlank(oe.getName())) {
                            resultForm.setParentId(oe.getParentId());
                            resultForm.setOrgName(oe.getName());
                            resultForm.setOrgType(oe.getOrgType());
                            resultForm.setOrgTypeChild(oe.getOrgTypeChild());
                            // 设置组织统计维度
                            setLevel(resultForm, regionId);
                            // 刷新组织缓存
                            userOrgCacheCallService.flushOrgInfo(oe, 1);
                        }
                    }
                    // 查询标签
                    String tagKey = Constants.CACHE_USER_TAG_PREFIX + userEntity.getUserId();
                    String tagId = "";
                    if (this.redisTemplate.hasKey(tagKey)) {
                        List<UserOrgAndCorpEntity> tagList = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(tagKey), new TypeReference<List<UserOrgAndCorpEntity>>() {
                        });
                        if (null != tagList && !tagList.isEmpty()) {
                            for (UserOrgAndCorpEntity uoce : tagList) {
                                if (id.equals(uoce.getOrganizationId())) {
                                    tagId = uoce.getTagId();
                                    break;
                                }
                            }
                        }
                    } else {
                        tagId = this.userOrgAndCorpMapper.findTag(userEntity.getUserId(), type, id);
                    }
                    resultForm.setTagId(tagId);
                    resultList.add(resultForm);
                }
                return resultList;
            }
        } else {
            List<GuestOrgResultForm> countList = this.userOrgAndCorpMapper.getOrgListByUserId(Constants.STATUS_YES, Constants.ACTIVATE_STATUS_YES, userEntity.getUserId());
            for (GuestOrgResultForm form : countList) {
                // 设置组织统计维度
                this.setLevel(form, regionId);
            }
            return countList;
        }
    }

    /**
     * 设置组织统计维度
     *
     * @param form
     */
    private void setLevel(GuestOrgResultForm form, Long regionId) {
        // 根据区县ID查询顶级党组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        // 如果是3-市级机关，则是1-工委维度
        if (orgData.getOrgId().equals(form.getOrgId())) {
            form.setLevel(Constants.ORG_COUNT_LEVEL_1);
        } else if (orgData.getOrgId().equals(form.getParentId())) {
            // 如果上级组织是3，则是2-党委维度
            form.setLevel(Constants.ORG_COUNT_LEVEL_2);
        } else {
            form.setLevel(Constants.ORG_COUNT_LEVEL_3);
        }
    }


    /**
     * 获取用户的oid,如果用户存在多个组织id
     *
     * @param rootOid
     * @param userId
     *
     * @return
     */
    public long getOid(long rootOid, long userId, Long regionId) {
        String redisBaseKey = Global.CACHE_BASE_USER_ID_PREFIX + userId + "_" + regionId;
        if (this.redisTemplate.hasKey(redisBaseKey)) {
            UserInfoBase base = Utils.fromJson(this.redisTemplate.opsForValue().get(redisBaseKey), UserInfoBase.class);
            if (base == null) {
                return rootOid;
            } else {
                List<Long> list = base.getOrgIdList();
                if (list == null || list.size() == 0) {
                    return rootOid;
                }
                return list.get(0);
            }
        } else {
            List<UserThirdpartyForm> list = this.userThirdMapper.findUserOrgList(userId, rootOid, regionId);
            if (list == null || list.size() == 0) {
                return rootOid;
            }
            return list.get(0).getOId();
        }
    }


    /**
     * 根据身份证获取用户信息
     *
     * @param certNumber
     *
     * @return
     */
    public UserEntity getUserByCertNumber(String certNumber) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("certNumber", NumEncryptUtils.encrypt(certNumber, 1));
        return this.userMapper.selectOneByExample(example);
    }

    /**
     * 根据phone查询userId
     *
     * @param phone
     *
     * @return
     */
    public UserForm findIdByPhone(String phone) {
        return this.userMapper.findIdByPhone(phone);
    }

    /**
     * @param phone
     *
     * @return
     */
    public List<CorpOrgForm> coList(String phone) {
        List<CorpOrgForm> resultList = new ArrayList<>();
        List<UserOrgAndCorpEntity> list = this.userOrgAndCorpMapper.getCoList(phone);
        if (list != null && !list.isEmpty()) {
            for (UserOrgAndCorpEntity entity : list) {
                CorpOrgForm cof = new CorpOrgForm();
                if (entity.getCorporationId() != null) {
                    cof.setType(Constants.DEP_TYPE_CORP);
                    cof.setName(findName(entity.getCorporationId(), Constants.DEP_TYPE_CORP));
                    cof.setOId(entity.getCorporationId());
                    resultList.add(cof);
                } else if (entity.getOrganizationId() != null) {
                    cof.setType(Constants.DEP_TYPE_ORG);
                    cof.setName(findName(entity.getOrganizationId(), Constants.DEP_TYPE_ORG));
                    cof.setOId(entity.getOrganizationId());
                    resultList.add(cof);
                }
            }
            return resultList;
        } else {
            return null;
        }
    }

    /**
     * @param oId
     * @param type
     *
     * @return
     */
    public String findName(long oId, int type) {
        String name = "";
        if (Constants.DEP_TYPE_CORP == type) {
            CorporationEntity ce = new CorporationEntity();
            ce.setCorporationId(oId);
            ce = this.corporationMapper.selectByPrimaryKey(ce);
            name = ce.getName();
        } else if (Constants.DEP_TYPE_ORG == type) {
            OrganizationEntity oe = new OrganizationEntity();
            oe.setOrganizationId(oId);
            oe = this.organizationMapper.selectByPrimaryKey(oe);
            name = oe.getName();
        }
        return name;
    }

    /**
     * 身份证数据库校验并更新
     *
     * @param entity
     * @param form
     */
    public void validateCertNumInData(UserEntity entity, BindForm form) {
        if (!entity.getName().equals(form.getName()) || !entity.getCertNumber().equalsIgnoreCase(form.getCertNumber())) {
            entity.setCertNumber(NumEncryptUtils.encrypt(form.getCertNumber(), 1));
            entity.setCertNumberSecret(NumEncryptUtils.numSecret(form.getCertNumber(), 1));
            entity.setName(form.getName());
            this.userMapper.updateByPrimaryKeySelective(entity);
        }
    }

    /**
     * 校验登录用户是否是系统超级管理员
     *
     * @param userId
     *
     * @return
     */
    public int isSystemManger(long userId) {
        Example example = new Example(UserDepartmentEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("duty", Constants.DUTY_SYSTEM);
        return this.userDepartmentMapper.selectCountByExample(example);
    }

    /**
     * 绑定并登陆
     *
     * @return
     */
    @Transactional(rollbackFor = Throwable.class)
    public Map<String, Object> guestBind(HttpServletRequest request, HeaderHelper.SysHeader header, String thToken, BindForm form, long oid) throws IOException {

        Map<String, Object> resultMap = new HashMap<>();
        // 校验是否已经绑定
        UserEntity ue = this.selectUserByPhone(form.getPhone());
        if (ue != null && ue.getUserId() != null) {
            // 校验该用户是否已经绑定openId
            Example thEx = new Example(UserThirdEntity.class);
            thEx.createCriteria().andEqualTo("userId", ue.getUserId()).andEqualTo("oId", oid);
            int cnt = this.userThirdMapper.selectCountByExample(thEx);
            if (cnt > 0) {
                resultMap.put("code", 30011);
                return resultMap;
            }
        } else {
            resultMap.put("code", 128);
            return resultMap;
        }
        // 校验手机验证码
        int isSuccess = this.messageService.checkCode(form.getPhone(), form.getContent(), form.getUuid());
        if (isSuccess != 1) {
            log.debug("用户中心-绑定失败，errorCode = {}", isSuccess);
            resultMap.put("code", isSuccess);
            return resultMap;
        }
        // 清理缓存
        this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), ue.getUserId(), 2);
        // 用户基本信息的合并，如果存在游客身份的数据，则把游客的数据直接合并到身份证的数据
        // 改为根据phone查询用户信息 2021-04-15
        int rs = this.mergeUserInfo(form.getThToken(), form.getPhone(), oid, header.getRegionId(), ue);
        if (rs != 0) {
            log.debug("用户中心-用户合并：失败，errorCode = {}", rs);
            resultMap.put("code", rs);
            return resultMap;
        }
        //封装原来的查询方法
        UserEntity userEntity = this.selectUserByPhone(form.getPhone());

        // 如果用户不存在则新增一个用户
        if (userEntity == null || userEntity.getUserId() == null) {
            resultMap.put("code", 197);
            return resultMap;
        } else if (!StringUtils.isBlank(form.getPhone())) {
            // 不同的openId绑定同一个身份证，
            // 校验用户是否拥有该公众号权限
            // 用户所在组织
            List<GuestOrgResultForm> userOrgList = getOidList(userEntity, header.getType(), header.getRegionId());
            if (CollectionUtils.isEmpty(userOrgList)) {
                log.debug("用户不是组织成员或者所在组织未激活 -> phone:{},password:{}", form.getPhone(), form.getPassword());
                resultMap.put("code", 122);
                return resultMap;
            }
            // 校验用户权限 2019-06-17
            List<Long> orgIdList = new ArrayList<>();
            userOrgList.forEach(orgResultForm -> orgIdList.add(orgResultForm.getOrgId()));
            // 2021-09-08 移除分级平台逻辑 shenjian
            /*boolean isPass = this.officialAccountService.checkOrgPermission(oid, orgIdList);
            if (!isPass) {
                log.debug("用户无权限操作 -> userId:{},phone:{},password:{},orgId:{}", userEntity.getUserId(), form.getPhone(), form.getPassword(), oid);
                resultMap.put("code", 220);
                return resultMap;
            }*/
            // 用户存在，更新电话
            userEntity.setPhoneSecret(NumEncryptUtils.numSecret(form.getPhone(), 2));
            userEntity.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
            userEntity.setUpdateTime(new Date());
            // 1-已认证
            userEntity.setIsVerify(Constants.IS_VERIFY_YES);
            this.userMapper.updateByPrimaryKeySelective(userEntity);
        }
        resultMap.put("code", 1);
        // 登录统一放在登录中心
        ResponseEntity<Result<?>> responseEntity = new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        resultMap.put("entity", responseEntity);

        // 查询用户所在党组织信息
        List<UserOrgAndCorpEntity> userList = this.userOrgAndCorpMapper.findUserOrgInfo(1, Constants.ORG_TYPE_COMMUNIST, userEntity.getUserId(), null, header.getRegionId());
        if (CollectionUtils.isNotEmpty(userList)) {
            // 绑定成功，同步学习系统
            UserEntity user = this.userMapper.selectByPrimaryKey(userEntity.getUserId());
            this.broadcastService.commonBroadcast(user, userList.get(0).getOrganizationId(), String.valueOf(userList.get(0).getOrganizationId()), "2", header.getRegionId());
            log.debug("绑定成功后同步学习系统 userEntity:[{}],openId:[{}]", user, thToken);
        }
        // 刷新缓存
        UserEntity paramEntity = this.userMapper.selectByPrimaryKey(userEntity.getUserId());
        this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), paramEntity, 1);
        // 同步MongoDB
        this.userMongoService.conversionUser(paramEntity, null, false, header);
        return resultMap;
    }

    /**
     * 湖南商会绑定完善信息
     *
     * @param header
     * @param phone
     *
     * @return
     */
    @Transactional
    public Integer bindCoc(HeaderHelper.SysHeader header, String phone) {
        Long regionId = header.getRegionId();
        Region.OrgData rootOrg = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        int rs;
        //手机号格式校验
        if (!Utils.checkPhone(phone)) {
            log.error("手机号格式错误，[{}]", phone);
            rs = 185;
            return rs;
        }
        //通过手机号查询要绑定的用户信息
        List<UserEntity> list = findUserByPhone(phone);
        //结果为空
        if (CollectionUtils.isEmpty(list)) {
            log.error("手机号不存在，[{}]", phone);
            rs = 128;
            return rs;
        }
        //结果>1
        if (list.size() > 1) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < list.size(); i++) {
                if (i != 0) {
                    sb.append(',');
                }
                sb.append(list.get(i).getUserId());
            }
            log.error("根据手机号查询到多个用户id：{}", sb.toString());
        }
        //结果唯一
        Long bindId = list.get(0).getUserId();
        Long uid = header.getUserId();
        // 判断该手机号是否已绑定
        Example ex = new Example(UserThirdEntity.class);
        ex.createCriteria().andEqualTo("userId", bindId).andEqualTo("oId", rootOrg.getOrgId()).andEqualTo("thType", Constants.TH_TOKEN_TYPE_WEIXIN);
        int thirdCount = this.userThirdMapper.selectCountByExample(ex);
        if (thirdCount != 0) {
            rs = 30011;
            return rs;
        }
        // 判断是否是游客
        UserEntity entity = this.userMapper.selectByPrimaryKey(uid);
        if (Objects.isNull(entity) || Constants.IS_VISITOR_NO == entity.getIsVisitor()) {
            log.debug("当前区县下[{}]用户[{}]的数据不是游客，不能绑定", regionId, uid);
            return 30013;
        }
        // 判断绑定用户是否在当前区县存在商会数据
        List<UserOrgResultForm> userOrgResult = this.userOrgAndCorpMapper.findUserById(Constants.YES, Constants.ORG_TYPE_COC, bindId, null, regionId);
        if (CollectionUtils.isEmpty(userOrgResult)) {
            log.debug("当前区县下[{}]不存在用户[{}]的数据", regionId, bindId);
            return 199;
        }
        // 判断即将绑定的账号是否存在党组织关系
        List<UserOrgResultForm> communistOrgList = this.userOrgAndCorpMapper.findUserById(Constants.YES, Constants.ORG_TYPE_COMMUNIST, bindId, null, regionId);
        if (CollectionUtils.isNotEmpty(communistOrgList)) {
            log.debug("该用户[{}]存在党组织关系，不能补全信息", bindId);
            return 30014;
        } else {
            //修改
            log.debug("t_user、t_user_third、t_user_role、t_user_org_corp、t_user_department 替换信息，uid[{}]，bindId[{}]", uid, bindId);
            Integer flag = this.userMapper.bindCocUpdate(uid, bindId);
            log.debug("共更新[{}]条数据。uid[{}]，bindId[{}]", flag, uid, bindId);
            // 设置账号为游客
            UserEntity userEntity = new UserEntity();
            userEntity.setUserId(bindId);
            userEntity.setIsVisitor(Constants.IS_VERIFY_YES);
            this.userMapper.updateByPrimaryKeySelective(userEntity);
        }
        userOrgCacheCallService.flushUserInfo(regionId, bindId, 2);
        // 删除openId对应的缓存
        Example exTh = new Example(UserThirdEntity.class);
        exTh.createCriteria().andEqualTo("userId", bindId);
        List<UserThirdEntity> userThirdList = this.userThirdMapper.selectByExample(exTh);
        userThirdList.forEach(third -> this.userOrgCacheCallService.flushOpenIdSingle(third.getThToken()));
        // 同步数据
        userMongoService.conversionUser(uid, null, false, header);
        userMongoService.conversionUser(bindId, null, false, header);
        rs = 1;
        return rs;
    }

    /**
     * 设置用户角色到缓存
     *
     * @param userId
     */
    public void setUserRoleToCache(Long userId, Long regionId) {
        if (userId == null || userId <= 0) {
            return;
        }
        List<UserRoleEntity> list = this.userRoleMapper.getUserRoleList(Constants.ROLE_BELONG_PC, Constants.STATUS_YES, userId, regionId);
        if (list != null && !list.isEmpty()) {
            try {
                redisTemplate.opsForValue().set(Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + regionId, OBJECTMAPPER.writeValueAsString(list), 12 * 60, TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("设置用户角色缓存出错：" + e.getMessage(), e);
            }
        }
    }

    /**
     * 根据userId查询user对象
     *
     * @param userId
     *
     * @return
     */
    public UserEntity findUserById(Long userId) {
        return this.userMapper.selectByPrimaryKey(userId);
    }


    /**
     * 通过组织id获得用户信息
     *
     * @param politicalType
     * @param orgId
     * @param pageNo
     * @param pageSize
     *
     * @return
     */
    @Transactional
    public List<UserCertNumberForm> getUserListByOrgId(Long orgId, String politicalType, Integer pageNo, Integer pageSize, Integer isPay, Integer isEmployee, Long regionId) {
        Integer startIndex = (pageNo - 1) * pageSize;
        String[] arry = null;
        if (StringUtil.isNotEmpty(politicalType)) {
            arry = politicalType.split(",");
        }
        // 如果在当前组织交纳党费 1-是 0-否
        String tagStr = "";
        if (Constants.YES == isPay) {
            Date curTime = DateTime.now().toDate();
            // 根据组织ID查询当前组织 3-党费标签
            Example example = new Example(TagEntity.class);
            example.createCriteria().andEqualTo("tagType", Constants.TAG_TYPE_PAY).andEqualTo("organizationId", orgId).andEqualTo("status", Constants.STATUS_YES);
            List<TagEntity> payList = this.tagMapper.selectByExample(example);
            if (null != payList && !payList.isEmpty()) {
                for (int i = 0; i < payList.size(); i++) {
                    TagEntity tag = payList.get(i);
                    if (i == payList.size() - 1) {
                        tagStr += tag.getTagId();
                    } else {
                        tagStr += tag.getTagId() + ",";
                    }
                }
            } else {
                // 新增组织党费标签
                TagEntity tag = new TagEntity();
                tag.setName(ppmdTagName);
                tag.setTagType(Constants.TAG_TYPE_PAY);
                tag.setOrganizationId(orgId);
                tag.setRegionId(regionId);
                tag.setStatus(Constants.STATUS_YES);
                tag.setParentId(Constants.PARENT_CODE);
                tag.setOrgType(Constants.DEP_TYPE_ORG);
                tag.setBelong(Constants.TAG_BELONG_USER);
                tag.setLastChangeUser(-1L);
                tag.setUpdateTime(curTime);
                tag.setCreateTime(curTime);
                this.tagMapper.insertSelective(tag);
                tagStr = tag.getTagId().toString();
            }
        }
        return userMapper.getUserListByOrgId(orgId, arry, startIndex, pageSize, isPay, tagStr, isEmployee);
    }


    private String initUserOrgTag(String tagId) {
        if (StringUtils.isBlank(tagId)) {
            return initTagId;
        } else {
            List<String> oldTags = Arrays.asList(tagId.split(","));
            List<String> initTags = Arrays.asList(initTagId.split(","));
            initTags.removeAll(oldTags);
            initTags.addAll(oldTags);
            initTags = new ArrayList<String>(new HashSet<>(initTags));
            return String.join(",", initTags);
        }
    }

    /**
     * 根据组织ID查询所有下级组织用户
     *
     * @param orgId         组织ID
     * @param politicalType 政治面貌code，逗号分隔
     * @param pageNo        页码
     * @param pageSize      每页条数
     * @param isPay         是否在当前组织交纳党费 1-是 0-否
     * @param isPage        是否分页 1-是 0否
     * @param isInclude     是否包含自身 1-是 0-否
     *
     * @return
     */
    public List<UserCertNumberForm> findUserByOrg(Long orgId, String politicalType, Integer pageNo, Integer pageSize, Integer isPay, Integer isPage, Integer isInclude, Integer orgType, Integer isIncludeRetire, Long regionId) {
        Integer startIndex = (pageNo - 1) * pageSize;
        String tagStr = "";
        // 如果 isPay=0，不在当前组织交纳党费，则查询所有用户
        if (Constants.YES == isPay) {
            //// 查询所有下级组织 是否在当前组织交纳党费 标签ID
            //List<Long> tagList = this.organizationMapper.findTagByOrg(orgId, orgType, Constants.STATUS_YES, isInclude);
            //if (null != tagList && !tagList.isEmpty()) {
            //     for (int i = 0; i < tagList.size(); i++) {
            //          Long tagId = tagList.get(i);
            //          if (i == tagList.size() - 1) {
            //               tagStr += tagId;
            //          } else {
            //               tagStr += tagId + ",";
            //          }
            //     }
            //} else {
            //     // 如果所有下级组织都没有 在当前组织交纳党费 标签
            //     return null;
            //}
            // 查询党费标签
            Example example = new Example(TagEntity.class);
            example.createCriteria().andEqualTo("tagType", Constants.TAG_TYPE_PAY).andEqualTo("status", Constants.YES).andEqualTo("regionId", regionId);
            TagEntity tagEntity = this.tagMapper.selectOneByExample(example);
            if (tagEntity != null) {
                tagStr = String.valueOf(tagEntity.getTagId());
            } else {
                return null;
            }
        }
        // 转换为List<Integer>
        String[] strings = userOptionRetireCode.split(",");
        List<Integer> userOptionList = new ArrayList<>(strings.length);
        for (int i = 0; i < strings.length; i++) {
            userOptionList.add(Integer.valueOf(strings[i]));
        }
        return this.userMapper.findUserByOrg(orgId, politicalType, startIndex, pageSize, isPage, isInclude, orgType, tagStr, Constants.STATUS_YES, isIncludeRetire, userOptionList);
    }

    /**
     * 根据手机号查询用户信息 TangKaiRui 20190214
     *
     * @param phone 明文手机号
     *
     * @return
     */
    public List<UserEntity> findUserByPhone(String phone) {
        UserEntity condition = new UserEntity();
        condition.setPhone(NumEncryptUtils.encrypt(phone, 2));
        return userMapper.select(condition);
    }

    /**
     * 解绑用户第三方账号
     *
     * @param userEntity
     *
     * @return
     */
    @Transactional
    public void unbindThird(UserEntity userEntity, Long regionId, HeaderHelper.SysHeader header) {
        UserThirdEntity condition = new UserThirdEntity();
        Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        condition.setUserId(userEntity.getUserId());
        condition.setOId(orgData.getOrgId());

        List<UserThirdEntity> userThirdList = userThirdMapper.select(condition);
        //清除第三方ID（例如openid）的缓存
        for (int i = 0; i < userThirdList.size(); i++) {
            UserThirdEntity userThirdEntity = userThirdList.get(i);
            redisTemplate.delete(Global.CACHE_USER_THTOKEN_PREFIX + userThirdEntity.getThToken());
        }
        //清除用户缓存
        userOrgCacheCallService.flushUserInfo(regionId, userEntity, 2);
        // 刷新Mongo
        userMongoService.conversionUser(userEntity, null, false, header);
        //解除绑定的第三方账号
        userThirdMapper.delete(condition);
        //删除redis中的短信验证码 重试时间 身份证对应手机号验证
        redisTemplate.delete(Constants.USER_UNBIND_CODE_PREFIX + userEntity.getPhone());
        redisTemplate.delete(Constants.USER_UNBIND_CODE_RETRY_PREFIX + userEntity.getPhone());
        redisTemplate.delete(Constants.USER_UNBIND_PHONE_CHECK_PREFIX + userEntity.getPhone());
    }

    /**
     * 根据条件查询数据
     *
     * @param condition
     *
     * @return
     */
    public List<UserEntity> select(UserEntity condition) {
        return userMapper.select(condition);
    }


    /**
     * 根据用户组查询可以加入用户
     *
     * @param groupId
     * @param page
     *
     * @return
     */
    public Page<UserCertNumberForm> findUserByGroup(Long groupId, Integer page) {
        PageNumber pageNumber = new PageNumber(page, PAGE_SIZE);
        int p = Preconditions.checkNotNull(pageNumber.getPage());
        int r = Preconditions.checkNotNull(pageNumber.getRows());
        List<GroupEntity> groupList = this.groupMapper.selectGroupByParentId(groupId, 1, Constants.STATUS_YES);
        List<Long> orgList = new ArrayList<>();
        for (int i = 0; i < groupList.size(); i++) {
            orgList.add(groupList.get(i).getOrgId());
        }
        return PageHelper.startPage(p, r).doSelectPage(() -> this.userMapper.findUserByGroup(orgList));
    }

    /**
     * 移除用户除了默认权限的所有权限
     *
     * @param userId
     * @param orgId
     *
     * @return
     */
    @Transactional
    public int delUserRole(Long userId, Long orgId, HeaderHelper.SysHeader header) {
        // 2021-11-17  删除组织用户补充信息
        Example example = new Example(UserOrgAndCorpEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("organizationId", orgId);
        final UserOrgAndCorpEntity entity = this.userOrgAndCorpMapper.selectOneByExample(example);
        if (entity.getIsEmployee() == Constants.IS_EMPLOYEE) {
            throw new ApiException("该组织为用户所属组织", new Result<>(errors, 35001, HttpStatus.OK.value()));
        }
        if (StringUtils.isNotBlank(entity.getTagId())) {
            throw new ApiException("用户在该组织存在标签", new Result<>(errors, 35002, HttpStatus.OK.value()));
        }
        // 清理角色关联和用户组关联
        this.userRoleMapper.deleteByOrgIdAndUserIdEXISTS(orgId, userId);
        this.userGroupMapper.deleteByUserIdAndOrgId(userId, orgId);
        // 删除用户组织关系
        this.userOrgAndCorpMapper.deleteByUserId(Constants.DEP_TYPE_ORG, orgId, userId);
        // 解除用户在组织里的部门绑定关系
        List<Long> orgIdList = new ArrayList<>(1);
        orgIdList.add(orgId);
        List<Long> depIdList = departmentMapper.selectDepIdsByOrgIds(orgIdList);
        if (!org.springframework.util.CollectionUtils.isEmpty(depIdList)) {
            this.userDepartmentMapper.batchDelete(depIdList, userId);
        }
        // 党小组
        final OrganizationEntity org = this.organizationMapper.selectByPrimaryKey(orgId);
        if (Constants.ORG_TYPE_SMALL_GROUP.equals(org.getOrgTypeChild())) {
            int code = this.orgGroupMemberService.deleteOrgGroupMember(userId, orgId);
            if (code != 0) {
                throw new ApiException("删除党小组成员出错", new Result<>(errors, Global.Errors.SYSTEM_UNKNOWN, HttpStatus.NOT_FOUND.value()));
            }
        }
        // 刷新用户缓存
        this.userMongoService.conversionUserSync(userId, null, false, header);
        this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), userId, 1);
        return Constants.YES;
    }

    /**
     * 查询推送数据
     *
     * @param flag 1-党员政治生日 2-党员自然生日
     *
     * @return
     */
    public List<PushRequest> findPushData(int flag, Long regionId) {
        List<PushRequest> resultList = new ArrayList<>();
        List<List<UserInfoBase>> userPushList;
        List<UserInfoBase> baseList = new ArrayList<>();
        // 当前日期
        String curDate = Utils.dateToString(DateTime.now().toDate()).substring(4);
        List<UserEntity> userList = this.userMapper.findPushData(curDate, Constants.STATUS_YES, flag, politicalCode, regionId);
        if (CollectionUtils.isNotEmpty(userList)) {
            userList.forEach(userEntity -> {
                UserInfoBase base = new UserInfoBase();
                BeanUtils.copyProperties(userEntity, base);
                if (1 == flag) {
                    // 转换入党时间
                    base.setDate(Utils.convertDate(userEntity.getJoiningTime()));
                    // 计算政治年龄
                    Calendar cal = Calendar.getInstance();
                    int curYear = cal.get(Calendar.YEAR);
                    base.setNumber(curYear - Integer.parseInt(userEntity.getJoiningTime().substring(0, 4)));
                } else if (2 == flag) {
                    try {
                        base.setAge(Utils.getAge(userEntity.getCertNumber(), userEntity.getCertNumberSecret()));
                    } catch (Exception e) {
                        log.error("身份证密文转明文失败[{}]", e.getMessage(), e);
                    }
                }
                baseList.add(base);
            });
            userPushList = Utils.splitList(baseList, this.dataSize);
            // 组装推送数据
            for (List<UserInfoBase> dataList : userPushList) {
                PushRequest form = new PushRequest();
                form.setData(dataList);
                form.setChannelType(this.channelType);
                if (1 == flag) {
                    form.setTemplateId(this.joinTemplateId);
                } else if (2 == flag) {
                    form.setTemplateId(this.birthTemplateId);
                }
                form.setSource(this.source);
                form.setPushModel(this.pushModel);
                resultList.add(form);
            }
        }
        return resultList;
    }

    /**
     * 重置人员年龄
     */
    @Transactional
    public void resetAge() {
        // 获取当前月
        String curMonth = Utils.dateToString(DateTime.now().toDate()).substring(4, 8);
        // 获取当前时间
        Date curDate = DateTime.now().toDate();
        Example example = new Example(UserEntity.class);
        example.createCriteria().andLike("birthday", "%" + curMonth + "%").andEqualTo("status", Constants.STATUS_YES).andNotEqualTo("certNumber", "").andIsNotNull("certNumber").andNotEqualTo("certNumberSecret", "").andIsNotNull("certNumberSecret");
        List<UserEntity> dataList = this.userMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(userEntity -> {
                // 更新用户年龄
                int age = Utils.getAge(userEntity.getCertNumber(), userEntity.getCertNumberSecret());
                userEntity.setAge(age > 200 ? 0 : age);
                userEntity.setBirthday(Utils.getBirthday(userEntity.getCertNumber(), userEntity.getCertNumberSecret()));
                userEntity.setUpdateTime(curDate);
                this.userMapper.updateByPrimaryKeySelective(userEntity);
                // 获取用户所在区县列表
                List<Long> regionList = this.userOrgAndCorpMapper.getRegionCountByUserId(userEntity.getUserId());
                if (CollectionUtils.isNotEmpty(regionList)) {
                    for (Long regionId : regionList) {
                        // 刷新人员缓存
                        this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
                    }
                }
            });
        }
        // 处理生日为空的数据
        example.clear();
        example.createCriteria().andIsNull("age").andEqualTo("status", Constants.STATUS_YES).andNotEqualTo("certNumber", "").andIsNotNull("certNumber").andNotEqualTo("certNumberSecret", "").andIsNotNull("certNumberSecret");
        List<UserEntity> birList = this.userMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(birList)) {
            birList.forEach(userEntity -> {
                // 更新用户年龄
                int age = Utils.getAge(userEntity.getCertNumber(), userEntity.getCertNumberSecret());
                userEntity.setAge(age > 200 ? 0 : age);
                userEntity.setBirthday(Utils.getBirthday(userEntity.getCertNumber(), userEntity.getCertNumberSecret()));
                userEntity.setUpdateTime(curDate);
                this.userMapper.updateByPrimaryKeySelective(userEntity);
                // 获取用户所在区县列表
                List<Long> regionList = this.userOrgAndCorpMapper.getRegionCountByUserId(userEntity.getUserId());
                if (CollectionUtils.isNotEmpty(regionList)) {
                    for (Long regionId : regionList) {
                        // 刷新人员缓存
                        this.userOrgCacheCallService.flushUserInfo(regionId, userEntity, 1);
                    }
                }
            });
        }
    }

    /**
     * 根据身份证回写籍贯为空的人员数据
     */
    public void updateBirthPlace(Long userId) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("status", Constants.STATUS_YES);
        List<UserEntity> list = this.userMapper.selectByExample(example);
        try {
            for (UserEntity user : list) {
                if (StringUtils.isBlank(user.getBirthPlace())) {
                    // 身份证解密
                    String certNumberSecret = user.getCertNumberSecret();
                    String birthPlace = this.userMapper.getNativeByCert(certNumberSecret);
                    user.setBirthPlace(birthPlace);
                    user.setUpdateTime(new Date());
                    user.setLastChangeUser(userId);
                    this.userMapper.updateByPrimaryKey(user);
                }
            }
        } catch (Exception e) {
            log.error("更新籍贯出现错误: ", e);
        }
    }

    /**
     * <p>用户基本信息合并</p>
     * <a href="http://10.10.100.12:8090/pages/viewpage.action?pageId=33915207">非党员接入流程图</a>
     *
     * @param openid              用户的openid
     * @param phone               电话号码
     * @param oid                 组织绑定的
     * @param regionId            区县ID
     * @param userEntityCernumber 用户信息
     *
     * @return 0：成功
     */
    @Transactional(rollbackFor = Exception.class)
    public int mergeUserInfo(String openid, String phone, Long oid, Long regionId, UserEntity userEntityCernumber) {
        //step1 查询用户数据 - 根据opendid查询 直接落在数据库，减少缓存带来的影响
        //UserEntity userEntityCernumber = this.selectUserByPhone(phone);

        //step2 查询用户数据 - 根据身份证查询，减少缓存带来的影响
        UserThirdEntity userThirdEntity = this.selectUserThirdByOpenidOid(openid, oid);

        log.debug("用户中心-用户合并：userEntityCernumber = {}", userEntityCernumber);
        log.debug("用户中心-用户合并：userThirdEntity = {}", userThirdEntity);

        //step3 判断用户的userId是否相等，如果不相等，则需要合并
        if (userEntityCernumber != null && userThirdEntity != null && !userEntityCernumber.getUserId().equals(userThirdEntity.getUserId())) {
            log.debug("用户中心-用户合并：需要合并!!!");
            //step4 检查传入的oid 检查用户is_employee = 1的org_level是否包含oid，如果不包含则提示绑定失败
            boolean flag = this.checkOidisExsitInOrgLevel(oid, userEntityCernumber.getUserId(), regionId);
            if (!flag) {
                log.debug("用户的绑定关系不对，无法绑定！！！");
                return 5632;
            }
            //step5 检查用户绑定openid是否与该openid相等，如果不相等则直接更新
            UserThirdEntity cernumberThirdEntity = this.selectUserThirdByUserIdOid(userEntityCernumber.getUserId(), oid);
            //step6 封装备份数据 这个地方主要是备份所有的用户数据，重命名只是为了之后做数据插入方便看命名，以便混淆
            UserEntity visitorUserEntityBak = this.selectUserByUserId(userThirdEntity.getUserId());

            //step7 直接更新游客的第三方绑定信息
            UserThirdEntity thirdEntityUpdate = new UserThirdEntity();
            thirdEntityUpdate.setThirdId(userThirdEntity.getThirdId());
            thirdEntityUpdate.setUpdateTime(DateTime.now().toDate());
            thirdEntityUpdate.setUserId(userEntityCernumber.getUserId());
            //thirdEntityUpdate.setHead(form.getHeadUrl());
            //thirdEntityUpdate.setNickName(form.getNickName());
            this.userThirdMapper.updateByPrimaryKeySelective(thirdEntityUpdate);

            //step8 异步清理无用数据 游客数据 查询备份数据
            this.synchService.updateVisiterInfo(visitorUserEntityBak, userEntityCernumber, cernumberThirdEntity, userThirdEntity, oid);

            //step9 更新用户是否是游客
            this.updateVisitorStatus(userEntityCernumber.getUserId());
            //step10 删除缓存
            long visitorUserId = visitorUserEntityBak.getUserId();
            this.redisTemplate.delete(Global.CACHE_USER_ID_PREFIX + visitorUserId);
            this.redisTemplate.delete(Global.CACHE_BASE_USER_ID_PREFIX + visitorUserId + "_" + regionId);
            this.redisTemplate.delete(Constants.CACHE_USER_TAG_PREFIX + visitorUserId + "_" + regionId);

            long cernumberUserId = userEntityCernumber.getUserId();
            this.redisTemplate.delete(Global.CACHE_USER_ID_PREFIX + cernumberUserId);
            this.redisTemplate.delete(Global.CACHE_BASE_USER_ID_PREFIX + cernumberUserId + "_" + regionId);
            this.redisTemplate.delete(Constants.CACHE_USER_TAG_PREFIX + cernumberUserId + "_" + regionId);

            String cernumberOpenid = cernumberThirdEntity == null ? "-999" : cernumberThirdEntity.getThToken();
            this.redisTemplate.delete(Global.CACHE_USER_THTOKEN_PREFIX + openid);
            this.redisTemplate.delete(Global.CACHE_USER_THTOKEN_PREFIX + cernumberOpenid);
        } else {
            //说明当前openid和身份证的就是同一人
            if (userEntityCernumber != null && userThirdEntity != null && userEntityCernumber.getUserId().equals(userThirdEntity.getUserId())) {
                return 0;
            }
            log.debug("用户中心-用户合并，用户基础信息有误");
            return 5633;
        }

        return 0;
    }

    /**
     * 新增第三方表
     *
     * @param form   绑定提交的表单
     * @param openid openid
     * @param oid    绑定公众号的id
     * @param userId 用户id
     *
     * @return 用户的基本信息
     *
     * <AUTHOR>
     * @date 2020-01-20 4:34 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public void inserThird(BindForm form, String openid, long oid, long userId) {
        //t_user_third
        UserThirdEntity insertEntity = new UserThirdEntity();
        insertEntity.setThToken(openid);
        insertEntity.setOId(oid);
        insertEntity.setHead(form.getHeadUrl());
        insertEntity.setNickName(form.getNickName());
        insertEntity.setUserId(userId);
        insertEntity.setThType(form.getThType());
        insertEntity.setCreateTime(new Date());
        insertEntity.setUpdateTime(new Date());
        this.userThirdMapper.insertSelective(insertEntity);
    }


    /**
     * 根据身份证查询用户
     *
     * @param phone 电话号码（明文）
     *
     * @return 用户的基本信息
     *
     * <AUTHOR>
     * @date 2020-01-15 4:34 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserEntity selectUserByPhone(String phone) {
        Example example = new Example(UserEntity.class);
        example.createCriteria()
                //.andEqualTo("certNumber", NumEncryptUtils.encrypt(certNumber.toUpperCase(), 1))
                .andEqualTo("phone", NumEncryptUtils.encrypt(phone, 2)).andEqualTo("status", Constants.YES);
        return this.userMapper.selectOneByExample(example);
    }

    /**
     * 根据身份证查询用户
     *
     * @param userId 用户id
     *
     * @return 用户的基本信息
     *
     * <AUTHOR>
     * @date 2020-01-15 4:34 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserEntity selectUserByUserId(long userId) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("status", Constants.YES);
        return this.userMapper.selectOneByExample(example);
    }

    /**
     * @param userId   用户id
     * @param regionId 区县ID
     *
     * @return 用户组织绑定关系
     *
     * <AUTHOR>
     * @date 2020-01-15 4:39 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserOrgAndCorpEntity selectUserCorpByUserId(long userId, Long regionId) {
        Example example = new Example(UserOrgAndCorpEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("isEmployee", 1).andEqualTo("regionId", regionId);
        return this.userOrgAndCorpMapper.selectOneByExample(example);
    }

    /**
     * 根据openid和绑定的组织ID  oid进行查询
     *
     * @param openid openid
     * @param oid    公众号绑定的组织id
     *
     * @return 用户第三方信息
     *
     * <AUTHOR>
     * @date 2020-01-15 4:34 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserThirdEntity selectUserThirdByOpenidOid(String openid, long oid) {
        Example thEx = new Example(UserThirdEntity.class);
        thEx.createCriteria().andEqualTo("thToken", openid).andEqualTo("oId", oid);
        return this.userThirdMapper.selectOneByExample(thEx);
    }

    /**
     * 根据userId和绑定的组织ID  oid进行查询
     *
     * @param userId openid
     * @param oid    公众号绑定的组织id
     *
     * @return 用户第三方信息
     *
     * <AUTHOR>
     * @date 2020-01-20 9:34 上午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserThirdEntity selectUserThirdByUserIdOid(long userId, long oid) {
        Example thEx = new Example(UserThirdEntity.class);
        thEx.createCriteria().andEqualTo("userId", userId).andEqualTo("oId", oid);
        return this.userThirdMapper.selectOneByExample(thEx);
    }

    /**
     * 检查oid是否被用户绑定的is_employee = 1的org_level包含
     *
     * @param oid      公众号绑定组织id
     * @param userId   用户id
     * @param regionId 区县ID
     *
     * @return true: 被包含 false: 不被包含
     *
     * <AUTHOR>
     * @date 2020-01-15 4:30 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public boolean checkOidisExsitInOrgLevel(long oid, long userId, Long regionId) {
        // 查询用户是否存在绑定关系的组织
        UserOrgAndCorpEntity entity = this.selectUserCorpByUserId(userId, regionId);
        // 党员数据必须是12371同步后，到我们数据后形成绑定关系的数据，才能通过身份证和手机号绑定openid
        if (entity == null) {
            log.debug("绑定的时候没有检查到用户有is_employee = 1 的数据，userId[{}],regionId[{}]", userId, regionId);
            return false;
        }
        // 查询用户绑定关系的组织的org_level
        long bindOrgId = entity.getOrganizationId();
        OrganizationEntity orgEntity = this.selectOrgByOrgId(bindOrgId);
        if (orgEntity == null || StringUtils.isEmpty(orgEntity.getOrgLevel())) {
            log.debug("根据用户user_org_corp 提供的orgId，查询org为空 或者 orgLevel为空，orgId = {}", bindOrgId);
            return false;
        }
        String orgLevel = orgEntity.getOrgLevel();
        String[] levels = orgLevel.split("-");
        // 根据regionID查询顶级党组织ID
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Long topOrgId = orgData.getOrgId();
        for (String level : levels) {
            if ((!StringUtils.isEmpty(level) && level.equals(String.valueOf(oid))) || topOrgId.equals(oid)) {
                return true;
            }
        }
        log.debug("12371所绑定的组织org_level，没有包含当前公众号所在oid不支持绑定");
        return false;
    }


    /**
     * 检查oid是否被用户绑定的is_employee = 1的org_level包含
     *
     * @param oid    公众号绑定组织id
     * @param userId 用户id
     *
     * @return true: 被包含 false: 不被包含
     *
     * <AUTHOR>
     * @date 2020-01-15 4:30 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public UserOrgAndCorpEntity selectUserCorpByOidUserid(long oid, long userId) {
        Example example = new Example(UserOrgAndCorpEntity.class);
        example.createCriteria().andEqualTo("organizationId", oid).andEqualTo("userId", userId).andEqualTo("isEmployee", 1);
        return this.userOrgAndCorpMapper.selectOneByExample(example);
    }


    /**
     * 查询组织信息
     *
     * @param orgId 组织id
     *
     * @return 组织的详细信息
     *
     * <AUTHOR>
     * @date 2020-01-19 3:30 下午
     * @since v1.0.6 非党员接入
     */
    @Transactional
    public OrganizationEntity selectOrgByOrgId(long orgId) {
        Example example = new Example(OrganizationEntity.class);
        example.createCriteria().andEqualTo("organizationId", orgId);
        return this.organizationMapper.selectOneByExample(example);
    }


    /**
     * 更新用户的游客状态
     *
     * @param userId 用户id
     *
     * <AUTHOR>
     * @date 2020-02-13 17:54 下午
     * @since v1.0.6 非党员接入
     */
    public void updateVisitorStatus(long userId) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", userId);
        UserEntity user = new UserEntity();
        user.setUserId(userId);
        user.setIsVisitor(2);
        user.setCreateTime(DateTime.now().toDate());
        this.userMapper.updateByExampleSelective(user, example);
    }

    /**
     * 更新志愿者信息
     *
     * @param infoForm
     * @param userId
     */
    public void updateVolunteerInfo(VolunteerInfoForm infoForm, Long userId) {
        // 是游客需要修改身份证和姓名
        // 非游客不需要修改
        UserEntity user = this.selectOne(userId);
        String contactNumber = infoForm.getContactNumber();
        String certNumber = infoForm.getCertNumber();
        // 脱敏
        String certSecret = NumEncryptUtils.numSecret(certNumber, 1);
        String phoneSecret = NumEncryptUtils.numSecret(contactNumber, 2);
        // 加密
        String cert = NumEncryptUtils.encrypt(certNumber, 1);
        String phone = NumEncryptUtils.encrypt(contactNumber, 2);
        if (user.getIsVisitor() == 1) {
            // 当前用户是游客
            // 实名认证
            user.setCertNumber(cert);
            user.setCertNumberSecret(certSecret);
            user.setName(infoForm.getName());
            user.setCertType(Constants.YES);
        }
        user.setContactNumber(phone);
        user.setContactNumberSecret(phoneSecret);
        user.setNativeLevel(infoForm.getNativeLevel());
        user.setAddress(infoForm.getAddress());
        user.setWorkUnit(infoForm.getWorkUnit());
        this.userMapper.updateByPrimaryKeySelective(user);
    }

    /**
     * 查询非游客用户
     *
     * @param userId
     *
     * @return
     */
    @Transactional
    public UserEntity selectNotVolunteerUser(Long userId) {
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", userId).andEqualTo("status", Constants.YES).andNotEqualTo("isVisitor", Constants.YES);
        return this.userMapper.selectOneByExample(example);
    }

    /**
     * 处理用户临时数据
     */
    @Transactional
    public void handlerTempUserInfo() {
        List<TempUserForm> temUserInfo = userMapper.getTemUserInfo();
        temUserInfo.forEach(item -> {
            try {
                String phone = "";
                String cerNumber = "";
                Long tempUserId = item.getTemp_user_id();
                if (!StringUtils.isEmpty(item.getPhone_secret())) {
                    phone = NumEncryptUtils.decrypt(item.getPhone(), item.getPhone_secret());

                }
                if (!StringUtils.isEmpty(item.getCert_number_secret())) {
                    cerNumber = NumEncryptUtils.decrypt(item.getCert_number(), item.getCert_number_secret());
                }
                userMapper.updateTempUserInfo(tempUserId, phone, cerNumber);
            } catch (Exception e) {
                e.printStackTrace();
            }

        });

    }

    /**
     * 验证身份证
     *
     * @param userId
     * @param certNumber
     *
     * @return
     */
    public int ValidaCertNumber(Long userId, String certNumber, String name, Integer certType) {

        if (!Utils.checkCertNumber(certType, certNumber)) {
            if (Constants.CERT_NUMBER_IDENTITY == certType) {
                log.debug("新增党员-身份证号码格式错误 certNumber:{}", certNumber);
                return 149;
            }
        }
        int exist = this.checkIdCard(certNumber, userId, name);
        if (exist > 0) {
            log.debug("新增党员-身份证号码已存在 name:{}  certNumber:{}", name, certNumber);
            return 191;
        }
        // 身份证实名验证
        String validateStr = openService.validateCertNumber(certNumber, name);
        Map rsMap = Utils.fromJson(validateStr, Map.class);
        if (rsMap == null || String.valueOf(rsMap.get(Constants.CERT_NUMBER_RS_CODE)).equals(Constants.CERT_NUMBER_RS_ERROR)) {
            log.debug("新增党员-身份证号码错误，请确认身份证信息是否正确 -> name:{}  certNumber:{}", name, certNumber);
            return 151;
        }
        return Constants.STATUS_YES;
    }

    /**
     * 验证手机号
     *
     * @param userId
     * @param phone
     *
     * @return
     */
    public int ValidaPhone(Long userId, String phone) {
        // 验证手机号格式
        Pattern pattern = Pattern.compile(phoneRegexp);
        Matcher matcher = pattern.matcher(phone);
        if (!matcher.matches()) {
            log.debug("新增党员-手机号码格式错误 phone:{}", phone);
            return 185;
        }
        // 监测手机号重复
        String p = NumEncryptUtils.encrypt(phone, Constants.PHONE_IDENTITY);
        boolean isExist = this.checkPhoneRepeat(p, userId);
        if (isExist) {
            log.debug("新增党员-手机号码已经存在 phone:{}", phone);
            return 125;
        }
        return Constants.STATUS_YES;
    }

    /**
     * 身份证姓名重复验证
     *
     * @param idCard 身份证号，明文号码
     * @param userId 用户编号
     *
     * @return
     */
    private int checkIdCard(String idCard, Long userId, String name) {
        String certNumber = NumEncryptUtils.encrypt(idCard, Constants.CERT_NUMBER_IDENTITY);//加密
        return userMapper.idCardCheck(certNumber, userId, name);
    }

    /**
     * 校验手机号码是否存在
     *
     * @param phone  电话号码密文
     * @param userId 用户ID
     *
     * @return 是/否
     */
    public boolean checkPhoneRepeat(String phone, Long userId) {
        Example example = new Example(UserEntity.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("phone", phone);
        if (userId != null) {
            criteria.andNotEqualTo("userId", userId);
        }
        int cnt = this.userMapper.selectCountByExample(example);
        return cnt > 0;
    }

    /**
     * 校验手机验证码
     *
     * @param phoneSecret 如果是游客，则手机号码是明文
     * @param userId
     * @param content
     * @param uuid
     * @param isVisitor   是否是游客 1-是 2-否
     *
     * @return
     *
     * @throws Exception
     */
    public int verifySendCheck(String phoneSecret, Long userId, String content, String uuid, Integer isVisitor) {
        String encryptPhone;
        String phone;
        if (2 == isVisitor) {
            UserEntity user = this.checkExistPhone(phoneSecret, userId);
            encryptPhone = user.getPhone();
            try {
                phone = NumEncryptUtils.decrypt(user.getPhone(), user.getPhoneSecret());
            } catch (Exception e) {
                log.error("手机号码解密失败 ->[{}]", e.getMessage(), e);
                return 9901;
            }
        } else {
            phone = phoneSecret;
            encryptPhone = NumEncryptUtils.encrypt(phoneSecret, 2);
        }
        return this.messageService.verifySendCheck(encryptPhone, phone, content, uuid);
    }

    /**
     * 获取用户第三方用户信息
     *
     * @param userIds  党建系统用户ID集合
     * @param unionIds 钉钉用户在企业唯一标识集合(对应党建系统openId)
     * @param ids      钉钉用户ID集合(对应党建系统第三方ID)
     * @param regionId 区域ID
     *
     * @return List<UserThirdInfoVO>
     */
    public List<UserThirdInfoVO> getUserThirdInfo(List<Long> userIds, List<String> unionIds, List<String> ids, Long regionId) {
        log.debug("获取用户第三方用户信息 -> userIds:{}, unionIds:{}, ids:{}, regionId:{}", userIds, unionIds, ids, regionId);
        List<UserThirdInfoVO> resultList = new ArrayList<>();
        Region.OrgData orgData = this.simpleApplicationConfigHelper.getOrgByRegionId(regionId);
        Map<String, Object> queryMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            queryMap.put("userId", userIds);
        } else if (CollectionUtils.isNotEmpty(unionIds)) {
            queryMap.put("thirdList.thToken", unionIds);
        } else if (CollectionUtils.isNotEmpty(ids)) {
            queryMap.put("thirdId", ids);
        } else {
            log.error("获取用户第三方用户信息, 传入参数都是 null ");
            return null;
        }
        final List<User> userList = this.userMongoService.getUserListByWhere(queryMap);
        log.debug("查询Mongo用户列表 -> [{}]", userList);
        userList.forEach(user -> {
            UserThirdInfoVO thirdInfoVO = new UserThirdInfoVO();
            thirdInfoVO.setUserId(user.getUserId());
            thirdInfoVO.setId(user.getName());
            thirdInfoVO.setId(user.getThirdId());
            thirdInfoVO.setJobNumber(user.getJobNumber());
            final Optional<ThirdToken> firstThirdOptional = user.getThirdList().stream().filter(third -> Objects.equals(third.getOId(), orgData.getOrgId())).findFirst();
            if (firstThirdOptional.isPresent()) {
                final ThirdToken thirdToken = firstThirdOptional.get();
                thirdInfoVO.setOpenId(thirdToken.getThToken());
            }
            resultList.add(thirdInfoVO);
        });
        return resultList;
    }

    public static void main(String[] args) {
        SM4 sm4 = (SM4) SmUtil.sm4("3A85F3A48ECB5355".getBytes());
        String p = sm4.decryptStr("1f19e21268c2520dd7717a8f98cf48c7", CharsetUtil.CHARSET_UTF_8);
        System.out.println(p);
    }


}
