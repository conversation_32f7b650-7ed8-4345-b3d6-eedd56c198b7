package com.goodsogood.ows.aspect;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 简单的测试拦截器
 * 用于验证MyBatis拦截器是否正常工作
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = Executor.class, method = "queryCursor", args = {MappedStatement.class, Object.class, RowBounds.class}),
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class SimpleTestInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        log.info("🔥🔥🔥 简单测试拦截器被调用！🔥🔥🔥");
        
        try {
            // 获取方法信息
            String methodName = invocation.getMethod().getName();
            log.info("拦截到方法: {}", methodName);
            
            // 获取MappedStatement
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            log.info("Mapper方法ID: {}", mappedStatement.getId());
            
            // 获取参数
            Object parameter = invocation.getArgs()[1];
            log.info("参数类型: {}", parameter != null ? parameter.getClass().getName() : "null");
            
            // 获取SQL
            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            String sql = boundSql.getSql();
            log.info("执行的SQL: {}", sql.trim());
            
        } catch (Exception e) {
            log.error("拦截器处理过程中发生错误: {}", e.getMessage(), e);
        }
        
        // 继续执行原方法
        Object result = invocation.proceed();
        
        log.info("🔥🔥🔥 简单测试拦截器执行完成！🔥🔥🔥");
        
        return result;
    }

    @Override
    public Object plugin(Object target) {
        log.debug("SimpleTestInterceptor.plugin被调用，target: {}", target.getClass().getName());
        // 使用Plugin.wrap来包装所有匹配@Signature的对象
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        log.info("SimpleTestInterceptor.setProperties被调用");
        log.info("简单测试拦截器初始化完成");
    }
}
