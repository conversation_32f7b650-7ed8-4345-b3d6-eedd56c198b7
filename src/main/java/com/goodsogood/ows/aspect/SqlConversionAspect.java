package com.goodsogood.ows.aspect;

import com.goodsogood.ows.util.SqlCompatibilityUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Properties;

/**
 * SQL转换拦截器
 * 拦截MyBatis执行的SQL，将MySQL语法转换为达梦数据库兼容的语法
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
@Intercepts({
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})
})
public class SqlConversionAspect implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            // 获取MappedStatement
            MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
            Object parameter = invocation.getArgs()[1];
            
            // 获取BoundSql
            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            String originalSql = boundSql.getSql();
            
            // 检查是否需要转换
            if (SqlCompatibilityUtil.needsConversion(originalSql)) {
                // 转换SQL
                String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
                
                if (!originalSql.equals(convertedSql)) {
                    log.debug("SQL转换 - 方法: {}, 原SQL: {}, 转换后: {}", 
                             mappedStatement.getId(), originalSql.trim(), convertedSql.trim());
                    
                    // 创建新的BoundSql
                    BoundSql newBoundSql = new BoundSql(
                        mappedStatement.getConfiguration(),
                        convertedSql,
                        boundSql.getParameterMappings(),
                        parameter
                    );
                    
                    // 复制额外参数
                    copyAdditionalParameters(boundSql, newBoundSql);
                    
                    // 创建新的MappedStatement
                    MappedStatement newMappedStatement = copyMappedStatement(mappedStatement, new BoundSqlSource(newBoundSql));
                    
                    // 替换参数中的MappedStatement
                    invocation.getArgs()[0] = newMappedStatement;
                }
            }
            
        } catch (Exception e) {
            log.error("SQL转换过程中发生错误: {}", e.getMessage(), e);
            // 发生错误时继续使用原始SQL
        }
        
        // 继续执行原方法
        return invocation.proceed();
    }

    /**
     * 复制额外参数
     */
    private void copyAdditionalParameters(BoundSql originalBoundSql, BoundSql newBoundSql) {
        try {
            Field additionalParametersField = BoundSql.class.getDeclaredField("additionalParameters");
            additionalParametersField.setAccessible(true);
            Object additionalParameters = additionalParametersField.get(originalBoundSql);
            additionalParametersField.set(newBoundSql, additionalParameters);
        } catch (Exception e) {
            log.debug("复制额外参数失败: {}", e.getMessage());
        }
    }

    /**
     * 复制MappedStatement
     */
    private MappedStatement copyMappedStatement(MappedStatement original, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
            original.getConfiguration(),
            original.getId(),
            newSqlSource,
            original.getSqlCommandType()
        );
        
        builder.resource(original.getResource());
        builder.fetchSize(original.getFetchSize());
        builder.statementType(original.getStatementType());
        builder.keyGenerator(original.getKeyGenerator());
        
        if (original.getKeyProperties() != null) {
            builder.keyProperty(String.join(",", original.getKeyProperties()));
        }
        if (original.getKeyColumns() != null) {
            builder.keyColumn(String.join(",", original.getKeyColumns()));
        }
        
        builder.databaseId(original.getDatabaseId());
        builder.lang(original.getLang());
        builder.resultOrdered(original.isResultOrdered());
        builder.resultSets(Arrays.toString(original.getResultSets()));
        builder.timeout(original.getTimeout());
        builder.parameterMap(original.getParameterMap());
        builder.resultMaps(original.getResultMaps());
        builder.flushCacheRequired(original.isFlushCacheRequired());
        builder.useCache(original.isUseCache());
        builder.cache(original.getCache());
        
        return builder.build();
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof Executor) {
            return Plugin.wrap(target, this);
        }
        return target;
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以通过properties配置一些参数
        log.info("SQL转换拦截器已初始化");
    }

    /**
     * 自定义SqlSource，用于包装转换后的SQL
     */
    private static class BoundSqlSource implements SqlSource {
        private final BoundSql boundSql;

        public BoundSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}
