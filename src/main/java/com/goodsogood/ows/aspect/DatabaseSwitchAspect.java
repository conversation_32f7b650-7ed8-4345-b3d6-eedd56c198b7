package com.goodsogood.ows.aspect;

import com.goodsogood.ows.configuration.DynamicDataSource;
import lombok.extern.log4j.Log4j2;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * 数据库切换切面
 * 用于在不修改mapper的情况下实现数据库的动态切换
 * 默认使用达梦数据库，在达梦数据库连接失败时自动切换到MySQL
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Aspect
@Component
@Log4j2
public class DatabaseSwitchAspect {

    @Autowired
    @Qualifier("dataSource")
    private DynamicDataSource dynamicDataSource;

    /**
     * 切入点：拦截所有mapper方法
     */
    @Pointcut("execution(* com.goodsogood.ows.mapper..*(..))")
    public void mapperMethods() {}

    /**
     * 环绕通知：在mapper方法执行前自动选择可用的数据源
     * 优先使用达梦数据库，如果不可用则自动切换到MySQL
     */
    @Around("mapperMethods()")
    public Object switchDatabase(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().toShortString();

        try {
            // 自动选择可用的数据源
            dynamicDataSource.autoSelectDataSource();

            String currentDataSource = DynamicDataSource.getDataSource();
            log.debug("使用数据源 {} 执行方法: {}", currentDataSource, methodName);

            return joinPoint.proceed();

        } catch (Exception e) {
            log.error("数据库操作失败，方法: {} - 错误: {}", methodName, e.getMessage());

            // 如果当前使用的是达梦数据库，尝试切换到MySQL重试
            if (DynamicDataSource.DM_DATA_SOURCE.equals(DynamicDataSource.getDataSource())) {
                log.warn("达梦数据库操作失败，尝试切换到MySQL重试");
                try {
                    DynamicDataSource.useMysqlDataSource();
                    Object result = joinPoint.proceed();
                    log.info("MySQL数据库操作成功");
                    return result;
                } catch (Exception mysqlException) {
                    log.error("MySQL数据库操作也失败: {}", mysqlException.getMessage());
                    throw mysqlException;
                } finally {
                    // 清除线程本地变量
                    DynamicDataSource.clearDataSource();
                }
            } else {
                throw e;
            }
        } finally {
            // 确保清除线程本地变量
            DynamicDataSource.clearDataSource();
        }
    }

    /**
     * 获取当前使用的数据源信息
     */
    public String getCurrentDataSourceInfo() {
        return dynamicDataSource.getDataSourceStatus();
    }

    /**
     * 手动测试数据库连接
     */
    public void testDatabaseConnections() {
        log.info("开始测试数据库连接...");

        if (dynamicDataSource.isDmDatabaseAvailable()) {
            log.info("✓ 达梦数据库连接正常");
        } else {
            log.error("✗ 达梦数据库连接失败");
        }

        if (dynamicDataSource.isMysqlDatabaseAvailable()) {
            log.info("✓ MySQL数据库连接正常");
        } else {
            log.error("✗ MySQL数据库连接失败");
        }

        log.info("数据库连接测试完成");
        log.info(getCurrentDataSourceInfo());
    }

    /**
     * 手动切换到达梦数据库
     */
    public void switchToDmDatabase() {
        DynamicDataSource.useDmDataSource();
        log.info("手动切换到达梦数据库");
    }

    /**
     * 手动切换到MySQL数据库
     */
    public void switchToMysqlDatabase() {
        DynamicDataSource.useMysqlDataSource();
        log.info("手动切换到MySQL数据库");
    }
}
