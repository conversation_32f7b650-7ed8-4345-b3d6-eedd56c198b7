package com.goodsogood.ows.component;

import com.goodsogood.ows.aspect.DatabaseSwitchAspect;
import com.goodsogood.ows.configuration.DynamicDataSource;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;

/**
 * 数据库连接测试组件
 * 在应用启动时自动测试数据库连接
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
public class DatabaseConnectionTester implements ApplicationRunner {

    @Autowired
    @Qualifier("dataSource")
    private DynamicDataSource dynamicDataSource;

    @Autowired
    private DatabaseSwitchAspect databaseSwitchAspect;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== 开始数据库连接测试 ===");
        
        try {
            testDatabaseConnections();
            testCurrentDataSource();
            log.info("=== 数据库连接测试完成 ===");
        } catch (Exception e) {
            log.error("=== 数据库连接测试失败 ===", e);
        }
    }

    /**
     * 测试所有数据库连接
     */
    private void testDatabaseConnections() {
        log.info("测试各数据库连接状态...");
        
        // 测试达梦数据库
        if (dynamicDataSource.isDmDatabaseAvailable()) {
            log.info("✓ 达梦数据库连接正常");
            testDmDatabaseDetails();
        } else {
            log.error("✗ 达梦数据库连接失败");
        }
        
        // 测试MySQL数据库
        if (dynamicDataSource.isMysqlDatabaseAvailable()) {
            log.info("✓ MySQL数据库连接正常");
            testMysqlDatabaseDetails();
        } else {
            log.error("✗ MySQL数据库连接失败");
        }
        
        // 输出数据源状态
        log.info("数据源状态:\n{}", dynamicDataSource.getDataSourceStatus());
    }

    /**
     * 测试达梦数据库详细信息
     */
    private void testDmDatabaseDetails() {
        try {
            DynamicDataSource.useDmDataSource();
            try (Connection connection = dynamicDataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                log.info("达梦数据库详细信息:");
                log.info("  - 产品名称: {}", metaData.getDatabaseProductName());
                log.info("  - 产品版本: {}", metaData.getDatabaseProductVersion());
                log.info("  - 驱动名称: {}", metaData.getDriverName());
                log.info("  - 驱动版本: {}", metaData.getDriverVersion());
                log.info("  - 连接URL: {}", metaData.getURL());
                log.info("  - 用户名: {}", metaData.getUserName());
            }
        } catch (SQLException e) {
            log.error("获取达梦数据库详细信息失败: {}", e.getMessage());
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }

    /**
     * 测试MySQL数据库详细信息
     */
    private void testMysqlDatabaseDetails() {
        try {
            DynamicDataSource.useMysqlDataSource();
            try (Connection connection = dynamicDataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                log.info("MySQL数据库详细信息:");
                log.info("  - 产品名称: {}", metaData.getDatabaseProductName());
                log.info("  - 产品版本: {}", metaData.getDatabaseProductVersion());
                log.info("  - 驱动名称: {}", metaData.getDriverName());
                log.info("  - 驱动版本: {}", metaData.getDriverVersion());
                log.info("  - 连接URL: {}", metaData.getURL());
                log.info("  - 用户名: {}", metaData.getUserName());
            }
        } catch (SQLException e) {
            log.error("获取MySQL数据库详细信息失败: {}", e.getMessage());
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }

    /**
     * 测试当前数据源
     */
    private void testCurrentDataSource() {
        log.info("测试当前数据源...");
        
        try {
            // 自动选择数据源
            dynamicDataSource.autoSelectDataSource();
            
            try (Connection connection = dynamicDataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                log.info("当前使用数据源:");
                log.info("  - 数据源标识: {}", DynamicDataSource.getDataSource());
                log.info("  - 数据库类型: {}", metaData.getDatabaseProductName());
                log.info("  - 连接URL: {}", metaData.getURL());
                
                // 执行简单查询测试
                try {
                    connection.createStatement().executeQuery("SELECT 1 FROM DUAL").close();
                    log.info("  - 查询测试: ✓ 成功");
                } catch (SQLException e) {
                    log.warn("  - 查询测试: ✗ 失败 - {}", e.getMessage());
                }
            }
        } catch (SQLException e) {
            log.error("当前数据源测试失败: {}", e.getMessage());
        } finally {
            DynamicDataSource.clearDataSource();
        }
    }

    /**
     * 手动触发数据库连接测试
     */
    public void manualTest() {
        log.info("=== 手动触发数据库连接测试 ===");
        try {
            testDatabaseConnections();
            testCurrentDataSource();
            log.info("=== 手动数据库连接测试完成 ===");
        } catch (Exception e) {
            log.error("=== 手动数据库连接测试失败 ===", e);
        }
    }

    /**
     * 获取数据库连接摘要信息
     */
    public String getConnectionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("数据库连接摘要:\n");
        
        if (dynamicDataSource.isDmDatabaseAvailable()) {
            summary.append("✓ 达梦数据库: 可用\n");
        } else {
            summary.append("✗ 达梦数据库: 不可用\n");
        }
        
        if (dynamicDataSource.isMysqlDatabaseAvailable()) {
            summary.append("✓ MySQL数据库: 可用\n");
        } else {
            summary.append("✗ MySQL数据库: 不可用\n");
        }
        
        summary.append("当前数据源: ").append(DynamicDataSource.getDataSource());
        
        return summary.toString();
    }
}
