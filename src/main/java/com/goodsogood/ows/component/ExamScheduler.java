//package com.goodsogood.ows.component;
//
//import com.goodsogood.ows.service.ExaminationService;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
///**
// * 考试同步结果
// */
//@Component
//@Log4j2
//public class ExamScheduler {
//
//    @Value("${credit.run}")
//    private boolean run;
//
//    private final ExaminationService examinationService;
//
//    public ExamScheduler(ExaminationService examinationService) {
//        this.examinationService = examinationService;
//    }
//
//
//    /**
//     * 同步当天的开始结果 每隔30分钟执行一下
//     */
//    @Scheduled(cron = "${credit.syn-exam-today-scheduler}")
//    public void synTodayExamScheduler() {
//        log.debug("每隔30分钟拉取考生结果，并且同步学校系统开始,run[{}]", run);
//        if (!run) {
//            log.debug("每隔30分钟拉取考生结果，并且同步学校系统开始,关闭！！！！");
//            return;
//        }
//        //处理同步积分
//        examinationService.synScore(0,0,1,1);
//    }
//
//
//
//    /**
//     * 每天进行计算
//     */
//    @Scheduled(cron = "${credit.syn-exam-result-scheduler}")
//    public void synExamScheduler() {
//        log.debug("拉取考生结果，并且同步学校系统开始,run[{}]", run);
//        if (!run) {
//            log.debug("拉取考生结果，并且同步学校系统开始,关闭！！！！");
//            return;
//        }
//        //处理同步积分
//        examinationService.synScore(0,1,1,1);
//    }
//
//    /**
//     * 考试结果补发，这里面就不用同步考试结果
//     */
//    @Scheduled(cron = "${credit.syn-exam-result-reissue}")
//    public void reissueScheduler() {
//        log.debug("考试结果补发结果，开始,run[{}]", run);
//        if (!run) {
//            log.debug("考试结果补发结果,关闭！！！！");
//            return;
//        }
//        //处理同步积分
//        examinationService.synScore(0,1,0,1);
//    }
//}
