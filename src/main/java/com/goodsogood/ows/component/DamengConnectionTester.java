package com.goodsogood.ows.component;

import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 达梦数据库连接测试组件
 * 在应用启动时自动测试达梦数据库连接
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
public class DamengConnectionTester implements ApplicationRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== 开始达梦数据库连接测试 ===");
        
        try {
            testDatabaseConnection();
            testBasicQuery();
            log.info("=== 达梦数据库连接测试完成 ===");
        } catch (Exception e) {
            log.error("=== 达梦数据库连接测试失败 ===");
            log.error("错误详情: {}", e.getMessage());
            // 不抛出异常，让应用继续启动，但会记录错误
        }
    }

    /**
     * 测试数据库连接
     */
    private void testDatabaseConnection() {
        log.info("测试达梦数据库连接...");
        
        try (Connection connection = dataSource.getConnection()) {
            if (connection != null && !connection.isClosed()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                log.info("✓ 达梦数据库连接成功");
                log.info("数据库信息:");
                log.info("  - 产品名称: {}", metaData.getDatabaseProductName());
                log.info("  - 产品版本: {}", metaData.getDatabaseProductVersion());
                log.info("  - 驱动名称: {}", metaData.getDriverName());
                log.info("  - 驱动版本: {}", metaData.getDriverVersion());
                log.info("  - 连接URL: {}", metaData.getURL());
                log.info("  - 用户名: {}", metaData.getUserName());
                log.info("  - 支持事务: {}", metaData.supportsTransactions());
                log.info("  - 支持存储过程: {}", metaData.supportsStoredProcedures());
                
            } else {
                log.error("✗ 达梦数据库连接异常：连接为空或已关闭");
            }
        } catch (SQLException e) {
            log.error("✗ 达梦数据库连接失败: {}", e.getMessage());
            throw new RuntimeException("达梦数据库连接失败", e);
        }
    }

    /**
     * 测试基本查询
     */
    private void testBasicQuery() {
        log.info("测试达梦数据库基本查询...");
        
        try (Connection connection = dataSource.getConnection()) {
            // 测试基本查询
            try (ResultSet rs = connection.createStatement().executeQuery("SELECT 1 FROM DUAL")) {
                if (rs.next()) {
                    int result = rs.getInt(1);
                    log.info("✓ 基本查询测试成功，结果: {}", result);
                } else {
                    log.warn("✗ 基本查询测试失败：没有返回结果");
                }
            }
            
            // 测试系统时间查询
            try (ResultSet rs = connection.createStatement().executeQuery("SELECT SYSDATE FROM DUAL")) {
                if (rs.next()) {
                    String sysdate = rs.getString(1);
                    log.info("✓ 系统时间查询成功: {}", sysdate);
                } else {
                    log.warn("✗ 系统时间查询失败：没有返回结果");
                }
            }
            
            // 测试数据库版本查询
            try (ResultSet rs = connection.createStatement().executeQuery("SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1")) {
                if (rs.next()) {
                    String version = rs.getString(1);
                    log.info("✓ 数据库版本查询成功: {}", version);
                } else {
                    log.debug("数据库版本查询没有返回结果（可能是权限问题）");
                }
            } catch (SQLException e) {
                log.debug("数据库版本查询失败（可能是权限问题）: {}", e.getMessage());
            }
            
        } catch (SQLException e) {
            log.error("✗ 达梦数据库查询测试失败: {}", e.getMessage());
            throw new RuntimeException("达梦数据库查询测试失败", e);
        }
    }

    /**
     * 手动触发数据库连接测试
     */
    public void manualTest() {
        log.info("=== 手动触发达梦数据库连接测试 ===");
        try {
            testDatabaseConnection();
            testBasicQuery();
            log.info("=== 手动达梦数据库连接测试完成 ===");
        } catch (Exception e) {
            log.error("=== 手动达梦数据库连接测试失败 ===", e);
        }
    }

    /**
     * 获取数据库连接摘要信息
     */
    public String getConnectionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("达梦数据库连接摘要:\n");
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            summary.append("✓ 连接状态: 正常\n");
            summary.append("✓ 数据库: ").append(metaData.getDatabaseProductName())
                   .append(" ").append(metaData.getDatabaseProductVersion()).append("\n");
            summary.append("✓ 驱动: ").append(metaData.getDriverName())
                   .append(" ").append(metaData.getDriverVersion()).append("\n");
            summary.append("✓ 用户: ").append(metaData.getUserName());
        } catch (SQLException e) {
            summary.append("✗ 连接状态: 失败\n");
            summary.append("✗ 错误信息: ").append(e.getMessage());
        }
        
        return summary.toString();
    }

    /**
     * 检查数据库是否可用
     */
    public boolean isDatabaseAvailable() {
        try (Connection connection = dataSource.getConnection()) {
            return connection != null && !connection.isClosed() && connection.isValid(5);
        } catch (SQLException e) {
            log.debug("数据库可用性检查失败: {}", e.getMessage());
            return false;
        }
    }
}
