package com.goodsogood.ows.component;

public enum FieldType {

	/** org_id <br/>
	 * type -> 组织<br/>
	 * returnType -> 下划线命名<br/>
	 * fieldName -> 对应的取值参数 -> org_id
	 */
	ORG_ID(2, 1, "org_id"),

	/** orgId <br/>
	 * type -> 组织<br/>
	 * returnType -> 驼峰命名<br/>
	 * fieldName -> 对应的取值参数 -> orgId
	 */
	ORGID(2, 2, "orgId"),

	/** organization_id <br/>
	 * type -> 组织<br/>
	 * returnType -> 下划线命名<br/>
	 * fieldName -> 对应的取值参数 -> organization_id
	 */
	ORGANIZATION_ID(2, 1, "organization_id"),

	/** organizationId <br/>
	 * type -> 组织<br/>
	 * returnType -> 驼峰命名<br/>
	 * fieldName -> 对应的取值参数 -> organizationId
	 */
	ORGANIZATIONID(2, 2, "organizationId"),

	/** oid <br/>
	 * type -> 组织<br/>
	 * returnType -> 通用<br/>
	 * fieldName -> 对应的取值参数 -> oid
	 */
	OID(2, 0, "oid"),

	/** user_id <br/>
	 * type -> 用户<br/>
	 * returnType -> 下划线命名<br/>
	 * fieldName -> 对应的取值参数 -> user_id
	 */
	USER_ID(1, 1, "user_id"),

	/** userid <br/>
	 * type -> 用户<br/>
	 * returnType -> 驼峰命名<br/>
	 * fieldName -> 对应的取值参数 -> userId
	 */
	USERID(1, 2, "userId");

	/** 1 - 用户, 2 - 组织 */
	private final int type;

	/** 0 -> 通用
	 * 1 -> 返回的参数是下划线<br/>
	 * 2 -> 返回的参数是驼峰命名 */
	private final int returnType;

	private final String fieldName;

	FieldType(int type, int returnType, String fieldName) {
		this.type = type;
		this.returnType = returnType;
		this.fieldName = fieldName;
	}

	public int getType() {
		return type;
	}

	public int getReturnType() {return returnType;}

	public String getFieldName() {
		return fieldName;
	}
}
