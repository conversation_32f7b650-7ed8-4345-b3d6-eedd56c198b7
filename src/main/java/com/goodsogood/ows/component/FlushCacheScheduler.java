package com.goodsogood.ows.component;

import com.goodsogood.ows.service.OpenService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.TemporalAdjusters;

@Component
@Log4j2
public class FlushCacheScheduler {

    @Value("${flush-cache.run}")
    private boolean run = false;

    private final OpenService openService;

    @Autowired
    public FlushCacheScheduler(OpenService openService) {
        this.openService = openService;
    }

    @Scheduled(cron = "${flush-cache.cron}")
    public void run() {
        if (run) {
             // 判断当天是不是本月最后一天
            LocalDate today = LocalDate.now();
            LocalDate lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth());
            int days = Period.between(today, lastDayOfMonth).getDays();
            log.debug("当前是[{}], 当月最后一天是[{}], [{}]最后一天", today.toString(), lastDayOfMonth.toString(), days == 0? "是":"不是");
            if (days == 0) {
                log.debug("全量刷新(用户、组织)缓存开始");
                this.openService.saveUserBaseInfoInRedis();
                log.debug("全量刷新(用户、组织)缓存结束");
            } else {
                log.debug("当前不是本月最后一天，离最后一天还有[{}]天", days);
            }
        }
    }
}
