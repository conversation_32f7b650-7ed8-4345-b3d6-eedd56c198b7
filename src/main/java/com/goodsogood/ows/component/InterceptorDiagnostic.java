package com.goodsogood.ows.component;

import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 拦截器诊断组件
 * 在应用启动时检查拦截器注册状态
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@Component
@Log4j2
public class InterceptorDiagnostic implements ApplicationRunner {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("=== 开始拦截器诊断 ===");
        
        try {
            diagnoseSqlSessionFactories();
            diagnoseInterceptors();
            log.info("=== 拦截器诊断完成 ===");
        } catch (Exception e) {
            log.error("=== 拦截器诊断失败 ===", e);
        }
    }

    /**
     * 诊断SqlSessionFactory
     */
    private void diagnoseSqlSessionFactories() {
        log.info("诊断SqlSessionFactory...");
        log.info("找到 {} 个SqlSessionFactory", sqlSessionFactoryList.size());
        
        if (sqlSessionFactoryList.isEmpty()) {
            log.error("❌ 没有找到SqlSessionFactory！这可能是配置问题。");
            return;
        }
        
        for (int i = 0; i < sqlSessionFactoryList.size(); i++) {
            SqlSessionFactory factory = sqlSessionFactoryList.get(i);
            log.info("SqlSessionFactory #{}: {}", i + 1, factory.getClass().getName());
            
            org.apache.ibatis.session.Configuration config = factory.getConfiguration();
            log.info("  - 数据库ID: {}", config.getDatabaseId());
            log.info("  - 默认执行器类型: {}", config.getDefaultExecutorType());
            log.info("  - 驼峰命名转换: {}", config.isMapUnderscoreToCamelCase());
            log.info("  - 已注册Mapper数量: {}", config.getMapperRegistry().getMappers().size());
        }
    }

    /**
     * 诊断拦截器
     */
    private void diagnoseInterceptors() {
        log.info("诊断拦截器注册状态...");
        
        for (int i = 0; i < sqlSessionFactoryList.size(); i++) {
            SqlSessionFactory factory = sqlSessionFactoryList.get(i);
            List<Interceptor> interceptors = factory.getConfiguration().getInterceptors();
            
            log.info("SqlSessionFactory #{} 的拦截器:", i + 1);
            log.info("  - 拦截器总数: {}", interceptors.size());
            
            if (interceptors.isEmpty()) {
                log.warn("  ❌ 没有找到任何拦截器！");
                log.warn("  可能的原因:");
                log.warn("    1. 拦截器没有被正确注册");
                log.warn("    2. ConfigurationCustomizer没有被执行");
                log.warn("    3. Spring Bean注入失败");
                continue;
            }
            
            boolean foundOurInterceptors = false;
            
            for (int j = 0; j < interceptors.size(); j++) {
                Interceptor interceptor = interceptors.get(j);
                String className = interceptor.getClass().getName();
                log.info("  拦截器 #{}: {}", j + 1, className);
                
                if (className.contains("SqlConversionAspect")) {
                    log.info("    ✅ 找到SQL转换拦截器");
                    foundOurInterceptors = true;
                } else if (className.contains("SimpleTestInterceptor")) {
                    log.info("    ✅ 找到简单测试拦截器");
                    foundOurInterceptors = true;
                } else {
                    log.info("    ℹ️ 其他拦截器: {}", className);
                }
            }
            
            if (!foundOurInterceptors) {
                log.warn("  ❌ 没有找到我们的拦截器！");
                log.warn("  请检查:");
                log.warn("    1. SqlConversionAspect 是否有 @Component 注解");
                log.warn("    2. SimpleTestInterceptor 是否有 @Component 注解");
                log.warn("    3. MyBatisInterceptorConfig 是否有 @Configuration 注解");
                log.warn("    4. 包扫描路径是否包含拦截器所在的包");
            } else {
                log.info("  ✅ 拦截器注册正常");
            }
        }
    }

    /**
     * 手动触发诊断
     */
    public void manualDiagnose() {
        log.info("=== 手动触发拦截器诊断 ===");
        try {
            diagnoseSqlSessionFactories();
            diagnoseInterceptors();
            log.info("=== 手动拦截器诊断完成 ===");
        } catch (Exception e) {
            log.error("=== 手动拦截器诊断失败 ===", e);
        }
    }

    /**
     * 获取诊断摘要
     */
    public String getDiagnosticSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("拦截器诊断摘要:\n");
        
        summary.append("SqlSessionFactory数量: ").append(sqlSessionFactoryList.size()).append("\n");
        
        if (sqlSessionFactoryList.isEmpty()) {
            summary.append("❌ 没有找到SqlSessionFactory\n");
            return summary.toString();
        }
        
        int totalInterceptors = 0;
        boolean foundOurInterceptors = false;
        
        for (SqlSessionFactory factory : sqlSessionFactoryList) {
            List<Interceptor> interceptors = factory.getConfiguration().getInterceptors();
            totalInterceptors += interceptors.size();
            
            for (Interceptor interceptor : interceptors) {
                String className = interceptor.getClass().getName();
                if (className.contains("SqlConversionAspect") || className.contains("SimpleTestInterceptor")) {
                    foundOurInterceptors = true;
                    break;
                }
            }
        }
        
        summary.append("拦截器总数: ").append(totalInterceptors).append("\n");
        
        if (foundOurInterceptors) {
            summary.append("✅ 找到我们的拦截器\n");
        } else {
            summary.append("❌ 没有找到我们的拦截器\n");
        }
        
        return summary.toString();
    }
}
