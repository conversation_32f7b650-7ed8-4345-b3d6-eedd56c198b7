package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.mapper.MenuUrlMapper;
import com.goodsogood.ows.mapper.RoleMenuMapper;
import com.goodsogood.ows.mapper.UserLoginLogMapper;
import com.goodsogood.ows.mapper.UserOrgAndCorpMapper;
import com.goodsogood.ows.mapper.UserRoleMapper;
import com.goodsogood.ows.mapper.UserThirdMapper;
import com.goodsogood.ows.model.db.DepartmentEntity;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.db.UserLoginLogEntity;
import com.goodsogood.ows.model.db.UserOrgAndCorpEntity;
import com.goodsogood.ows.model.db.UserRoleEntity;
import com.goodsogood.ows.model.db.UserThirdEntity;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.RoleMenuForm;
import com.goodsogood.ows.service.LoginOpenService;
import com.goodsogood.ows.service.OrgService;
import com.goodsogood.ows.service.UserOrgCacheCallService;
import com.goodsogood.ows.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/uc/login")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "用户登录", tags = {"Login"})
@Validated
public class LoginOpenController {

     private final ObjectMapper OBJECTMAPPER = new ObjectMapper();

     private final Errors errors;
     private final UserService userService;
     private final UserOrgAndCorpMapper userOrgAndCorpMapper;
     private final StringRedisTemplate redisTemplate;
     private final UserOrgCacheCallService userOrgCacheCallService;
     private final LoginOpenService loginOpenService;
     private final UserRoleMapper userRoleMapper;
     private final RoleMenuMapper roleMenuMapper;
     private final MenuUrlMapper menuUrlMapper;
     private final UserThirdMapper userThirdMapper;
     private final UserLoginLogMapper userLoginLogMapper;
     private final OrgService orgService;

     @Autowired
     public LoginOpenController(Errors errors, UserService userService, StringRedisTemplate redisTemplate, UserOrgAndCorpMapper userOrgAndCorpMapper,
                                UserOrgCacheCallService userOrgCacheCallService, LoginOpenService loginOpenService, UserRoleMapper userRoleMapper,
                                RoleMenuMapper roleMenuMapper, MenuUrlMapper menuUrlMapper, UserThirdMapper userThirdMapper, UserLoginLogMapper userLoginLogMapper,
                                OrgService orgService) {
          this.errors = errors;
          this.userService = userService;
          this.redisTemplate = redisTemplate;
          this.userOrgAndCorpMapper = userOrgAndCorpMapper;
          this.userOrgCacheCallService = userOrgCacheCallService;
          this.loginOpenService = loginOpenService;
          this.userRoleMapper = userRoleMapper;
          this.roleMenuMapper = roleMenuMapper;
          this.menuUrlMapper = menuUrlMapper;
          this.userThirdMapper = userThirdMapper;
          this.userLoginLogMapper = userLoginLogMapper;
          this.orgService = orgService;
     }

     /**
      * 根据手机号和密码查询用户
      *
      * @param phone
      * @param password
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/validatedPassword")
     @ApiOperation("根据手机号和密码查询用户")
     public ResponseEntity<Result<?>> validatedPassword(@RequestParam("phone") String phone, @RequestParam("password") String password) {
          UserEntity userEntity = this.userService.check(phone, password);
          return new ResponseEntity<>(new Result<>(userEntity, errors), HttpStatus.OK);
     }

     /**
      * 根据用户ID查询组织列表
      *
      * @param userId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getOrgListByUser")
     @ApiOperation("根据用户ID查询组织列表")
     public ResponseEntity<Result<?>> getOrgListByUser(@RequestParam("user_id") Long userId) {
          List<UserOrgAndCorpEntity> list = this.userOrgAndCorpMapper.getUserOrgList(userId, Constants.ACTIVATE_STATUS_YES, Constants.STATUS_YES);
          return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
     }

     /**
      * 根据用户ID查询详情
      *
      * @param userId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getUserById")
     @ApiOperation("根据手机号和密码查询用户")
     public ResponseEntity<Result<?>> getUserById(@RequestParam("user_id") Long userId) {
          UserEntity user = this.userService.selectOne(userId);
          return new ResponseEntity<>(new Result<>(user, errors), HttpStatus.OK);
     }

     /**
      * 刷新用户信息缓存
      *
      * @param userId
      * @param type   1 刷新缓存，2 清理缓存
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/flushUserInfo")
     @ApiOperation("刷新用户信息缓存")
     public ResponseEntity<Result<?>> flushUserInfo(@RequestParam("user_id") Long userId,
                                                    @RequestParam("type") Integer type,
                                                    @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), userId, type);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 查询人员的菜单信息
      *
      * @param userId
      * @param orgId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getMenuByUser")
     @ApiOperation("查询人员的菜单信息")
     public ResponseEntity<Result<?>> getMenuByUser(@RequestParam("user_id") Long userId, @RequestParam("org_id") Long orgId,
                                                    @RequestParam("token_id") String tokenId,
                                                    @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          List<MenuForm> menuList = this.loginOpenService.selectMenuByUser(userId, orgId, tokenId, header.getRegionId());
          return new ResponseEntity<>(new Result<>(menuList, errors), HttpStatus.OK);
     }

     /**
      * 查询组织详情
      *
      * @param orgId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getOrgInfo")
     @ApiOperation("查询组织详情")
     public ResponseEntity<Result<?>> getOrgInfo(@RequestParam("org_id") Long orgId) {
          OrganizationEntity orgInfo = new OrganizationEntity();
          String orgKey = Global.CACHE_ORG_ID_PREFIX + orgId;
          if (this.redisTemplate.hasKey(orgKey)) {
               orgInfo = Utils.fromJson(this.redisTemplate.opsForValue().get(orgKey), OrganizationEntity.class);
          } else {
               orgInfo = this.loginOpenService.getOrgInfo(orgId);
               this.redisTemplate.opsForValue().set(orgKey, Utils.toJson(orgInfo), Global.CACHE_USER_TIMEOUT, Global.CACHE_USER_TIME_UNIT);
          }
          return new ResponseEntity<>(new Result<>(orgInfo, errors), HttpStatus.OK);
     }

     /**
      * 查询单位详情
      *
      * @param orgId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getDepartment")
     @ApiOperation("查询单位详情")
     public ResponseEntity<Result<?>> getDepartment(@RequestParam("org_id") Long orgId) {
          DepartmentEntity department = this.loginOpenService.getDepartment(orgId);
          return new ResponseEntity<>(new Result<>(department, errors), HttpStatus.OK);
     }

     /**
      * 根据用户和组织查询用户的角色
      *
      * @param userId
      * @param orgId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getRoleListByUser")
     @ApiOperation("根据用户和组织查询用户的角色")
     public ResponseEntity<Result<?>> getRoleListByUser(@RequestParam("user_id") Long userId, @RequestParam("org_id") Long orgId,
                                                        @RequestParam("token_id") String tokenId,
                                                        @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          String roleKey = Constants.USER_ROLE_CACHE_KEY_PREFIX + userId + "_" + header.getRegionId();
          RoleMenuForm resultForm = new RoleMenuForm();
          List<Long> roleList = new ArrayList<>();
          List<MenuForm> menuList = new ArrayList<>();
          try {
               if (this.redisTemplate.hasKey(roleKey)) {
                    // 从缓存获取角色
                    log.debug("登录时获取用户角色缓存信息 roleKey:{}", roleKey);
                    List<UserRoleEntity> list = OBJECTMAPPER.readValue(this.redisTemplate.opsForValue().get(roleKey), new
                            TypeReference<List<UserRoleEntity>>() {
                            });
                    for (UserRoleEntity role : list) {
                         if (role.getOrganizationId().intValue() == orgId.intValue()) {
                              roleList.add(role.getRoleId());
                         }
                    }
               } else {
                    // 从数据库获取角色
                    UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
                    entity.setOrganizationId(orgId);
                    entity.setUserId(userId);
                    roleList = this.userRoleMapper.getRoleList(entity);
               }
          } catch (Exception e) {
               log.error("查询用户出现错误", e);
               // 从数据库获取角色
               UserOrgAndCorpEntity entity = new UserOrgAndCorpEntity();
               entity.setOrganizationId(orgId);
               entity.setUserId(userId);
               roleList = this.userRoleMapper.getRoleList(entity);
          }
          // 根据角色查询菜单
          if (!roleList.isEmpty()) {
               menuList = this.roleMenuMapper.getMenuByRole(roleList, Constants.MENU_BELONG_PC, header.getRegionId());
               // 根据菜单查询菜单对应接口地址列表
               List<String> menuIdList = new ArrayList<>();
               for (MenuForm menuForm : menuList) {
                    menuIdList.add(menuForm.getMenuId());
               }
               if (!menuIdList.isEmpty()) {
                    List<String> redisList = this.menuUrlMapper.getLinkUrlList(menuIdList, Constants.STATUS_YES);
                    if (redisList != null && !redisList.isEmpty()) {
                         // 菜单接口存入redis
                         log.debug("登录时，用户权限菜单存入redis tokenId:{},linkUrl:{}", tokenId, redisList);
                         this.redisTemplate.opsForValue().set(Constants.LOGIN_PREFIX_M + tokenId, Utils.toJson(redisList), 30, TimeUnit.MINUTES);
                    }
               }
          }
          resultForm.setRoleList(roleList);
          resultForm.setMenuList(menuList);
          return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/getTag")
     @ApiOperation("根据用户获取标签")
     public ResponseEntity<Result<?>> getTag(@RequestParam("user_id") Long userId, @RequestParam("org_id") Long orgId) {
          String tag = this.userOrgAndCorpMapper.findTag(userId, Constants.DEP_TYPE_ORG, orgId);
          return new ResponseEntity<>(new Result<>(tag, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/getUserOpenId")
     @ApiOperation("根据用户OPENID")
     public ResponseEntity<Result<?>> getUserOpenId(@RequestParam("user_id") Long userId, @RequestParam("org_id") Long orgId) {
          Example example = new Example(UserThirdEntity.class);
          example.selectProperties("thToken");
          example.createCriteria().andEqualTo("userId", userId)
                  .andEqualTo("oId", orgId);
          UserThirdEntity ute = this.userThirdMapper.selectOneByExample(example);
          return new ResponseEntity<>(new Result<>(ute, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/insertLog")
     @ApiOperation("新增登录日志")
     public void insertLog(@RequestParam("user_id") Long userId, @RequestParam("now") Date now, @RequestParam("ip") String ip,
                           @RequestParam("source") String source, @RequestParam("token_id") String tokenId, @RequestParam("belong") Integer belong,
                           @RequestParam("org_id") Long orgId, @RequestParam("open_id") String openId) {
          // 新增登录日志
          log.debug("新增登录日志 userId:{},date:{}", userId, now);
          UserLoginLogEntity logEntity = new UserLoginLogEntity();
          logEntity.setLoginId(userId);
          logEntity.setLoginDate(now);
          logEntity.setLoginIp(ip);
          logEntity.setLoginStatus(Constants.STATUS_YES);
          logEntity.setSource(StringUtils.isBlank(source) ? null : source);
          logEntity.setToken(tokenId);
          logEntity.setBelong(belong);
          logEntity.setOrgId(orgId);
          logEntity.setOpenId(StringUtils.isBlank(openId) ? null : openId);
          this.userLoginLogMapper.insert(logEntity);
     }

     @HttpMonitorLogger
     @GetMapping("/setUserRoleToCache")
     @ApiOperation("设置用户角色到缓存")
     public void setUserRoleToCache(@RequestParam("user_id") Long userId,
                                    @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          this.userService.setUserRoleToCache(userId, header.getRegionId());
     }

     /**
      * 刷新用户信息缓存
      *
      * @param orgId
      * @param type  1 刷新缓存，2 清理缓存
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/flushOrgInfo")
     @ApiOperation("刷新组织信息缓存")
     public ResponseEntity<Result<?>> flushOrgInfo(@RequestParam("org_id") Long orgId, @RequestParam("type") Integer type) {
          this.userOrgCacheCallService.flushOrgInfo(orgId, type);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

}
