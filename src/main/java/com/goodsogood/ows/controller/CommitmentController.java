package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.CommitmentEntity;
import com.goodsogood.ows.model.vo.CommitmentForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.CommitmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/commitment")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class CommitmentController {
    private final Errors errors;

    private final CommitmentService commitmentService;

    @Autowired
    public CommitmentController(Errors errors, CommitmentService commitmentService) {
        this.errors = errors;
        this.commitmentService = commitmentService;
    }

    /**
     *
     */
    @HttpMonitorLogger
    @ApiOperation(value = "新增承诺践诺")
    @PostMapping("/append")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> addCommitment(@RequestHeader HttpHeaders headers, @Valid @RequestBody CommitmentEntity commitmentEntity, BindingResult bindingResult) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        var oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        String orgName = sysHeader.getOrgName();
        commitmentEntity.setOrgId(oid);
        commitmentEntity.setOrgName(orgName);
        commitmentEntity.setCreateUser(commitmentEntity.getUserId());
        commitmentEntity.setCreateTime(LocalDateTime.now());
        commitmentEntity.setUpdateUser(commitmentEntity.getUserId());
        commitmentEntity.setUpdateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(commitmentService.addCommitment(commitmentEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 修改承诺践诺
     */
    @HttpMonitorLogger
    @ApiOperation(value = "修改承诺践诺")
    @PostMapping("/compile")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> changeCommitment(@RequestHeader HttpHeaders headers, @Valid @RequestBody CommitmentEntity commitmentEntity, BindingResult bindingResult) {
        Result<Long> result = new Result<>(commitmentService.updateCommitment(commitmentEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 承诺践诺删除
     */
    @HttpMonitorLogger
    @ApiOperation(value = "承诺践诺删除")
    @GetMapping("/erasure")
    public ResponseEntity<Result<Boolean>> eliminateCommitment(@RequestHeader HttpHeaders headers, @RequestParam("ids") Long[] ids) {
        Result<Boolean> result = new Result<>(commitmentService.deleteCommitment(ids) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 承诺践诺详情查询
     */
    @HttpMonitorLogger
    @ApiOperation(value = "承诺践诺详情查询")
    @GetMapping("/obtain")
    public ResponseEntity<Result<CommitmentEntity>> getCommitment(@RequestHeader HttpHeaders headers, @RequestParam("id") Long id) {
        Result<CommitmentEntity> result = new Result<>(commitmentService.getCommitment(id), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);

    }

    /**
     * 查看承诺践诺列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "承诺践诺列表")
    @PostMapping("/list")
    public ResponseEntity<Result<List<CommitmentEntity>>> listCommitment(
            @RequestHeader HttpHeaders headers,
            @RequestBody CommitmentForm commitmentForm, @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        Long oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        commitmentForm.setUserId(userId);
        commitmentForm.setOrgId(oid);
        return new ResponseEntity<>(new Result<>(commitmentService.listCommitment(commitmentForm, page, pageSize), errors), HttpStatus.OK);
    }

}
