package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.TempGroupEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.TempGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 临时群组管理
 *
 * <AUTHOR>
 * @date 2018-03-23
 */
@RestController
@RequestMapping("/temp-group")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "临时群组管理")
@Validated
public class TempGroupController {
    private final TempGroupService tempGroupService;
    private final Errors errors;

    @Autowired
    public TempGroupController(TempGroupService tempGroupService, Errors errors) {
        this.tempGroupService = tempGroupService;
        this.errors = errors;
    }


    @ApiOperation("列表查询临时群组-分页")
    @HttpMonitorLogger
    @GetMapping("/query-page")
    public ResponseEntity<Result<Page<TempGroupEntity>>> queryListForPage(@RequestHeader HttpHeaders headers,
                                                                   String name,
                                                                   @RequestParam(value = "type",defaultValue = "1") Short type,
                                                                   @RequestParam(value="org_id",required = false) Long orgId,
                                                                   @RequestParam(required = false,defaultValue = "1") Integer page,
                                                                   @RequestParam(required = false,defaultValue = "10",name = "page_size") Integer pageSize) {

       Page<TempGroupEntity> tempGroupEntities =  tempGroupService.queryListForPage(headers, name, type, orgId, page, pageSize);
       return new ResponseEntity<Result<Page<TempGroupEntity>>>(new Result<>(tempGroupEntities, errors), HttpStatus.OK);
    }

    @ApiOperation("列表查询临时群组-不分页")
    @HttpMonitorLogger
    @GetMapping("/query-list")
    public ResponseEntity<Result<List<TempGroupEntity>>> queryList(@RequestHeader HttpHeaders headers,
                                                                   String name,
                                                                   @RequestParam("type") Short type,
                                                                   @RequestParam(value="org_id",required = false) Long orgId) {
        List<TempGroupEntity> tempGroupEntities =  tempGroupService.queryList(name, type, orgId,headers);
        return new ResponseEntity<Result<List<TempGroupEntity>>>(new Result<>(tempGroupEntities, errors), HttpStatus.OK);
    }


//    @ApiOperation("临时群组详情查询")
//    @HttpMonitorLogger
//    @GetMapping("/query-info")
//    public ResponseEntity<Result<TempRelationVo>> queryListInfo(@RequestHeader HttpHeaders headers,
//                                                                @RequestParam(value="temp_group_id") Long tempGroupId,
//                                                                @RequestParam(required = false,defaultValue = "1") Integer page,
//                                                                @RequestParam(required = false,defaultValue = "10",name = "page_size") Integer pageSize) {
//        TempRelationVo tempRalationVo =  tempGroupService.queryInfo(headers, tempGroupId,page,pageSize);
//        return new ResponseEntity<Result<TempRelationVo>>(new Result<>(tempRalationVo, errors), HttpStatus.OK);
//    }

    @ApiOperation("临时群组详情查询,经冯凯郎确认，该接口由前端做假分页")
    @HttpMonitorLogger
    @GetMapping("/query-info")
    public ResponseEntity<Result<TempRelationNewVo>> queryListInfo(@RequestHeader HttpHeaders headers,
                                                                @RequestParam(value="temp_group_id") Long tempGroupId) {
        TempRelationNewVo tempRalationVo =  tempGroupService.queryUserInfoList(headers, tempGroupId);
        return new ResponseEntity<Result<TempRelationNewVo>>(new Result<>(tempRalationVo, errors), HttpStatus.OK);
    }



    /**
     * 根据前端传入的用户ids获取用户信息-不分页
     * @param headers
     * @param userIds
     * @return
     */
    @ApiOperation("查询临时群组关联的用户-不分页")
    @HttpMonitorLogger
    @GetMapping("/query-user")
    public ResponseEntity<Result<List<TempRelationNewVo.RelateUserVo>>> queryUserInfo(@RequestHeader HttpHeaders headers,
                                                                                   @RequestParam(value="user_ids") List<Long> userIds) {
        List<TempRelationNewVo.RelateUserVo> tempRalationVos =  tempGroupService.queryTempUserList(userIds);
        return new ResponseEntity<Result<List<TempRelationNewVo.RelateUserVo>>>(new Result<>(tempRalationVos, errors), HttpStatus.OK);
    }



    /**
     * 根据前端传入的用户ids获取用户信息
     * @param headers
     * @param tempGroupId
     * @return
     */
    @ApiOperation("根据选择的临时群组id查询人员信息-分页-任务系统调用")
    @HttpMonitorLogger
    @GetMapping("/query/user")
    public ResponseEntity<Result<Page<TempRelationVo.RelateUserVo>>> queryUserInfoByGroup(@RequestHeader HttpHeaders headers,
                                                                                          @RequestParam(value="temp_group_id") Long tempGroupId,
                                                                                          @RequestParam(required = false,defaultValue = "1") Integer page,
                                                                                          @RequestParam(required = false,defaultValue = "10",name = "page_size") Integer pageSize) {
        Page<TempRelationVo.RelateUserVo> tempRalationVos =  tempGroupService.queryUserInfoByGroup(tempGroupId,page,pageSize);
        return new ResponseEntity<Result<Page<TempRelationVo.RelateUserVo>>>(new Result<>(tempRalationVos, errors), HttpStatus.OK);
    }


    /**
     * 添加/编辑临时群组，包含添加人员
     * @param headers
     * @param tempGroupForm
     * @return
     */
    @ApiOperation("添加、编辑临时群组")
    @HttpMonitorLogger
//    @RepeatedCheck
    @PostMapping("/add-group")
    public ResponseEntity<Result<?>> addGroup(
            @RequestHeader HttpHeaders headers,
            @Valid @RequestBody TempGroupForm tempGroupForm,
            BindingResult bindingResult){
        tempGroupService.addGroup(headers,tempGroupForm);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    /**
     * 删除临时群组-同时会删除关联人员
     * @param headers
     * @param tempGroupIds
     * @return
     */
    @ApiOperation("删除临时群组-同时会删除关联人员")
    @HttpMonitorLogger
    @RepeatedCheck
    @GetMapping("/del-group")
    public ResponseEntity<Result<Boolean>> delGroup(@RequestHeader HttpHeaders headers,
                                                    @RequestParam("temp_group_ids") List<Long> tempGroupIds) {
        return new ResponseEntity<Result<Boolean>>(new Result<>(tempGroupService.delGroup(headers, tempGroupIds), errors), HttpStatus.OK);
    }



}
