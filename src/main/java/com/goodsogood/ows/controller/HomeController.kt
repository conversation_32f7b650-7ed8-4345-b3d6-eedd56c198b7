package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.HomeService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

/**
 * 首页
 * <AUTHOR>
 * @createTime 2023年02月07日 09:45:00
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "首页控制层", tags = ["首页"])
@RequestMapping("/home")
@Validated
class HomeController @Autowired constructor(
    val errors: Errors,
    val homeService: HomeService
) {

    @HttpMonitorLogger
    @GetMapping("/index")
    @ApiOperation("首页数据")
    fun ami(@RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.homeService.index(header)
                , errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/map_org_detail")
    @ApiOperation("区县列表")
    fun mapOrgDetail(@RequestParam("adcode") adcode: String? = null,
                     @RequestParam("unit_id") unitId: Long? = null): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.homeService.mapOrgDetail(adcode, unitId)
                , errors
            ), HttpStatus.OK
        )
    }
}