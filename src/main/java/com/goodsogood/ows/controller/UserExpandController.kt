package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.service.UserExpandService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/user-expand")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "人员拓展信息", tags = ["人员拓展信息"])
@Validated
class UserExpandController(@Autowired val errors: Errors,
                           @Autowired val userExpandService: UserExpandService) {

    @PostMapping("/set-user-hand-sign")
    @ApiOperation("设置用户手写签名")
    @HttpMonitorLogger
    fun setUserHandSign(@Valid @RequestBody handSignVO: HandSignVO,
                        bindingResult: BindingResult,
                        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.userExpandService.setUserHandSign(handSignVO.userId, handSignVO.handSign, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-hand-sign")
    @ApiOperation("获取用户手写签名")
    @HttpMonitorLogger
    fun getUserHandSign(@RequestParam("user_id") userId: Long, @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val result = this.userExpandService.getUserHandSign(userId)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-info")
    @ApiOperation("用户信息")
    @HttpMonitorLogger
    fun getUserInfo(@RequestParam("user_id") userId: Long? = null, @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(this.userExpandService.getUserInfo(headers), errors), HttpStatus.OK)
    }

    @PostMapping("/set-user-info")
    @ApiOperation("设置用户字段信息")
    @HttpMonitorLogger
    fun setUserInfo(@RequestBody setUserData: SetUserData,
                    bindingResult: BindingResult,
                    @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(this.userExpandService.setUserData(setUserData, headers), errors), HttpStatus.OK)
    }

    @PostMapping("/set-photo")
    @ApiOperation("设置用户照片")
    @HttpMonitorLogger
    fun setPhone(@Valid @RequestBody photoVO: PhotoVO,
                 bindingResult: BindingResult,
                 @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.userExpandService.setPhoto(photoVO.userId, photoVO.photo, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @PostMapping("/add-tag")
    @ApiOperation("添加标签")
    @HttpMonitorLogger
    fun addTag(@Valid @RequestBody userTag: UserTag,
               bindingResult: BindingResult,
               @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.userExpandService.addTag(header.userId, userTag.name, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @PostMapping("/del-tag")
    @ApiOperation("删除标签")
    @HttpMonitorLogger
    fun delTag(@Valid @RequestBody userTag: UserTag,
               bindingResult: BindingResult,
               @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.userExpandService.delTag(header.userId, userTag.name, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-corp-info")
    @ApiOperation("查询人员单位信息")
    @HttpMonitorLogger
    fun getUserCorpInfo(@RequestParam("user_id") userId: Long,
                        @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val result = this.userExpandService.getUserCorpInfo(userId, header)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @PostMapping("/get-user-by-third-id")
    @ApiOperation("通过第三方ID查询人员")
    @HttpMonitorLogger
    fun getUserById(@Valid @RequestBody userThird: UserThird,
                    @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val result = this.userExpandService.getUserThird(userThird)
        return ResponseEntity(Result(result, errors), HttpStatus.OK)
    }

    @PostMapping("/get-user-by-ids")
    @ApiOperation("通过批量ID查询人员列表")
    @HttpMonitorLogger
    fun getUserInfoByIds(@Valid @RequestBody queryUserForm: QueryUserForm,
                         @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val userInfoList = this.userExpandService.getUserInfoList(queryUserForm, headers)
        return ResponseEntity(Result(userInfoList, errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-list-by-org")
    @ApiOperation("根据组织获取用户列表")
    @HttpMonitorLogger
    fun getUserListByOrg(@RequestParam("org_id") orgId: Long,
                         @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                userExpandService.getUserListByOrg(orgId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @GetMapping("/get-phone")
    @ApiOperation("查询用户手机号")
    @HttpMonitorLogger
    fun getPhone(@RequestParam("user_id") userId: Long,
                 @RequestParam("phone_secret") phoneSecret: String,
                 @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                userExpandService.getUserPhone(userId, phoneSecret, headers),
                errors),
            HttpStatus.OK
        )
    }

    @GetMapping("/get-user-unit-list")
    @ApiOperation("查询用户的行政单位列表")
    @HttpMonitorLogger
    fun getUserUnitList(@RequestParam("user_id") userId: Long,
                    @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                userExpandService.getUserUnitList(userId, headers),
                errors),
            HttpStatus.OK
        )
    }

    @PostMapping("/get-user-unit")
    @ApiOperation("查询用户的行政单位列表")
    @HttpMonitorLogger
    fun getUserUnit(@Valid @RequestBody form: QueryUserForm,
                        @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                userExpandService.getUserUnit(form, headers),
                errors),
            HttpStatus.OK
        )
    }
}