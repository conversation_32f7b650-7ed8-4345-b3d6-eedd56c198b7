package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.ListUtils;
import com.goodsogood.ows.common.LogType;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.RoleEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 角色controller
 *
 * <AUTHOR>
 * @date 2018-03-26
 */
@RestController
@RequestMapping("/uc/role")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "角色管理", tags = {"角色管理"})
@Validated
public class RoleController {

    private final Errors errors;
    private final RoleService roleService;

    @Autowired
    public RoleController(Errors errors, RoleService roleService) {
        this.errors = errors;
        this.roleService = roleService;
    }

    /**
     * 新增角色
     *
     * @param roleForm
     * @return
     * @description 默认权限只有一个，新增角色未默认权限，那么所有拥有默认权限的人员的权限会自动变成该新增权限；
     * 新增权限是默认权限，原默认权限自动变成自定义权限
     */
    @HttpMonitorLogger
    @PostMapping("/addRole")
    @ApiOperation("新增角色")
    public ResponseEntity<Result<?>> addRole(@Valid @RequestBody RoleForm roleForm,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long regionId = header.getRegionId();
        // 查询默认权限ID
        RoleEntity re = this.roleService.getRoleByType(Constants.ROLE_TYPE_DEFAULT, header.getType(), header.getOid(), Constants.MENU_BELONG_PC, regionId);
        // 新增权限时未指定为默认权限
        if (roleForm.getRoleType() != null && roleForm.getRoleType() != Constants.ROLE_TYPE_DEFAULT) {
            // 新增权限
            long roleId = insertRole(roleForm.getName(), header.getUserId(), Constants.ROLE_TYPE_DEFINED, header.getType(), header.getOid(), regionId);
            log.debug("新增角色成功 roleId -> {}", roleId);

            // 更改所有拥有默认权限的人员的权限为新增权限
            updateUserRole(header.getType(), header.getOid(), roleId, re.getRoleId());

            // 新增角色菜单关系信息
            this.roleService.batchInsertRoleMenu(roleForm.getMenuIds(), roleId, regionId);
        } else {
            // 更新原默认权限为自定义权限
            int flag = this.roleService.updateDefaultRole(Constants.ROLE_TYPE_DEFINED, Constants.ROLE_TYPE_DEFAULT);
            log.debug("更新原默认权限为自定义权限成功 flag -> {}", flag);

            // 新增权限
            long roleId = insertRole(roleForm.getName(), header.getUserId(), Constants.ROLE_TYPE_DEFAULT, header.getType(), header.getOid(), regionId);
            log.debug("新增角色成功 roleId -> {}", roleId);

            // 更改所有拥有默认权限的人员的权限为新增权限
            updateUserRole(header.getType(), header.getOid(), roleId, re.getRoleId());

            // 新增角色菜单关系信息
            this.roleService.batchInsertRoleMenu(roleForm.getMenuIds(), roleId, regionId);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 更改权限
     *
     * @param type
     * @param oId
     * @param newRoleId
     * @param oldRoleId
     */
    private void updateUserRole(int type, long oId, long newRoleId, long oldRoleId) {
        // 更改所有拥有默认权限的人员的权限为新增权限
        ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
        map.put("type", type);
        map.put("oId", oId);
        map.put("newRoleId", newRoleId);
        map.put("oldRoleId", oldRoleId);

        this.roleService.updateUserRoleByWhere(map);
        log.debug("更改默认权限为新增权限成功 -> newRoleId:{},oldRoleId:{}", newRoleId, oldRoleId);
    }

    /**
     * 新增权限
     *
     * @param name
     * @param uId
     * @return
     */
    private Long insertRole(String name, long uId, int roleType, int type, long oId, long regionId) {

        RoleEntity roleEntity = new RoleEntity();
        roleEntity.setName(name);
        roleEntity.setRoleType(roleType);
        roleEntity.setOrgType(type);
        if (Constants.DEP_TYPE_CORP == type) {
            roleEntity.setCorporationId(oId);
        } else if (Constants.DEP_TYPE_ORG == type) {
            roleEntity.setOrganizationId(oId);
        }
        roleEntity.setRegionId(regionId);
        roleEntity.setStatus(Constants.STATUS_YES);
        roleEntity.setLastChangeUser(uId);

        return this.roleService.insertRole(roleEntity);
    }

    /**
     * 删除角色
     *
     * @param id
     * @return
     */
    @HttpMonitorLogger
    @DeleteMapping("/delRole/{id}")
    @ApiOperation("删除角色")
    public ResponseEntity<Result<?>> deleteRole(@PathVariable @Range(min = 1, max = 99999999999L, message = "{Range.role.id}") Long id,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 默认权限不能删除
        int roleType = this.roleService.getRoleTypeByRoleId(id);
        if (roleType != Constants.ROLE_TYPE_DEFINED) {
            log.error("默认权限不能删除 roleID -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 129, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (!this.roleService.deleteRole(id, header.getType(), header.getOid(), header.getRegionId())) {
            log.error("删除角色失败 roleID -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 112, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("删除角色成功 roleID -> {}", id);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 修改角色
     *
     * @param roleForm
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/updateRole")
    @ApiOperation("修改角色")
    public ResponseEntity<Result<?>> updateRole(@Valid @RequestBody RoleForm roleForm,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int i = this.roleService.updateRole(roleForm, header.getUserId(), header.getType(), header.getOid(), header.getRegionId());
        if (i == 0) {
            log.error("修改角色失败 roleID -> {}", roleForm.getRoleId());
            return new ResponseEntity<>(new Result<>(errors, 113, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (i == 2) {
            log.error("默认角色不能修改为自定义角色 roleID -> {}", roleForm.getRoleId());
            return new ResponseEntity<>(new Result<>(errors, 114, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("修改角色成功 roleID -> {}", roleForm.getRoleId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据名称查询角色
     *
     * @param page
     * @param name
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("根据名称查询角色")
    public ResponseEntity<Result<Page<List<RoleForm>>>> getRoleList(@RequestParam(required = false) Integer page,
                                                                    @RequestParam(required = false)
                                                                    @Length(max = 10, message = "{Length.role.name}") String name,
                                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null) {
            page = 1;
        }
        Page<List<RoleForm>> listPage = this.roleService.getRoleByName(new PageNumber(page), name, Constants.STATUS_YES, header.getType(), header.getOid(), header.getRegionId());
        log.debug("根据名称查询角色成功  -> {},{}", name, listPage.getTotal());
        return new ResponseEntity<>(new Result<>(listPage, errors), HttpStatus.OK);
    }

    /**
     * 根据roleId查询menuList
     *
     * @param roleId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/menu-list")
    @ApiOperation("根据角色查询菜单列表")
    public ResponseEntity<Result<?>> menuList(@RequestParam(required = false)
                                              @Range(min = 1, max = 99999999999L, message = "{Range.role.id}")
                                                      // @NotNull(message = "{NotNull.role.id}")
                                                      Long roleId,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        RoleResultForm resultForm = this.roleService.menuList(roleId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询权限列表
     *
     * @param orgId
     * @param belong
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-role-by-oid")
    @ApiOperation("根据组织ID查询权限列表")
    public ResponseEntity<Result<?>> findRoleByOid(@RequestParam(value = "org_id", required = false, defaultValue = "1") Long orgId,
                                                   @RequestParam(value = "belong", required = false) Integer belong) {
        List<RoleListResultForm> result = this.roleService.findRoleByOid(orgId, belong);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 新增组织角色
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/add-role")
    @ApiOperation("新增组织角色")
    @DoLog(type = LogType.INSERT,content = "新增组织角色")
    public ResponseEntity<Result<?>> addRole(@Valid @RequestBody RoleAddForm form,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.roleService.addRole(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 修改组织角色
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update-role")
    @ApiOperation("修改组织权限")
    @DoLog(type = LogType.UPDATE,content = "修改组织权限")
    public ResponseEntity<Result<?>> updateRole(@Valid @RequestBody RoleAddForm form,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.roleService.updateRole(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 删除组织权限
     *
     * @param roleId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/remove-role")
    @ApiOperation("删除组织权限")
    public ResponseEntity<Result<?>> removeRole(@RequestParam(value = "role_id") Long roleId,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int i = this.roleService.removeRole(roleId, header);
        if (i == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, i, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }


    @HttpMonitorLogger
    @PostMapping("/user-update-role")
    @ApiOperation("新增、更新、删除用户权限")
    public ResponseEntity<Result<?>> userUpdateList(@Valid @RequestBody UserUpdateRoleForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("操作用户权限请求 -> [{}]", form);
        this.roleService.userUpdateRole(form, header.getRegionId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-role-by-uid")
    @ApiOperation("根据用户ID查询权限列表")
    public ResponseEntity<Result<?>> findRoleByUid(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                   @RequestParam("org_id") @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                   @RequestHeader HttpHeaders headers) {
        List<RoleUserForm> roleList = this.roleService.getRoleByUserId(orgId, userId);
        return new ResponseEntity<>(new Result<>(roleList, errors), HttpStatus.OK);
    }

    /**
     * 根据roleId查询menuList
     *
     * @param roleId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-menu-by-role")
    @ApiOperation("根据权限ID查询菜单列表")
    @DoLog(type = LogType.QUERY,content = "根据权限ID查询菜单列表")
    public ResponseEntity<Result<?>> findMenuByRole(@RequestParam("role_id") @NotNull(message = "{NotNull.role.id}") Long roleId,
                                                    @RequestParam(value = "region_id", required = false) Long regionId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        RoleAddForm form = this.roleService.findRoleMenuByRole(roleId, regionId);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/push-public-role")
    @ApiOperation("推送公共角色")
    public ResponseEntity<Result<?>> pushPublicRole(@RequestParam("role_id") @NotNull(message = "{NotNull.role.id}") Long roleId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.roleService.pushPublicRole(roleId, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询当前组织的角色列表
     *
     * @param param
     * @param status
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-role-by-where")
    @ApiOperation("根据条件查询当前组织角色列表")
    @DoLog(type = LogType.QUERY,content = "查询当前组织角色列表")
    public ResponseEntity<Result<?>> findRoleByWhere(@RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                     @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                     @RequestParam(value = "param", required = false) String param,
                                                     @RequestParam(value = "status", required = false)
                                                     @Range(max = 2, min = 1, message = "{Range.role.status}") Integer status,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<List<RoleAddForm>> resultList = this.roleService.findRoleByWhere(new PageNumber(page,pageSize), param, status, header.getOid(), header.getRegionId());
        log.debug("根据条件查询当前组织角色列表成功，result:{}", resultList);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 批量角色管理
     *
     * @param form          参数
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/deal-role")
    @ApiOperation("批量操作角色")
    @DoLog(type = LogType.DELETE,content = "批量移除/删除角色")
    public ResponseEntity<Result<?>> dealRole(@Valid @RequestBody RoleDealForm form,
                                              BindingResult bindingResult,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (ListUtils.isEmpty(form.getRoleList())) {
            return new ResponseEntity<>(new Result<>(errors, 4000, HttpStatus.OK.value()), HttpStatus.OK);
        }
        this.roleService.dealRole(form, header);
        log.debug("批量操作角色列表成功，状态(1-启用 2-停用 3-移除):{}", form.getOperation());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * create by fuxiao
     */
    @HttpMonitorLogger
    @GetMapping("/reset-permission")
    @ApiOperation("重置权限")
    public ResponseEntity<Result<?>> resetPermission(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.roleService.resetPermission(header.getRegionId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 用户权限列表查询
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-role-by-where")
    @ApiOperation("用户权限管理列表查询")
    @DoLog(type = LogType.QUERY,content = "查询用户权限管理列表")
    public ResponseEntity<Result<?>> findUserRoleByWhere(@Valid @RequestBody RoleQueryForm form,
                                                         BindingResult bindingResult,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String param = form.getParam();
        Integer type = 0;
        // 处理查询条件
        if (!StringUtils.isBlank(param)) {
            if (param.length() == 11 && Utils.checkPhone(param)) {
                // 根据电话精确匹配
                param = NumEncryptUtils.encrypt(param, 2);
                form.setParam(param);
                type = 1;
            } else {
                // 根据用户姓名模糊匹配
                type = 2;
            }
        }
        // 如果选择组织为空，则默认查询当前登录用户所在组织
        if (ListUtils.isEmpty(form.getOrgIdList())) {
            List<Long> orgIdList = new ArrayList<>();
            orgIdList.add(header.getOid());
            form.setOrgIdList(orgIdList);
        }
        Page<List<RoleQueryResultForm>> resultList = this.roleService.findUserRoleByWhere(new PageNumber(form.getPage(), form.getPageSize()), form, type, header.getRegionId());
        log.debug("查询用户权限列表成功，param：{}", form);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 查询用户权限
     *
     * @param userId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-power-by-user")
    @ApiOperation("查询用户权限")
    @DoLog(type = LogType.QUERY,content = "查询用户权限")
    public ResponseEntity<Result<?>> findPowerByUser(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                     @RequestParam(value = "name", required = false) String orgName,
                                                     @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserOrgPowerForm> powerList = this.roleService.findPowerBuUser(userId, orgName, page, header.getOid(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(powerList, errors), HttpStatus.OK);
    }

    /**
     * 查询用户在当前组织下的角色
     *
     * @param userId
     * @param orgId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/select-user-role-by-org")
    @ApiOperation("查询用户在当前组织下的角色")
    @DoLog(type = LogType.QUERY,content = "查询用户在当前组织下的角色")
    public ResponseEntity<Result<?>> selectUserRoleByOrg(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                         @RequestParam("org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserRoleListForm> roleList = this.roleService.selectUserRoleByOrg(userId, orgId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(roleList, errors), HttpStatus.OK);
    }

    /**
     * 查询用户在当前组织下的用户组
     *
     * @param userId
     * @param orgId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/select-user-group-by-org")
    @ApiOperation("查询用户在当前组织下的用户组")
    @DoLog(type = LogType.QUERY,content = "查询用户在当前组织下的用户组")
    public ResponseEntity<Result<?>> selectUserGroupByOrg(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                          @RequestParam("org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                          @RequestHeader HttpHeaders headers) {
        List<UserGroupListForm> groupList = this.roleService.selectUserGroupByOrg(userId, orgId);
        return new ResponseEntity<>(new Result<>(groupList, errors), HttpStatus.OK);
    }

    /**
     * 更新用户权限
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update-user-power")
    @ApiOperation("更新用户权限")
    @DoLog(type = LogType.UPDATE,content = "修改用户权限")
    public ResponseEntity<Result<?>> updateUserPower(@Valid @RequestBody UpdateUserPowerForm form,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.roleService.updateUserPower(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 新增用户与组织关系
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/add-user-org")
    @ApiOperation("新增用户与组织关系")
    public ResponseEntity<Result<?>> addUserOrg(@Valid @RequestBody AddUserOrgForm form,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.roleService.addUserOrg(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @GetMapping("/get-volunteer-menu")
    @ApiOperation("获取用户志愿者二级菜单")
    public ResponseEntity<Result<?>> getVolunteerMenu(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<MenuForm> manageMenu = this.roleService.getVolunteerManageMenu(header.getUserId(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(manageMenu, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID和角色id查询用户列表
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-by-org-with-role")
    @ApiOperation("根据组织ID和角色id查询用户列表")
    public ResponseEntity<Result<?>> findUserByOrgWithRole(@Valid @RequestBody UserRoleWithRoleForm form,
                                                           BindingResult bindingResult,
                                                           @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.roleService.findUserByOrgWithRole(form), errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID和角色id查询用户列表
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-by-org-and-role")
    @ApiOperation("根据组织ID和角色id查询用户列表")
    public ResponseEntity<Result<?>> findUserByOrgAndRole(@Valid @RequestBody UserRoleWithRoleForm form,
                                                          BindingResult bindingResult,
                                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (form.getType() == 0 && CollectionUtils.isEmpty(form.getRoleIds())) {
            return new ResponseEntity<>(new Result<>(errors, 35003, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(this.roleService.findUserByOrgAndRole(form, header.getRegionId()),
                errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/update-user-eval-role")
    @ApiOperation("设置考核员和考核组长权限")
    public ResponseEntity<Result<?>> updateUserEvalRole(@Valid @RequestBody List<EvalRoleForm> list,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.roleService.updateUserEvalRole(list, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

}