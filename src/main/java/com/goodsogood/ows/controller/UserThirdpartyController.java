package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.db.UserThirdEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.OptionService;
import com.goodsogood.ows.service.UserService;
import com.goodsogood.ows.service.UserThirdService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 第三方平台控制类
 *
 * <AUTHOR>
 * @create 2018-05-24 9:38
 **/
@RestController
@RequestMapping("/third")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "第三方平台管理", tags = {"第三方平台"})
@Validated
public class UserThirdpartyController {

     private final Errors errors;
     private final UserService userService;
     private final UserThirdService userThirdService;
     private final OptionService optionService;

     @Autowired
     public UserThirdpartyController(Errors errors, UserService userService, UserThirdService userThirdService, OptionService optionService) {
          this.errors = errors;
          this.userService = userService;
          this.userThirdService = userThirdService;
          this.optionService = optionService;
     }

     /**
      * 微信端登录校验token
      *
      * @param request
      * @param headers
      * @param thToken
      * @return
      * @Edit 2018-12-06 TangLilin
      */
     @HttpMonitorLogger
     @GetMapping(value = "/check")
     @ApiOperation("检查th_token")
     public ResponseEntity<Result<?>> checkOpenId(HttpServletRequest request,
                                                  @RequestHeader HttpHeaders headers,
                                                  @RequestParam(value = "th_token") String thToken,
                                                  @RequestParam(value = "sel_org_id", required = false) Long selOrgId) {
          StopWatch stopWatch = new StopWatch();
          stopWatch.start();
          HeaderHelper.SysHeader header;
          GuestResultForm resultForm = new GuestResultForm();
          resultForm.setToken("-1");
          try {
               header = HeaderHelper.buildMyHeader(headers);
          } catch (Exception e) {
               throw new ApiException("第三方登录头信息错误", e, new Result<>(errors, Global.Errors.ERROR_HEADER, HttpStatus.OK.value()));
          }
          // 校验第三方token在库里面是否存在
          UserThirdEntity thirdEntity;
          try {
               thirdEntity = this.userThirdService.findByToken(thToken, header.getOid());
          } catch (Exception e) {
               throw new ApiException("第三方登录检查token出错", e, new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.OK.value()));
          }
          // 数据库不存在，则直接返回一个token给前端
          if (thirdEntity == null) {
               throw new ApiException("用户不存在", new Result<>(resultForm, errors, 199, HttpStatus.OK.value()));
          }
          // 通过token查询用户基本信息，返回用户的密码
          UserEntity userEntity = userService.selectOne(thirdEntity.getUserId());
          // 校验用户是否存在
          if (userEntity == null) {
               throw new ApiException("用户不存在", new Result<>(resultForm, errors, 199, HttpStatus.OK.value()));
          }
          // 校验 idcard、phone、name、nickname、headurl
          if (StringUtils.isEmpty(userEntity.getPhone())
                  || StringUtils.isEmpty(userEntity.getCertNumber())
                  || StringUtils.isEmpty(userEntity.getName())) {
               throw new ApiException("用户信息不全", new Result<>(resultForm, errors, 200, HttpStatus.OK.value()));
          }

          GuestForm guestForm = new GuestForm();
          guestForm.setUserEntity(userEntity);
          log.debug("第三方公众号进入平台，check -> guestLogin前耗时：{}", stopWatch.toString());
          stopWatch.stop();
          // 检查通过以后，走手机登录
          return userService.guestLogin(request, guestForm, header, Constants.OPER_PHONE, selOrgId);
     }

     /**
      * 修改昵称
      *
      * @param headers
      * @param thType
      * @param nickname
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/updNickname")
     @ApiOperation("修改昵称")
     public ResponseEntity<Result<?>> updNickname(@RequestHeader HttpHeaders headers,
                                                  @RequestParam @NotNull(message = "{NotBlank.third.thType}") Integer thType,
                                                  @RequestParam @NotBlank(message = "{NotBlank.third.nickname}")
                                                  @javax.validation.constraints.Pattern(regexp = "(^[a-zA-Z0-9\u4e00-\u9fa5]+$)", message = "{Format.third.nickname}")
                                                          String nickname) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          Long userId = header.getUserId();
          log.debug("参数：userId = {} thType = {} nickname = {}", userId, thType, nickname);
          //验证昵称字符长度,4-16个，字母、数字算1个，汉字算2个
          int re = checkNicknameLength(nickname);
          if (re < 4 || re > 16) {
               return new ResponseEntity<>(new Result<>(errors, 196, HttpStatus.OK.value()), HttpStatus.OK);
          }
          try {
               if (!userThirdService.updNickname(userId, thType, nickname, header.getOid(), header.getRegionId())) {
                    log.debug("系统异常，修改组织人员昵称失败！userId = {} thType = {} nickname = {}", userId, thType, nickname);
                    return new ResponseEntity<>(new Result<>(errors, 194, HttpStatus.OK.value()), HttpStatus.OK);
               }
          } catch (Exception e) {
               log.error("修改组织人员昵称失败!", e);
               throw new ApiException("修改组织人员昵称失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "修改组织人员昵称失败"));
          }
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 根据公众号所在组织，获取用户头像等信息
      *
      * @param headers
      * @param thType
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/getInfo")
     @ApiOperation("获取第三方登录用户资料")
     public ResponseEntity<Result<?>> getInfo(@RequestHeader HttpHeaders headers,
                                              @RequestParam @NotNull(message = "{NotBlank.third.thType}") Integer thType) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          Long userId = header.getUserId();
          log.debug("参数 userId:{},oid:{},thType:{}", header.getUserId(), header.getOid(), thType);
          try {
               ThirdUserReForm turf = userThirdService.getInfo(userId, thType, optionService, header.getOid(), header.getRegionId());
               return new ResponseEntity<>(new Result<>(turf, errors), HttpStatus.OK);
          } catch (Exception e) {
               log.error("获取第三方登录用户资料失败!", e);
               throw new ApiException("获取第三方登录用户资料失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取第三方登录用户资料失败"));
          }
     }


     /**
      * @Author: tc
      * @Description 验证昵称字符长度, 4-16个，字母算1个，汉字算2个
      * @Date 18:44 2018/7/19
      */
     private int checkNicknameLength(String nickname) {
          String getChinese = "([\u4e00-\u9fa5]+)";
          String chinese = "";
          Matcher matcher1 = Pattern.compile(getChinese).matcher(nickname);
          while (matcher1.find()) {
               chinese += matcher1.group(0);
          }
          String getEnglish = "([A-Za-z]+)";
          String english = "";
          Matcher matcher2 = Pattern.compile(getEnglish).matcher(nickname);
          while (matcher2.find()) {
               english += matcher2.group(0);
          }

          String getNumber = "([0-9]+)";
          String number = "";

          Matcher matcher3 = Pattern.compile(getNumber).matcher(nickname);
          while (matcher3.find()) {
               number += matcher3.group(0);
          }
          return chinese.length() * 2 + english.length() * 1 + number.length() * 1;
     }

     /**
      * 更新用户头像
      *
      * @param form
      * @param bindingResult
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/update-head")
     @ApiOperation("更新用户头像")
     public ResponseEntity<Result<?>> updateHead(@Valid @RequestBody UpdateHeadForm form,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          boolean flag = this.userThirdService.updateHead(form, header.getRegionId());
          if (!flag) {
               log.debug("更新用户头像失败 userId:{}", form.getUserId());
               throw new ApiException("更新用户头像失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "更新用户头像失败"));
          }
          log.debug("更新用户头像成功 userId:{}", form.getUserId());
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 根据公众号所在组织ID和人员列表查询人员对应openId列表
      *
      * @param form
      * @param bindingResult
      * @return
      * <AUTHOR> 2018-11-08
      */
     @HttpMonitorLogger
     @PostMapping("/find-open-id-by-list")
     @ApiOperation("根据公众号所在组织ID和人员列表查询人员对应openId列表")
     public ResponseEntity<Result<?>> findOpenIdByList(@Valid @RequestBody UserOpenIdRequestForm form,
                                                       BindingResult bindingResult) {
          List<UserOpenIdResultForm> resultList = this.userThirdService.findOpenIdByList(form.getOrgId(), form.getUserList());
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 根据用户ID查询third
      *
      * @param userId
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/find-third-by-id")
     @ApiOperation("根据用户ID查询openID")
     public ResponseEntity<Result<?>> findThirdByUserId(@RequestParam(name = "user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                        @RequestParam(name = "org_id", required = false) Long orgId) {
          List<UserThirdEntity> resultList = this.userThirdService.findThirdByUserId(userId, orgId);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 根据用户ID和组织ID删除third
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/remove-third-by-id")
     @ApiOperation("根据用户ID和组织ID删除third")
     public ResponseEntity<Result<?>> removeThirdByUserId(@Valid @RequestBody UserOrgForm form) {
          this.userThirdService.removeThirdByUserId(form.getUserId(), form.getOrgId());
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 根据openId和regionId查询userId
      *
      * @param regionId 区县ID
      * @param openId   openId
      * @return userId
      */
     @HttpMonitorLogger
     @GetMapping("/find-user-by-open-id")
     @ApiOperation("根据openId和regionId查询userId")
     public ResponseEntity<Result<?>> findUserByOpenId(@RequestParam(value = "region_id")
                                                       @NotNull(message = "{NotBlank.user.regionId}") Long regionId,
                                                       @RequestParam(value = "open_id")
                                                       @NotBlank(message = "{NotBlank.openId}") String openId) {
          return new ResponseEntity<>(new Result<>(this.userThirdService.findUserByOpenId(regionId, openId), errors),
                  HttpStatus.OK);
     }

}
