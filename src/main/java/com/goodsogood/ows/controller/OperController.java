package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.MenuUrlEntity;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MenuUrlService;
import com.google.common.base.Preconditions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @date 2018/11/20
 * @description class 操作/按钮/接口管理
 */
@RestController
@RequestMapping("/uc/uri")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = " 操作/按钮/接口管理", tags = {"operating"})
@Validated
public class OperController {
    private final Errors errors;
    private final MenuUrlService menuUrlService;

    @Autowired
    public OperController(Errors errors,
                          MenuUrlService menuUrlService) {
        this.errors = errors;
        this.menuUrlService = menuUrlService;
    }

    /**
     * 通过网关或者其他接口添加uri
     *
     * @param method 请求方法
     * @param uri    请求的uri（如果是/{xxx}模式，使用/*替换）
     * @return null
     */
    @HttpMonitorLogger
    @ApiOperation("通过网关添加uri")
    @GetMapping("/add-auto")
    public ResponseEntity<Result<Void>> addUriByGateWay(
            @RequestParam(required = false, name = "menu_id") String menuId,
            @RequestParam String method,
            @RequestParam String uri
    ) {
        try {
            log.debug("menuId->{}", menuId);
            log.debug("method->{}", method);
            log.debug("uri->{}", URLDecoder.decode(uri, "UTF-8"));
            MenuUrlEntity menuUrlEntity = new MenuUrlEntity();
            menuUrlEntity.setMethod(method.toUpperCase());
            menuUrlEntity.setLinkUrl(URLDecoder.decode(uri, "UTF-8"));
            menuUrlEntity.setCreateTime(DateTime.now().toDate());
            menuUrlEntity.setLastChangeUser(-1L);
            //默认为禁用
            menuUrlEntity.setStatus(2);
            this.menuUrlService.addMenuUrl(menuId, menuUrlEntity);
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }
        return ResponseEntity.ok(new Result<>(null, errors));
    }


    /**
     * 获取所有的url
     *
     * @return page
     */
    @HttpMonitorLogger
    @ApiModelProperty("获取所有的url")
    @PostMapping("/list")
    public ResponseEntity<Result<Page<MenuUrlEntity>>> getAllUrl(@RequestHeader HttpHeaders headers,
                                                                 @Valid @RequestBody MenuUrlEntity menuUrlEntity,
                                                                 @RequestParam(required = false)
                                                                         Integer page,
                                                                 @RequestParam(required = false, name = "pagesize")
                                                                         Integer pageSize,
                                                                 BindingResult bindingResult) {
        if (menuUrlEntity.getStatus() != null && menuUrlEntity.getStatus() == 0) {
            menuUrlEntity.setStatus(null);
        }
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (menuUrlEntity.getMenuId() == null) { // 所有的uri
            return ResponseEntity.ok(new Result<>(this.menuUrlService.findAllMenuUrl(menuUrlEntity, new PageNumber(page, pageSize)), errors));
        } else {
            if (menuUrlEntity.getLinkUrl() != null) {
                menuUrlEntity.setLinkUrl("%" + menuUrlEntity.getLinkUrl() + "%");
            }
            return ResponseEntity.ok(new Result<>(this.menuUrlService.findAllMenuUrlByMenuId(menuUrlEntity, new PageNumber(page, pageSize)), errors));
        }
    }

    /**
     * 通过id，删除uri
     *
     * @param headers   headers
     * @param menuUrlId menu_url_id
     * @return ResponseEntity
     */
    @HttpMonitorLogger
    @ApiModelProperty("通过id，删除uri")
    @DeleteMapping("/remove")
    public ResponseEntity<Result<?>> removeUrl(@RequestHeader HttpHeaders headers,
                                               @RequestParam(name = "menu_url_id")
                                                       // TODO 请求验证
                                                       Long menuUrlId) {
        try {
            Preconditions.checkNotNull(this.menuUrlService.findOne(menuUrlId));
        } catch (NullPointerException npe) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "url"));
        }
        this.menuUrlService.removeMenuUrlById(menuUrlId);
        return ResponseEntity.ok(new Result<>(null, errors));
    }

    /**
     * 通过id，设置url启用与否
     *
     * @param headers   headers
     * @param menuUrlId menu_url_id
     * @param status    1 启用 2 禁用
     * @return ResponseEntity
     */
    @HttpMonitorLogger
    @ApiModelProperty("通过id，设置url启用与否")
    @GetMapping("/switch")
    public ResponseEntity<Result<?>> switchUrl(@RequestHeader HttpHeaders headers,
                                               @RequestParam(name = "menu_url_id")
                                                       // TODO 请求验证
                                                       Long menuUrlId,
                                               @RequestParam
                                                       // TODO 请求验证
                                                       Integer status
    ) {
        try {
            Preconditions.checkNotNull(this.menuUrlService.findOne(menuUrlId));
        } catch (NullPointerException npe) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "url"));
        }
        MenuUrlEntity menuUrlEntity = new MenuUrlEntity();
        menuUrlEntity.setStatus(status);
        menuUrlEntity.setMenuUrlId(menuUrlId);
        this.menuUrlService.updateByPrimaryKeySelective(menuUrlEntity);
        return ResponseEntity.ok(new Result<>(null, errors));
    }
}
