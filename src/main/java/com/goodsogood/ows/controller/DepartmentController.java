package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.DepartmentEntity;
import com.goodsogood.ows.model.vo.DepChildrenForm;
import com.goodsogood.ows.model.vo.DepartmentForm;
import com.goodsogood.ows.model.vo.MoveStaffForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.DepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 部门Controller
 *
 * <AUTHOR>
 * @date 2018-03-24
 */
@RestController
@RequestMapping("/uc/dep")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "部门管理", tags = {"department"})
@Validated
public class DepartmentController {

     @Value("${tog-services.activity-plat}")
     private String activityServer;

     private final Errors errors;
     private final DepartmentService departmentService;
     private final RestTemplate restTemplate;

     @Autowired
     public DepartmentController(Errors errors, DepartmentService departmentService, RestTemplate restTemplate) {
          this.errors = errors;
          this.departmentService = departmentService;
          this.restTemplate = restTemplate;
     }

     /**
      * 查询部门-部门管理首页
      *
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/get-dep")
     @ApiOperation("查询部门")
     public ResponseEntity<Result<List<DepChildrenForm>>> getDep(@RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          List<DepChildrenForm> depPage = this.departmentService.getDepByPage(header.getType(), header.getOid());
          log.debug("查询成功 -> total:{}", depPage.size());
          return new ResponseEntity<>(new Result<>(depPage, errors), HttpStatus.OK);
     }

     /**
      * 查询部门下拉列表
      *
      * @param headers
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/list")
     @ApiOperation("查询部门下拉列表")
     public ResponseEntity<Result<List<DepartmentForm>>> getList(@RequestHeader HttpHeaders headers) {

          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          return new ResponseEntity<>(new Result<>(this.departmentService.getList(header.getType(), header.getOid()), errors), HttpStatus.OK);
     }

     /**
      * 修改部门
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/update")
     @ApiOperation("修改部门")
     public ResponseEntity<Result<String>> updateDepInfo(@Valid @RequestBody DepartmentForm form,
                                                         BindingResult bindingResult,
                                                         @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          int flag = this.departmentService.updateDepByWhere(form, header.getUserId(), header.getType(), header.getOid());
          // 系统异常
          if (flag == 4) {
               return new ResponseEntity<>(new Result<>(errors, 146, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (flag == 3) { // 不能选当前部门为上级部门
               return new ResponseEntity<>(new Result<>(errors, 147, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (flag == 2) {
               return new ResponseEntity<>(new Result<>(errors, 148, HttpStatus.OK.value()), HttpStatus.OK);
          }
          log.debug("修改完成 -> name:{}", form.getName());
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 删除部门
      *
      * @param id
      * @return
      */
     @HttpMonitorLogger
     @DeleteMapping("/del/{id}")
     @ApiOperation("删除部门")
     public ResponseEntity<Result<?>> deleteDep(@PathVariable
                                                @NotNull(message = "{NotNull.dep.id}")
                                                @Range(min = 1, max = 99999999999L, message = "{Range.dep.id}")
                                                        long id,
                                                @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          //职工数量不为0的部门不能删除
          DepartmentEntity entity = this.departmentService.getEntityByWhere(id);
          if (entity == null) {
               log.debug("该部门不存在 departmentId:{}", id);
               return new ResponseEntity<>(new Result<>(errors, 403, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (entity.getStaffNumber() > 0) {
               log.debug("部门员工数量大于0不能删除 departmentId:{}", id);
               return new ResponseEntity<>(new Result<>(errors, 401, HttpStatus.OK.value()), HttpStatus.OK);
          } else {
               entity = new DepartmentEntity();
               entity.setParentId(id);
               List<DepartmentEntity> list = this.departmentService.getListByWhere(entity);
               // 存在下级部门的不能删除
               if (list != null && !list.isEmpty()) {
                    String isDel = "0";
                    for (DepartmentEntity de : list) {
                         if (de.getStatus() == Constants.STATUS_YES) {
                              isDel = "1";
                              break;
                         }
                    }
                    if ("1".equals(isDel)) {
                         log.debug("该部门存在下级部门，不能删除 departmentId:{}", id);
                         return new ResponseEntity<>(new Result<>(errors, 402, HttpStatus.OK.value()), HttpStatus.OK);
                    }
               }
          }
          // 如果部门下有活动不能删除
          headers = HeaderHelper.setMyHttpHeader(headers, header);
          HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
          try {
               ResponseEntity<Result> result = this.restTemplate.exchange(
                       String.format("http://%s/activity/count-deptId/%d",
                               activityServer,
                               id),
                       HttpMethod.GET, httpEntity, Result.class);
               int cnt = (int) result.getBody().getData();
               if (cnt > 0) {
                    log.debug("该部门下有活动，不允许删除 departmentId:{}", id);
                    return new ResponseEntity<>(new Result<>(errors, 404, HttpStatus.OK.value()), HttpStatus.OK);
               }
               log.debug("调用查询部门下活动接口成功 departmentId:{}", id);
               this.departmentService.deleteDepByWhere(id);
               log.debug("删除部门完成 -> departmentId:{}", id);
          } catch (ApiException e) {
               log.debug("result -> {}", e.getResult());
          } catch (IllegalStateException ise) {
               log.error("未知错误", ise.getMessage(), ise);
          }
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 批量移动员工
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/move")
     @ApiOperation("批量移动员工")
     public ResponseEntity<Result<String>> moveStaff(@Valid @RequestBody MoveStaffForm form,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          int i = this.departmentService.moveStaff(form.getId(), form.getNid(), header.getType(), header.getOid());
          if (i == 2) {
               log.debug("批量移动员工失败");
               return new ResponseEntity<>(new Result<>(errors, 132, HttpStatus.OK.value()), HttpStatus.OK);
          }
          log.debug("批量移动员工完成");
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 新增部门
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/add")
     @ApiOperation("新增部门")
     public ResponseEntity<Result<String>> addDep(@Valid @RequestBody DepartmentForm form,
                                                  BindingResult bindingResult,
                                                  @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          DepartmentEntity entity = new DepartmentEntity();
          entity.setName(form.getName());
          entity.setParentId(form.getParentId());
          // 单位
          if (header.getType() == Constants.DEP_TYPE_CORP) {
               entity.setCorporationId(header.getOid());
               entity.setDepType(Constants.DEP_TYPE_CORP);
          } else if (header.getType() == Constants.DEP_TYPE_ORG) { // 组织
               entity.setOrganizationId(header.getOid());
               entity.setDepType(Constants.DEP_TYPE_ORG);
          }
          entity.setStatus(Constants.STATUS_YES);
          // 新增部门时员工数量默认0
          entity.setStaffNumber(0);
          entity.setLastChangeUser(header.getUserId());
          entity.setCreateTime(new Date());
          entity.setUpdateTime(new Date());

          int i = this.departmentService.insertDep(entity);
          log.debug("新增部门完成 i -> {}", i);

          return new ResponseEntity<>(new Result<>(i > 0 ? "success" : "failure", errors), HttpStatus.OK);
     }

     /**
      * 查询所有部门
      *
      * @param headers
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/all")
     @ApiOperation("查询所有部门")
     public ResponseEntity<Result<?>> getAllDep(@RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          List<DepartmentForm> resultList = this.departmentService.getAllDep(header.getType(), header.getOid(),
                  Constants.STATUS_YES);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

}
