package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.CloudOrgForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CloudOrgService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * CloudOrgController
 *
 * <AUTHOR>
 * @Date 2021-12-13 16:50
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "云区组织", tags = ["云区组织"])
@RequestMapping("/cloud")
class CloudOrgController @Autowired constructor(
    val errors: Errors,
    val cloudOrgService: CloudOrgService
) {

    /**
     * 新增云区党支部
     */
    @HttpMonitorLogger
    @PostMapping("/add-org")
    @ApiOperation("新增云区组织")
    fun addOrg(
        @Valid @RequestBody form: CloudOrgForm,
        bindingResult: BindingResult,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val cnt = this.cloudOrgService.addOrg(form, header)
        return ResponseEntity(Result(cnt, errors), HttpStatus.OK)
    }

    /**
     * 编辑云区党支部
     */
    @HttpMonitorLogger
    @PostMapping("/update-org")
    @ApiOperation("编辑云区组织")
    fun updateOrg(
        @Valid @RequestBody form: CloudOrgForm,
        bindingResult: BindingResult,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val cnt = this.cloudOrgService.updateOrg(form, header)
        return ResponseEntity(Result(cnt, errors), HttpStatus.OK)
    }

    /**
     * 删除云区党支部
     */
    @HttpMonitorLogger
    @PostMapping("/delete-org")
    @ApiOperation("新增云区组织")
    fun deleteOrg(
        @Valid @RequestBody form: CloudOrgForm,
        bindingResult: BindingResult,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        this.cloudOrgService.deleteOrg(form, header)
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }

    /**
     * 加入云区党支部
     */
    @HttpMonitorLogger
    @PostMapping("/add-user")
    @ApiOperation("加入云区组织")
    fun addUser(
        @Valid @RequestBody form: CloudOrgForm,
        bindingResult: BindingResult,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        this.cloudOrgService.addUser(form, header)
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }
}