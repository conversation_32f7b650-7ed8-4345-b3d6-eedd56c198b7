package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.PartyGroupDetailForm
import com.goodsogood.ows.model.vo.PartyGroupForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.PartyGroupService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.hibernate.validator.constraints.Range
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * <AUTHOR> ruoyu
 * @date : 2021/9/7
 */
@RestController
@RequestMapping("/party-group")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党组管理", tags = ["党组管理"])
@Validated
class PartyGroupController @Autowired constructor(
    private val errors: Errors,
    private val partyGroupService: PartyGroupService
) {

    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("党组列表查询")
    fun list(
        @RequestParam("org_id") orgId: Long,
        @RequestParam(name = "unit_org_name", required = false) unitOrgName: String?,
        @RequestParam(name = "type", required = false) @Range(min = 1, max = 2, message = "是否设置党组选择条件错误") type: Int?,
        @RequestParam(name = "page", required = false, defaultValue = "1") page: Int?,
        @RequestParam(name = "size", required = false, defaultValue = "10") size: Int?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val join = partyGroupService.list(header, orgId, unitOrgName, type, page!!, size!!)
        return ResponseEntity(Result(join, errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/detail")
    @ApiOperation("查询党组信息")
    fun detail(
        @RequestParam("unit_org_id") unitOrgId: Long,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val detail = partyGroupService.detail(header, unitOrgId)
        return ResponseEntity(Result(detail, errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @PostMapping("/oper")
    @ApiOperation("新增/编辑/移除 党组信息")
    fun oper(
        @Valid @RequestBody operForm: PartyGroupDetailForm,
        bindingResult: BindingResult, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val oper = partyGroupService.oper(header, operForm)
        return ResponseEntity(Result(oper, errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/find-user-by-base-org")
    @ApiOperation("根据组织id查询关联单位上的党组成员")
    fun findUserByBaseOrg(
        @RequestParam("org_id") orgId: Long, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val oper = partyGroupService.findUserByBaseOrg(header, orgId)
        return ResponseEntity(Result(oper, errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/find-group-by-user")
    @ApiOperation("根据人员查询党组id")
    fun findGroupByUser(
        @RequestParam("userId") userId: Long,
        @RequestParam("regionId") regionId: Long
    ): ResponseEntity<Result<*>> {
        val groupIdList = partyGroupService.findGroupByUser(userId, regionId)
        return ResponseEntity(Result(groupIdList, errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/find-group-by-org")
    @ApiOperation("根据行政单位id获取党组id")
    fun findGroupByOrg(
        @RequestParam("org_id") orgId: Long,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(partyGroupService.findGroupByOrg(orgId), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @PostMapping("/find-group-list-by-ids")
    @ApiOperation("根据党组id列表查询党组详情")
    fun findGroupListByIds(
        @RequestBody form: PartyGroupForm,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(partyGroupService.findGroupListByIds(form), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/find-all-party-group")
    @ApiOperation("查询所有党组")
    fun findAllPartyGroup(
        @RequestParam(name = "page", required = false, defaultValue = "1") page: Int = 1,
        @RequestParam(name = "pageSize", required = false, defaultValue = "10") pageSize: Int = 10,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(
            Result(partyGroupService.findAllPartyGroup(page, pageSize, header), errors), HttpStatus.OK
        )
    }
}