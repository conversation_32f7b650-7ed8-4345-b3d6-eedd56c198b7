package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.helper.VerifyCodeHelper;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.vo.GuestForm;
import com.goodsogood.ows.model.vo.LoginForm;
import com.goodsogood.ows.model.vo.LoginResultForm;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.RegistForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.user.TokenBase;
import com.goodsogood.ows.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.awt.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * 登录controller
 *
 * <AUTHOR>
 * @date 2018-03-29
 */
@RestController
@RequestMapping("")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "用户登录", tags = {"Login"})
@Validated
public class LoginController {

     private final ObjectMapper OBJECTMAPPER = new ObjectMapper();

     private final Errors errors;
     private final UserService userService;
     private final StringRedisTemplate redisTemplate;

     @Value("${login.auth-code.usable}")
     private boolean authCodeUsable = true;

     @Autowired
     public LoginController(Errors errors, UserService userService, StringRedisTemplate redisTemplate) {
          this.errors = errors;
          this.userService = userService;
          this.redisTemplate = redisTemplate;
     }

     /**
      * 登录
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/login")
     @ApiOperation("登录")
     public ResponseEntity<Result<?>> doLogin(@Valid @RequestBody LoginForm form,
                                              BindingResult bindingResult,
                                              @RequestHeader HttpHeaders headers,
                                              HttpServletRequest request) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          String ip = Utils.getRemoteHost(request);
          // 1、ios，2、android，3、windows，4、macOS，5、unix/linux'
          String source = request.getHeader("_os");
          try {
               // 校验图片验证码
               if (authCodeUsable) {
                    if (!this.redisTemplate.hasKey(Constants.LOGIN_CAPTCHA_PREFIX.concat(form.getUuid()))) {
                         log.debug("图片验证码已失效，请重新获取 captcha -> {}", form.getCaptcha());
                         return new ResponseEntity<>(new Result<>(errors, 176, HttpStatus.OK.value()), HttpStatus.OK);
                    } else {
                         String captcha = this.redisTemplate.opsForValue().get(Constants.LOGIN_CAPTCHA_PREFIX.concat(form.getUuid()));
                         if (!captcha.equalsIgnoreCase(form.getCaptcha())) {
                              log.debug("图片验证码错误，请重新输入 captcha -> {}", form.getCaptcha());
                              return new ResponseEntity<>(new Result<>(errors, 177, HttpStatus.OK.value()), HttpStatus.OK);
                         } else {
                              this.redisTemplate.delete(Constants.LOGIN_CAPTCHA_PREFIX.concat(form.getUuid()));
                         }
                    }
               }
               UserEntity userEntity = this.userService.check(form.getPhone(), form.getPassword());
               // 账号或密码错误
               if (userEntity == null) {
                    log.debug("账号或密码错误 -> phone:{},password:{}", form.getPhone(), form.getPassword());
                    return new ResponseEntity<>(new Result<>(errors, 118, HttpStatus.OK.value()), HttpStatus.OK);
               } else {
                    // 获取header里的token
                    String tokenId = header.getToken();
                    if ("-1".equals(tokenId)) {
                         // 首次登录
                         ConcurrentMap<String, Object> resultMap = this.userService.firstLogin(userEntity.getUserId(), ip, source,
                                 userEntity.getName(),
                                 NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()),
                                 NumEncryptUtils.decrypt(userEntity.getCertNumber(), userEntity.getCertNumberSecret()), userEntity.getIsVerify(),
                                 userEntity.getPhoneSecret(), header.getRegionId());
                         // 如果只有一个单位或组织
                         List<LoginResultForm> resultList = (List<LoginResultForm>) resultMap.get("list");
                         if (resultList.size() == 1) {
                              int activate = (int) resultMap.get("activate");
                              if (activate == Constants.ACTIVATE_STATUS_NO) {
                                   log.debug("用户所在组织未激活 -> phone:{}", form.getPhone());
                                   return new ResponseEntity<>(new Result<>(errors, 188, HttpStatus.OK.value(), "用户所在"), HttpStatus.OK);
                              }
                         }
                         String flag = resultMap.get("flag").toString();
                         if ("1".equals(flag)) {
                              TokenBase tokenBase = (TokenBase) resultMap.get("token");
                              this.redisTemplate.opsForValue().set(resultList.get(0).getToken(), OBJECTMAPPER.writeValueAsString(tokenBase), 60 * 30, TimeUnit.SECONDS);
                         } else if ("3".equals(flag)) {
                              log.debug("登录用户不存在单位或组织信息 -> phone:{}", form.getPhone());
                              return new ResponseEntity<>(new Result<>(errors, 122, HttpStatus.OK.value()), HttpStatus.OK);
                         }
                         // 添加用户角色到缓存，2018-8-1
                         userService.setUserRoleToCache(userEntity.getUserId(), header.getRegionId());
                         return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
                    } else {
                         // 校验redis里token是否过期
                         if (this.redisTemplate.hasKey(tokenId)) {
                              // 再次登录
                              ConcurrentMap<String, Object> resultMap = this.userService.againLogin(userEntity.getUserId(), tokenId, ip, source,
                                      userEntity.getName(), header.getOid(),
                                      NumEncryptUtils.decrypt(userEntity.getPhone(), userEntity.getPhoneSecret()),
                                      NumEncryptUtils.decrypt(userEntity.getCertNumber(), userEntity.getCertNumberSecret()), userEntity.getIsVerify(),
                                      userEntity.getPhoneSecret(), header.getRegionId());
                              //添加用户角色到缓存，2018-8-1
                              userService.setUserRoleToCache(userEntity.getUserId(), header.getRegionId());
                              return new ResponseEntity<>(new Result<>((List<LoginResultForm>) resultMap.get("list"), errors), HttpStatus.OK);
                         } else {
                              // 不存在,重新登录,前端设置token为初始值"-1"
                              log.debug("登录超时，请重新登录 -> phone:{}", form.getPhone());
                              return new ResponseEntity<>(new Result<>(errors, 119, HttpStatus.OK.value()), HttpStatus.OK);
                         }
                    }
               }
          } catch (JsonProcessingException e) {
               log.error("JSON转换失败:" + e.getMessage(), e);
               return new ResponseEntity<>(new Result<>(errors, 120, HttpStatus.OK.value()), HttpStatus.OK);
          } catch (NullPointerException n) {
               log.error("获取缓存失败:" + n.getMessage(), n);
               return new ResponseEntity<>(new Result<>(errors, 121, HttpStatus.OK.value()), HttpStatus.OK);
          } catch (Exception ex) {
               log.error("系统异常，未知错误" + ex.getMessage(), ex);
               return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
          }
     }

     /**
      * 登录用户存在多个单位或组织
      *
      * @param headers
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/choose")
     @ApiOperation("登录成功，选择单位或组织")
     public ResponseEntity<Result<?>> chooseOne(@RequestHeader HttpHeaders headers, HttpServletRequest request) {
          try {
               HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
               // 获取header里的token
               String tokenId = header.getToken();
               // 首次登录
               Date now = new Date();
               if ("-1".equals(tokenId)) {
                    List<Long> idList = new ArrayList<>();
                    idList.add(header.getOid());
                    ConcurrentMap<String, Object> map = this.userService.chooseOne(tokenId, header.getUserId(),
                            header.getType(), header.getOid(), request, now, header.getRegionId());
                    // 如果组织已经被删除
                    if (Constants.STATUS_NO == (int) map.get("status")) {
                         log.debug("用户所在组织已经被删除 -> oid:{},userId:{}", header.getOid(), header.getUserId());
                         return new ResponseEntity<>(new Result<>(errors, 4001, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                    if ((int) map.get("activate") == Constants.ACTIVATE_STATUS_NO) {
                         log.debug("用户所在组织未激活 -> oid:{},userId:{}", header.getOid(), header.getUserId());
                         return new ResponseEntity<>(new Result<>(errors, 188, HttpStatus.OK.value(), "该"), HttpStatus.OK);
                    }
                    TokenBase base = new TokenBase();
                    base.setOid(header.getOid());
                    base.setType(header.getType());
                    base.setLoginTime(now);
                    base.setIdList(idList);
                    base.setUserId(header.getUserId());
                    this.redisTemplate.opsForValue().set(map.get("tokenId").toString(), OBJECTMAPPER.writeValueAsString(base), 60 * 30, TimeUnit.SECONDS);
                    //添加用户角色到缓存，2018-8-1
                    userService.setUserRoleToCache(header.getUserId(), header.getRegionId());
                    return new ResponseEntity<>(new Result<>(map.get("form"), errors), HttpStatus.OK);
               } else {
                    // 校验redis里token是否过期
                    if (this.redisTemplate.hasKey(tokenId)) {
                         ConcurrentMap<String, Object> map = this.userService.chooseOne(tokenId, header.getUserId(),
                                 header.getType(), header.getOid(), request, now, header.getRegionId());
                         //添加用户角色到缓存，2018-8-1
                         userService.setUserRoleToCache(header.getUserId(), header.getRegionId());
                         return new ResponseEntity<>(new Result<>(map.get("form"), errors), HttpStatus.OK);
                    } else {
                         // 不存在,重新登录,前端设置token为初始值"-1"
                         log.debug("登录超时，请重新登录!");
                         return new ResponseEntity<>(new Result<>(errors, 119, HttpStatus.OK.value()), HttpStatus.OK);
                    }
               }
          } catch (JsonProcessingException e) {
               log.error("JSON转换失败:" + e.getMessage(), e);
               return new ResponseEntity<>(new Result<>(errors, 120, HttpStatus.OK.value()), HttpStatus.OK);
          } catch (NullPointerException n) {
               log.error("获取缓存失败:" + n.getMessage(), n);
               return new ResponseEntity<>(new Result<>(errors, 121, HttpStatus.OK.value()), HttpStatus.OK);
          } catch (Exception ex) {
               log.error("身份证或者手机号加密失败" + ex.getMessage(), ex);
               return new ResponseEntity<>(new Result<>(errors, 186, HttpStatus.OK.value()), HttpStatus.OK);
          }
     }

     /**
      * 未实名用户注册
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/regist")
     @ApiOperation("未实名用户注册")
     public ResponseEntity<Result<?>> regist(@Valid @RequestBody RegistForm form,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          // 校验两次输入密码是否相同
          if (!form.getPassword().equals(form.getRepeatPassword())) {
               log.debug("两次密码不一致 -> pwd:{},rpwd:{}", form.getPassword(), form.getRepeatPassword());
               return new ResponseEntity<>(new Result<>(errors, 126, HttpStatus.OK.value()), HttpStatus.OK);
          }
          int flag = this.userService.regist(form, header.getType(), header.getOid(), header.getRegionId());
          if (flag == 9) {
               return new ResponseEntity<>(new Result<>(errors, 138, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (flag == 2) {
               log.debug("该手机号码已经存在 -> phone:{}", form.getPhone());
               return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
          }
          log.debug("未实名用户注册成功");
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 获取图片验证码
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/show-captcha.png")
     @ApiOperation("获取图片验证码")
     public void showCaptcha(@RequestParam String uuid,
                             HttpServletResponse httpServletResponse) {
          String[] rands = VerifyCodeHelper.getRands();
          String sRand = StringUtils.join(rands);
          // 压入缓存(5分钟失效)
          redisTemplate.opsForValue().set(Constants.LOGIN_CAPTCHA_PREFIX + uuid, sRand, 5, TimeUnit.MINUTES);
          log.debug("图片验证码随机数 -> {}", sRand);
          httpServletResponse.addHeader("Cache-Control", "no-store");
          httpServletResponse.addHeader("Pragma", "no-cache");
          httpServletResponse.addDateHeader("Expires", 0);
          try {
               httpServletResponse.addHeader("Access-Control-Allow-Origin", "*");
               httpServletResponse.setContentType("image/png");
               VerifyCodeHelper.showVerifyCodePng(httpServletResponse.getOutputStream(), rands, 100, 38);
          } catch (FontFormatException | IOException e) {
               log.error(e.getMessage(), e);
          }
     }

     /**
      * 未实名用户登录
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/guest-login")
     @ApiOperation("未实名用户登录")
     public ResponseEntity<Result<?>> guestLogin(HttpServletRequest request,
                                                 @Valid @RequestBody GuestForm form,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          String oper = Constants.OPER_GUEST;
          if (Constants.OPER_PHONE.equals(form.getSource())) {
               oper = Constants.OPER_PHONE;
          }
          return userService.guestLogin(request, form, header, oper, null);
     }

     /**
      * 根据用户获取角色菜单信息
      *
      * @param id
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/user-menu")
     @ApiOperation("根据用户获取菜单列表")
     public ResponseEntity<Result<?>> userMenu(@RequestParam
                                               @Range(min = 1, max = 99999999999L, message = "{Range.user.id}")
                                               @NotNull(message = "{NotNull.user.id}") Long id, @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          List<MenuForm> result = this.userService.userMenu(id, header.getType(), header.getOid(), header.getRegionId());
          log.debug("获取用户菜单信息成功 userId -> {}", id);
          return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
     }
}
























