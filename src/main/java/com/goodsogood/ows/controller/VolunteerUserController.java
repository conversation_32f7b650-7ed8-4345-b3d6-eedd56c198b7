package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.EvalHeaderHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.VolunteerUserEntity;
import com.goodsogood.ows.model.vo.AddTempVolunteerUserForm;
import com.goodsogood.ows.model.vo.BusinessCardForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.VolunteerResultForm;
import com.goodsogood.ows.model.vo.VolunteerUserAddForm;
import com.goodsogood.ows.model.vo.VolunteerUserBaseForm;
import com.goodsogood.ows.model.vo.VolunteerUserRemoveForm;
import com.goodsogood.ows.model.vo.VolunteerUserServiceForm;
import com.goodsogood.ows.model.vo.VolunteerUserSkillForm;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.VolunteerUserService;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/6/23
 * Description: 志愿者人员管理
 */
@RestController
@RequestMapping("/volunteer/user")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class VolunteerUserController {

    private final Errors errors;
    private final VolunteerUserService volunteerUserService;

    @Autowired
    public VolunteerUserController(Errors errors, VolunteerUserService volunteerUserService) {
        this.errors = errors;
        this.volunteerUserService = volunteerUserService;
    }


    /**
     * 新增志愿者
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @PostMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody VolunteerUserAddForm form,
                                         BindingResult bindingResult,
                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer type = volunteerUserService.add(form, sysHeader);
        if (type > 1) {
            return new ResponseEntity<>(new Result<>(errors, type, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 根据用户Id 查询志愿者
     *
     * @param volunteerUserId
     * @param headers
     * @return
     */
    @GetMapping("/find-by-id")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findById(@RequestParam(value = "user_id") Long volunteerUserId,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.findById(volunteerUserId, Constants.FIND_VOL_USER_BY_CACHE_NO), errors), HttpStatus.OK);
    }

    /**
     * 移动端编辑个人基本信息
     *
     * @param volunteerUserBaseForm
     * @param headers
     * @return
     */
    @PostMapping("/update-user-base")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> updateUserBase(@Valid @RequestBody VolunteerUserBaseForm volunteerUserBaseForm,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer type = volunteerUserService.updateUserBase(volunteerUserBaseForm, sysHeader);
        if (type > 1) {
            return new ResponseEntity<>(new Result<>(errors, type, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 移动端编辑个人服务意愿
     *
     * @param volunteerUserBaseForm
     * @param headers
     * @return
     */
    @PostMapping("/update-user-service")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> updateUserService(@Valid @RequestBody VolunteerUserServiceForm volunteerUserBaseForm,
                                                       BindingResult bindingResult,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer type = volunteerUserService.updateUserService(volunteerUserBaseForm, sysHeader);
        if (type > 1) {
            return new ResponseEntity<>(new Result<>(errors, type, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 移动端编辑个人特长
     *
     * @param volunteerUserBaseForm
     * @param headers
     * @return
     */
    @PostMapping("/update-user-skill")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> updateUserSkill(@Valid @RequestBody VolunteerUserSkillForm volunteerUserBaseForm,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer type = volunteerUserService.updateUserSkill(volunteerUserBaseForm, sysHeader);
        if (type > 1) {
            return new ResponseEntity<>(new Result<>(errors, type, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 志愿者列表查询
     *
     * @param volunteerTeamId 志愿团队id
     * @param name            志愿者名称
     * @param phone           志愿者手机
     * @param volunteerNumber 志愿者编号
     * @param headers
     * @return
     */
    @GetMapping("/query-volunteer-list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> queryVolunteerList(@RequestParam(value = "org_id", required = false) Long orgId,
                                                        @RequestParam(value = "volunteer_team_id", required = false) Long volunteerTeamId,
                                                        @RequestParam(value = "level_type") @Range(min = 1, max = 2) Integer levelType,
                                                        @RequestParam(value = "name", required = false) String name,
                                                        @RequestParam(value = "phone", required = false) @Length(min = 11, max = 11, message = "{Length.VolunteerUserController.phone}") String phone,
                                                        @RequestParam(value = "volunteer_number", required = false) String volunteerNumber,
                                                        @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                        @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.queryVolunteerList(volunteerTeamId, levelType, name, phone, volunteerNumber, page, size, sysHeader, orgId), errors), HttpStatus.OK);
    }

    /**
     * 移除志愿者
     *
     * @return
     */
    @PostMapping("/remove")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> remove(@Valid @RequestBody List<VolunteerUserRemoveForm> list,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer type = volunteerUserService.remove(list, sysHeader);
        if (type > 1) {
            return new ResponseEntity<>(new Result<>(errors, type, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 志愿者关联的团体列表
     *
     * @return
     */
    @GetMapping("/team_list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> teamList(@RequestParam(value = "volunteer_user_id") Long volunteerUserId,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.teamList(volunteerUserId, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * 志愿者列表查询下载
     *
     * @param volunteerTeamId 志愿团队id
     * @param name            志愿者名称
     * @param phone           志愿者手机
     * @param volunteerNumber 志愿者编号
     * @param headers
     * @return
     */
    @GetMapping("/query-volunteer-list-download")
    @HttpMonitorLogger
    public void queryVolunteerListDownload(@RequestParam(value = "volunteer_team_id") Long volunteerTeamId,
                                           @RequestParam(value = "level_type") @Range(min = 1, max = 2) Integer levelType,
                                           @RequestParam(value = "name", required = false) String name,
                                           @RequestParam(value = "phone", required = false) @Length(min = 11, max = 11, message = "{Length.VolunteerUserController.phone}") String phone,
                                           @RequestParam(value = "volunteer_number", required = false) String volunteerNumber,
                                           HttpServletResponse response,
                                           @RequestHeader HttpHeaders headers) throws IOException {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        volunteerUserService.queryVolunteerListDownload(volunteerTeamId, levelType, name, phone, volunteerNumber, response, sysHeader);
    }

    /**
     * 通过用户Id查询关联志愿者状态
     */
    @GetMapping("/check-volunteer-user")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> checkVolunteerUser(@RequestParam(value = "user_id", required = false) Long userId,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        userId = userId == null ? sysHeader.getUserId() : userId;
        return new ResponseEntity<>(new Result<>(volunteerUserService.checkVolunteerUser(userId, sysHeader), errors), HttpStatus.OK);
    }


    /**
     * 志愿者名片
     *
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping(value = "/businessCard")
    @ApiOperation(value = "志愿者名片")
    public ResponseEntity<Result<?>> businessCard(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        List<BusinessCardForm> result = volunteerUserService.businessCard(sysHeader.getUserId());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 志愿者信息回填
     *
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping(value = "/backfill")
    @ApiOperation(value = "志愿者信息回填")
    public ResponseEntity<Result<?>> backfill(@RequestHeader HttpHeaders headers,
                                              @RequestParam(value = "phone", required = false)
                                              @Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}") String phone) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        VolunteerUserEntity result = volunteerUserService.backfill(sysHeader.getUserId(), phone,sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取二维码
     *
     * @param userId
     * @param request
     * @param response
     * @return
     * @throws WriterException
     * @throws IOException
     */
    @HttpMonitorLogger
    @GetMapping(value = "/getQrCode")
    @ApiOperation(value = "获取二维码")
    public ResponseEntity<Result<?>> getQrCode(@RequestParam(value = "user_id") Long userId, HttpServletRequest request, HttpServletResponse response) throws WriterException, IOException {
        String content = userId.toString();
        Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
        // 指定编码格式
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // 指定纠错级别(L--7%,M--15%,Q--25%,H--30%)
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        // 编码内容,编码类型(这里指定为二维码),生成图片宽度,生成图片高度,设置参数
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, 300, 300, hints);
        //设置请求头
        response.setHeader("Content-Type", "application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + userId + ".jpg");
        OutputStream outputStream = response.getOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "jpg", outputStream);
        outputStream.flush();
        outputStream.close();
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 是否需要补全信息
     *
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping(value = "/needComplete")
    @ApiOperation(value = "是否需要补全信息")
    public ResponseEntity<Result<?>> needComplete(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        Integer needComplete = volunteerUserService.needComplete(sysHeader.getUserId());
        Boolean result = (null == needComplete || 1 == needComplete);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 临时新增用户表中用户到志愿者体系中 ，正式环境需要关闭
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping(value = "/addTempVolunteerUser")
    @ApiOperation(value = "")
    public ResponseEntity<Result<?>> addTempVolunteerUser(@Valid @RequestBody AddTempVolunteerUserForm addTempVolunteerUserForm,
                                                          BindingResult bindingResult,
                                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.addTempVolunteerUser(addTempVolunteerUserForm, sysHeader), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/find-volunteer-user-list-by-volunteer")
    @ApiOperation(value = "批量通过志愿ID查询志愿者信息")
    public ResponseEntity<Result<?>> findVolunteerUserListByVolunteer(@RequestBody @Size(min = 1, message = "volunteer user id cannot by empty") Set<Long> volunteerUserIds,
                                                                      BindingResult bindingResult,
                                                                      @RequestHeader HttpHeaders headers) {
//        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        String regionIdStr = headers.get("_region_id").get(0);
        assert StringUtils.isNotBlank(regionIdStr);
        return new ResponseEntity<>(new Result<>(volunteerUserService.findVolunteerUserListByVolunteer(volunteerUserIds, new Long(regionIdStr)), errors), HttpStatus.OK);
    }

    /**
     * 通过用户Id查询关联志愿者状态
     */
    @PostMapping("/check-volunteer-user")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> checkVolunteerUserList(@RequestBody @Size(min = 1, message = "最少传一个撒") List<Long> userId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.checkVolunteerUserList(userId, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * 通过用户Id查询关联志愿者状态
     */
    @GetMapping("/find-org-level")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgLevel(@RequestParam(value = "org_id") Long orgId,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(volunteerUserService.findOrgLevel(orgId, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询查询可以管理的团体列表 去志愿服务中获取为设置管理员的项目所属团队
     *
     * @param userId 用户ID
     * @return success
     */
    @HttpMonitorLogger
    @GetMapping("/find-team-by-user-id")
    @ApiOperation("根据用户ID查询管理团体列表")
    public ResponseEntity<Result<?>> findTeamByUserId(@RequestParam(value = "user_id") Long userId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        List<VolunteerResultForm> resultList = this.volunteerUserService.findTeamByUserId(userId, sysHeader);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 同步接口同步用户时异步添加志愿者信息
     *
     * @param base 参数
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/syc-init-volunteer")
    @ApiOperation(value = "同步接口同步用户时异步添加志愿者信息")
    public ResponseEntity<Result<?>> sycInitVolunteer(@Valid @RequestBody UserInfoBase base,
                                                      BindingResult bindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        this.volunteerUserService.sycInitVolunteer(base);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/test-cache-volunteer-user")
    @ApiOperation("组织志愿团体双向同步")
    public ResponseEntity<Result<?>> testCacheVolunteerUser(@RequestParam(value = "orgId") Long orgId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.volunteerUserService.testVolunteerUser(orgId, header.getRegionId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/sync-volunteer-user")
    @ApiOperation("内部调用 组织志愿团体双向同步")
    public ResponseEntity<Result<?>> syncVolunteerUser(@RequestParam(value = "volunteer_team_id") Long volunteerTeamId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.volunteerUserService.syncVolunteerUser(volunteerTeamId, header.getRegionId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-period-by-volunteer")
    @ApiOperation("内部调用 获取第二支部对应的支委会")
    public ResponseEntity<Result<?>> findPeriodByVolunteer(@RequestParam(value = "volunteer_team_id") Long volunteerTeamId,
                                                           @RequestParam(value = "region_id") Long regionId) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.volunteerUserService.findPeriodByVolunteer(volunteerTeamId,regionId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-unit-by-user")
    @ApiOperation("内部调用 根据user_id 获取单位信息")
    public ResponseEntity<Result<?>> findUnitByUser(@RequestParam(value = "user_id") List<Long> userId,
                                                    @RequestParam(value = "region_id") Long regionId) {
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.volunteerUserService.findUnitByUser(userId, regionId), errors), HttpStatus.OK);
    }

}