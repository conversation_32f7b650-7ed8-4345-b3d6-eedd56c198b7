package com.goodsogood.ows.controller;

import com.goodsogood.ows.aspect.DatabaseSwitchAspect;
import com.goodsogood.ows.configuration.DynamicDataSource;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库测试控制器
 * 用于测试数据库连接和切换功能
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@RestController
@RequestMapping("/database-test")
@Api(value = "数据库测试", tags = {"数据库测试"})
@Log4j2
public class DatabaseTestController {

    @Autowired
    @Qualifier("dataSource")
    private DynamicDataSource dynamicDataSource;

    @Autowired
    private DatabaseSwitchAspect databaseSwitchAspect;

    @Autowired
    private Errors errors;

    /**
     * 测试数据库连接状态
     */
    @GetMapping("/connection-status")
    @ApiOperation("测试数据库连接状态")
    public ResponseEntity<Result<Map<String, Object>>> testConnectionStatus() {
        log.info("开始测试数据库连接状态...");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试达梦数据库
            boolean dmAvailable = dynamicDataSource.isDmDatabaseAvailable();
            result.put("dmDatabase", Map.of(
                "available", dmAvailable,
                "status", dmAvailable ? "连接正常" : "连接失败"
            ));
            
            // 测试MySQL数据库
            boolean mysqlAvailable = dynamicDataSource.isMysqlDatabaseAvailable();
            result.put("mysqlDatabase", Map.of(
                "available", mysqlAvailable,
                "status", mysqlAvailable ? "连接正常" : "连接失败"
            ));
            
            // 当前使用的数据源
            result.put("currentDataSource", DynamicDataSource.getDataSource());
            result.put("dataSourceStatus", dynamicDataSource.getDataSourceStatus());
            
            log.info("数据库连接状态测试完成");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("测试数据库连接状态时发生错误: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取数据库详细信息
     */
    @GetMapping("/database-info")
    @ApiOperation("获取数据库详细信息")
    public ResponseEntity<Result<Map<String, Object>>> getDatabaseInfo() {
        log.info("开始获取数据库详细信息...");
        
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dynamicDataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            result.put("databaseProductName", metaData.getDatabaseProductName());
            result.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            result.put("driverName", metaData.getDriverName());
            result.put("driverVersion", metaData.getDriverVersion());
            result.put("url", metaData.getURL());
            result.put("userName", metaData.getUserName());
            result.put("currentDataSource", DynamicDataSource.getDataSource());
            
            log.info("数据库详细信息获取完成: {}", metaData.getDatabaseProductName());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (SQLException e) {
            log.error("获取数据库详细信息时发生错误: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动切换到达梦数据库
     */
    @PostMapping("/switch-to-dm")
    @ApiOperation("手动切换到达梦数据库")
    public ResponseEntity<Result<String>> switchToDmDatabase() {
        log.info("手动切换到达梦数据库...");
        
        try {
            databaseSwitchAspect.switchToDmDatabase();
            String message = "已切换到达梦数据库";
            log.info(message);
            return new ResponseEntity<>(new Result<>(message, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("切换到达梦数据库时发生错误: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(e.getMessage(), errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 手动切换到MySQL数据库
     */
    @PostMapping("/switch-to-mysql")
    @ApiOperation("手动切换到MySQL数据库")
    public ResponseEntity<Result<String>> switchToMysqlDatabase() {
        log.info("手动切换到MySQL数据库...");
        
        try {
            databaseSwitchAspect.switchToMysqlDatabase();
            String message = "已切换到MySQL数据库";
            log.info(message);
            return new ResponseEntity<>(new Result<>(message, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("切换到MySQL数据库时发生错误: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(e.getMessage(), errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 自动选择数据源
     */
    @PostMapping("/auto-select")
    @ApiOperation("自动选择数据源")
    public ResponseEntity<Result<String>> autoSelectDataSource() {
        log.info("自动选择数据源...");
        
        try {
            dynamicDataSource.autoSelectDataSource();
            String currentDataSource = DynamicDataSource.getDataSource();
            String message = "自动选择数据源完成，当前使用: " + currentDataSource;
            log.info(message);
            return new ResponseEntity<>(new Result<>(message, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("自动选择数据源时发生错误: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(e.getMessage(), errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 执行简单的数据库查询测试
     */
    @GetMapping("/query-test")
    @ApiOperation("执行简单的数据库查询测试")
    public ResponseEntity<Result<Map<String, Object>>> queryTest() {
        log.info("开始执行数据库查询测试...");
        
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dynamicDataSource.getConnection()) {
            // 执行简单的查询
            try (ResultSet rs = connection.createStatement().executeQuery("SELECT 1 FROM DUAL")) {
                if (rs.next()) {
                    result.put("queryResult", rs.getInt(1));
                    result.put("status", "查询成功");
                    result.put("currentDataSource", DynamicDataSource.getDataSource());
                    
                    DatabaseMetaData metaData = connection.getMetaData();
                    result.put("databaseType", metaData.getDatabaseProductName());
                    
                    log.info("数据库查询测试成功");
                    return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
                }
            }
        } catch (SQLException e) {
            log.error("数据库查询测试失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("status", "查询失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        
        result.put("status", "未知错误");
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 清除数据源设置
     */
    @PostMapping("/clear-datasource")
    @ApiOperation("清除数据源设置")
    public ResponseEntity<Result<String>> clearDataSource() {
        log.info("清除数据源设置...");
        
        try {
            DynamicDataSource.clearDataSource();
            String message = "数据源设置已清除，将使用默认数据源";
            log.info(message);
            return new ResponseEntity<>(new Result<>(message, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("清除数据源设置时发生错误: {}", e.getMessage(), e);
            return new ResponseEntity<>(new Result<>(e.getMessage(), errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
