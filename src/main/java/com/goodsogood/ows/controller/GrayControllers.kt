package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.annotation.RepeatedCheck
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.GrayAppService
import com.goodsogood.ows.service.GrayUserService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*


@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "灰度测试用户管理", tags = ["灰度测试"])
@RequestMapping("/gray")
@Validated
class GrayUserController(val errors: Errors, val grayUserServices: GrayUserService) {
    private val log = LoggerFactory.getLogger(GrayAppController::class.java)

    @HttpMonitorLogger
    @GetMapping("/am-i")
    @ApiOperation("验证用户在当前regionId下是否是灰度测试用户")
    fun ami(
        @RequestParam(name = "third_id") thridId: String,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Boolean>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Boolean>>(
            Result<Boolean>(
                this.grayUserServices.isGray(thridId, header.regionId), errors
            ), HttpStatus.OK
        )
    }


    @HttpMonitorLogger
    @RepeatedCheck // 防重复提交
    @GetMapping("/add-user")
    @ApiOperation("添加灰度测试用户")
    fun addUser(
        @RequestParam(required = true, name = "third_id") thirdId: List<String>,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<GrayUserService.GrayUsers>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        // 并集
        val users = thirdId.union(this.grayUserServices.getAllUsersThirdId(header.regionId))
        return ResponseEntity<Result<List<GrayUserService.GrayUsers>>>(
            Result<List<GrayUserService.GrayUsers>>(
                this.grayUserServices.updateUsers(users.toList(), header.regionId, header.userId), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @RepeatedCheck // 防重复提交
    @GetMapping("/remove-user")
    @ApiOperation("移除灰度测试用户")
    fun removeUser(
        @RequestParam(required = true, name = "third_id") thirdId: List<String>,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<GrayUserService.GrayUsers>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        // 差集
        val users = this.grayUserServices.getAllUsersThirdId(header.regionId).subtract(thirdId)
        return ResponseEntity<Result<List<GrayUserService.GrayUsers>>>(
            Result<List<GrayUserService.GrayUsers>>(
                this.grayUserServices.updateUsers(users.toList(), header.regionId, header.userId), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @RepeatedCheck // 防重复提交
    @GetMapping("/update-user")
    @ApiOperation("更新灰度测试用户")
    fun updateUser(
        @RequestParam(required = false, name = "third_id") thirdId: List<String>?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<GrayUserService.GrayUsers>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val users = thirdId ?: listOf()
        return ResponseEntity<Result<List<GrayUserService.GrayUsers>>>(
            Result<List<GrayUserService.GrayUsers>>(
                this.grayUserServices.updateUsers(users, header.regionId, header.userId), errors
            ), HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/users")
    @ApiOperation("获取灰度测试用户")
    fun users(
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<GrayUserService.GrayUsers>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<List<GrayUserService.GrayUsers>>>(
            Result<List<GrayUserService.GrayUsers>>(
                this.grayUserServices.getUsers(header.regionId), errors
            ), HttpStatus.OK
        )
    }
}

@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "灰度测试模块管理", tags = ["灰度测试"])
@RequestMapping("/gray")
@Validated
class GrayAppController(val errors: Errors, val grayAppServices: GrayAppService) {
    private val log = LoggerFactory.getLogger(GrayAppController::class.java)

    /**
     * 获取当前灰度测试模块
     */
    @HttpMonitorLogger
    @GetMapping("/in-progress")
    @ApiOperation("获取当前灰度测试模块")
    fun inProgress(@RequestHeader headers: HttpHeaders): ResponseEntity<Result<List<String>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val servers = this.grayAppServices.findByRegionId(header.regionId).map {
            it.serverName ?: "未知模块"
        }
        return ResponseEntity<Result<List<String>>>(
            Result(servers, errors),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @RepeatedCheck // 防重复提交
    @GetMapping("/update-app")
    @ApiOperation("更新灰度测试模块")
    fun updateApp(
        @RequestParam(required = false) app: List<String>?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<List<String>>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val servers = this.grayAppServices.updateApps(app ?: listOf(), header.regionId, header.userId).map {
            it.serverName ?: "未知模块"
        }
        return ResponseEntity<Result<List<String>>>(
            Result(servers, errors),
            HttpStatus.OK
        )
    }
}