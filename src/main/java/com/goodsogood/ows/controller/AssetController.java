package com.goodsogood.ows.controller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.asset.AssetFrom;
import com.goodsogood.ows.model.vo.asset.ClassFrom;
import com.goodsogood.ows.service.AssetService;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/27
 */
@RestController
@Validated
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "资产管理", tags = {"资产信息登记"})
@RequestMapping("/asset")
public class AssetController {
    public final AssetService assetService;
    public final Errors errors;

    public AssetController(AssetService assetService, Errors errors) {
        this.assetService = assetService;
        this.errors = errors;
    }

    /**
     * 创建资产登记
     */
    @HttpMonitorLogger
    @PostMapping("/add")
    public ResponseEntity<Result<?>> list(@Valid @RequestBody AssetFrom from,
                                          BindingResult bindingResult,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.addAsset(header, from), errors), HttpStatus.OK);
    }

    /**
     * 查询资产列表信息
     */
    @HttpMonitorLogger
    @GetMapping("/find")
    public ResponseEntity<Result<?>> findAsset(@RequestHeader HttpHeaders headers,
                                               @RequestParam(name = "flag", required = false) Integer flag,
                                               @RequestParam(name = "oid", required = false) Long orgId,
                                               @RequestParam(name = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                               @RequestParam(name = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                               @RequestParam(name = "asset_name", required = false) String assetName,
                                               @RequestParam(name = "asset_type_id", required = false) String assetTypeId,
                                               @RequestParam(name = "code", required = false) String code,
                                               @DateTimeFormat(pattern = "yyyy-MM-dd")
                                               @RequestParam(name = "buy_start_time", required = false) Date buyStartTime,
                                               @DateTimeFormat(pattern = "yyyy-MM-dd")
                                               @RequestParam(name = "buy_end_time", required = false) Date buyEndTime,
                                               @DateTimeFormat(pattern = "yyyy-MM-dd")
                                               @RequestParam(name = "register_start_time", required = false) Date RegisterStartTime,
                                               @DateTimeFormat(pattern = "yyyy-MM-dd")
                                               @RequestParam(name = "register_end_time", required = false) Date RegisterEndTime
    ) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.findAsset(header,flag,orgId, pageNum, pageSize, assetName, assetTypeId, code,
                buyStartTime, buyEndTime, RegisterStartTime, RegisterEndTime), errors), HttpStatus.OK);
    }

    /**
     * 查询资产详情信息
     */
    @HttpMonitorLogger
    @GetMapping("/details")
    public ResponseEntity<Result<?>> list(@RequestParam(name = "id") Long assetId,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.findAssetDetails(header, assetId), errors), HttpStatus.OK);
    }

    /**
     * 修改资产信息
     */
    @HttpMonitorLogger
    @PostMapping("/update")
    public ResponseEntity<Result<?>> updateAsset(@Valid @RequestBody AssetFrom from,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.updateAsset(header, from), errors), HttpStatus.OK);
    }

    /**
     * 删除资产信息
     */
    @HttpMonitorLogger
    @PostMapping("/del-asset")
    public ResponseEntity<Result<?>> delAsset(@RequestBody AssetFrom from,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.delAsset(header, from), errors), HttpStatus.OK);
    }

    /**
     * 添加类型
     */
    @HttpMonitorLogger
    @PostMapping("/add-type")
    public ResponseEntity<Result<?>> addType(@Valid @RequestBody ClassFrom from,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.addType(header, from), errors), HttpStatus.OK);
    }

    /**
     * 添加下级
     */
    @HttpMonitorLogger
    @PostMapping("/add-type-level")
    public ResponseEntity<Result<?>> list(@Valid @RequestBody ClassFrom from,
                                          BindingResult bindingResult,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.addTypeLevel(header, from), errors), HttpStatus.OK);
    }

    /**
     * 查询类别列表
     */
    @HttpMonitorLogger
    @GetMapping("/find-type")
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers,
                                          @RequestParam(name = "page",defaultValue = "1") Integer page,
                                          @RequestParam(name = "page_size",defaultValue = "10") Integer pageSize) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.findTypes(header,page,pageSize), errors), HttpStatus.OK);
    }

    /**
     * 查询类别通用接口
     */
    @HttpMonitorLogger
    @GetMapping("/type")
    public ResponseEntity<Result<?>> findType(@RequestHeader HttpHeaders headers,
                                              @RequestParam(required = false)Integer level,
                                              @RequestParam(name = "type_id",required = false)Long typeId) {
        if (level != null){ level = 1; }
        return new ResponseEntity<>(new Result<>(assetService.findType(level,typeId), errors), HttpStatus.OK);
    }

    /**
     * 编辑类别
     */
    @HttpMonitorLogger
    @PostMapping("/update-type")
    public ResponseEntity<Result<?>> updateType(@Valid @RequestBody ClassFrom from,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.updateType(header, from), errors), HttpStatus.OK);
    }

    /**
     * 删除类别
     */
    @HttpMonitorLogger
    @PostMapping("/del-type")
    public ResponseEntity<Result<?>> delType(@RequestBody ClassFrom from,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(assetService.delType(header, from), errors), HttpStatus.OK);
    }
}
