package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.PartyWorkForm
import com.goodsogood.ows.model.vo.QueryCorpVO
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.PartyWorkLeaderGroupService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/party-work-leader-group")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "党建领导小组", tags = ["党建领导小组"])
@Validated
class PartyWorkLeaderGroupController(
    @Autowired val errors: Errors,
    @Autowired val partyWorkLeaderGroupService: PartyWorkLeaderGroupService
) {

    @HttpMonitorLogger
    @PostMapping("/query-corp-list")
    @ApiOperation("党建领导小组列表")
    fun queryPartyWorkCorpList(
        @Valid @RequestBody queryForm: QueryCorpVO,
        bindingResult: BindingResult
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.partyWorkLeaderGroupService.queryCorpList(queryForm),
                errors
            ),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @PostMapping("/set-party-work")
    @ApiOperation("设置党建工作领导小组")
    fun setPartyWork(
        @Valid @RequestBody form: PartyWorkForm,
        bindingResult: BindingResult,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.partyWorkLeaderGroupService.setPartyWorkGroup(form, headers),
                errors
            ),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/del")
    @ApiOperation("删除设置党建工作领导小组用户")
    fun delPartyWorkUser(
        @RequestParam("group_id") groupId: Long,
        @RequestParam("user_id", required = false) userId: Long,
        @RequestParam("org_id", required = false) orgId: Long
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.partyWorkLeaderGroupService.delPartyWorkUserOrOrg(
                    groupId = groupId,
                    userId = userId,
                    orgId = orgId
                ),
                errors
            ),
            HttpStatus.OK
        )
    }

    @HttpMonitorLogger
    @GetMapping("/get-party-work-group")
    @ApiOperation("获取党建工作领导小组")
    fun getPartyWork(@RequestParam("group_id", required = false, defaultValue = "0") groupId: Long)
            : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.partyWorkLeaderGroupService.getPartyWorkGroup(groupId),
                errors
            ),
            HttpStatus.OK
        )
    }
}