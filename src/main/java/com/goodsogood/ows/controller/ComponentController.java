package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Groups;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.ComponentEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.ComponentService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @create 2019-07-23 17:11
 **/
@RestController
@RequestMapping("/component")
@CrossOrigin(origins = "*", maxAge = 3600)
@Log4j2
public class ComponentController {

     private final Errors errors;
     private final ComponentService componentService;

     @Autowired
     public ComponentController(Errors errors,
                                ComponentService componentService) {
          this.errors = errors;
          this.componentService=componentService;
     }

     /**
      * 查询组件列表信息
      */
     @HttpMonitorLogger
     @GetMapping("/list")
     public ResponseEntity<Result<List<ComponentEntity>>> list() {
          List<ComponentEntity> componentList = componentService.getComponentList();
          Result<List<ComponentEntity>> result = new Result<>(componentList, errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }

     /**
      * 添加组件列表信息
      */
     @HttpMonitorLogger
     @PostMapping("/add")
     public ResponseEntity<Result<?>> add(HttpServletRequest request,
                                          @Validated(value = Groups.Insert.class)
                                          @RequestBody  ComponentForm componentForm,
                                          BindingResult br) {
          log.debug("添加组件列表信息：componentForm = {}", componentForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Map<String, Integer> componentList = componentService.add(componentForm,request);
          Result<Map<String, Integer> > result = new Result<>(componentList, errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }
     /**
      * 添加组件列表初始化信息
      */
     @HttpMonitorLogger
     @PostMapping("/addInit")
     public ResponseEntity<Result<?>> addInit(@Validated(value = Groups.Insert.class)
                                              @RequestBody  InitComponentForm initComponentForm,
                                              BindingResult br) {
          log.debug("添加组件列表初始化信息：initComponentForm = {}", initComponentForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Map<String, Long> componentList = componentService.addInitComponent(initComponentForm);
          Result<Map<String, Long> > result = new Result<>(componentList, errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }
     /**
      * 编辑组件组件信息
      */
     @HttpMonitorLogger
     @PostMapping("/edit")
     public ResponseEntity<Result<?>> edit(HttpServletRequest request,
                                           @Validated(value = {Groups.Update.class})
                                           @RequestBody ComponentForm componentForm,
                                           BindingResult br) {
          log.debug("编辑组件组件信息：componentForm = {}", componentForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Integer editResult = componentService.edit(componentForm,request);
          if(editResult<1){
               throw new ApiException("修改组库失败！", new Result<>(errors, 21003,
                       HttpStatus.OK.value()));
          }
          Result<String> result = new Result<>("", errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }

     /**
      * 删除组件列表信息
      */
     @HttpMonitorLogger
     @PostMapping("/del")
     public ResponseEntity<Result<?>> del(HttpServletRequest request,
                                          @Validated(value = {Groups.PrimaryKey.class})
                                          @RequestBody ComponentForm componentForm,
                                          BindingResult br) {
          log.debug("删除组件列表信息：componentForm = {}", componentForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Integer delResult = componentService.del(componentForm,request);
          if(delResult<1){
               throw new ApiException("删除组库失败！", new Result<>(errors, 21004,
                       HttpStatus.OK.value()));
          }
          Result<String > result = new Result<>("", errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }

     /**
      * 查询具体组件列表信息
      */
     @GetMapping("/get/{componentId}/{componentType}")
     public ResponseEntity<Result<?>> getDetailComponent(@NotNull(message = "组库Id必填")
                                                         @PathVariable Long componentId,
                                                         @PathVariable Integer componentType) {
         log.debug("查询具体组件列表信息：componentId = {}", componentId);
         if (componentId == null || componentId <= 0) {
             throw new ApiException("组库信息不存在",
                     new Result<>(errors, 21006, HttpStatus.OK.value()));
         }
         ComponentFormList componentFormList = componentService.getDetailComponent(componentId);
         List<DictionaryForm> optionList = componentService.getComponentOptionByCache(componentId);
         if(optionList==null){
             //刷新redis缓存 然后再次获取
             refreshComponent(componentId,componentType);
             optionList = componentService.getComponentOptionByCache(componentId);
         }
         componentFormList.setOptionList(optionList);
         Result<ComponentFormList> result = new Result<>(componentFormList, errors);
         return new ResponseEntity<>(result, HttpStatus.OK);
     }


    /**
     * 刷新组件列表信息
     */
    @HttpMonitorLogger
    @GetMapping("/refresh/{componentId}/{componentType}")
    public ResponseEntity<Result<?>> refreshComponent(@NotNull(message = "组库Id必填")
                                                      @PathVariable Long componentId,
                                                      @PathVariable Integer componentType) {
        log.debug("刷新组件列表信息：componentId = {}", componentId);
        if (componentId == null || componentId <= 0) {
            throw new ApiException("组库信息不存在",
                    new Result<>(errors, 21006, HttpStatus.OK.value()));
        }
        List<DictionaryForm> dictionaryForms = componentService.refreshComponent(componentId,componentType);
        Result<List<DictionaryForm>> result = new Result<>(dictionaryForms, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 拉取组件数据
     */
    @HttpMonitorLogger
    @GetMapping("/pull/{componentId}")
    public ResponseEntity<Result<?>> pullComponentData(@NotNull(message = "组库Id必填")
                                                       @PathVariable Long componentId) {
        log.debug("拉取组件数据：componentId = {}", componentId);
        if (componentId == null || componentId <= 0) {
            throw new ApiException("组库信息不存在",
                    new Result<>(errors, 21006, HttpStatus.OK.value()));
        }
        componentService.pullComponentData(componentId);
        Result<String> result = new Result<>("", errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 从缓存得到组库选项信息
     */
    @HttpMonitorLogger
    @GetMapping("/getCache/{componentId}")
    public ResponseEntity<Result<?>> getComponentOptionByCache(@NotNull(message = "组库Id必填")
                                                               @PathVariable Long componentId) {
        log.debug("从缓存得到组库选项信息：componentId = {}", componentId);
        if (componentId == null || componentId <= 0) {
            throw new ApiException("组库信息不存在",
                    new Result<>(errors, 21006,HttpStatus.OK.value()));
        }
        List<DictionaryForm> dictionaryForms = componentService.getComponentOptionByCache(componentId);
        Result<List<DictionaryForm>> result = new Result<>(dictionaryForms, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 移动组件列表选项信息
     */
    @HttpMonitorLogger
    @GetMapping("/getDetail/{componentId}/{optionCode}")
    public ResponseEntity<Result<?>> getDetail(@NotNull(message = "组库Id必填") @PathVariable Long componentId,
                                               @NotNull(message = "选项编码不能这空")
                                               @PathVariable Long optionCode) {
        SingleComponentOption componentOptionByCache =
                componentService.getComponentOptionLevel(componentId, optionCode);
          Result<SingleComponentOption> result = new Result<>(componentOptionByCache, errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }



     /**
      * 处理请求异常
      * @param br
      */
     public  void  handlerParameterError(BindingResult br){
          String message= br.getFieldError().getDefaultMessage();
          throw new ApiException(message ,new Result<>(errors, 200000, HttpStatus.OK.value(),message));
     }


}
