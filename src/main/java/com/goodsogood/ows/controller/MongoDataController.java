package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgMongoService;
import com.goodsogood.ows.service.UserMongoService;
import com.goodsogood.ows.service.UserOrgCacheCallService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

/**
 * 同步MongoDB中的数据
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mongo")
@Log4j2
public class MongoDataController {

	private final Errors errors;
	private final UserMongoService userMongoService;
	private final OrgMongoService orgMongoService;
	private final UserOrgCacheCallService userOrgCacheCallService;

	@Autowired
	public MongoDataController(Errors errors, UserMongoService userMongoService, OrgMongoService orgMongoService,
							   UserOrgCacheCallService userOrgCacheCallService) {
		this.errors = errors;
		this.userMongoService = userMongoService;
		this.orgMongoService = orgMongoService;
		this.userOrgCacheCallService = userOrgCacheCallService;
	}

	@HttpMonitorLogger
	@GetMapping("/synchronizeUser")
	@ApiOperation("同步MongoDB用户信息")
	public ResponseEntity<Result<?>> synchronizeUser(@RequestParam(value = "user_id") @NotNull(message = "{NotNull.user.id}")Long userId,
													 @RequestParam(value = "type", defaultValue = "1") Integer type,
													 @RequestHeader HttpHeaders headers) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		if (type == 1) {
			this.userMongoService.conversionUser(userId, null, false, header);
		} else {
			this.userMongoService.remove(userId);
		}
		this.userOrgCacheCallService.flushUserInfo(userId, header.getRegionId(), 1);
		return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
	}

	@HttpMonitorLogger
	@GetMapping("/synchronizeOrg")
	@ApiOperation("同步MongoDB组织信息")
	public ResponseEntity<Result<?>> synchronizeOrg(@RequestParam(value = "org_id") @NotNull(message = "{NotNull.org.id}") Long orgId,
													@RequestParam(value = "type", defaultValue = "1") Integer type,
													@RequestHeader HttpHeaders headers) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		if (type == 1) {
			this.orgMongoService.conversionOrg(orgId, null, header);
		} else {
			this.orgMongoService.remove(orgId);
		}
		this.userOrgCacheCallService.flushOrgInfo(orgId, 1);
		return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
	}

	@HttpMonitorLogger
	@GetMapping("/addAllUser")
	@ApiOperation("添加所有用户到MongoDB")
	public ResponseEntity<Result<?>> AddAllUser(@RequestParam(value = "region_id", required = false) Long regionId,
												@RequestHeader HttpHeaders headers) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		this.userMongoService.addAllUser(regionId, header);
		return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
	}

	@HttpMonitorLogger
	@GetMapping("/addAllOrg")
	@ApiOperation("添加所有组织到MongoDB")
	public ResponseEntity<Result<?>> AddAllOrg(@RequestParam(value = "region_id", required = false) Long regionId,
											   @RequestHeader HttpHeaders headers) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		this.orgMongoService.addAllOrg(header, regionId);
		return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
	}
}
