package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.OrganizationTreeEntity;
import com.goodsogood.ows.model.vo.OrganizationTreeForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrganizationTreeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 组织树Controller
 * Created by huangyp on 2018/10/17.
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "组织树", tags = {"组织树管理"})
@RequestMapping("/org/tree")
public class OrganizationTreeController {

     @Autowired
     private OrganizationTreeService organizationTreeService;

     @Autowired
     private Errors errors;

     /**
      * 新建组织树
      *
      * <AUTHOR>
      * @create 2018-6-19
      **/
     @HttpMonitorLogger
     @PostMapping("/add")
     @ApiOperation("新建组织树")
     public ResponseEntity<Result<String>> add(@Valid @RequestBody OrganizationTreeForm orgFrom,
                                               BindingResult bindingResult,
                                               @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          // 超级管理员才具有操作权限
          if (!sysHeader.getUserId().equals(1L) && !sysHeader.getOid().equals(1L)) {
               return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (orgFrom.getOrganizationId() == null) {
               orgFrom.setOrganizationId(sysHeader.getOid());
          }
          if (orgFrom.getTreeType() == 1 && organizationTreeService.parentTreeIsExists(null)) {
               log.debug("新建组织树 - 父树已经存在请勿重复创建 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 610, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (orgFrom.getTreeType() == 2 && orgFrom.getOrgType() == null) {
               log.debug("新建组织树 - 组织类型不能为空 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 607, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (organizationTreeService.isExists(orgFrom.getOrganizationId(), orgFrom.getTreeName(), null)) {
               log.debug("新建组织树 - 该组织树名称已存在 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 608, HttpStatus.OK.value()), HttpStatus.OK);
          }
          try {
               OrganizationTreeEntity orgTree = new OrganizationTreeEntity();
               BeanUtils.copyProperties(orgFrom, orgTree);
               orgTree.setLastChangeUser(sysHeader.getUserId());
               orgTree.setRegionId(sysHeader.getRegionId());
               Boolean b = organizationTreeService.save(orgTree);
               return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
          } catch (Exception e) {
               log.error("新建组织树出错", e);
               throw new ApiException("新建组织树出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "添加组织树出错"));
          }
     }

     /**
      * 修改组织树
      *
      * <AUTHOR>
      * @create 2018-6-19
      **/
     @HttpMonitorLogger
     @PostMapping("/update")
     @ApiOperation("修改组织树")
     public ResponseEntity<Result<String>> update(@Valid @RequestBody OrganizationTreeForm orgFrom,
                                                  BindingResult bindingResult,
                                                  @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          // 超级管理员才具有操作权限
          if (!(sysHeader.getUserId().equals(1L) && sysHeader.getOid().equals(1L))) {
               return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (orgFrom.getOrganizationId() == null) {
               orgFrom.setOrganizationId(sysHeader.getOid());
          }
          if (orgFrom.getTreeId() == null) {
               log.debug("修改建组织树 - 组织树Id不能为空 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 609, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (orgFrom.getTreeType() == 2 && orgFrom.getOrgType() == null) {
               log.debug("修改组织树 - 组织类型不能为空 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 607, HttpStatus.OK.value()), HttpStatus.OK);
          } else if (organizationTreeService.isExists(orgFrom.getOrganizationId(), orgFrom.getTreeName(), orgFrom.getTreeId())) {
               log.debug("修改组织树 - 该组织树名称已存在 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 608, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (orgFrom.getTreeType() == 1 && organizationTreeService.parentTreeIsExists(orgFrom.getTreeId())) {
               log.debug("修改组织树 - 父树已经存在请勿重复创建 name:{}", orgFrom.getTreeName());
               return new ResponseEntity<>(new Result<>(errors, 610, HttpStatus.OK.value()), HttpStatus.OK);
          }

          try {
               OrganizationTreeEntity org = new OrganizationTreeEntity();
               BeanUtils.copyProperties(orgFrom, org);
               org.setLastChangeUser(sysHeader.getUserId());
               Boolean b = organizationTreeService.update(org);
               return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
          } catch (Exception e) {
               log.error("新建组织树出错", e);
               throw new ApiException("新建组织树出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "添加组织树出错"));
          }
     }


     /**
      * 删除组织树
      *
      * <AUTHOR>
      * @create 2018-6-19
      **/
     @HttpMonitorLogger
     @DeleteMapping("/delete")
     @ApiOperation("删除组织树")
     public ResponseEntity<Result<String>> delete(@RequestParam(value = "tree_id") @NotNull(message = "{NotNull.tree.id}") Long treeId,
                                                  @RequestParam(value = "org_id", required = false) Long orgId,
                                                  @RequestHeader HttpHeaders headers) {
          try {
               HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
               // 超级管理员才具有操作权限
               if (!(sysHeader.getUserId().equals(1L) && sysHeader.getOid().equals(1L))) {
                    return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
               }
               if (orgId == null) {
                    orgId = sysHeader.getOid();
               }
               OrganizationTreeEntity orgTree = new OrganizationTreeEntity();
               orgTree.setTreeId(treeId);
               orgTree.setStatus(0);
               orgTree.setOrganizationId(orgId);
               orgTree.setLastChangeUser(sysHeader.getUserId());
               Boolean b = organizationTreeService.delete(orgTree);
               return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
          } catch (Exception e) {
               log.error("删除组织树出错", e);
               throw new ApiException("删除组织树出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "删除组织树出错"));
          }
     }

     /**
      * 获取组织树详情
      *
      * @param treeId
      * @param headers
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/get")
     @ApiOperation("获取组织树详情")
     public ResponseEntity<Result<?>> get(@RequestParam(value = "tree_id") @NotNull(message = "${NotNull.tree.id}") Long treeId,
                                          @RequestHeader HttpHeaders headers) {
          // 超级管理员才具有操作权限
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          if (!(sysHeader.getUserId().equals(1L) && sysHeader.getOid().equals(1L))) {
               return new ResponseEntity<>(new Result<>(errors, 220, HttpStatus.OK.value()), HttpStatus.OK);
          }
          OrganizationTreeForm form = new OrganizationTreeForm();
          OrganizationTreeEntity resultList = organizationTreeService.getById(treeId);
          if (resultList != null) {
               BeanUtils.copyProperties(resultList, form);
          }
          return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
     }

     /**
      * 查询组织树列表
      *
      * @param orgId
      * @param headers
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/list")
     @ApiOperation("查询组织树列表")
     public ResponseEntity<Result<?>> list(@RequestParam(value = "org_id", required = false) Long orgId,
                                           @RequestParam(value = "is_show_parent", required = false, defaultValue = "0") int isShowParent,
                                           @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          if (orgId == null) {
               orgId = sysHeader.getOid();
          }
          List<OrganizationTreeForm> listForm = organizationTreeService.getList(orgId,
                  sysHeader.getOrgType(), sysHeader.getUserId(), sysHeader.getRegionId(), isShowParent);
          return new ResponseEntity<>(new Result<>(listForm, errors), HttpStatus.OK);
     }
}
