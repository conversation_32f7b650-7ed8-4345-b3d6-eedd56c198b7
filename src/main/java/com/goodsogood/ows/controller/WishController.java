package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.CommitmentEntity;
import com.goodsogood.ows.model.db.WishEntity;
import com.goodsogood.ows.model.vo.CommitmentForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.CommitmentService;
import com.goodsogood.ows.service.WishService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/wish")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class WishController{
    private final Errors errors;

    private final WishService wishService;

    @Autowired
    public WishController(Errors errors, WishService wishService) {
        this.errors = errors;
        this.wishService = wishService;
    }

    /**
     * 新增志愿服务
     * @param headers
     * @param wishEntity
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @ApiOperation(value = "新增志愿服务")
    @PostMapping("/append")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> addWish(@RequestHeader HttpHeaders headers, @Valid @RequestBody WishEntity wishEntity, BindingResult bindingResult) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        var oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        String orgName = sysHeader.getOrgName();
        wishEntity.setOrgId(oid);
        wishEntity.setOrgName(orgName);
        wishEntity.setCreateUser(wishEntity.getUserId());
        wishEntity.setCreateTime(LocalDateTime.now());
        wishEntity.setUpdateUser(wishEntity.getUserId());
        wishEntity.setUpdateTime(LocalDateTime.now());
        Result<Long> result = new Result<>(wishService.addWish(wishEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 修改志愿服务
     */
    @HttpMonitorLogger
    @ApiOperation(value = "修改承诺践诺")
    @PostMapping("/compile")
    @RepeatedCheck
    @Validated
    public ResponseEntity<Result<Long>> updateWish(@RequestHeader HttpHeaders headers, @Valid @RequestBody WishEntity wishEntity, BindingResult bindingResult) {
        Result<Long> result = new Result<>(wishService.updateWish(wishEntity), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 志愿服务删除
     */
    @HttpMonitorLogger
    @ApiOperation(value = "志愿服务删除")
    @GetMapping("/erasure")
    public ResponseEntity<Result<Boolean>> eliminateWish(@RequestHeader HttpHeaders headers, @RequestParam("ids") Long[] ids) {
        Result<Boolean> result = new Result<>(wishService.deleteWish(ids) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 志愿服务详情查询
     */
    @HttpMonitorLogger
    @ApiOperation(value = "志愿服务详情查询")
    @GetMapping("/obtain")
    public ResponseEntity<Result<WishEntity>> getWish(@RequestHeader HttpHeaders headers, @RequestParam("id") Long id) {
        Result<WishEntity> result = new Result<>(wishService.getWish(id), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 查看志愿服务列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "查看志愿服务列表")
    @PostMapping("/list")
    public ResponseEntity<Result<List<WishEntity>>> listWish(
            @RequestHeader HttpHeaders headers,
            @RequestBody CommitmentForm commitmentForm, @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize) {
        var sysHeader = HeaderHelper.buildMyHeader(headers);
        Long userId = sysHeader.getUserId();
        Long oid = sysHeader.getUoid() == null ? sysHeader.getOid() : sysHeader.getUoid();
        commitmentForm.setUserId(userId);
        commitmentForm.setOrgId(oid);
        return new ResponseEntity<>(new Result<>(wishService.listWish(commitmentForm,page,pageSize),errors),HttpStatus.OK);
    }


}
