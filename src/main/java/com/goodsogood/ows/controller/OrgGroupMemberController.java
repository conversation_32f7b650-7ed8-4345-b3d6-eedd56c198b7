package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgGroupMemberAddForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgGroupMemberService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @program: ows-user-center
 * @description: 党小组
 * @author: taiqian.Luo
 * @create: 2019-04-09 10:00
 **/
@RestController
@RequestMapping("/org-group-member")
@CrossOrigin(origins = "*", maxAge = 3600)
@Log4j2
public class OrgGroupMemberController {

    private final OrgGroupMemberService orgGroupMemberService;

    @Autowired
    public OrgGroupMemberController(OrgGroupMemberService orgGroupMemberService) {
        this.orgGroupMemberService = orgGroupMemberService;
    }

    /**
     * 添加党小组
     *
     * @param orgGroupMemberAddForm
     * @param bindingResult
     * @param headers
     * @return
     */
    @PostMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody OrgGroupMemberAddForm orgGroupMemberAddForm,
                                         BindingResult bindingResult, @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupMemberService.add(orgGroupMemberAddForm, header);
    }


    /**
     *  查询党小组信息
     * @param orgGroupId
     * @return
     */
    @GetMapping("/list/{org_group_id}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> list(@Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                          @PathVariable("org_group_id") long orgGroupId) {
        return orgGroupMemberService.list(orgGroupId);
    }

    /**
     * TODO 党小组成员-添加党小组成员-查询所有选择成员
     * @param param 手机号或者姓名
     * @param orgId
     * @param orgType
     * @param page
     * @param rows
     * @return
     */
    @GetMapping("/find-by-user")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findByUser(@Length(max =200,message = "{Length.param}")
              @RequestParam(name = "param", required = false) String param
            , @Range(min = 1, max = 99999999999L, message = "{Range.org.id}")
              @RequestParam("org_id") long orgId, @RequestParam("org_type") int orgType
            , @RequestParam(name = "page", defaultValue = "1") int page
            , @RequestParam(name = "rows", defaultValue = "10") int rows) {

        return orgGroupMemberService.findByUser(param ,orgId,orgType,rows,page);
    }


    @PostMapping("/delete/{org_group_member_id}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delete(@Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                            @PathVariable("org_group_member_id") long orgGroupMemberId, @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupMemberService.delete(orgGroupMemberId,header );
    }

    @PostMapping("/set-leader/{org_group_member_id}/{type}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> setGroupLeader(@Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                                    @PathVariable("org_group_member_id") long orgGroupMemberId,
                                                    @PathVariable(name = "type") Integer type,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupMemberService.setGroupLeader(orgGroupMemberId, (type == null? 1 : type), header);
    }
}
