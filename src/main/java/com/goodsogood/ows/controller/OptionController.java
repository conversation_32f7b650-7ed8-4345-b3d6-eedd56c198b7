package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.OptionEntity;
import com.goodsogood.ows.model.vo.OptionVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 数据字典controller
 *
 * <AUTHOR>
 * @date 2018-03-29
 */
@RestController
@RequestMapping("/uc/op")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "数据字典管理", tags = {"option"})
@Validated
public class OptionController {

     private final Errors errors;
     private final OptionService optionService;

     @Autowired
     public OptionController(Errors errors, OptionService optionService) {
          this.errors = errors;
          this.optionService = optionService;
     }

     /**
      * 根据code查询数据字典列表
      *
      * @param code
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/list")
     @ApiOperation("根据code查询数据字典列表")
     public ResponseEntity<Result<List<OptionEntity>>> getOption(@RequestParam @NotBlank(message = "{NotBlank.option.code}")
                                                                         String code) {
          List<OptionEntity> list = this.optionService.getList(code);
          return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/list-child")
     @ApiOperation("根据code查询数据字典列表-包含下级")
     public ResponseEntity<Result<List<OptionVO>>> getOptionChild(@RequestParam @NotBlank(message = "{NotBlank.option.code}")
                                                                  @Length(max = 10, message = "{Length.option.code}")
                                                                          String code) {
          List<OptionVO> childList = this.optionService.getChildList(code);
          return new ResponseEntity<>(new Result<>(childList, errors), HttpStatus.OK);
     }
}
