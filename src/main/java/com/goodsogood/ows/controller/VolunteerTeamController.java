package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.VolunteerTeamEntity;
import com.goodsogood.ows.model.vo.AddTeamForm;
import com.goodsogood.ows.model.vo.FindOrgListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UpdateStatusForm;
import com.goodsogood.ows.model.vo.VolunteerBatchForm;
import com.goodsogood.ows.model.vo.VolunteerCountForm;
import com.goodsogood.ows.model.vo.VolunteerResultForm;
import com.goodsogood.ows.model.vo.VolunteerTeamResultForm;
import com.goodsogood.ows.model.vo.VolunteerTeamSortForm;
import com.goodsogood.ows.model.vo.VolunteerTreeForm;
import com.goodsogood.ows.model.vo.user.VolunteerTeamBase;
import com.goodsogood.ows.service.VolunteerTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 志愿团体
 *
 * <AUTHOR>
 * @date 2020-06-23 11:09
 * @since 1.0.6
 **/
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "志愿团体 ", tags = {"志愿团体"})
@Validated
@RequestMapping("/volunteer/team")
public class VolunteerTeamController {

     private final Errors errors;
     private final VolunteerTeamService volunteerTeamService;

     @Autowired
     public VolunteerTeamController(Errors errors, VolunteerTeamService volunteerTeamService) {
          this.errors = errors;
          this.volunteerTeamService = volunteerTeamService;
     }


     /**
      * 新增志愿团体
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/add")
     @ApiOperation("新增志愿团体")
     public ResponseEntity<Result<?>> add(@Valid @RequestBody AddTeamForm form,
                                          BindingResult bindingResult,
                                          @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          int resultCode = this.volunteerTeamService.add(form, header);
          if (resultCode > 1) {
               return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
          }
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 修改志愿团体
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/update")
     @ApiOperation("修改志愿团体")
     public ResponseEntity<Result<?>> update(@Valid @RequestBody AddTeamForm form,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          int resultCode = this.volunteerTeamService.update(form, sysHeader);
          if (resultCode > 1) {
               return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
          }
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 志愿团体详细信息
      *
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/info")
     @ApiOperation("志愿团体详细信息")
     public ResponseEntity<Result<?>> info(@RequestParam(value = "org_id", required = false) Long orgId,
                                           @RequestParam(value = "volunteer_team_id", required = false) Long volunteerTeamId) {
          VolunteerTeamEntity infoEntity = this.volunteerTeamService.info(orgId, volunteerTeamId);
          return new ResponseEntity<>(new Result<>(infoEntity, errors), HttpStatus.OK);
     }

     /**
      * 启用/停用/删除志愿团体
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/update-status")
     @ApiOperation("启用/停用/删除志愿团体")
     public ResponseEntity<Result<?>> updateStatus(@Valid @RequestBody UpdateStatusForm form,
                                                   @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          int resultCode = this.volunteerTeamService.updateStatus(form, sysHeader);
          if (resultCode > 1) {
               return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
          }
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 调整志愿团体顺序
      *
      * @param form 请求参数
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/update-seq")
     @ApiOperation("调整志愿团体顺序")
     public ResponseEntity<Result<?>> updateSeq(@Valid @RequestBody VolunteerTeamSortForm form,
                                                @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          this.volunteerTeamService.updateSeq(form, sysHeader);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 查询志愿者团体/志愿者数量
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/find-volunteer-count")
     @ApiOperation("查询志愿者团体/志愿者数量")
     public ResponseEntity<Result<?>> findVolunteerCount(@RequestParam(value = "volunteer_team_id", required = false) Long volunteerTeamId,
                                                         @RequestParam(value = "org_id", required = false) Long orgId,
                                                         @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          VolunteerCountForm resultForm = this.volunteerTeamService.findVonlunteerCount(volunteerTeamId, orgId);
          return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
     }

     /**
      * 根据团体名称查询
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/find-volunteer-by-where")
     @ApiOperation("根据团体名称查询")
     public ResponseEntity<Result<?>> findVolunteerByWhere(@RequestParam(value = "org_id") Long orgId,
                                                           @RequestParam(value = "volunteer_team_name", required = false) String volunteerTeamName,
                                                           @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                           @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                           @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          Page<VolunteerResultForm> resultList = this.volunteerTeamService.findVolunteerTeamByWhere(orgId,
                  volunteerTeamName, page, pageSize);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 查询志愿团体树
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/tree")
     @ApiOperation("查询志愿团体树")
     public ResponseEntity<Result<?>> tree(@RequestParam(value = "volunteer_team_id") Long volunteerTeamId,
                                           @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          VolunteerTreeForm result = this.volunteerTeamService.tree(volunteerTeamId);
          return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
     }

     /**
      * 根据志愿者ID查询所在团体
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/find-team-by-where")
     @ApiOperation("根据志愿者ID查询所在团体")
     public ResponseEntity<Result<?>> findTeamByWhere(@RequestParam(value = "volunteer_user_id") Long volunteerUserId,
                                                      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                      @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                      @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          Page<VolunteerTeamResultForm> resultList = this.volunteerTeamService.findVolunteerTeamByWhere(volunteerUserId,
                  page, pageSize);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 根据志愿团体ID批量查询团体信息
      *
      * @param form 查询参数
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/find-team-by-list")
     @ApiOperation("根据志愿团体ID批量查询团体信息")
     public ResponseEntity<Result<?>> findTeamByList(@Valid @RequestBody VolunteerBatchForm form,
                                                     @RequestHeader HttpHeaders headers) {
//          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          List<VolunteerTeamBase> resultList = this.volunteerTeamService.findTeamByList(form);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 初始化志愿团体
      *
      * @param orgId    顶级组织ID
      * @param regionId 区县ID
      * @return success
      */
     @HttpMonitorLogger
     @GetMapping("/init-volunteer-team")
     @ApiOperation("初始化志愿团体")
     public ResponseEntity<Result<?>> initVolunteerTeam(@RequestParam(value = "org_id") Long orgId,
                                                        @RequestParam(value = "region_id") Long regionId) {
          this.volunteerTeamService.initVolunteerTeam(orgId, regionId);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 根据团体ID查询团体管理员
      *
      * @param volunteerTeamId 顶级组织ID
      * @return success
      */
     @HttpMonitorLogger
     @GetMapping("/find-team-mgr-by-id")
     @ApiOperation("根据团体ID查询团体管理员")
     public ResponseEntity<Result<?>> findTeamMgrById(@RequestParam(value = "volunteer_team_id") Long volunteerTeamId,
                                                      @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          List<VolunteerResultForm> resultList = this.volunteerTeamService.findTeamMgrById(volunteerTeamId, sysHeader);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 根据用户ID查询管理团体列表
      *
      * @param userId 用户ID
      * @return success
      */
     @HttpMonitorLogger
     @GetMapping("/find-team-by-user-id")
     @ApiOperation("根据用户ID查询管理团体列表")
     public ResponseEntity<Result<?>> findTeamByUserId(@RequestParam(value = "user_id") Long userId,
                                                       @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          List<VolunteerResultForm> resultList = this.volunteerTeamService.findTeamByUserId(userId, sysHeader);
          return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
     }

     /**
      * 根据组织ID查询志愿团体信息
      *
      * @param orgId 组织ID
      * @return success
      */
     @HttpMonitorLogger
     @GetMapping("/find-team-by-org-id")
     @ApiOperation("根据组织ID查询志愿团体信息")
     public ResponseEntity<Result<?>> findTeamByOrgId(@RequestParam(value = "org_id") Long orgId,
                                                      @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          VolunteerTeamBase base = this.volunteerTeamService.findTeamByOrgId(orgId);
          if (null == base) {
               return new ResponseEntity<>(new Result<>(errors, 6002, HttpStatus.OK.value()), HttpStatus.OK);
          }
          return new ResponseEntity<>(new Result<>(base, errors), HttpStatus.OK);
     }

     /**
      * 刷新志愿团体组织下级数量和用户数量
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/flush-vol-count")
     @ApiOperation("刷新志愿团体组织下级数量和用户数量")
     public ResponseEntity<Result<?>> flushVolCount(@RequestBody FindOrgListForm form) {
          this.volunteerTeamService.flushVolCount(form);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }


     /**
      * 导出根据名称查询志愿团体结果
      *
      * @param
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/download-volunteer-by-where")
     @ApiOperation("导出根据名称查询志愿团体结果")
     public void downloadVolunteerByWhere(@RequestParam(value = "org_id") Long orgId,
                                          @RequestParam(value = "volunteer_team_name", required = false) String volunteerTeamName,
                                          HttpServletResponse response,
                                          @RequestHeader HttpHeaders headers) throws IOException {
          HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
          this.volunteerTeamService.downloadVolunteerByWhere(orgId, volunteerTeamName, response);
     }

}
