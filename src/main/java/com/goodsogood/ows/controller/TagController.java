package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.TagEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TagEditForm;
import com.goodsogood.ows.model.vo.TagForm;
import com.goodsogood.ows.model.vo.UserOrgTagVO;
import com.goodsogood.ows.service.TagService;
import com.goodsogood.ows.service.UserMongoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 标签
 *
 * <AUTHOR>
 * @date 2018-03-23
 */
@RestController
@RequestMapping("/uc/tag")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "标签管理", tags = {"tag"})
@Validated
public class TagController {

    private final Errors errors;
    private final TagService tagService;
    private final UserMongoService userMongoService;

    @Autowired
    public TagController(Errors errors,
                         TagService tagService,
                         UserMongoService userMongoService) {
        this.errors = errors;
        this.tagService = tagService;
        this.userMongoService = userMongoService;
    }

    /**
     * 新增标签、标签组
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新增标签、标签组")
    public ResponseEntity<Result<?>> addTag(@Valid @RequestBody TagForm form,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        TagEntity entity = new TagEntity();
        // 新增标签组时，parentId默认为0
        entity.setParentId(0L);
        entity.setName(form.getName());
        entity.setStatus(Constants.STATUS_YES);
        // 新增标签类型
        entity.setTagType(form.getTagType());
        entity.setLastChangeUser(header.getUserId());
        entity.setBelong(form.getBelong());
        int flag = this.tagService.addTag(entity, header.getType(), header.getOid(), header.getRegionId());
        if (flag == 2) {
            log.debug("存在同名的标签");
            return new ResponseEntity<>(new Result<>(errors, 145, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("新增标签成功 -> {}", entity.getTagId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 修改标签、标签组
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation("修改标签、标签组")
    public ResponseEntity<Result<String>> updateTag(@Valid @RequestBody TagForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 判断标签类型可不可以更改
        TagEntity tag = this.tagService.getTag(form.getTagId());
        if (!tag.getTagType().equals(Constants.TAG_TYPE_DEFINED)) {
            log.debug("标签类型不属于自定义类型，不能编辑");
            return new ResponseEntity<>(new Result<>(errors, 3604, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 标签所属不能修改
        if (!tag.getBelong().equals(form.getBelong())) {
            log.debug("标签所属不能修改");
            return new ResponseEntity<>(new Result<>(errors, 3606, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 判断标签类型执行对应操作
        int i =0;
        TagEntity tagEntity = new TagEntity();
        tagEntity.setTagId(form.getTagId());
        tagEntity.setName(form.getName());
        tagEntity.setBelong(tag.getBelong());
        tagEntity.setLastChangeUser(header.getUserId());
        tagEntity.setUpdateTime(new Date());
        if(tag.getBelong()==3){
            //活动标签修改
            i = this.tagService.updateMeetingTagByWhere(header.getRegionId(),tagEntity);
        }else{
            //非活动标签修改
            i = this.tagService.updateTagByWhere(tagEntity, header);
        }
        if (i == 2) {
            log.debug("存在同名的标签");
            return new ResponseEntity<>(new Result<>(errors, 145, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("修改标签完成 -> {}", i);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 删除标签
     *
     * @param id
     * @return
     */
    @HttpMonitorLogger
    @DeleteMapping("/del/{id}")
    @ApiOperation("删除标签")
    public ResponseEntity<Result<String>> deleteTag(@PathVariable
                                                    @NotNull(message = "{NotNull.tag.id}")
                                                    @Range(max = 9999999999L, message = "{Range.tag.id}") long id,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 判断标签类型执行对应操作
        TagEntity tag = this.tagService.getTag(id);
        if(tag.getBelong()==3){
            //活动标签的删除
            this.tagService.deleteMeetingTagByWhere(header.getRegionId(),id);
        }else {
            //非活动标签的删除
            List<Long> userList = this.tagService.deleteTagByWhere(id);
            this.userMongoService.conversionUser(userList, header);
        }
        log.debug("删除标签完成");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询所有人员标签
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getUserTag")
    @ApiOperation("查询所有人员标签")
    public ResponseEntity<Result<List<TagEntity>>> getUserTag(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        // 系统保留 人员标签，parentId为0，tag_type为1
        TagEntity entity = this.tagService.getUserTag(Constants.PARENT_CODE, Constants.TAG_TYPE_DEFAULT, header.getType(), header.getOid());
        List<TagEntity> list = this.tagService.getUserTagList(entity.getTagId(), header.getType(), header.getOid());
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);

    }

    /**
     * 查询所有标签、标签组
     *
     * @param type 1-标签 2-标签组
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getTag")
    @ApiOperation("查询标签、标签组")
    public ResponseEntity<Result<?>> getTagByWhere(@RequestParam @NotNull(message = "{NotNull.tag.type}") Integer type,
                                                   @RequestParam @NotNull(message = "{NotNull.tag.id}") Long tagId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (type == 2) {
            tagId = 0L;
        }
        List<TagEntity> tagEntityPage = this.tagService.getTagByPage(header.getType(), header.getOid(), tagId);
        return new ResponseEntity<>(new Result<>(tagEntityPage, errors), HttpStatus.OK);
    }

    /**
     * 批量新增、修改标签
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/batch-edit")
    @ApiOperation("批量新增、修改标签")
    public ResponseEntity<Result<?>> batchEdit(@Valid @RequestBody TagEditForm form,
                                               BindingResult bindingResult,
                                               @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int flag = this.tagService.batchEdit(form, header.getUserId(), header.getType(), header.getOid());
        if (flag == 3) {
            log.debug("系统保留标签不允许删除");
            return new ResponseEntity<>(new Result<>(errors, 130, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (flag == 2) {
            return new ResponseEntity<>(new Result<>(errors, 136, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("批量新增、修改标签成功");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 获取党费标签
     *
     * @param tagType
     * @param orgId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getTagByType")
    @ApiOperation("获取标签")
    public ResponseEntity<Result<?>> getTagByType(@RequestParam("tag_type") @NotBlank(message = "{NotNull.tag.type}") String tagType,
                                                  @RequestParam("org_id") @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                  @RequestParam("user_id") @NotNull(message = "{NotNull.user.id}") Long userId,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserOrgTagVO> tags = this.tagService.getTagByType(tagType, orgId, userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(tags, errors), HttpStatus.OK);
    }

    /**
     * 查询标签列表
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getTagList")
    @ApiOperation("查询标签列表")
    public ResponseEntity<Result<?>> getTagList(@RequestParam(value = "tag_name", required = false) String tagName,
                                                @RequestParam(value = "org_id") @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                @RequestParam(value = "belong") @NotNull(message = "{NotBlank.tag.belong}") Integer belong,
                                                @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                @RequestParam(value = "filter", required = false,defaultValue = "2") Integer filter,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<UserOrgTagVO> tagList = this.tagService.getTagList(tagName, orgId, belong, new PageNumber(page,
                pageSize), header.getRegionId(),filter);
        return new ResponseEntity<>(new Result<>(tagList, errors), HttpStatus.OK);
    }

    /**
     * 查询活动标签列表
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/get/meetingTag/page")
    @ApiOperation("查询活动标签列表")
    public ResponseEntity<Result<?>> getMeetingTag(@RequestParam(value = "tag_name", required = false) String tagName,
                                                @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<UserOrgTagVO> tagList = this.tagService.getMeetingTagList(tagName,3, new PageNumber(page,
                pageSize), header.getRegionId());
        return new ResponseEntity<>(new Result<>(tagList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get-public-tag")
    @ApiOperation("查询公共标签列表")
    public ResponseEntity<Result<?>> getPublicTag(@RequestParam(value = "belong", required = false) Integer belong,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<TagEntity> publicTag = this.tagService.getPublicTag(header.getRegionId(), belong);
        return new ResponseEntity<>(new Result<>(publicTag, errors), HttpStatus.OK);
    }


    @ApiOperation("查询云区专用标签")
    @HttpMonitorLogger
    @GetMapping("/queryEcpTag")
    public ResponseEntity<Result<List<TagEntity>>> queryEcpTag(@RequestParam(value="tag_type",defaultValue = "12",required = false) Integer tagType,
                                                               @RequestParam(value="name",required = false) String name,
                                                       @RequestHeader HttpHeaders header) {
        HeaderHelper.SysHeader head = HeaderHelper.buildMyHeader(header);
        List<TagEntity> list =  tagService.queryEcpTag(tagType,name,header);
        return new ResponseEntity(new Result(list, errors), HttpStatus.OK);
    }

}
