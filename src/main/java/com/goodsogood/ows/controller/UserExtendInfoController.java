package com.goodsogood.ows.controller;

import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.UserExtendInfoService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/11/21
 * @Description: 用户扩展信息
 **/
@RestController
@RequestMapping("/user-extend-info")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class UserExtendInfoController {

    private final UserExtendInfoService userExtendInfoService;

    @Autowired
    public UserExtendInfoController(UserExtendInfoService userExtendInfoService) {
        this.userExtendInfoService = userExtendInfoService;
    }

    /**
     * 查询是否专职党办，是否专职纪检
     */
    @GetMapping("/is-full-time/{user_id}")
    public ResponseEntity<Result<?>> findIsFullTime(@PathVariable(name = "user_id") @Range(min = 1, message = "{NotNull.limit.userId}") long userId) {
        return new ResponseEntity<>(userExtendInfoService.findIsFullTime(userId), HttpStatus.OK);
    }


}
