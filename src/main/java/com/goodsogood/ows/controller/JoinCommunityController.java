package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.EvalHeaderHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.CommunityCheckForm;
import com.goodsogood.ows.model.vo.CommunityEditForm;
import com.goodsogood.ows.model.vo.CommunityUserListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.JoinCommunityService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> ruoyu
 * @date : 2021/7/6
 * 进云区相关
 */
@RestController
@RequestMapping("/jo-co")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class JoinCommunityController {

    private final Errors errors;
    private final JoinCommunityService joinCommunityService;

    @Autowired
    public JoinCommunityController(Errors errors, JoinCommunityService joinCommunityService) {
        this.errors = errors;
        this.joinCommunityService = joinCommunityService;
    }

    /**
     * 判断用户状态
     *
     * @throws IOException
     */
    @GetMapping("/ck")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> check(@RequestParam("user_id") Long userId,
                                           @RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        CommunityCheckForm communityCheckForm = joinCommunityService.check(sysHeader, userId);
        return new ResponseEntity<>(new Result<>(communityCheckForm, errors), HttpStatus.OK);
    }

    /**
     * 获取二维码 二维码中只存储user_id
     */
    @GetMapping("/img")
    @HttpMonitorLogger
    public void getImg(@RequestHeader HttpHeaders headers,
                       HttpServletResponse response) throws Exception {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        joinCommunityService.getImg(response, sysHeader);
    }

    /**
     * 游客党员编辑信息
     */
    @PostMapping("/edit")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> edit(@Valid @RequestBody CommunityEditForm editForm,
                                          BindingResult bindingResult,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        joinCommunityService.editVisitor(sysHeader, editForm);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 加入进小区支部
     */
    @GetMapping("/join")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> join(@RequestHeader HttpHeaders headers,
                                          @RequestParam("user_id") Long userId,
                                          @RequestParam("type")
                                          @Range(min = 1, max = 2, message = "非法加入类型") Integer type,
                                          @RequestParam(value = "address", required = false) String address) throws Exception {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        joinCommunityService.joinPeople(sysHeader, userId, type, address);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 小区支部人员列表
     */
    @GetMapping("/list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        CommunityUserListForm communityUserListForm = joinCommunityService.list(sysHeader);
        return new ResponseEntity<>(new Result<>(communityUserListForm, errors), HttpStatus.OK);
    }

    /**
     * 切换人员状态 由退回为初始状态
     */
    @GetMapping("/reset")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> reset(@RequestHeader HttpHeaders headers,
                                           @RequestParam("user_id") Long userId) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        joinCommunityService.reset(userId, sysHeader);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询标签列表
     */
    @GetMapping("/tag/list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> tagList(@RequestHeader HttpHeaders headers,
                                             @RequestParam("type") Integer type) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        List<CommunityCheckForm.CommunityUserTagForm> list = joinCommunityService.tagList(type, sysHeader);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 编辑用户进小区标签
     */
    @GetMapping("/tag/edit")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> tagEdit(@RequestHeader HttpHeaders headers,
                                             @RequestParam(name = "ids",required = false) String ids) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        joinCommunityService.tagEdit(ids, sysHeader);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
