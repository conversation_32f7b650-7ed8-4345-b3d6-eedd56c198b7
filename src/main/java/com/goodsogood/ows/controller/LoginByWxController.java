package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.db.UserThirdEntity;
import com.goodsogood.ows.model.vo.GuestOrgResultForm;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.OrgNameResultForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.RoleMenuForm;
import com.goodsogood.ows.service.UserForWxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 微信端登录查询相关信息controller
 *
 * <AUTHOR>
 * @date 2019-04-10 10:52:35
 */
@RestController
@RequestMapping("/login-by-wx")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "微信端登录查询相关信息", tags = {"login-by-wx"})
@Validated
public class LoginByWxController {


     private final Errors errors;
     private final UserForWxService userForWxService;

     @Value("${login.auth-code.usable}")
     private boolean authCodeUsable = true;

     @Autowired
     public LoginByWxController(Errors errors, UserForWxService userForWxService) {
          this.errors = errors;
          this.userForWxService = userForWxService;
     }

     /**
      * 根据oid和thToken查询第三方用户信息
      *
      * @param oid     关联的组织id
      * @param thToken 第三方token（微信为openId）
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/user-third/{oid}")
     @ApiOperation("根据oid和thToken查询第三方用户信息")
     public ResponseEntity<Result<?>> userThirdInfo(
             @PathVariable("oid") long oid,
             @RequestParam(value = "th_token") String thToken) {
          log.info("根据oid和thToken查询第三方用户信息.userThirdInfo oid:{},th_token:{}", oid, thToken);
          UserThirdEntity userThirdEntity = this.userForWxService.findUserThirdInfoByToken(thToken, oid);
          log.debug("根据oid和thToken查询第三方用户信息.userThirdInfo Result:{}", Utils.toJson(userThirdEntity));
          return new ResponseEntity<>(new Result<>(userThirdEntity, errors), HttpStatus.OK);
     }

     /**
      * 根据uid查询用户信息 用户名和密码
      *
      * @param uid 用户id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/user/{uid}")
     @ApiOperation("根据uid查询用户信息 用户名和密码")
     public ResponseEntity<Result<?>> userInfo(
             @PathVariable("uid") long uid) {
          log.info("根据uid查询用户信息 用户名和密码.userInfo uid:{}", uid);
          // 通过token查询用户基本信息，返回用户的密码
          UserEntity userEntity = this.userForWxService.findUserInfoById(uid);
          log.debug("根据uid查询用户信息 用户名和密码.userInfo Result:{}", Utils.toJson(userEntity));
          return new ResponseEntity<>(new Result<>(userEntity, errors), HttpStatus.OK);
     }

     /**
      * 根据uid type 查询用户所在组织
      *
      * @param uid 用户id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/user-oids/{uid}/{type}")
     @ApiOperation("根据uid type 查询用户所在组织")
     public ResponseEntity<Result<?>> userOids(@PathVariable("uid") long uid, @PathVariable("type") int type,
                                               @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          log.info("根据uid type 查询用户所在组织.userOids uid:{},type:{}", uid, type);
          List<GuestOrgResultForm> guestOrgResultFormList = this.userForWxService.getOidList(uid, type, header.getRegionId());
          log.debug("根据uid type 查询用户所在组织.Result:{}", Utils.toJson(guestOrgResultFormList));
          return new ResponseEntity<>(new Result<>(guestOrgResultFormList, errors), HttpStatus.OK);
     }

     /**
      * 根据oid查询组织信息
      *
      * @param oid 组织id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/org/{oid}")
     @ApiOperation("查询组织信息")
     public ResponseEntity<Result<?>> OrganizationInfo(
             @PathVariable("oid") long oid) {
          log.info("查询组织信息.OrganizationInfo oid:{}", oid);
          OrganizationEntity organizationEntity = this.userForWxService.findOrganizationInfo(oid);
          log.debug("查询组织信息.userOids Result:{}", Utils.toJson(organizationEntity));
          return new ResponseEntity<>(new Result<>(organizationEntity, errors), HttpStatus.OK);
     }


     /**
      * 根据uid查询用户标签
      *
      * @param uid 用户id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/tag/{uid}/{oid}/{type}")
     @ApiOperation("根据uid查询用户标签")
     public ResponseEntity<Result<?>> userTags(
             @PathVariable("uid") long uid, @PathVariable("oid") long oid, @PathVariable("type") int type) {
          log.info("根据uid查询用户标签.userTags uid:{},type:{}", uid, type);
          String tags = this.userForWxService.findTags(uid, oid, type);
          log.debug("根据uid查询用户标签.userTags Result:{}", tags);
          return new ResponseEntity<>(new Result<>(tags, errors), HttpStatus.OK);
     }


     /**
      * 根据oid所有下级组织
      *
      * @param oid 组织id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/all-org/{oid}/{org_level}")
     @ApiOperation("根据oid所有下级组织")
     public ResponseEntity<Result<?>> allOrg(@PathVariable("oid") long oid, @PathVariable("org_level") String orgLevel) {
          log.info("根据oid所有下级组织.allOrg oid:{},orgLevel:{}", oid, orgLevel);
          List<OrgNameResultForm> orgNameResultForms = this.userForWxService.findAllOrg(oid, orgLevel);
          log.debug("根据oid所有下级组织.userTags Result:{}", orgNameResultForms);
          return new ResponseEntity<>(new Result<>(orgNameResultForms, errors), HttpStatus.OK);
     }

     /**
      * 查询用户是管理员的组织
      *
      * @param uid 用户id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/manage-org-ids/{uid}")
     @ApiOperation("根据uid查询用户是管理员的组织")
     public ResponseEntity<Result<?>> manageOrgIds(@PathVariable("uid") long uid,
                                                   @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          log.info("根据uid查询用户是管理员的组织.allOrg uid:{}", uid);
          List<Long> manageOrgIds = this.userForWxService.findManageOrgIds(uid, header.getRegionId());
          log.debug("根据uid查询用户是管理员的组织.userTags Result:{}", manageOrgIds);
          return new ResponseEntity<>(new Result<>(manageOrgIds, errors), HttpStatus.OK);
     }

     /**
      * 查询用户的角色信息和菜单
      *
      * @param uid  用户id
      * @param oid  公众号关联的组织id
      * @param uoid 用户所在组织id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @GetMapping("/role-list/{oid}/{uoid}/{uid}")
     @ApiOperation("查询用户的角色信息和菜单")
     public ResponseEntity<Result<?>> findRoleListByUser(@PathVariable("uid") long uid,
                                                         @PathVariable("oid") long oid,
                                                         @PathVariable("uoid") long uoid,
                                                         @RequestParam(value = "is_manage") Integer isManage,
                                                         @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          log.info("查询用户的角色信息和菜单.allOrg oid:{},uoid:{},uid:{},isManage:{}", oid, uoid, uid, isManage);
          RoleMenuForm roleMenuForm = this.userForWxService.findRoleListByUser(oid, uoid, uid, isManage,
                  header.getRegionId());
          log.debug("查询用户的角色信息和菜单.findRoleListByUser Result:{}", roleMenuForm);
          return new ResponseEntity<>(new Result<>(roleMenuForm, errors), HttpStatus.OK);
     }

     /**
      * 登录成功后 需要添加缓存的信息
      * 1.菜单对应url存入redis
      * 2.用户信息
      *
      * @param menuFormList 菜单form
      * @param tokenId      登录id
      * @return ResponseEntity
      */
     @HttpMonitorLogger
     @PostMapping("/login-after-token-cache/{token-id}")
     @ApiOperation(" 菜单对应url存入redis")
     public ResponseEntity<Result<?>> loginAfterTokenCache(@PathVariable("token-id") String tokenId,
                                                           @RequestBody List<MenuForm> menuFormList,
                                                           @RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          log.info(" 菜单对应url存入redis.menuFormList tokenId:{},menuFormList:{}", tokenId, Utils.toJson(menuFormList));
          this.userForWxService.loginAfterTokenCache(menuFormList, tokenId, header.getRegionId());
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }


}
























