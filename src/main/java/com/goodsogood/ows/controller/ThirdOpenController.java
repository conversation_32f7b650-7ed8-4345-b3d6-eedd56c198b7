package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.AddDynamicFields;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldType;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.ecp.QueryEcpOrgForm;
import com.goodsogood.ows.model.vo.ecp.UserEcpNickForm;
import com.goodsogood.ows.model.vo.ecp.UserIdsVO;
import com.goodsogood.ows.service.ThirdOpenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/third")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "进云区控制层", tags = {"进云区"})
public class ThirdOpenController {

    private final Errors errors;
    private final ThirdOpenService thirdOpenService;

    @Autowired
    public ThirdOpenController(Errors errors,
                               ThirdOpenService thirdOpenService) {
        this.errors = errors;
        this.thirdOpenService = thirdOpenService;
    }

    @HttpMonitorLogger
    @ApiOperation("云区组织列表")
    @PostMapping(value = "/org/list")
    public ResponseEntity<Result<?>> getThirdOrgList(@RequestBody QueryEcpOrgForm queryForm,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用云区组织列表接口, QueryEcpOrgForm -> [{}], orgType -> [{}]", queryForm, header.getOrgType());
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getEcpOrgList(queryForm, header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询用户的状态")
    @GetMapping(value = "/user/status")
    public ResponseEntity<Result<?>> getThirdUserStatus(@RequestParam(value = "user_id") Long userId,
                                                        @RequestParam(value = "org_id", required = false) Long orgId) {
        log.debug("调用查询用户的状态接口, userId -> [{}], orgId -> [{}]", userId, orgId);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getThirdUserStatus(userId, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("人员加入云区")
    @GetMapping(value = "/user/add")
    public ResponseEntity<Result<?>> getThirdUserAdd(@RequestParam(value = "user_id") Long userId,
                                                     @RequestParam(value = "org_id") Long orgId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用人员加入云区接口, userId -> [{}], orgId -> [{}], regionId -> [{}]", userId, orgId, header.getRegionId());
        this.thirdOpenService.thirdUserOrgAdd(userId, orgId, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("根据人员查询信息")
    @PostMapping(value = "/user/info")
    public ResponseEntity<Result<?>> getThirdUserInfo(@RequestBody UserIdsVO userIdsVO,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用根据人员查询信息接口, UserIdsVO -> [{}]", userIdsVO);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getUserInfo(userIdsVO, header.getRegionId()), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("根据组织查询信息")
    @PostMapping(value = "/org/info")
    public ResponseEntity<Result<?>> getThirdOrgInfo(@RequestBody UserIdsVO userIdsVO,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用根据组织查询信息接口, orgIds -> [{}]", userIdsVO.getOrgIds());
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getOrgInfo(userIdsVO.getOrgIds(), header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("根据组织查询人员列表(批量)")
    @PostMapping(value = "/org/user/list")
    public ResponseEntity<Result<?>> getThirdUserInfoByOrg(@RequestBody UserIdsVO userIdsVO,
                                                           @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用根据组织查询人员列表接口, UserIdsVO -> [{}]", userIdsVO);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getUserInfoByOrg(userIdsVO, header.getRegionId()), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("判断当前用户是否有审核权限")
    @GetMapping(value = "/user/org/is-approval")
    public ResponseEntity<Result<?>> userOrgIsApproval(@RequestParam(value = "user_id") Long userId,
                                                       @RequestParam(value = "org_id", required = false) Long orgId,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用判断当前用户是否有审核权限接口, userId -> [{}], orgId -> [{}], regionId -> [{}]", userId, orgId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.userOrgIsApproval(userId, orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询用户在该区县的是哪些组织的管理员")
    @GetMapping(value = "/user/org/admin")
    public ResponseEntity<Result<?>> queryUserOrgAdmin(@RequestParam(value = "user_id") Long userId,
                                                       @RequestParam(value = "region_id", required = false) Long regionId,
                                                       @RequestParam(value = "role_type", required = false) Integer roleType,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        log.debug("查询用户在该区县的是哪些组织的管理员, userId -> [{}], regionId -> [{}]", userId, regionId);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.queryUserOrgAdmin(userId, regionId,roleType), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询组织下面的管理员")
    @GetMapping(value = "/org/admin")
    public ResponseEntity<Result<?>> queryOrgAdmin(@RequestParam(value = "org_id") Long orgId) {
        log.debug("查询组织下面的管理员, orgId -> [{}]", orgId);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.queryOrgAdmin(orgId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询人员加入云区组织数量")
    @GetMapping(value = "/user/org/num")
    public ResponseEntity<Result<?>> queryUserOrgNum(@RequestParam(value = "user_id", required = false) Long userId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        userId = Objects.isNull(userId) ? header.getUserId() : userId;
        log.debug("查询人员加入云区组织数量, userId -> [{}]", userId);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.findUserOrgNum(userId, header.getRegionId(), header.getOrgType()), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询是不是第一次进入云区")
    @GetMapping(value = "/is-first")
    public ResponseEntity<Result<?>> isFirst(@RequestParam(value = "user_id", required = false) Long userId,
                                             HttpServletRequest request,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        userId = Objects.isNull(userId) ? header.getUserId() : userId;
        log.debug("查询是不是第一次进入云区, userId -> [{}]", userId);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.isFirst(userId, header, request), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("判断当前用户是否有终审权限")
    @GetMapping(value = "/user/org/is-final-approval")
    public ResponseEntity<Result<?>> userOrgIsFinalApproval(@RequestParam(value = "user_id") Long userId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用判断当前用户是否有终审权限接口, userId -> [{}], regionId -> [{}]", userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.userOrgIsFinalApproval(userId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("云区组织用户列表")
    @GetMapping(value = "/org/user-list")
    public ResponseEntity<Result<?>> getThirdOrgUserList(@RequestParam(value = "orgId") Long orgId,
                                                         @RequestParam(value = "nickName", required = false) String nickName,
                                                         @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                                         @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("调用云区组织用户列表接口, orgId -> [{}], nickName -> [{}]", orgId, nickName);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.getEcpOrgUserLike(orgId, nickName, header, page, pageSize), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查询加入云区人数")
    @GetMapping(value = "/user/total")
    public ResponseEntity<Result<?>> getEcpUserTotal(@RequestParam(value = "orgIds", required = false) List<Long> orgIds,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询加入云区人数, orgId -> [{}]", orgIds);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.selectUserCount(orgIds, header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("设置云区昵称")
    @PostMapping(value = "/user/set-nick-name")
    public ResponseEntity<Result<?>> setNickName(@RequestBody UserEcpNickForm form,
                                                 @RequestHeader HttpHeaders headers) {
        log.debug("设置云区昵称, UserEcpNickForm -> [{}]", form);
        return new ResponseEntity<>(new Result<>(this.thirdOpenService.setNickName(form.getNickName(), headers), errors), HttpStatus.OK);
    }

}
