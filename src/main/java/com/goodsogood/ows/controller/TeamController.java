package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.ProcessEntity;
import com.goodsogood.ows.model.db.TeamEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.TeamService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: Stone
 * @CreateTime: 2023/10/14 14:58
 * @Description:
 */
@RestController
@RequestMapping("/fc")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class TeamController {
    private final Errors errors;
    private final TeamService teamService;

    public TeamController(Errors errors, TeamService teamService) {
        this.errors = errors;
        this.teamService = teamService;
    }

    /**
     * 新增突击队
     */
    @HttpMonitorLogger
    @PostMapping("/addTeam")
    @ApiOperation("新增突击队")
    @RepeatedCheck
    public ResponseEntity<Result<TeamEntity>> addTeamionPost(@RequestHeader HttpHeaders headers,
                                                                                @Valid @RequestBody TeamEntity entity,
                                                                                BindingResult result) {
        TeamEntity postTeamEntity = teamService.addTeamion(headers, entity);
        return new ResponseEntity<>(new Result<>(postTeamEntity, errors), HttpStatus.OK);
    }


    /**
     * 删除一个突击队
     */
    @HttpMonitorLogger
    @GetMapping("/deleteTeamById")
    @ApiOperation("删除突击队")
    public ResponseEntity<Result<Integer>> deleteTeamionPostById(@RequestHeader HttpHeaders headers,
                                                                       @RequestParam Long[] id) {
        return new ResponseEntity<>(new Result<>(teamService.deleteTeamById(id), errors), HttpStatus.OK);
    }

    /**
     * 更新一个示范区
     */
    @HttpMonitorLogger
    @PostMapping("/updateTeam")
    @ApiOperation("更新示范区")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> updateTeamionPost(@RequestHeader HttpHeaders headers,
                                                                   @Valid @RequestBody TeamEntity entity,
                                                                   BindingResult result) {
        return new ResponseEntity<>(new Result<>(teamService.updateTeam(headers, entity), errors), HttpStatus.OK);
    }


    /**
     * 查询突击队详情页面
     */
    @HttpMonitorLogger
    @GetMapping("/getTeamById")
    @ApiOperation("查询突击队详情页面")
    public ResponseEntity<Result<TeamEntity>> getTeamById(@RequestHeader HttpHeaders headers, @RequestParam Long id) {
        var data = teamService.getTeamById(id);
        if (data == null) {
            throw new ApiException("责任区[" + id + "]找不到", new Result<>(errors, 9404, HttpStatus.OK.value(), "责任区[" + id + "]"));
        }
        return new ResponseEntity<>(new Result<>(data, errors), HttpStatus.OK);
    }

    /**
     * 查询突击队
     */
    @HttpMonitorLogger
    @PostMapping("/getTeam")
    @ApiOperation("查询突击队")
    public ResponseEntity<Result<List<TeamEntity>>> getTeamionPostByCondition(@RequestHeader HttpHeaders headers,
                                                                                                 @Valid @RequestBody TeamEntity entity,
                                                                                                 @RequestParam(required = false) Integer page,
                                                                                                 @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                                                 BindingResult result) {
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 20 : pageSize;
        return new ResponseEntity<>(new Result<>(teamService.getTeam(headers, entity, page, pageSize), errors), HttpStatus.OK);

    }

    /**
     * 查询审核列表
     */
    @HttpMonitorLogger
    @PostMapping("/getProcessList")
    @ApiOperation("查询审核列表")
    public ResponseEntity<Result<List<ProcessEntity>>> getProcessList(@RequestHeader HttpHeaders headers,
                                                                      @Valid @RequestBody ProcessEntity entity,
                                                                      @RequestParam(required = false) Integer page,
                                                                      @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                      BindingResult result) {
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 20 : pageSize;
        return new ResponseEntity<>(new Result<>(teamService.getProcessList(headers, entity, page, pageSize), errors), HttpStatus.OK);

    }
    /**
     * 审核
     */
    @HttpMonitorLogger
    @PostMapping("/process")
    @ApiOperation("审核")
    public ResponseEntity<Result<Integer>> process(@RequestHeader HttpHeaders headers,
                                                             @Valid @RequestBody ProcessEntity entity,
                                                             BindingResult result) {
        Integer process = teamService.process(headers, entity);
        if (process==2){

            throw new ApiException( "暂无权限公开,请联系管理员!", new Result<>(errors, 220, HttpStatus.OK.value(), "暂无权限公开,请联系管理员!"));

        }
        return new ResponseEntity<>(new Result<>(process, errors), HttpStatus.OK);
    }

}
