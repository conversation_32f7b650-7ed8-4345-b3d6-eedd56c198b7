package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.OfficialAccountEntity;
import com.goodsogood.ows.model.vo.OfficialAccountBindingForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UpdateAuthorizerTreeForm;
import com.goodsogood.ows.model.vo.component.*;
import com.goodsogood.ows.service.OfficialAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 分级平台公众号 controller
 *
 * <AUTHOR>
 * @date 2018-11-06 10:40
 **/
@RestController
@RequestMapping("official_account")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(tags = "分级平台公众号")
@Validated
public class OfficialAccountController {

    private final Errors errors;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final OfficialAccountService officialAccountService;

    @Autowired
    public OfficialAccountController(Errors errors,
                                     SimpleApplicationConfigHelper simpleApplicationConfigHelper,
                                     OfficialAccountService officialAccountService) {
        this.errors = errors;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.officialAccountService = officialAccountService;
    }

    /**
     * 从头信息中获取组织ID
     *
     * @param headers HttpHeaders
     * @return 组织ID
     */
    private Long getOrgIdFromHttpHeader(HttpHeaders headers) {
        List<String> oidList = headers.get("_oid");
        if (oidList != null && !oidList.isEmpty()) {
            String oidString = oidList.get(0);
            return Long.parseLong(oidString);
        }
        throw new ApiException("_oid解析出错", new Result<>(errors, Global.Errors.ERROR_HEADER, HttpStatus.BAD_REQUEST.value()));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取预授权码信息")
    @GetMapping("pre_auth_code")
    public ResponseEntity<Result<PreAuthCodeInfoForm>> getPreAuthCode(@RequestHeader HttpHeaders headers) {
        PreAuthCodeInfoForm preAuthCodeInfoForm = officialAccountService.getPreAuthCodeByOrgId(getOrgIdFromHttpHeader(headers));
        if (preAuthCodeInfoForm == null) {
            return new ResponseEntity<>(new Result<>(errors, 9903, HttpStatus.OK.value()), HttpStatus.OK);
        }
        preAuthCodeInfoForm.setProjectName(officialAccountService.getProjectName(getOrgIdFromHttpHeader(headers)));
        return ResponseEntity.ok(new Result<>(preAuthCodeInfoForm, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取已绑定公众号基本信息")
    @GetMapping("is_bind")
    public ResponseEntity<Result<Boolean>> isBind(@RequestParam(name = "org_id") long orgId) {
        OfficialAccountEntity officialAccountEntity = officialAccountService.getByOrgId(orgId);
        return ResponseEntity.ok(new Result<>(officialAccountEntity != null, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取已绑定公众号基本信息")
    @GetMapping("authorizer_info")
    public ResponseEntity<Result<OfficialAccountEntity>> getOaInfo(@RequestHeader HttpHeaders headers) {
        OfficialAccountEntity officialAccountEntity = officialAccountService.getByOrgId(getOrgIdFromHttpHeader(headers));
        if (officialAccountEntity == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return ResponseEntity.ok(new Result<>(officialAccountEntity, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "绑定或更新公众号基本信息_微信中心用")
    @PostMapping("authorizer_info")
    public ResponseEntity<Result<Integer>> getOaInfo(@RequestBody AuthorizerInfoForm authorizerInfoForm) {
        officialAccountService.save(authorizerInfoForm);
        return ResponseEntity.ok(new Result<>(HttpStatus.OK.value(), errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "删除公众号基本信息_微信中心用")
    @PostMapping("unbind_authorizer_info")
    public ResponseEntity<Result<Integer>> deleteOaInfo(@RequestParam("authorizer_appid") String authorizerAppId) {
        officialAccountService.deleteOfficialAccountByAuthorizerAppId(authorizerAppId);
        return ResponseEntity.ok(new Result<>(HttpStatus.OK.value(), errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "更新公众号授权组织树权限列表")
    @PostMapping("authorizer_tree")
    public ResponseEntity<Result<Integer>> updateOaOrg(@RequestHeader HttpHeaders headers, @RequestBody OrgTypeForm orgTypeForm) {
        officialAccountService.updateOaOrgByOrgId(getOrgIdFromHttpHeader(headers), orgTypeForm);
        return ResponseEntity.ok(new Result<>(HttpStatus.OK.value(), errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "新增下级组织更新上级组织的授权列表")
    @PostMapping("update_authorizer_tree")
    public ResponseEntity<Result<Boolean>> updateAuthorizerTree(@Valid @RequestBody UpdateAuthorizerTreeForm updateAuthorizerTreeForm,
                                                                BindingResult bindingResult) {
        if (updateAuthorizerTreeForm.getType() == 0 || updateAuthorizerTreeForm.getType() == 2) {
            if (StringUtils.isEmpty(updateAuthorizerTreeForm.getOwn().getOrgName())) {
                throw new ApiException("组织名称不能为空", new Result<>(Global.Errors.VALID_ERROR.getCode(),
                        "组织名称不能为空", HttpStatus.BAD_REQUEST.value(), null));
            }
        }
        officialAccountService.updateAuthorizerTree(updateAuthorizerTreeForm);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取公众号授权组织树权限列表")
    @GetMapping("authorizer_tree")
    public ResponseEntity<Result<OrgTypeForm>> getOaOrg(@RequestHeader HttpHeaders headers) {
        OrgTypeForm orgTypeForm = officialAccountService.getOaOrgByOrgId(getOrgIdFromHttpHeader(headers));
        if (orgTypeForm == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return ResponseEntity.ok(new Result<>(orgTypeForm, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取已绑定公众号accessToken_推送消息用")
    @GetMapping("authorizer_access_token")
    public ResponseEntity<Result<AuthorizerAccessTokenInfoForm>> getAuthorizerAccessToken(@RequestParam("org_id") Long orgId) {
        AuthorizerAccessTokenInfoForm authorizerAccessTokenInfoForm = officialAccountService.getAuthorizerAccessTokenByOrgId(orgId);
        if (authorizerAccessTokenInfoForm == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return ResponseEntity.ok(new Result<>(authorizerAccessTokenInfoForm, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取微信登录参数")
    @GetMapping("oauth2_info")
    public ResponseEntity<Result<Oauth2InfoForm>> getOauth2Info(@RequestParam("org_id") Long orgId) {
        Oauth2InfoForm oauth2InfoForm = officialAccountService.getOauth2InfoByOrgId(orgId);
        if (oauth2InfoForm == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        oauth2InfoForm.setProjectName(officialAccountService.getProjectName(orgId));
        return ResponseEntity.ok(new Result<>(oauth2InfoForm, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "根据微信登录code获取oath2_access_token")
    @GetMapping("oauth2_access_token")
    public ResponseEntity<Result<Oauth2AccessTokenInfoForm>> getOauth2AccessToken(@RequestParam("org_id") Long orgId, @RequestParam("code") String code) {
        Oauth2AccessTokenInfoForm oauth2AccessTokenInfoForm = officialAccountService.getOauth2AccessToken(orgId, code);
        if (oauth2AccessTokenInfoForm == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return ResponseEntity.ok(new Result<>(oauth2AccessTokenInfoForm, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取js api ticket")
    @GetMapping("jsapi_ticket")
    public ResponseEntity<Result<JsApiTicketResponseForm>> getJsApiTicket(@RequestParam("org_id") Long orgId) {
        JsApiTicketResponseForm jsApiTicket = officialAccountService.getJsApiTicket(orgId);
        if (jsApiTicket == null) {
            return new ResponseEntity<>(new Result<>(errors, 222, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return ResponseEntity.ok(new Result<>(jsApiTicket, errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "解绑公众号")
    @PostMapping("unbind")
    public ResponseEntity<Result<Integer>> unbind(@RequestHeader HttpHeaders headers) {
        officialAccountService.deleteOfficialAccountByOrgId(getOrgIdFromHttpHeader(headers));
        return ResponseEntity.ok(new Result<>(HttpStatus.OK.value(), errors));
    }

    @HttpMonitorLogger
    @ApiOperation(value = "获取组织和公众号的绑定统计信息")
    @GetMapping("binding_statistical")
    public ResponseEntity<Result<OfficialAccountBindingForm>> getOfficialAccountBinding(@RequestHeader HttpHeaders headers) {
        String regionId = headers.get(HeaderHelper.OPERATOR_REGION).get(0);
        Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(Long.valueOf(regionId));
        OfficialAccountBindingForm officialAccountBindingForm = officialAccountService.getOfficialAccountBinding(orgData.getOrgId());
        return new ResponseEntity<>(new Result<>(officialAccountBindingForm, errors), HttpStatus.OK);
    }
}
