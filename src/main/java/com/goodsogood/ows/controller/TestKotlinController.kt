package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.TransferListForm
import com.goodsogood.ows.service.TestService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *
 * <AUTHOR>
 * @createTime 2023年03月22日 17:57:00
 */
@RestController
@RequestMapping("/test")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "测试相关接口", tags = ["测试相关接口"])
@Validated
class TestKotlinController @Autowired constructor(
    private val errors: Errors,
    private val testService: TestService
) {

    @HttpMonitorLogger
    @GetMapping("/tissue")
    @ApiOperation("组织地址信息转换")
    fun tissue(): ResponseEntity<Result<*>> {
        testService.tissueLocationData()
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }
}