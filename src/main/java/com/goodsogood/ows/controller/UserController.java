package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.AddDynamicFields;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.LogType;
import com.goodsogood.ows.common.MD5Helper;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldType;
import com.goodsogood.ows.configuration.ClientExceptionHandler;
import com.goodsogood.ows.configuration.EcsConfig;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.TogServicesConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.mapper.UserMapper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.BoundValueOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2018-03-21
 */
@RestController
@RequestMapping("/uc/user")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "用户管理", tags = {"user"})
@Validated
public class UserController {

    @Value("${tog-services.workflow}")
    private String workFlowServer;

    @Value("${send.url}")
    private String sendUrl;

    @Value("${send.client-id}")
    private String clientId;

    // 短信渠道ID
    @Value("${send.channel-id}")
    private String channelId;

    private final Errors errors;
    private final UserService userService;
    private final UserMapper userMapper;
    private final RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;
    private final OpenService openService;
    private final OptionService optionService;
    private final BroadcastService broadcastService;
    private final TogServicesConfig togServicesConfig;
    private final OrgUserCommonService orgUserCommonService;
    private final UserOrgCacheCallService userOrgCacheCallService;
    private final OrgUserService orgUserService;
    private final MessageService messageService;
    private final UserMongoService userMongoService;
    private final EcsService ecsService;
    private final EcsConfig ecsConfig;

    @Autowired
    public UserController(Errors errors, UserService userService, UserMapper userMapper, RestTemplate restTemplate, StringRedisTemplate redisTemplate,
                          OpenService openService, OptionService optionService, BroadcastService broadcastService, TogServicesConfig togServicesConfig,
                          OrgUserCommonService orgUserCommonService, UserOrgCacheCallService userOrgCacheCallService, OrgUserService orgUserService,
                          MessageService messageService, UserMongoService userMongoService, EcsService ecsService, EcsConfig ecsConfig) {
        this.errors = errors;
        this.userService = userService;
        this.userMapper = userMapper;
        this.restTemplate = restTemplate;
        this.redisTemplate = redisTemplate;
        this.openService = openService;
        this.optionService = optionService;
        this.broadcastService = broadcastService;
        this.togServicesConfig = togServicesConfig;
        this.orgUserCommonService = orgUserCommonService;
        this.userOrgCacheCallService = userOrgCacheCallService;
        this.orgUserService = orgUserService;
        this.messageService = messageService;
        this.userMongoService = userMongoService;
        this.ecsService = ecsService;
        this.ecsConfig = ecsConfig;
    }

    /**
     * 新增用户
     *
     * @param user
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/addUser")
    @ApiOperation("新增员工")
    public ResponseEntity<Result<?>> addUser(@Valid @RequestBody UserForm user, BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 监测手机号重复
        int isExist = this.userService.checkPhone(NumEncryptUtils.encrypt(user.getPhone(), 2), null);
        if (isExist > 0) {
            log.debug("该手机号码已经存在 -> phone:{}", user.getPhone());
            return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (!Utils.checkCertNumber(user.getCertType(), user.getCertNumber())) {
            if (Constants.CERT_NUMBER_IDENTITY == user.getCertType()) {
                log.debug("身份证号码格式错误 -> certNumber:{}", user.getCertNumber());
                return new ResponseEntity<>(new Result<>(errors, 149, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }
        UserEntity entity = new UserEntity();
        entity.setPassword(MD5Helper.getMD5(Constants.PASSWORD));
        entity.setName(user.getName());
        entity.setPhone(user.getPhone());
        entity.setCertType(user.getCertType());
        entity.setCertNumber(user.getCertNumber());
        entity.setGender(user.getGender());
        entity.setCensusType(user.getCensusType());
        entity.setNationality(user.getNationality());
        entity.setMarriage(user.getMarriage());
        entity.setEducation(user.getEducation());
        entity.setPoliticalType(user.getPoliticalType());
        entity.setEthnic(user.getEthnic());
        entity.setStatus(Constants.STATUS_YES);
        entity.setLastChangeUser(header.getUserId());

        if (!this.userService.addUser(entity, user, header.getType(), header.getOid(), header.getRegionId())) {
            log.debug("系统异常，新增员工失败");
            return new ResponseEntity<>(new Result<>(errors, 134, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("新增用户完成");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);

    }

    /**
     * 监测手机号重复
     *
     * @param phone
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/check-phone")
    @ApiOperation("监测手机号码重复")
    public ResponseEntity<Result<?>> checkPhone(@RequestParam
                                                @NotBlank(message = "{NotBlank.user.phone}")
                                                @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}")
                                                        String phone) {
        int isExist = this.userService.checkPhone(NumEncryptUtils.encrypt(phone, 2), null);
        if (isExist > 0) {
            log.debug("该手机号码已经存在 -> phone:{}", phone);
            return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 用户新增时监测手机号重复
     *
     * @param phone
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/add-check-phone")
    @ApiOperation("用户新增时监测手机号码重复")
    public ResponseEntity<Result<?>> addCheckPhone(@RequestParam
                                                   @NotBlank(message = "{NotBlank.user.phone}")
                                                   @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}")
                                                           String phone,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UserForm form = this.userService.addCheckPhone(NumEncryptUtils.encrypt(phone, 2), header.getType(), header.getOid());
        if (form != null && form.getUserId() != null) {
            log.debug("该手机号码已经在该组织下存在，提示 用户已存在 -> phone:{}", phone);
            return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            // 在其他组织是否存在
            int exist = this.userService.checkPhone(phone, null);
            UserForm existForm = new UserForm();
            // 存在
            if (exist > 0) {
                existForm = this.userService.findIdByPhone(phone);
                UserEntity entity = this.userService.selectOne(existForm.getUserId());
                existForm.setName(entity.getName());
                existForm.setPhone(entity.getPhone());
                existForm.setCertType(entity.getCertType());
                existForm.setCertNumber(entity.getCertNumber());
                existForm.setGender(entity.getGender());
                existForm.setCensusType(entity.getCensusType());//户籍类型
                existForm.setNativeProvince(entity.getNativeProvince());//籍贯一级区域
                existForm.setNativeCity(entity.getNativeCity());//籍贯二级区域
                existForm.setEducation(entity.getEducation());//学历
                existForm.setEthnic(entity.getEthnic());//民族
                existForm.setPoliticalType(entity.getPoliticalType());//政治面貌
                existForm.setFlag("2");
            } else {
                existForm.setFlag("1");
            }
            return new ResponseEntity<>(new Result<>(existForm, errors), HttpStatus.OK);
        }
    }

    /**
     * 修改用户信息
     *
     * @param form
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/updateUser")
    @ApiOperation("修改员工信息")
    public ResponseEntity<Result<?>> updateUser(@Valid @RequestBody UserForm form,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        //获取电话密文
        String encryptPhone = NumEncryptUtils.encrypt(form.getPhone(), 2);
        //如果电话不包含*（即更改了电话）
        if (!form.getPhone().contains("*")) {
            // 监测手机号重复
            int isExist = this.userService.checkPhone(encryptPhone, form.getUserId());
            if (isExist > 0) {
                log.debug("该手机号码已经存在 -> phone:{}", form.getPhone());
                return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
            }
            //手机不重复
            form.setPhoneSecret(NumEncryptUtils.numSecret(form.getPhone(), 2));
            form.setPhone(encryptPhone);
        } else {
            form.setPhoneSecret(null);
            form.setPhone(null);
        }
        if (!form.getCertNumber().contains("*")) { //身份证号发生改变
            if (!Utils.checkCertNumber(form.getCertType(), form.getCertNumber())) {
                if (Constants.CERT_NUMBER_IDENTITY == form.getCertType()) {
                    log.debug("身份证号码格式错误 -> certNumber:{}", form.getCertNumber());
                    return new ResponseEntity<>(new Result<>(errors, 149, HttpStatus.OK.value()), HttpStatus.OK);
                }
            } else {
                if (Constants.CERT_NUMBER_IDENTITY == form.getCertType()) {
                    form.setCertNumberSecret(NumEncryptUtils.numSecret(form.getCertNumber(), 1));
                    form.setCertNumber(NumEncryptUtils.encrypt(form.getCertNumber(), 1));
                }
            }
        } else {    //身份证没有发生改变
            form.setCertNumber(null);
            form.setCertNumberSecret(null);
        }
        if (!this.userService.editUser(form, header)) {
            log.debug("系统异常，修改用户信息失败");
            return new ResponseEntity<>(new Result<>(errors, 135, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            // 如果修改了姓名，广播到其他模块
            UserEntity userEntity = this.userService.selectOne(form.getUserId());
            if (!userEntity.getName().equals(form.getName())) {
                this.restTemplate.setErrorHandler(new ClientExceptionHandler());
                headers = HeaderHelper.setMyHttpHeader(headers, header);
                HttpEntity<String> entity = new HttpEntity<>(null, headers);
                try {
                    ResponseEntity<Result> result = this.restTemplate.exchange(
                            String.format("http://%s/user_change_name_callback?uid=%d&name=%s&time=%s",
                                    workFlowServer, form.getUserId(), form.getName(), new Date()),
                            HttpMethod.GET, entity, Result.class);
                    if (result.getStatusCode() != HttpStatus.OK || result.getBody().getCode() != 0) {
                        log.error("修改用户姓名调用工作流失败 ->{},{}", result.getStatusCodeValue(), result.getBody() != null ? result.getBody().getCode() : -1);
                    }
                    log.debug("修改用户姓名调用工作流成功 ->{},{}", result.getStatusCodeValue(), result.getBody().getCode());
                } catch (ApiException e) {
                    log.debug("result -> {}", e.getResult());
                } catch (IllegalStateException ise) {
                    log.error("未知错误", ise.getMessage(), ise);
                }
            }
        }
        log.debug("修改用户信息完成");

        // 调用学习系统订阅接口
        uesrInfoBroadcast(form, header.getOid());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * @param form
     */
    @Async
    @Transactional
    public void uesrInfoBroadcast(UserForm form, long oid) {
        BroadcastUserForm synchUserForm = new BroadcastUserForm();
        synchUserForm.setUserId(form.getUserId());
        synchUserForm.setName(form.getName());
        synchUserForm.setGender(this.optionService.getNameByKey(form.getGender(), "1002"));
        synchUserForm.setEducation(this.optionService.getNameByKey(form.getEducation(), "1003"));
        synchUserForm.setEthnic(this.optionService.getNameByKey(form.getEthnic(), "1004"));
        synchUserForm.setPoliticalType(this.optionService.getNameByKey(form.getPoliticalType(), "1013"));
        synchUserForm.setCensusType(this.optionService.getNameByKey(form.getCensusType(), "1011"));
        this.broadcastService.userInfoBroadcast(synchUserForm, null);
    }

    /**
     * 根据用户ID查询用户信息-修改时回显基本信息
     *
     * @param id
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserForm.class)
    @HttpMonitorLogger
    @GetMapping("/info")
    @ApiOperation("根据用户ID查询基本信息")
    public ResponseEntity<Result<?>> info(@RequestParam @NotNull(message = "{NotBlank.user.id}") Long id,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            UserForm form = this.userService.userInfo(id, header.getType(), header.getOid());
            return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("身份证或手机号码密文转明文失败", e.getMessage(), e);
            throw new ApiException("身份证或手机号码密文转明文失败", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "身份证或手机号码密文转明文失败"));
        }
    }

    /**
     * 获取验证码
     *
     * @param phone
     * @param flag  1-校验手机号码是否存在 2-不校验手机号码 3-校验排除自己的手机号码
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/send")
    @ApiOperation("获取验证码")
    public ResponseEntity<Result<?>> sendText(HttpServletRequest request,
                                              @RequestParam @NotBlank(message = "{NotBlank.user.phone}")
                                              @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}")
                                                      String phone,
                                              @RequestParam(required = false) String flag,
                                              @RequestParam(value = "user_id", required = false) Long userId) {
        boolean isSend = true;
        if (!"2".equals(flag)) {
            if ("3".equals(flag) && userId == null) {
                log.debug("用户ID不能为空");
                return new ResponseEntity<>(new Result<>(errors, 198, HttpStatus.OK.value()), HttpStatus.OK);
            }
            // 校验手机号是否存在
            int isExist = this.userService.checkPhone(NumEncryptUtils.encrypt(phone, 2), userId);
            if (org.apache.commons.lang.StringUtils.isBlank(flag)) {
                if (isExist <= 0) {
                    log.debug("该手机号未注册 -> phone:{}", phone);
                    isSend = false;
                    // 2022-03-17 中高位漏洞，以免通过数据进行枚举存在的手机号。已修复
                   // return new ResponseEntity<>(new Result<>(errors, 128, HttpStatus.OK.value()), HttpStatus.OK);
                }
            } else if ("1".equals(flag) || "3".equals(flag)) {
                if (isExist > 0) {
                    log.debug("手机号码已存在，请重新输入 -> phone:{}", phone);
                    isSend = false;
                    // return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
                }
            }
        }
        if (isSend) {
            // 调用短信验证码发送服务
            this.messageService.sendCode(phone);
        }
        return new ResponseEntity<>(new Result<>(UUID.randomUUID().toString().replaceAll("-", ""), errors),
                HttpStatus.OK);
    }

    /**
     * 获取验证码
     *
     * @param phone
     * @param captcha 图形验证码
     * @param flag    1-校验手机号码是否存在 2-不校验手机号码 3-校验排除自己的手机号码
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/verify/send")
    @ApiOperation("获取验证码")
    public ResponseEntity<Result<?>> verifySendText(HttpServletRequest request,
                                                    @RequestParam @NotBlank(message = "{NotBlank.user.phone}")
                                                    @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}") String phone,
                                                    @RequestParam @NotBlank(message = "{NotBlank.user.uuid}") String uuid,
                                                    @RequestParam @NotBlank(message = "{NotBlank.user.captcha}") String captcha,
                                                    @RequestParam(required = false) String flag,
                                                    @RequestParam(value = "user_id", required = false) Long userId) {
        // 校验图形验证码
        String captchaKey = Constants.LOGIN_CAPTCHA_PREFIX.concat(uuid);
        boolean isSend = true;
        if (Boolean.FALSE.equals(this.redisTemplate.hasKey(captchaKey))) {
            log.debug("手机号 -> [{}], 图片验证码已失效, 请重新获取 captcha -> {}", phone, captcha);
            return new ResponseEntity<>(new Result<>(errors, 176, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            String captchaRedis = this.redisTemplate.opsForValue().get(captchaKey);
            if (Objects.nonNull(captchaRedis) && !captchaRedis.equalsIgnoreCase(captcha)) {
                log.debug("手机号 -> [{}], 图片验证码错误，请重新输入 captcha -> {}", phone, captcha);
                return new ResponseEntity<>(new Result<>(errors, 177, HttpStatus.OK.value()), HttpStatus.OK);
            } else {
                this.redisTemplate.delete(captchaKey);
            }
        }
        // 校验手机号是否存在
        int isExist = this.userService.checkPhone(NumEncryptUtils.encrypt(phone, 2), userId);
        // 判断逻辑
        if (Objects.nonNull(flag)) {
            switch (flag) {
                case "1":
                    if (isExist > 0) {
                        log.debug("手机号码已存在，请重新输入 -> phone:{}", phone);
                        isSend = false;
                        //return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                    break;
                case "3":
                    if (Objects.isNull(userId)) {
                        log.debug("用户ID不能为空");
                        return new ResponseEntity<>(new Result<>(errors, 198, HttpStatus.OK.value()), HttpStatus.OK);
                    } else {
                        if (isExist > 0) {
                            log.debug("手机号码已存在，请重新输入 -> phone:{}", phone);
                            isSend = false;
                            //return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
                        }
                    }
                    break;
                case "4":
                    if (isExist < 0) {
                        log.debug("手机号码不存在，请重新输入 -> phone:{}", phone);
                        isSend = false;
                        //return new ResponseEntity<>(new Result<>(errors, 30012, HttpStatus.OK.value()), HttpStatus.OK);
                    }
                    break;
                default:
                    break;
            }
        } else {
            if (isExist <= 0) {
                log.debug("该手机号未注册 -> phone:{}", phone);
                isSend = false;
                // 2022-03-17 中高位漏洞，以免通过数据进行枚举存在的手机号。已修复
               // return new ResponseEntity<>(new Result<>(errors, 128, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }
        if (isSend) {
            // 调用短信验证码发送服务
            this.messageService.sendCode(phone);
        }
        return new ResponseEntity<>(new Result<>(UUID.randomUUID().toString().replaceAll("-", ""), errors), HttpStatus.OK);
    }

    /**
     * 验证码获取成功--提交
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/check")
    @ApiOperation("验证码获取成功 --> 提交")
    public ResponseEntity<Result<?>> check(@Valid @RequestBody UpdatePhoneForm form,
                                           BindingResult bindingResult) {

        int result = this.messageService.checkCode(form.getPhone(), form.getContent(), form.getUuid());
        if (result != Constants.YES) {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 修改手机号码
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update-phone")
    @ApiOperation("修改手机号")
    public ResponseEntity<Result<String>> updatePhone(@Valid @RequestBody UpdatePhoneForm form,
                                                      BindingResult bindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        String phoneSecret = NumEncryptUtils.encrypt(form.getPhone(), 2);
        int isExist = this.userService.checkPhone(phoneSecret, null);
        if (isExist > 0) {
            log.debug("该手机号码已经存在 -> phone:{}", form.getPhone());
            return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
        }
        int result = this.messageService.checkCode(form.getPhone(), form.getContent(), null);
        if (result != Constants.YES) {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }

        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(header.getUserId());
        userEntity.setPhone(phoneSecret);
        userEntity.setPhoneSecret(NumEncryptUtils.numSecret(form.getPhone(), 2));
        userEntity.setLastChangeUser(header.getUserId());
        userEntity.setUpdateTime(new Date());
        this.userService.changePhoneById(userEntity);
        log.debug("修改手机号码完成，新手机号码: -> {}", form.getPhone());

        // 刷新缓存 tc2018.08.17
        // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("userId", header.getUserId());
        UserEntity cacheUser = this.userMapper.selectOneByExample(example);
        cacheUser.setPhone(phoneSecret);
        cacheUser.setPhone(phoneSecret);
        cacheUser.setPhoneSecret(NumEncryptUtils.numSecret(form.getPhone(), 2));
        cacheUser.setLastChangeUser(header.getUserId());
        cacheUser.setUpdateTime(new Date());
        userOrgCacheCallService.flushUserInfo(header.getRegionId(), cacheUser, 1);
        // 刷新MongoDB数据
        this.userMongoService.conversionUser(cacheUser, null, false, header);
        // 修改电话号码广播到其他模块
        log.debug("修改手机号码调用广播接口 phone：{}", form.getPhone());
        this.orgUserCommonService.callbackDoGet("user_change_phone_callback",
                header.getUserId(), "phone", form.getPhone(),
                header.getRegionId());

        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 验证通过，重置密码
     *
     * @param
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update-pwd")
    @ApiOperation("验证通过，重置密码")
    public ResponseEntity<Result<String>> updatePassword(@Valid @RequestBody ResetPasswordForm form,
                                                         BindingResult bindingResult,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 校验redis手机号码状态,为1则正常
        String uuidKey = Constants.UUID_PREFIX + form.getUuid();
        if (this.redisTemplate.hasKey(uuidKey)) {
            String[] uuidStr = this.redisTemplate.opsForValue().get(uuidKey).split("-");
            if (uuidStr.length == 2) {
                String phone = uuidStr[0];
                log.debug("phone[{}],paramPhone[{}]", phone, form.getPhone());
                if (!phone.equalsIgnoreCase(form.getPhone())) {
                    return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
                }
                int status = Integer.parseInt(uuidStr[1]);
                if (status != Constants.STATUS_YES) {
                    return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
                }
            } else {
                return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } else {
            return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 校验两次输入密码是否相同
        if (!form.getPassword().equals(form.getRepeatPassword())) {
            log.debug("两次密码不一致 -> pwd:{},rpwd:{}", form.getPassword(), form.getRepeatPassword());
            return new ResponseEntity<>(new Result<>(errors, 126, HttpStatus.OK.value()), HttpStatus.OK);
        }
        String phone = NumEncryptUtils.encrypt(form.getPhone(), 2);
        UserEntity userEntity = new UserEntity();
        userEntity.setPhone(phone);
        if (this.ecsConfig.getRun()) {
            // 调用ecs服务，加密登录密码-SM3
            try {
                String hmac = this.ecsService.hmacData(2,form.getPassword());
                userEntity.setPassword(hmac);
            } catch (Exception e) {
                log.debug("调用ECS服务加密失败 -> [{}]", e.getMessage(), e);
                return new ResponseEntity<>(new Result<>(errors, 40101, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }else{
            userEntity.setPassword(form.getPassword());
        }
        userEntity.setLastChangeUser(header.getUserId());
        int i = this.userService.updateUserByWhere(userEntity);
        log.debug("重置密码完成 -> {}", i);
        // 验证成功，删除redis
        this.redisTemplate.delete(uuidKey);
        // 刷新缓存 tc2018.08.17
        // 更改缓存为缓存userId -> userd对象 huangkangjie2018.08.24
        Example example = new Example(UserEntity.class);
        example.createCriteria().andEqualTo("phone", phone)
                .andEqualTo("password", form.getPassword());
        UserEntity cacheUser = this.userMapper.selectOneByExample(example);
        if (cacheUser != null && cacheUser.getUserId() != null) {
            cacheUser.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
            cacheUser.setPassword(form.getPassword());
            cacheUser.setLastChangeUser(header.getUserId());
            userOrgCacheCallService.flushUserInfo(header.getRegionId(), cacheUser, 1);
            this.userMongoService.conversionUser(cacheUser, null, false, header);
        }
        return new ResponseEntity<>(new Result<>(i > 0 ? "success" : "failure", errors), HttpStatus.OK);
    }

    /**
     * 系统管理员--编辑
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/edit")
    @ApiOperation("系统管理员-编辑")
    public ResponseEntity<Result<?>> edit(@RequestParam
                                          @Range(min = 1, max = 99999999999L, message = "{Range.user.id}")
                                          @NotNull(message = "{NotNull.user.id}") long userId,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        SysEditForm form = this.userService.editManager(header.getType(), header.getOid(), userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    /**
     * 离职
     *
     * @param id
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/leave/{id}")
    @ApiOperation("离职")
    public ResponseEntity<Result<String>> leave(@PathVariable
                                                @Range(min = 1, max = 99999999999L, message = "{Range.user.id}")
                                                @NotNull(message = "{NotNull.user.id}") long id,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UserEntity userEntity = new UserEntity();
        userEntity.setUserId(id);
        if (id == header.getUserId()) {
            log.debug("当前登录用户不允许离职 userId -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 171, HttpStatus.OK.value()), HttpStatus.OK);
        }
        String name = this.userService.leave(userEntity, header.getType(), header.getOid());
        if (org.apache.commons.lang.StringUtils.isBlank(name)) {
            log.debug("系统异常，离职操作失败 userId -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 143, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if ("exist".equals(name)) {
            log.debug("超级管理员不允许离职 userId -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 150, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 调用工作流
        this.restTemplate.setErrorHandler(new ClientExceptionHandler());
        headers = HeaderHelper.setMyHttpHeader(headers, header);
        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        try {
            ResponseEntity<Result> result = this.restTemplate.exchange(
                    String.format("http://%s/approval/leave/%d/%s",
                            workFlowServer,
                            id,
                            name),
                    HttpMethod.GET, entity, Result.class);
            if (result.getStatusCode() != HttpStatus.OK || result.getBody().getCode() != 0) {
                log.error("离职调用工作流失败 ->{},{}", result.getStatusCodeValue(), result.getBody() != null ? result.getBody().getCode() : -1);
            }
            log.debug("离职调用工作流成功 ->{},{}", result.getStatusCodeValue(), result.getBody().getCode());
        } catch (ApiException e) {
            log.debug("result -> {}", e.getResult());
        } catch (IllegalStateException ise) {
            log.error("未知错误", ise.getMessage(), ise);
        }
        log.debug("离职操作完成 -> {},{}", id, name);

        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据名称和部门查询用户信息
     *
     * @param
     * @param
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserForm.class)
    @HttpMonitorLogger
    @GetMapping("/user-list")
    @ApiOperation("根据名称查询用户信息")
    public ResponseEntity<Result<Page<UserForm>>> userList(@RequestParam(required = false) Integer page,
                                                           @RequestParam(required = false)
                                                           @Length(max = 100, message = "{Length.user.name}")
                                                                   String name,
                                                           @RequestParam(required = false) Long depId,
                                                           @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null) {
            page = 1;
        }

        Page<UserForm> userEntityPage = this.userService.getUser(new PageNumber(page), name, header.getOid(), header.getType(), depId);
        log.debug("查询成功 -> total:{}", userEntityPage.getTotal());
        return new ResponseEntity<>(new Result<>(userEntityPage, errors), HttpStatus.OK);
    }

    /**
     * 查询部门管理员列表-部门管理
     *
     * @param did
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getDepManager/{did}")
    @ApiOperation("查询部门管理员")
    public ResponseEntity<Result<List<UserForm>>> getDepManager(@PathVariable @NotNull(message = "{NotNull.dep.depId}") long did) {

        List<UserForm> list = this.userService.getDepManager(did, Constants.DUTY_MANAGER);
        log.debug("查询成功 -> total:{}", list.size());
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 新增、取消部门管理员
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/setManager")
    @ApiOperation("新增、取消部门管理员")
    public ResponseEntity<Result<String>> setManager(@Valid @RequestBody ManagerForm form,
                                                     BindingResult bindingResult) {
        int flag = 0;
        // 新增管理员
        if (form.getType() == Constants.DEP_TYPE_ADD) {
            flag = this.userService.addDepManager(form.getId(), form.getDid());
            if (flag == 9) {
                log.debug("该人员已经是超级管理员 -> userId:{}", form.getId());
                return new ResponseEntity<>(new Result<>(errors, 133, HttpStatus.OK.value()), HttpStatus.OK);
            } else if (flag == 2) {
                log.debug("该人员已经是管理员 -> userId:{}", form.getId());
                return new ResponseEntity<>(new Result<>(errors, 137, HttpStatus.OK.value()), HttpStatus.OK);
            }
            log.debug("新增管理员操作完成 -> {}", flag);
        } else if (form.getType() == Constants.DEP_TYPE_CANCEL) { // 取消管理员
            flag = this.userService.updateDepManager(form.getId(), form.getDid(), Constants.DUTY_GENERAL);
            log.debug("取消管理员操作完成 -> {}", flag);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 系统管理员首页
     *
     * @param name
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/sysList")
    @ApiOperation("系统管理员首页")
    public ResponseEntity<Result<Page<SysUserForm>>> sysList(@RequestParam(required = false) Integer page,
                                                             @RequestParam(required = false)
                                                             @Length(max = 100, message = "{Length.dep.userName}") String name,
                                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null) {
            page = 1;
        }
        ConcurrentMap<String, Object> map = new ConcurrentHashMap<>();
        if (!org.apache.commons.lang.StringUtils.isBlank(name)) {
            map.put("name", name);
        }
        map.put("oId", header.getOid());
        map.put("type", header.getType());
        map.put("duty", Constants.DUTY_GENERAL);
        map.put("userId", header.getUserId());
        Page<SysUserForm> sysUserForm = this.userService.getSysList(new PageNumber(page), map);
        return new ResponseEntity<>(new Result<>(sysUserForm, errors), HttpStatus.OK);
    }

    /**
     * 新建管理员账号首页
     *
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/getSys")
    @ApiOperation("新建管理员账号首页")
    public ResponseEntity<Result<SysForm>> getSys(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        SysForm sysForm = this.userService.getSys(header.getType(), header.getOid());
        return new ResponseEntity<>(new Result<>(sysForm, errors), HttpStatus.OK);
    }

    /**
     * 新建-编辑系统管理员账号
     *
     * @param addForm
     * @param headers
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/addSys")
    @ApiOperation("新建-编辑系统管理员账号")
    public ResponseEntity<Result<?>> addSys(@Valid @RequestBody SysAddForm addForm,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (!this.userService.addSys(addForm, header)) {
            log.error("操作管理员账号失败 userId -> {}", addForm.getUserId());
            return new ResponseEntity<>(new Result<>(errors, 115, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("操作管理员账号成功 userId -> {}", addForm.getUserId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 移除管理
     *
     * @param id
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/cancel/{id}")
    @ApiOperation("移除管理员")
    public ResponseEntity<Result<?>> cancel(@PathVariable @NotNull(message = "{NotNull.user.id}")
                                            @Range(min = 1, max = 99999999999L, message = "{Range.user.id}")
                                                    long id,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (!this.userService.cancel(id, header.getType(), header.getOid(), header.getRegionId())) {
            log.error("移除管理失败 userId -> {}", id);
            return new ResponseEntity<>(new Result<>(errors, 116, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("移除管理成功 userId -> {}", id);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 移交超级管理员
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/change")
    @ApiOperation("移交超级管理员")
    public ResponseEntity<Result<?>> change(@Valid @RequestBody ChangeForm form,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int flag = this.userService.change(form.getId(), form.getNid(), header.getType(), header.getOid());
        if (flag == 9) {
            log.error("系统异常，移交管理失败 userId -> {}", form.getId());
            return new ResponseEntity<>(new Result<>(errors, 117, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (flag == 2) {
            log.debug("该用户已经是超级管理员 userId -> {}", form.getId());
            return new ResponseEntity<>(new Result<>(errors, 144, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("移交管理成功 userId -> {}", form.getId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 人员管理-查询
     *
     * @param
     * @param headers
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/getUserList")
    @ApiOperation("人员管理-查询")
    public ResponseEntity<Result<Page<UserResultForm>>> getUserList(@RequestParam(required = false) @Length(max = 100, message = "{Length.}") String name,
                                                                    @RequestParam(required = false) @Range(max = 9999999999L, message = "{Range.dep.id}") Long departmentId,
                                                                    @RequestParam(required = false) @Length(max = 20, message = "{Length.user.phone}") String phone,
                                                                    @RequestParam(required = false) @Range(min = 1, max = 3, message = "{Range.user.status}") Integer status,
                                                                    @RequestParam(required = false) String tag,
                                                                    @RequestParam(required = false) Integer page,
                                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null) {
            page = 1;
        }
        UserQueryForm form = new UserQueryForm();
        form.setDepartmentId(departmentId);
        form.setName(name);
        form.setPhoneSecret(NumEncryptUtils.numSecret(phone, 2));
        form.setStatus(status);
        if (!"".equals(tag) && tag != null) {
            List<Long> tagList = new ArrayList<>();
            for (String tagId : tag.split(",")) {
                tagList.add(Long.parseLong(tagId));
            }
            form.setTag(tagList);
        }
        Page<UserResultForm> list = this.userService.getUserList(new PageNumber(page), form, header.getType(), header.getOid(), header.getUserId());

        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 人员管理-权限分配-查询
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/allotList")
    @ApiOperation("人员管理-权限分配-查询")
    public ResponseEntity<Result<UserRoleForm>> allotList(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        UserRoleForm userRoleForm = this.userService.allotList(header.getType(), header.getOid());
        return new ResponseEntity<>(new Result<>(userRoleForm, errors), HttpStatus.OK);
    }

    /**
     * 人员管理-权限分配-提交
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/updateUserRole")
    @ApiOperation("人员管理-权限分配-提交")
    public ResponseEntity<Result<?>> updateUserRole(@Valid @RequestBody RoleAllotForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        if (!this.userService.updateUserRole(header.getUserId(), header.getType(), header.getOid(), header.getRegionId(), form)) {
            log.error("权限分配失败  -> userId:{}", form.getUserId());
            return new ResponseEntity<>(new Result<>(errors, 111, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("权限分配成功 -> userId:{}", form.getUserId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 人员管理-权限分配-编辑页面
     *
     * @param userId
     * @param roleId
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/role-list")
    @ApiOperation("人员管理-权限分配-编辑页面")
    public ResponseEntity<Result<?>> roleList(@RequestParam
                                              @NotNull(message = "{NotNull.user.id}")
                                              @Range(min = 1, max = 99999999999L, message = "{Range.user.id}") long userId,
                                              @RequestParam
                                              @Range(min = 1, max = 99999999999L, message = "{Range.role.id}")
                                              @NotNull(message = "{NotNull.role.id}") long roleId,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        RoleResultForm resultForm = this.userService.editUserRole(roleId, userId, header.getType(), header.getOid(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
    }

    /**
     * 根据电话号码查询用户信息
     *
     * @param phone
     * @return
     */
    @HttpMonitorLogger
    // @GetMapping("/getUserByPhone") 废弃
    @ApiOperation("根据电话查询用户信息")
    public ResponseEntity<Result<UserInfoBase>> getUserByPhone(@RequestParam
                                                               @NotBlank(message = "{NotBlank.user.phone}")
                                                                       // @Pattern(regexp = "^1([34578])\\d{9}$",message = "{Pattern.user.phone}")
                                                                       String phone,
                                                               @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // java.util.regex.Pattern regex = Pattern.compile("^1([34578])\\d{9}$");
        java.util.regex.Pattern regex = Pattern.compile("^1\\d{10}$");
        Matcher m = regex.matcher(phone);
        boolean flag = m.matches();
        UserInfoBase base;
        String str;
        if (flag) {
            str = "1";
            base = this.userService.getUserByPhone(phone, header.getUserId(), header.getType(), header.getOid(), str);
        } else {
            str = "2";
            base = this.userService.getUserByPhone(phone, header.getUserId(), header.getType(), header.getOid(), str);
        }
        return new ResponseEntity<>(new Result<>(base, errors), HttpStatus.OK);
    }

    /**
     * 增加用户积分
     *
     * @param form
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/add-score")
    @ApiOperation("增加用户积分")
    public ResponseEntity<Result<?>> addScore(@Valid @RequestBody ScoreResultForm form,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        ScoreResultForm resultForm = new ScoreResultForm();
        resultForm.setUserId(form.getUserId());
        int resultScore = this.userService.updateScore(header.getType(), header.getOid(), form.getScore(), form.getUserId());
        if (resultScore == -1) {
            log.debug("更新用户积分失败 -> userId:{},score:{}", form.getUserId(), form.getScore());
            return new ResponseEntity<>(new Result<>(errors, 127, HttpStatus.OK.value()), HttpStatus.OK);
        }
        resultForm.setScore(resultScore);
        return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
    }

    /**
     * 绑定信息
     *
     * @param form
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/bind")
    @ApiOperation("绑定信息")
    public ResponseEntity<Result<?>> bind(@Valid @RequestBody BindForm form,
                                          BindingResult bindingResult,
                                          @RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "th_token") String thToken,
                                          HttpServletRequest request) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            Map<String, Object> resultMap = this.userService.guestBind(request, header, thToken, form, header.getOid());
            int code = (int) resultMap.get("code");
            if (code != 1) {
                return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
            } else {
                return (ResponseEntity<Result<?>>) resultMap.get("entity");
            }
        } catch (IOException e) {
            log.error("用户绑定失败：[{}]", e.getMessage(), e);
            throw new ApiException("用户绑定失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "绑定用户信息失败"));
        }
    }

    /**
     * 绑定完善信息-商会
     *
     * @param cocBindForm
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/bind_coc")
    @ApiOperation("绑定完善信息-商会")
    public ResponseEntity<Result<?>> bindCoc(@RequestBody CocBindForm cocBindForm,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String phone = cocBindForm.getPhone();
        String code = cocBindForm.getCode();
        String uuid = cocBindForm.getUuid();
        //验证码校验
        int rs = messageService.checkCode(phone, code, uuid);
        try {
            if (rs != 1) {
                return new ResponseEntity<>(new Result<>(errors, rs, HttpStatus.OK.value()), HttpStatus.OK);
            }
            rs = userService.bindCoc(header, phone);
            if (rs != 1) {
                return new ResponseEntity<>(new Result<>(errors, rs, HttpStatus.OK.value()), HttpStatus.OK);
            }
            return new ResponseEntity<>(new Result<>(0, "信息绑定成功", HttpStatus.OK.value(), rs), HttpStatus.OK);
        } catch (Exception e) {
            log.error("用户绑定失败：[{}]", e.getMessage(), e);
            throw new ApiException("用户绑定失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "绑定用户信息失败"));
        }
    }

    /**
     * 根据phone查询单位或组织ID列表
     *
     * @param phone
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/co-list")
    @ApiOperation("根据phone查询单位或组织ID列表")
    public ResponseEntity<Result<?>> coList(@RequestParam
                                            @NotBlank(message = "{NotBlank.user.phone}")
                                            @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}")
                                                    String phone) {
        List<CorpOrgForm> resultList = this.userService.coList(phone);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 激活组织-更新/新增注册人信息
     *
     * @param
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/deal-regist-info")
    @ApiOperation("激活组织-更新/新增注册人信息")
    public ResponseEntity<Result<?>> dealRegistInfo(@Valid @RequestBody UserForm form,
                                                    BindingResult bindingResult) {
        //校验短信验证码
        int checkCode = this.messageService.checkCode(form.getPhone(), form.getCaptcha(), null);
        if (checkCode != 1) {
            return new ResponseEntity<>(new Result<>(errors, checkCode, HttpStatus.OK.value()), HttpStatus.OK);
        }
        Map<String, Object> resultMap = this.openService.dealRegistInfo(form);
        int code = (int) resultMap.get("code");
        if (code != 1) {
            log.debug("身份证合法校验失败，--> name:{},phone:{},certNumber:{}", form.getName(), form.getPhone(), form.getCertNumber());
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(resultMap.get("userId"), errors), HttpStatus.OK);
    }

    /**
     * 激活组织-设置密码
     *
     * @param form
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/deal-pwd")
    @ApiOperation("激活组织-设置密码")
    public ResponseEntity<Result<?>> dealPwd(@Valid @RequestBody RegistPwdForm form,
                                             BindingResult bindingResult) {
        // 校验redis手机号码状态,为1则正常
        String uuidKey = Constants.UUID_PREFIX + form.getUuid();
        if (this.redisTemplate.hasKey(uuidKey)) {
            String[] uuidStr = this.redisTemplate.opsForValue().get(uuidKey).split("-");
            if (uuidStr.length == 2) {
                String phone = uuidStr[0];
                if (!form.getPhone().equalsIgnoreCase(phone)) {
                    return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
                }
                int status = Integer.parseInt(uuidStr[1]);
                if (status != Constants.STATUS_YES) {
                    return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
                }
            } else {
                return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } else {
            return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (!form.getPassword().equals(form.getRepeatPassword())) {
            log.debug("两次密码不一致 -> pwd:{},rpwd:{}", form.getPassword(), form.getRepeatPassword());
            return new ResponseEntity<>(new Result<>(errors, 126, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 通过组织id获得用户信息
     * 如果isPay=1时则不分页查询
     *
     * @param orgId
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserCertNumberForm.class)
    @HttpMonitorLogger
    @GetMapping("/getUserListByOrgId")
    @ApiOperation("根据组织Id获得用户信息")
    public ResponseEntity<Result<?>> getUserListByOrgId(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                        @RequestParam(value = "political_type", required = false) String politicalType,
                                                        @RequestParam(value = "is_pay", required = false, defaultValue = "0") Integer isPay,
                                                        @RequestParam(value = "is_employee", required = false, defaultValue = "2") Integer isEmployee,
                                                        @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
                                                        @RequestParam(value = "page_size", required = false, defaultValue = "99999") Integer pageSize,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (StringUtil.isNotEmpty(politicalType) && !politicalType.matches("^\\d+(,\\d+)*$")) {
            log.debug("用户政治面貌格式错误 -> value:{} ", politicalType);
            return new ResponseEntity<>(new Result<>(errors, 218, HttpStatus.OK.value()), HttpStatus.OK);
        }
        List<UserCertNumberForm> resultList = this.userService.getUserListByOrgId(orgId, politicalType, pageNo, pageSize, isPay, isEmployee, header.getRegionId());
        return new ResponseEntity<>(new Result<>(null == resultList ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * 通过组织id查询所有下级组织用户
     *
     * @param orgId
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserCertNumberForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-by-org")
    @ApiOperation("根据组织Id查询所有下级组织用户")
    public ResponseEntity<Result<?>> findUserByOrg(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                   @RequestParam(value = "political_type", required = false) String politicalType,
                                                   @RequestParam(value = "is_pay", required = false, defaultValue = "1") Integer isPay,
                                                   @RequestParam(value = "is_page", required = false, defaultValue = "0") Integer isPage,
                                                   @RequestParam(value = "is_include", required = false, defaultValue = "0") Integer isInclude,
                                                   @RequestParam(value = "is_include_retire", required = false, defaultValue = "1") Integer isIncludeRetire,
                                                   @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
                                                   @RequestParam(value = "page_size", required = false, defaultValue = "1000") Integer pageSize,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (StringUtil.isNotEmpty(politicalType) && !politicalType.matches("^\\d+(,\\d+)*$")) {
            log.debug("用户政治面貌格式错误 -> value:{} ", politicalType);
            return new ResponseEntity<>(new Result<>(errors, 218, HttpStatus.OK.value()), HttpStatus.OK);
        }
        List<UserCertNumberForm> resultList = this.userService.findUserByOrg(orgId, politicalType, pageNo, pageSize,
                isPay, isPage, isInclude, header.getOrgType(), isIncludeRetire, header.getRegionId());
        return new ResponseEntity<>(new Result<>(null == resultList ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * @param form
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/updateUserOrgTag")
    @ApiOperation("更新人员党组织标签")
    public ResponseEntity<Result<?>> updateUserOrgTag(@Valid @RequestBody UpdateUserOrgTagForm form,
                                                      BindingResult bindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return this.orgUserService.updateUserOrgTag(form, header.getRegionId());
    }

    @HttpMonitorLogger
    @GetMapping("/unbind_third_code")
    @ApiOperation("获取解绑短信验证码")
    public ResponseEntity<Result<String>> getUnbindValidCode(@RequestParam String phone, HttpServletRequest request) {
        String reg = "^1\\d{10}$";
        //如果手机号格式不匹配
        if (!phone.matches(reg)) {
            return ResponseEntity.ok(new Result<>(errors, 185, HttpStatus.OK.value()));
        }
        String encryptPhone = NumEncryptUtils.encrypt(phone, 2);

        //验证手机号是否匹配身份信息
        BoundValueOperations<String, String> phoneCheckOps = redisTemplate.boundValueOps(Constants.USER_UNBIND_PHONE_CHECK_PREFIX + encryptPhone);
        //如果不存在身份证验证后产生的rediskey，说明过期或者手机号不匹配
        if (StringUtils.isEmpty(phoneCheckOps.get())) {
            return ResponseEntity.ok(new Result<>(errors, 228, HttpStatus.OK.value()));
        }

        this.messageService.sendCode(phone);
        return ResponseEntity.ok(new Result<>("success", errors));
    }


    @HttpMonitorLogger
    @PostMapping("/unbind_third")
    @ApiOperation("解绑第三方账号")
    public ResponseEntity<Result<String>> unbindThird(@Validated @RequestBody UnbindThirdForm unbindThirdForm,
                                                      BindingResult bindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (bindingResult.hasErrors()) {
            throw new ApiException("参数错误", new Result<>(Global.Errors.VALID_ERROR.getCode(), bindingResult.getFieldError().getDefaultMessage(), HttpStatus.BAD_REQUEST.value(), null));
        }
        //验证验证码是否正确
        String phone = unbindThirdForm.getPhone();
        String code = unbindThirdForm.getValidCode();
        // 校验短信验证码
        int result = this.messageService.checkCode(phone, code, null);
        if (result != 1) {
            return ResponseEntity.ok(new Result<>(errors, result, HttpStatus.OK.value()));
        }
        //匹配用户信息
        List<UserEntity> userList = userService.findUserByPhone(phone);
        //如果未根据手机号查询到用户
        if (userList == null || userList.isEmpty()) {
            return ResponseEntity.ok(new Result<>(errors, 199, HttpStatus.OK.value()));
        }
        UserEntity userEntity = userList.get(0);
        //如果根据手机号查询到多条数据
        if (userList.size() > 1) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < userList.size(); i++) {
                if (i != 0) {
                    sb.append(',');
                }
                sb.append(userList.get(i).getUserId());
            }
            log.error("用户第三方账号解绑-根据手机号查询到用户姓名:{} 有多个用户id：{}", userEntity.getName(), sb.toString());
        }

        userService.unbindThird(userEntity, unbindThirdForm.getRegionId(), header);
        return ResponseEntity.ok(new Result<>("success", errors));
    }

    @HttpMonitorLogger
    @PostMapping("/unbind_third_check_cert")
    @ApiOperation("解绑-验证身份证信息")
    public ResponseEntity<Result<String>> unbindThirdCheckCert(@Validated @RequestBody UnbindThirdCheckCertForm unbindThirdCheckCertForm, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ApiException("参数错误", new Result<>(Global.Errors.VALID_ERROR.getCode(), bindingResult.getFieldError().getDefaultMessage(), HttpStatus.BAD_REQUEST.value(), null));
        }
        String name = unbindThirdCheckCertForm.getName();
        UserEntity condition = new UserEntity();
        condition.setName(name);
        condition.setCertNumber(NumEncryptUtils.encrypt(unbindThirdCheckCertForm.getCertNumber().toUpperCase(), 1));
        condition.setStatus(Constants.STATUS_YES);
        List<UserEntity> userList = userService.select(condition);
        //如果未查询到用户
        if (userList == null || userList.isEmpty()) {
            return ResponseEntity.ok(new Result<>(errors, 199, HttpStatus.OK.value()));
        }
        if (userList.size() > 1) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < userList.size(); i++) {
                if (i != 0) {
                    sb.append(',');
                }
                sb.append(userList.get(i).getUserId());
            }
            log.error("用户第三方账号解绑-根据身份证信息查询到用户姓名:[{}],有多个用户id:[{}]", name, sb.toString());
        }
        UserEntity userEntity = userList.get(0);
        String encryptPhone = userEntity.getPhone();
        String phoneSecret = userEntity.getPhoneSecret();
        //保存身份证对应手机号key到redis
        BoundValueOperations<String, String> phoneCheckOps = redisTemplate.boundValueOps(Constants.USER_UNBIND_PHONE_CHECK_PREFIX + encryptPhone);
        phoneCheckOps.set(phoneSecret, 10, TimeUnit.MINUTES);
        return ResponseEntity.ok(new Result<>(phoneSecret, errors));
    }

    /**
     * 根据用户组查询可以加入用户
     *
     * @param groupId
     * @param page
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-user-by-group")
    @ApiOperation("根据用户组查询可以加入用户")
    public ResponseEntity<Result<?>> findUserByOrgs(@RequestParam(value = "group_id") @NotNull(message = "{NotNull.group.groupId}") Long groupId,
                                                    @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<UserCertNumberForm> resultList = this.userService.findUserByGroup(groupId, page);
        return new ResponseEntity<>(new Result<>(null == resultList ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * 移除用户除了默认权限的所有权限
     *
     * @param userId
     * @param orgId
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/del-role-user")
    @ApiOperation("移除用户除了默认权限的所有权限")
    public ResponseEntity<Result<?>> delUserRole(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                 @RequestParam("org_id") @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.userService.delUserRole(userId, orgId, header);
        if (result == Constants.YES) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 批量移除用户除了默认权限的所有权限
     *
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/del-role-users")
    @ApiOperation("批量移除用户除了默认权限的所有权限")
    @DoLog(type = LogType.UPDATE,content = "修改用户角色")
    public ResponseEntity<Result<?>> delUserRoles(@Validated @RequestBody DelUserRolesForm form, BindingResult bindingResult,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("移除用户[{}]在组织[{}]下所有权限", form.getUserId(), form.getOrgIds());
        List<Long> orgIds = form.getOrgIds();
        Long userId = form.getUserId();
        if (null != orgIds && orgIds.size() > 0) {
            for (Long orgId : orgIds) {
                this.userService.delUserRole(userId, orgId, header);
            }
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/update-birth-place")
    @ApiOperation("根据身份证更新籍贯")
    public ResponseEntity<Result<?>> updateBirthPlace(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.userService.updateBirthPlace(header.getUserId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/hand-temp-user")
    @ApiOperation("处理临时用户信息")
    public ResponseEntity<Result<?>> handTempUserInfo(@RequestHeader HttpHeaders headers) {
        this.userService.handlerTempUserInfo();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 获取验证码v2版本
     *
     * @param phone 手机号码脱敏文
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/verify/v2/send")
    @ApiOperation("获取验证码V2版本")
    public ResponseEntity<Result<?>> verifySend(@RequestParam @NotBlank(message = "{NotBlank.user.phone}") String phone,
                                                @RequestParam(value = "user_id") Long userId,
                                                @RequestParam(value = "is_visitor", defaultValue = "2") Integer isVisitor) {
        // 校验手机号是否存在
        String decryptPhone = phone;
        // 如果不是游客，手机号码为脱敏文
        if (2 == isVisitor) {
            UserEntity user = this.userService.checkExistPhone(phone, userId);
            // 调用短信验证码发送服务
            try {
                decryptPhone = NumEncryptUtils.decrypt(user.getPhone(), user.getPhoneSecret());
            } catch (Exception e) {
                log.error("手机号码解密失败->[{}]", e.getMessage(), e);
            }
        }
        if (!StringUtils.isEmpty(decryptPhone)) {
            this.messageService.sendCode(decryptPhone);
        }
        return new ResponseEntity<>(new Result<>(UUID.randomUUID().toString().replaceAll("-", ""), errors), HttpStatus.OK);
    }

    /**
     * 校验手机验证码v2版本
     *
     * @param phone   手机号码脱敏文
     * @param content
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/verify/v2/send/check")
    @ApiOperation("校验手机验证码v2版本")
    public ResponseEntity<Result<?>> verifySendCheck(@RequestParam @NotBlank(message = "{NotBlank.user.phone}") String phone,
                                                     @RequestParam @NotBlank(message = "{NotBlank.user.uuid}") String uuid,
                                                     @RequestParam(value = "content") String content,
                                                     @RequestParam(value = "user_id") Long userId,
                                                     @RequestParam(value = "is_visitor", defaultValue = "2") Integer isVisitor) {
        int code = this.userService.verifySendCheck(phone, userId, content, uuid, isVisitor);
        if (code != 1) {
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}

