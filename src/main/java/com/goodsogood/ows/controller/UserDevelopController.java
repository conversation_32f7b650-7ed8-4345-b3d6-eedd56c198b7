package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.UserDevelopScheduler;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.RecusalService;
import com.goodsogood.ows.service.UserDevelopService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.Objects;

/**
 * UserDevelopController
 *
 * <AUTHOR>
 * @Date 2021-12-24 10:33
 */
@RestController
@RequestMapping("/develop")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "发展党员管理", tags = {"发展党员管理"})
@Validated
public class UserDevelopController {
    private final Errors errors;
    private final RecusalService recusalService;
    private final UserDevelopService userDevelopService;
    private final UserDevelopScheduler userDevelopScheduler;

    @Autowired
    public UserDevelopController(Errors errors, RecusalService recusalService, UserDevelopService userDevelopService, UserDevelopScheduler userDevelopScheduler) {
        this.errors = errors;
        this.recusalService = recusalService;
        this.userDevelopService = userDevelopService;
        this.userDevelopScheduler = userDevelopScheduler;
    }

    @HttpMonitorLogger
    @PostMapping("/recusal")
    @ApiOperation("取消资格")
    public ResponseEntity<Result<?>> recusalQualification(@Valid @RequestBody RecusalForm form,
                                                          @RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (!header.getOid().equals(form.getOrgId())) {
            throw new ApiException("没有取消资格", new Result<>(errors, 35029, HttpStatus.OK.value()));
        } else {
            this.recusalService.recusal(form, header, 0);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/delete")
    @ApiOperation("删除人员")
    public ResponseEntity<Result<?>> deletePeople(@Valid @RequestBody DeleteDevelopFrom form,
                                                  @RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (!header.getOid().equals(form.getOrgId())) {
            throw new ApiException("没有删除资格", new Result<>(errors, 35029, HttpStatus.OK.value()));
        } else {
            this.recusalService.deletePeople(form, header);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("发展党员列表/历史发展党员列表")
    @GetMapping(value = "/find-develop-by-where")
    public ResponseEntity<Result<?>> findDevelopByWhere(@RequestParam(value = "stage", required = false) Integer stage,
                                                        @RequestParam(value = "phone", required = false) String phone,
                                                        @RequestParam(value = "gender", required = false) Integer gender,
                                                        @RequestParam(value = "name", required = false) String name,
                                                        @RequestParam(value = "status", required = false) Integer status,
                                                        @RequestParam(value = "type", defaultValue = "1") Integer type,
                                                        @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                        @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.userDevelopService.findDevelopByWhere(stage, phone, gender, name, type, status, page, pageSize, header),
                errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("导出发展党员列表/历史发展党员列表")
    @GetMapping(value = "/export")
    public void export(@RequestParam(value = "stage", required = false) Integer stage,
                       @RequestParam(value = "phone", required = false) String phone,
                       @RequestParam(value = "gender", required = false) Integer gender,
                       @RequestParam(value = "name", required = false) String name,
                       @RequestParam(value = "status", required = false) Integer status,
                       @RequestParam(value = "type", defaultValue = "1") Integer type,
                       @RequestHeader HttpHeaders headers,
                       HttpServletResponse response) throws IOException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.userDevelopService.export(stage, phone, gender, name, type, status, response, header);
    }

    @HttpMonitorLogger
    @ApiOperation("编辑发展党员回显")
    @GetMapping(value = "/prepare-update")
    public ResponseEntity<Result<?>> prepareUpdate(@RequestParam(value = "userDevelopId")
                                                   @NotNull(message = "{NotNull.develop.id}") Long userDevelopId) {
        return new ResponseEntity<>(new Result<>(this.userDevelopService.prepareUpdate(userDevelopId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("编辑发展党员")
    @PostMapping(value = "/update")
    public ResponseEntity<Result<?>> updateDevelopUser(@RequestBody UserDevelopForm form,
                                                       @RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int resultCode = this.userDevelopService.updateDevelopUser(form, header);
        if (resultCode > 1) {
            return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("查看")
    @GetMapping(value = "/view-details")
    public ResponseEntity<Result<?>> details(@RequestParam(value = "user_develop_id") Long userDevelopId,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.userDevelopService.viewDitails(userDevelopId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("新增人员（入党申请人/积极分子/发展对象）")
    @PostMapping(value = "/add-develop-user")
    public ResponseEntity<Result<?>> addDevelopUser(@Valid @RequestBody UserDevelopForm form,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int resultCode = this.userDevelopService.addDevelopUser(form, header);
        if (resultCode > 1) {
            return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(resultCode, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("确定发展阶段")
    @PostMapping(value = "/object/confirm")
    public ResponseEntity<Result<?>> objectConfirm(@RequestBody DevelopFileForm form,
                                                   @RequestHeader HttpHeaders headers) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.nonNull(form.getNewOrgId()) && !form.getNewOrgId().equals(header.getOid())) {
            throw new ApiException("没有确定阶段资格", new Result<>(errors, 35029, HttpStatus.OK.value()));
        } else {
            int resultCode = this.userDevelopService.objectConfirm(form, header, headers);
            if (resultCode > 1) {
                return new ResponseEntity<>(new Result<>(errors, resultCode, HttpStatus.OK.value()), HttpStatus.OK);
            }
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 手动执行积极分子到期定时任务-供测试使用
     */
    @HttpMonitorLogger
    @GetMapping("/actTimeScheduler")
    @ApiOperation("手动执行积极分子到期定时任务")
    public ResponseEntity<Result<?>> actScheduler() {
        this.userDevelopScheduler.actScheduler();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 手动执行预备党员到期定时任务-供测试使用
     */
    @HttpMonitorLogger
    @GetMapping("/preTimeScheduler")
    @ApiOperation("手动执行预备党员到期定时任务")
    public ResponseEntity<Result<?>> preScheduler() {
        this.userDevelopScheduler.preScheduler();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
