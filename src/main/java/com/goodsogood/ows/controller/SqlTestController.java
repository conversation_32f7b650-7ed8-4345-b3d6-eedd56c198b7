package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.util.SqlCompatibilityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * SQL测试控制器
 * 用于测试SQL转换功能
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@RestController
@RequestMapping("/sql-test")
@Api(value = "SQL测试", tags = {"SQL测试"})
@Log4j2
public class SqlTestController {

    @Autowired
    private Errors errors;

    /**
     * 测试具体的SQL转换
     */
    @PostMapping("/convert")
    @ApiOperation("测试SQL转换")
    public ResponseEntity<Result<Map<String, Object>>> testSqlConversion(@RequestBody Map<String, String> request) {
        String originalSql = request.get("sql");
        if (originalSql == null || originalSql.trim().isEmpty()) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "SQL语句不能为空");
            return new ResponseEntity<>(new Result<>(errorResult, errors), HttpStatus.BAD_REQUEST);
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始转换SQL: {}", originalSql);
            
            // 检查是否需要转换
            boolean needsConversion = SqlCompatibilityUtil.needsConversion(originalSql);
            
            // 转换SQL
            String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
            
            // 获取SQL类型
            SqlCompatibilityUtil.SqlType sqlType = SqlCompatibilityUtil.getSqlType(originalSql);
            
            result.put("originalSql", originalSql);
            result.put("convertedSql", convertedSql);
            result.put("needsConversion", needsConversion);
            result.put("sqlType", sqlType.toString());
            result.put("changed", !originalSql.equals(convertedSql));
            result.put("status", "转换成功");
            
            log.info("SQL转换完成 - 原SQL: {}", originalSql.trim());
            log.info("SQL转换完成 - 转换后: {}", convertedSql.trim());
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("SQL转换失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("status", "转换失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 测试错误SQL的转换
     */
    @GetMapping("/test-error-sql")
    @ApiOperation("测试错误SQL的转换")
    public ResponseEntity<Result<Map<String, Object>>> testErrorSql() {
        // 这是从错误日志中提取的SQL
        String errorSql = "SELECT COUNT(1) orgCount, IFNULL(SUM(IFNULL(user_num,0)),0) orgUserTotal, IFNULL(SUM(IFNULL(party_member_num,0)),0) partyMemberTotal FROM `t_organization` WHERE (org_level like CONCAT('%-',?,'-%') OR organization_id = ?) AND `status` = 1 AND parent_id <> -999 AND org_type_child not in ( ? , ? )";
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("测试错误SQL转换...");
            
            // 检查是否需要转换
            boolean needsConversion = SqlCompatibilityUtil.needsConversion(errorSql);
            
            // 转换SQL
            String convertedSql = SqlCompatibilityUtil.convertSql(errorSql);
            
            result.put("originalSql", errorSql);
            result.put("convertedSql", convertedSql);
            result.put("needsConversion", needsConversion);
            result.put("changed", !errorSql.equals(convertedSql));
            result.put("status", "转换成功");
            
            // 分析转换的具体变化
            Map<String, String> changes = new HashMap<>();
            if (errorSql.contains("`") && !convertedSql.contains("`")) {
                changes.put("反引号", "已移除");
            }
            if (errorSql.contains("IFNULL") && convertedSql.contains("NVL")) {
                changes.put("IFNULL函数", "已转换为NVL");
            }
            result.put("changes", changes);
            
            log.info("错误SQL转换完成");
            log.info("原SQL: {}", errorSql);
            log.info("转换后: {}", convertedSql);
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("错误SQL转换失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("status", "转换失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取常见转换示例
     */
    @GetMapping("/examples")
    @ApiOperation("获取常见转换示例")
    public ResponseEntity<Result<Map<String, Object>>> getExamples() {
        Map<String, Object> result = new HashMap<>();
        Map<String, Map<String, String>> examples = new HashMap<>();
        
        // 反引号转换示例
        Map<String, String> backtickExample = new HashMap<>();
        backtickExample.put("before", "SELECT * FROM `users` WHERE `name` = ?");
        backtickExample.put("after", SqlCompatibilityUtil.convertSql("SELECT * FROM `users` WHERE `name` = ?"));
        examples.put("反引号转换", backtickExample);
        
        // IFNULL转换示例
        Map<String, String> ifnullExample = new HashMap<>();
        ifnullExample.put("before", "SELECT IFNULL(name, 'Unknown') FROM users");
        ifnullExample.put("after", SqlCompatibilityUtil.convertSql("SELECT IFNULL(name, 'Unknown') FROM users"));
        examples.put("IFNULL转换", ifnullExample);
        
        // LIMIT转换示例
        Map<String, String> limitExample = new HashMap<>();
        limitExample.put("before", "SELECT * FROM users LIMIT 10, 20");
        limitExample.put("after", SqlCompatibilityUtil.convertSql("SELECT * FROM users LIMIT 10, 20"));
        examples.put("LIMIT转换", limitExample);
        
        // NOW()转换示例
        Map<String, String> nowExample = new HashMap<>();
        nowExample.put("before", "SELECT NOW() FROM DUAL");
        nowExample.put("after", SqlCompatibilityUtil.convertSql("SELECT NOW() FROM DUAL"));
        examples.put("NOW转换", nowExample);
        
        // 复合转换示例
        Map<String, String> complexExample = new HashMap<>();
        String complexSql = "SELECT COUNT(1), IFNULL(SUM(`user_num`),0) FROM `t_organization` WHERE `status` = 1 LIMIT 10";
        complexExample.put("before", complexSql);
        complexExample.put("after", SqlCompatibilityUtil.convertSql(complexSql));
        examples.put("复合转换", complexExample);
        
        result.put("examples", examples);
        result.put("status", "成功");
        
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }
}
