package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UserLoginLogEntity;
import com.goodsogood.ows.model.db.UserOperationLogEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UserLoginLogVO;
import com.goodsogood.ows.model.vo.UserOperationLogVO;
import com.goodsogood.ows.service.SysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "系统日志", tags = {"系统日志"})
@RequestMapping("/sys/log")
public class SysLogController {

    private final Errors errors;

    private final SysLogService sysLogService;

    @Autowired
    public SysLogController(Errors errors, SysLogService sysLogService) {
        this.errors = errors;
        this.sysLogService = sysLogService;
    }

    @ApiOperation("登录日志列表")
    @GetMapping("/loginLog")
    public ResponseEntity<Result<?>> loginLog(@RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                              @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "userName", required = false) String userName,
                                              @RequestParam(value = "orgName", required = false) String orgName,
                                              @RequestParam(value = "loginStatus", required = false) Integer loginStatus,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserLoginLogVO> result = sysLogService.loginLog(page,pageSize,header,userName,orgName,loginStatus);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @ApiOperation("操作日志列表")
    @GetMapping("/operationLog")
    public ResponseEntity<Result<?>> operationLog(@RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                  @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                  @RequestParam(value = "userName", required = false) String userName,
                                                  @RequestParam(value = "operationContent", required = false) String operationContent,
                                                  @RequestParam(value = "operationType", required = false) Integer operationType,
                                                  @RequestParam(value = "operationStatus", required = false) Integer operationStatus,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserOperationLogVO> result = sysLogService.operationLog(page,pageSize,header,userName,operationContent,operationType,operationStatus);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

}
