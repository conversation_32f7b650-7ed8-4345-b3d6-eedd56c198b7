package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.vo.DictAreaForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.DictAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据字典 - 区域控制类
 * <AUTHOR>
 * @create 2018-04-24 17:10
 **/
@RestController
@RequestMapping("/dict/area")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "区域管理", tags = {"区域管理"})
@Validated
public class DictAreaController {

     private final Errors errors;

     private final DictAreaService dictAreaService;

     @Autowired
     public DictAreaController(Errors errors, DictAreaService dictAreaService) {
          this.errors = errors;
          this.dictAreaService = dictAreaService;
     }


     /**
      * 根据code查询数据字典列表
      *
      * @param pid
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/list")
     @ApiOperation("根据区域父级code获取区域列表")
     public ResponseEntity<Result<List<DictAreaForm>>> getOption(
                              @RequestParam(required = false) @ApiParam(value = "区域父级区域码")  Long pid
     ){
          List<DictAreaForm> list = new ArrayList<>();
          try {
               list = this.dictAreaService.findDictAreaByPid(pid);
          } catch (Exception e) {
               log.error("获取区域列表出错", e);
               throw new ApiException("获取区域列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取区域列表出错"));
          }
          return new ResponseEntity<>(new Result<>(list,errors), HttpStatus.OK);
     }

     /**
      * 根据code查询该code的区域详细信息
      *
      * @param adcode
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/detail")
     @ApiOperation("根据code查询该code的区域信息")
     public ResponseEntity<Result<DictAreaForm>> detail(
                              @RequestParam @ApiParam(value = "区域码", required = true)  Long adcode
     ){
          DictAreaForm from;
          try {
               from = this.dictAreaService.findDictAreaByCode(adcode == null ? -1 : adcode);
          } catch (Exception e) {
               log.error("获取区域信息出错", e);
               throw new ApiException("获取区域信息出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取区域信息出错"));
          }
          return new ResponseEntity<>(new Result<>(from,errors), HttpStatus.OK);
     }

     /**
      * 根据code查询该code的区域详细信息
      *
      * @param adcode
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/address")
     @ApiOperation(value = "根据区域码，获取完整的地址")
     public ResponseEntity<Result<Map<String,String>>> address(
                              @RequestParam @ApiParam(value = "区域码", required = true)  Long adcode
     ){
          Map<String,String> rs = new HashMap<>();
          String backInfo = "未知地址";
          try {
               backInfo = this.dictAreaService.getAllAddress(adcode);
          } catch (Exception e) {
               log.error("获取完整信息出错", e);
               throw new ApiException("获取完整信息出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取完整信息出错"));
          }
          rs.put("address", backInfo);
          return new ResponseEntity<>(new Result<>(rs, errors), HttpStatus.OK);
     }


     /**
      * 获取区域信息和所有父级信息，包含父级的子集列表
      *
      * @param adcode
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/all")
     @ApiOperation(value = "获取区域信息和所有父级信息，包含父级的子集列表")
     public ResponseEntity<Result<DictAreaForm>> all(
             @RequestParam @ApiParam(value = "区域码", required = true)  Long adcode
     ){

          DictAreaForm dictAreaForm;
          DictAreaForm form = this.dictAreaService.checkArea(adcode);
          try {
               dictAreaForm = this.dictAreaService.getAll(form);
          } catch (Exception e) {
               log.error("获取完整信息出错", e);
               throw new ApiException("获取完整信息出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取完整信息出错"));
          }
          return new ResponseEntity<>(new Result<>(dictAreaForm, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/setRedis")
     @ApiOperation(value = "地区信息进入缓存")
     public ResponseEntity<Result<?>> setRedis(){
          this.dictAreaService.setRedis();
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }
}
