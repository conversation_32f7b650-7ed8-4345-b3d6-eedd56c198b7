package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.OrgExpandService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/org-expand")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "组织拓展信息", tags = ["组织拓展信息"])
@Validated
class OrgExpandController(@Autowired val errors: Errors,
                          @Autowired val orgExpandService: OrgExpandService) {

    @GetMapping("/get-org-period-info")
    @ApiOperation("查询组织届次信息和人数")
    @HttpMonitorLogger
    fun getOrgPeriodInfo(@RequestParam("org_id") orgId: Long): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.orgExpandService.getOrgExpandInfo(orgId),
                errors),
            HttpStatus.OK
        )
    }

    @GetMapping("/get-child-org-coordinate")
    @ApiOperation("查询坐标不能为空的组织")
    @HttpMonitorLogger
    fun getChildOrgListCoordinate(@RequestParam("org_id") orgId: Long): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.orgExpandService.getChildOrgListCoordinate(orgId),
                errors),
            HttpStatus.OK
        )
    }
}