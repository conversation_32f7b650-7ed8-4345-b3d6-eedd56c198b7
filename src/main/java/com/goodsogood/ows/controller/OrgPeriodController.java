package com.goodsogood.ows.controller;

import cn.hutool.core.lang.Assert;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.EvalHeaderHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgMgrForm;
import com.goodsogood.ows.model.vo.OrgPeriodForm;
import com.goodsogood.ows.model.vo.PeriodFindOrgsForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgPeriodService;
import com.goodsogood.ows.service.OrgPeriodSyncService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.ParseException;
import java.util.List;
import java.util.Set;

/**
 * Auther: ruoyu
 * Date: 19-4-12
 * Description: 党组织委员会管理
 */
@RestController
@RequestMapping("/period")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class OrgPeriodController {

    private final OrgPeriodService orgPeriodService;
    private final OrgPeriodSyncService orgPeriodSyncService;
    private final Errors errors;

    @Autowired
    public OrgPeriodController(OrgPeriodService orgPeriodService,
                               OrgPeriodSyncService orgPeriodSyncService,
                               Errors errors) {
        this.orgPeriodService = orgPeriodService;
        this.orgPeriodSyncService = orgPeriodSyncService;
        this.errors = errors;
    }


    /**
     * @param form          请求对象
     * @param bindingResult 验证
     * @param headers       请求头
     * @return 创建委员会届次
     */
    @PostMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody OrgPeriodForm form,
                                         BindingResult bindingResult,
                                         @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);

        try {
            return new ResponseEntity<>(new Result<>(orgPeriodService.add(form, sysHeader), errors), HttpStatus.OK);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException("创建组织届次失败", new Result(errors, 4650, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * @param form          请求对象
     * @param bindingResult 验证
     * @param headers       请求头
     * @return 修改委员会届次
     */
    @PostMapping("/update")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> update(@Valid @RequestBody OrgPeriodForm.UpdateForm form,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        try {
            return new ResponseEntity<>(new Result<>(orgPeriodService.update(form, sysHeader), errors), HttpStatus.OK);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException("修改组织届次失败", new Result(errors, 4653, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * @param periodId 会员会id
     * @param headers  请求头
     * @return 删除委员会届次信息
     */
    @GetMapping("/delete/{periodId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delete(@PathVariable long periodId, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.delete(periodId, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * @param periodId id
     * @return 委员会届次详细信息
     */
    @GetMapping("/detail/{periodId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> detail(@PathVariable long periodId, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.detail(periodId, sysHeader), errors), HttpStatus.OK);
    }


    /**
     * @param orgId   组织id
     * @param headers 请求头
     * @return 查询委员会届次
     */
    @GetMapping("/find")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> find(@RequestParam("org_id") @Range(min = 1, message = "{NotNull.OrgLeaderForm.orgId}") long orgId,
                                          @RequestParam(name = "page_size", defaultValue = "10") @Range(min = 1, max = 500) Integer pageSize,
                                          @RequestParam(name = "page_num", defaultValue = "1") @Range(min = 1, max = Integer.MAX_VALUE) Integer pageNum,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(orgPeriodService.find(orgId, sysHeader, pageSize, pageNum), HttpStatus.OK);

    }

    /**
     * @param orgId   组织id
     * @param headers 请求头
     * @return 查询委员会届次组织信息
     */
    @GetMapping("/find-org/{orgId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrg(@PathVariable Long orgId,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findOrg(orgId, sysHeader), errors), HttpStatus.OK);

    }

    /**
     * 互动活动需求需要 查询支委会信息
     *
     * @param orgId
     * @return
     */
    @GetMapping("/get-all/{orgId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> getAll(@PathVariable(value = "orgId") Long orgId) {

        return new ResponseEntity<>(new Result<>(orgPeriodService.getAll(orgId), errors), HttpStatus.OK);
    }

    /**
     * 查询支委会开始时间..
     * 党小组数量
     *
     * @return
     */
    @PostMapping("/find-orgs")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgs(@Valid @RequestBody List<PeriodFindOrgsForm> list) throws ParseException {

        return new ResponseEntity<>(new Result<>(orgPeriodService.findOrgs(list), errors), HttpStatus.OK);

    }


    /**
     * 移动端 h5 组织换届首页接口
     *
     * @return
     */
    @GetMapping("/find-all-app")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findAllApp(@RequestHeader HttpHeaders headers,
                                                @RequestParam(name = "org_id", required = false) Long orgId) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);

        return new ResponseEntity<>(new Result<>(orgPeriodService.findAllApp(orgId == null ? sysHeader.getUoid() : orgId, Assert.notNull(sysHeader.getRegionId())), errors), HttpStatus.OK);
    }

    /**
     * 移动端 h5 组织换届 工委级中间表
     *
     * @param type 1:本月到期
     *             <p>
     *             2:6个月到期的数量
     *             <p>
     *             3:7人以上未设置支委会
     *             <p>
     *             4:7人以下设置了支委会
     * @return
     */
    @GetMapping("/find-top-orgs")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findTopOrgs(@RequestHeader HttpHeaders headers,
                                                 @RequestParam(name = "type", required = true) @Range(min = 1, max = 4) Integer type) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);

        return new ResponseEntity<>(new Result<>(orgPeriodService.findTopOrgs(sysHeader.getUoid(), type), errors), HttpStatus.OK);
    }

    /**
     * 移动端 h5 即将到期还为换届的支委会
     *
     * @param type    1　本月到期　2　6个月到期
     * @param page
     * @param size
     * @param headers
     * @return
     */
    @GetMapping("/find-expire-app")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findExpireApp(@RequestParam(name = "type")
                                                   @Range(min = 1, max = 2) Integer type,
                                                   @RequestParam(name = "page", defaultValue = "1") Integer page,
                                                   @RequestParam(name = "size", defaultValue = "20") Integer size,
                                                   @RequestParam(name = "org_id", required = false) Long orgId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findExpireApp(orgId == null ? sysHeader.getUoid() : orgId, type, page, size, Assert.notNull(sysHeader.getRegionId())), errors), HttpStatus.OK);
    }


    /**
     * 移动端 h5 支委会设置异常详情
     *
     * @param type    1: 7人以上未设置支委会 2: 7人以下设置了支委会
     * @param page
     * @param size
     * @param headers
     * @return
     */
    @GetMapping("/find-abnormal-app")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findAbnormalApp(@RequestParam(name = "type")
                                                     @Range(min = 1, max = 2) Integer type,
                                                     @RequestParam(name = "page", defaultValue = "1") Integer page,
                                                     @RequestParam(name = "size", defaultValue = "20") Integer size,
                                                     @RequestParam(name = "org_id", required = false) Long orgId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findAbnormalApp(orgId == null ? sysHeader.getUoid() : orgId, type, page, size, Assert.notNull(sysHeader.getRegionId())), errors), HttpStatus.OK);
    }

    /**
     * 手动初始化当前天监督预警　组织换届中间表
     *
     * @return
     */
    @GetMapping("/init-period")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> initPeriod() {
        orgPeriodService.runOfCommitteeExpireRegion();
        return new ResponseEntity<>(new Result<>("ok", errors), HttpStatus.OK);
    }

    /**
     * 查询上个月支部换届数量
     */
    @GetMapping("/last-month-change")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> lastMonthChange(@RequestParam(value = "org_id_list") String orgIdList) {
        return new ResponseEntity<>(new Result<>(orgPeriodService.getChangeLastMonth(orgIdList), errors), HttpStatus.OK);
    }

    /**
     * 同步12371届次信息到本系统中
     */
    @PostMapping("/syncOrgPeriod")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> syncOrgPeriods(@RequestBody(required = false) Set<Long> set) {
//        orgPeriodSyncService.syncOrgPeriods(set);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 获取所有有效支委会关联的组织 并添加标签
     */
    @GetMapping("/syncOrgPeriodTag")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> syncOrgPeriodTag() {
        orgPeriodSyncService.syncOrgPeriodTag();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 根据时间查询所有符合时间的届次里的人员
     */
    @GetMapping("/find-users-by-time")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findUsersByTime(@RequestParam("query_time") String queryTime,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findUsersByTime(queryTime, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * 根据orgId,regionId 查询最新的届次成员信息
     */
    @GetMapping("/find-new-users")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findNewUsersByOrgId(@RequestParam("org_id") String orgId,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getAppHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findNewUsersByOrgId(orgId, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * @param form    组织id列表
     * @param headers 请求头
     * @return 查询委员会届次组织信息
     */
    @PostMapping("/find-leader-by-org-id")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findLeaderByOrgId(@Valid @RequestBody OrgMgrForm form,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        return new ResponseEntity<>(new Result<>(this.orgPeriodService.findLeaderByOrgId(form, sysHeader), errors), HttpStatus.OK);
    }
}
