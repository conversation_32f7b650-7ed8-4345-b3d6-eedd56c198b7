package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.MienResponseEntity;
import com.goodsogood.ows.model.db.PostDemonstrationEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.DemonstratService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: Stone
 * @CreateTime: 2023/10/14 14:58
 * @Description:
 */
@RestController
@RequestMapping("/fc")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class DemonstratController {
    private final Errors errors;
    private final DemonstratService demonstratService;

    public DemonstratController(Errors errors, DemonstratService demonstratService) {
        this.errors = errors;
        this.demonstratService = demonstratService;
    }

    /**
     * 新增示范岗
     */
    @HttpMonitorLogger
    @PostMapping("/addDemonstrationPost")
    @ApiOperation("新增示范岗")
    @RepeatedCheck
    public ResponseEntity<Result<PostDemonstrationEntity>> addDemonstrationPost(@RequestHeader HttpHeaders headers,
                                                                                @Valid @RequestBody PostDemonstrationEntity entity,
                                                                                BindingResult result) {
        PostDemonstrationEntity postDemonstrationEntity = demonstratService.addDemonstrationPost(headers, entity);
        return new ResponseEntity<>(new Result<>(postDemonstrationEntity, errors), HttpStatus.OK);
    }


    /**
     * 删除一个示范岗
     */
    @HttpMonitorLogger
    @GetMapping("/deleteDemonstrationPostById")
    @ApiOperation("删除示范岗")
    public ResponseEntity<Result<Integer>> deleteDemonstrationPostById(@RequestHeader HttpHeaders headers,
                                                                       @RequestParam Long[] id) {
        return new ResponseEntity<>(new Result<>(demonstratService.deleteDemonstrationPostById(id), errors), HttpStatus.OK);
    }

    /**
     * 更新一个示范区
     */
    @HttpMonitorLogger
    @PostMapping("/updateDemonstrationPost")
    @ApiOperation("更新示范区")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> updateDemonstrationPost(@RequestHeader HttpHeaders headers,
                                                                   @Valid @RequestBody PostDemonstrationEntity entity,
                                                                   BindingResult result) {
        return new ResponseEntity<>(new Result<>(demonstratService.updateDemonstrationPost(headers, entity), errors), HttpStatus.OK);
    }


    /**
     * 查询示范岗详情页面
     */
    @HttpMonitorLogger
    @GetMapping("/getPostDemonstrationById")
    @ApiOperation("查询示范岗详情页面")
    public ResponseEntity<Result<PostDemonstrationEntity>> getPostDemonstrationById(@RequestHeader HttpHeaders headers, @RequestParam Long id) {
        var data = demonstratService.getPostDemonstrationById(id);
        if (data == null) {
            throw new ApiException("责任区[" + id + "]找不到", new Result<>(errors, 9404, HttpStatus.OK.value(), "责任区[" + id + "]"));
        }
        return new ResponseEntity<>(new Result<>(data, errors), HttpStatus.OK);
    }

    /**
     * 查询示范岗
     */
    @HttpMonitorLogger
    @PostMapping("/getDemonstrationPostByCondition")
    @ApiOperation("查询示范岗")
    public ResponseEntity<Result<List<PostDemonstrationEntity>>> getDemonstrationPostByCondition(@RequestHeader HttpHeaders headers,
                                                                                                 @Valid @RequestBody PostDemonstrationEntity entity,
                                                                                                 @RequestParam(required = false) Integer page,
                                                                                                 @RequestParam(value = "page_size", required = false) Integer pageSize,
                                                                                                 BindingResult result) {
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 20 : pageSize;
        return new ResponseEntity<>(new Result<>(demonstratService.getDemonstrationPostByCondition(headers, entity, page, pageSize), errors), HttpStatus.OK);

    }


}
