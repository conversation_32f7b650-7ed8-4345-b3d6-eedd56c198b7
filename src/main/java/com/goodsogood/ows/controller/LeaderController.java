package com.goodsogood.ows.controller;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.EvalHeaderHelper;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.FindAutoOrgsForm;
import com.goodsogood.ows.model.vo.OrgLeaderForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SasLeaderOrgForm;
import com.goodsogood.ows.service.LeaderService;
import com.goodsogood.ows.service.OrgPeriodService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Set;


/**
 * Auther: ruoyu
 * Date: 19-4-10
 * Description: 单位领导班子管理
 */
@RestController
@RequestMapping("/leader")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class LeaderController {

    private final Errors errors;
    private final LeaderService leaderService;
    private final ObjectMapper OBJECTMAPPER = new ObjectMapper();
    private final OrgPeriodService orgPeriodService;


    @Autowired
    public LeaderController(Errors errors, LeaderService leaderService, OrgPeriodService orgPeriodService) {
        this.errors = errors;
        this.leaderService = leaderService;
        this.orgPeriodService = orgPeriodService;
    }

    /* 保存数据字典类型相对应的code 1030,1033 职务　1031职级 */
    public static final HashMap<Integer, String> optionType = new HashMap<>(3);

    static {
        optionType.put(1, "1030,1033");
        optionType.put(2, "1031");
        optionType.put(3, "1034");
    }

    /**
     * 新增领导班子成员
     * 同修改接口　如修改则逻辑删除之前的数据　再新增一条新的数据
     *
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @RequestMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody OrgLeaderForm form,
                                         BindingResult bindingResult,
                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        try {
            return new ResponseEntity<>(new Result<>(leaderService.add(form, sysHeader), errors), HttpStatus.OK);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException("创建领导班子成员失败", new Result(errors, 4600, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 删除班子成员
     *
     * @param leaderId
     * @return
     */
    @GetMapping("/delete/{leaderId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delete(@PathVariable Long leaderId,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);

        try {
            return new ResponseEntity<>(new Result<>(leaderService.delete(leaderId, sysHeader), errors), HttpStatus.OK);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException("删除领导班子失败", new Result(errors, 4609, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 获取组织的领导班子信息
     *
     * @param orgId
     * @param headers
     * @return
     */
    @GetMapping("/find/{orgId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findLeaderOfOrg(@PathVariable Long orgId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);

        return new ResponseEntity<>(new Result<>(leaderService.findLeaderOfOrg(orgId, sysHeader), errors), HttpStatus.OK);

    }


    /**
     * 获取组织的领导班子信息 领导班子优化后借口
     *
     * @param orgId     班子成员所属组织id  组织单位id
     * @param name      领导姓名
     * @param position  行政职务
     * @param startTime 从该时间后任职的领导班子
     * @param endTime   从该时间前任职的领导班子
     * @param isLeader  是否市管领导干部
     * @param isHead    是否单位负责人
     * @param page      页数
     * @param size      每页显示数量
     * @param headers
     * @return
     */
    @GetMapping("/find-now")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findLeaderOfOrgNow(@RequestParam(value = "org_id") Long orgId,
                                                        @RequestParam(value = "name", required = false) @Length(max = 50, message = "{Length.findLeaderOfOrgNow.name}") String name,
                                                        @RequestParam(value = "position", required = false) @Length(max = 50, message = "{Length.findLeaderOfOrgNow.position}") String position,
                                                        @RequestParam(value = "start_time", required = false) String startTime,
                                                        @RequestParam(value = "end_time", required = false) String endTime,
                                                        @RequestParam(value = "is_leader", required = false) @Range(min = 1, max = 2) Integer isLeader,
                                                        @RequestParam(value = "is_head", required = false) @Range(min = 1, max = 2) Integer isHead,
                                                        @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                        @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);

        return new ResponseEntity<>(new Result<>(leaderService.findLeaderOfOrgNow(orgId, name, position, startTime, endTime, isLeader, isHead, page, size, sysHeader), errors), HttpStatus.OK);

    }

    @GetMapping("/join-sequence")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> joinSeq(@RequestParam(value = "leader_ids") List<Long> leaderIds,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        leaderService.joinSequence(leaderIds, sysHeader);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询当前及其下级组织关联单位列表
     *
     * @param orgId       组织id
     * @param orgUnitName 关联单位名称
     * @param hasLeader   是否设置领导班子
     * @param page        页数
     * @param size        每页显示条数
     * @param headers
     * @return
     */
    @GetMapping("/org-list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgList(@RequestParam(value = "org_id") Long orgId,
                                                 @RequestParam(value = "org_unit_name", required = false) @Length(max = 50, message = "{Length.findOrgList.orgUnitName}") String orgUnitName,
                                                 @RequestParam(value = "has_leader", required = false) @Range(min = 1, max = 2) Integer hasLeader,
                                                 @RequestParam(value = "page", required = false, defaultValue = "1") @Range(min = 1) Integer page,
                                                 @RequestParam(value = "size", required = false, defaultValue = "10") @Range(min = 1) Integer size,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);

        return new ResponseEntity<>(new Result<>(leaderService.findOrgList(orgId, orgUnitName, hasLeader, page, size, sysHeader), errors), HttpStatus.OK);
    }


//    @RequestMapping("/update")
//    public ResponseEntity<Result<?>> update(@Valid @RequestBody OrgLeaderForm form,
//                                            BindingResult bindingResult,
//                                            @RequestHeader HttpHeaders headers) {
//
//        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
//        if (form.getLeaderId() == null) {
//            throw new ApiException("领导班子主键id为空", new Result(errors, 4605, HttpStatus.OK.value()));
//        }
//
//        try {
//            return new ResponseEntity<>(new Result<>(leaderService.update(form, sysHeader), errors), HttpStatus.OK);
//        } catch (ApiException e) {
//            throw e;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new ApiException("修改领导班子成员失败", new Result(errors, 4604, HttpStatus.INTERNAL_SERVER_ERROR.value()));
//        }
//
//    }

    /**
     * 通用获取职务/职级数据字典
     *
     * @param type   1:职务 2:职级
     * @param filter 1:获取书记 2:获取副书记 3:获取除了书记和副书记外的职务 在type=1时生效，不传默认查全部
     */
    @GetMapping("/option/{type}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> getOption(@PathVariable
                                               @Range(min = 1, max = 3, message = "{Range.leader.option.type}") Integer type,
                                               @RequestParam(name = "filter", required = false) @Range(min = 1, max = 3, message = "超出类型限制") Integer filter,
                                               @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(leaderService.getOption(optionType.get(type), filter), errors), HttpStatus.OK);
    }

    /**
     * 模糊查询人员
     *
     * @param orgId      组织id
     * @param searchType 1:全表搜索 2:党组织树中当前组织及其所有下级组织中的成员
     * @param userName   人名
     * @return
     */
    @GetMapping("/search/{orgId}/{searchType}/{userName}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> searchPeople(@PathVariable Long orgId,
                                                  @PathVariable @Range(min = 1, max = 2, message = "{Range.leader.search.type}") Integer searchType,
                                                  @PathVariable /*@Length(min = 2, message = "{Range.leader.search.userName}")*/ String userName,
                                                  @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader sysHeader = EvalHeaderHelper.getSysHeader(headers, errors);
        try {
            userName = URLDecoder.decode(userName, "utf-8");
            if (StringUtils.isBlank(userName) || userName.length() < 2) {
                throw new ApiException("最少填写两个字", new Result(errors, 4618, HttpStatus.OK.value()));
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            throw new ApiException("URL解码失败:" + userName, new Result(errors, 4617, HttpStatus.OK.value(), userName));
        } catch (ApiException e) {
            throw e;
        }

        return new ResponseEntity<>(new Result<>(leaderService.searchPeople(orgId, searchType, userName, sysHeader), errors), HttpStatus.OK);
    }

    /**
     * 批量获取组织类别
     *
     * @param orgs
     * @return
     */
    @PostMapping("/find-org-type")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgType(@RequestBody List<Long> orgs) {

        return new ResponseEntity<>(new Result<>(leaderService.findOrgType(orgs), errors), HttpStatus.OK);

    }


    /**
     * 自动发放统计组织数据
     *
     * @return
     */
    @PostMapping("/find-auto-orgs")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findAutoOrgs(@Valid @RequestBody FindAutoOrgsForm form,
                                                  BindingResult bindingResult,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(leaderService.findAutoOrgs(form, sysHeader), errors), HttpStatus.OK);

    }


    /**
     * 获取组织与关联单位信息
     *
     * @param orgId
     * @return
     */
    @GetMapping("/org-type/{orgId}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgType(@PathVariable Long orgId) {

        return new ResponseEntity<>(new Result<>(leaderService.findOrgTypeAndOwnerInfo(orgId), errors), HttpStatus.OK);

    }

    /**
     * 获取下领导班子相关数据
     * 考核二期 提供的接口
     *
     * @param orgIds  需要查询的组织
     * @param endTime 结束时间 yyyy-MM-dd HH:mm:ss
     * @return 数据采集相关需要获取的组织数据集
     */
    @PostMapping("/find-org-creates")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgCreates(@RequestBody Set<Long> orgIds,
                                                    @RequestParam(value = "end_time") String endTime,
                                                    @RequestParam(value = "name", required = false) String name) {

        return new ResponseEntity<>(new Result<>(leaderService.findOrgCreates(orgIds, endTime, name), errors), HttpStatus.OK);

    }

    /**
     * 手动推送　组织支委会换届通知
     *
     * @return
     */
    @GetMapping("/the-push-request-committee")
    public ResponseEntity<Result<?>> thePushRequestOfCommittee(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        orgPeriodService.runOfCommittee(sysHeader.getRegionId());
        return new ResponseEntity<>(new Result<>("OK", errors), HttpStatus.OK);

    }

    /**
     * 用于提供给测试人员　确认组织换届推送数据的正确性
     *
     * @return
     */
    @GetMapping("/find-expire-of-committee-test")
    public ResponseEntity<Result<?>> findExpireOfCommittee(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(orgPeriodService.findExpireOfCommittee(sysHeader.getRegionId()), errors), HttpStatus.OK);
    }

//    @GetMapping("/test")
//    public void test() throws JsonProcessingException {
//
//        PushRequest pushRequest = new PushRequest();
//        pushRequest.setTemplateId(24L);
//        pushRequest.setChannelType((byte) 1);
//        pushRequest.setSource("push");
//
//        ArrayList<CommitteeCallbackEntity> needChangeCommittee = new ArrayList<>();
//        CommitteeCallbackEntity committeeCallbackEntities = new CommitteeCallbackEntity();
//        committeeCallbackEntities.setOrgName("王若宇测试组织");
//        committeeCallbackEntities.setUserId(63520L);
//        committeeCallbackEntities.setPeriodEndTime("2019年5月5日");
//        needChangeCommittee.add(committeeCallbackEntities);
//
//        pushRequest.setData(needChangeCommittee);
//
//        System.out.println(OBJECTMAPPER.writeValueAsString(pushRequest));
//
//        String url = "http://" + pushService + "/global/push/diff";
//
//        System.out.println(pushRequest.toString());
//
//        HttpHeaders httpHeaders = new HttpHeaders();
//        // 调用远程方法
//        try {
//
//            Long l = RemoteApiHelper.post(this.restTemplate, url, pushRequest, httpHeaders, new TypeReference<Result<Long>>() {
//            });
//            System.out.println(OBJECTMAPPER.writeValueAsString(l));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }


    /**
     * 党务看板统计-查询所有领导干部+所在组织+组织信息
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/findAllLeader")
    @ApiOperation("查询所有的领导班子成员")
    public ResponseEntity<Result<?>> findAllLeader(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        List<SasLeaderOrgForm> allLeader = this.leaderService.findAllLeader(Assert.notNull(sysHeader.getRegionId()));
        return new ResponseEntity<>(new Result<>(allLeader, errors), HttpStatus.OK);
    }

    /**
     * 根据用户Id 查询相关组织信息
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-by-org")
    @ApiOperation("根据用户Id 查询相关组织信息")
    public ResponseEntity<Result<?>> findUserByOrg(@RequestBody List<Long> users,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(leaderService.findUserByOrg(users, Assert.notNull(sysHeader.getRegionId())), errors), HttpStatus.OK);
    }


    /**
     * 根据组织id 查询党员干部信息
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-org-by-leader")
    @ApiOperation("查询党员干部信息")
    public ResponseEntity<Result<?>> findOrgByLeader(@RequestParam(name = "org_id") Long orgId,
                                                     @RequestParam(name = "type") @Range(min = 1, max = 4) Integer type) {
        return new ResponseEntity<>(new Result<>(leaderService.findOrgByLeader(orgId, type), errors), HttpStatus.OK);
    }

    /**
     * 逻辑调整更新人员领导班子相关的标签
     *
     * @param regionId 需要更新的区县
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/update-leader-tags")
    @ApiOperation("逻辑调整更新人员领导班子相关的标签")
    public ResponseEntity<Result<?>> updateLeaderTags(@RequestParam(name = "region_id") Long regionId) {
        return new ResponseEntity<>(new Result<>(leaderService.updateLeaderTags(regionId), errors), HttpStatus.OK);
    }

    /**
     * 活动录入需要 根据组织ID查询联系领导班子
     *
     * @param isTabacco 1:是 2:否 是否为烟草查询类型 如果是烟草则走党组逻辑 如果不是则走正常逻辑
     */
    @HttpMonitorLogger
    @GetMapping("/find-leader-contact-meeting")
    @ApiOperation("活动录入需要 根据组织ID查询联系领导班子")
    public ResponseEntity<Result<?>> findLeaderContactMeeting(
            @RequestParam(name = "org_id") Long orgId,
            @RequestParam(name = "is_tabacco", required = false, defaultValue = "2")
            @Range(min = 1, max = 2) Integer isTabacco) {
        return new ResponseEntity<>(new Result<>(leaderService.findLeaderContactMeeting(orgId, isTabacco), errors), HttpStatus.OK);
    }
}
