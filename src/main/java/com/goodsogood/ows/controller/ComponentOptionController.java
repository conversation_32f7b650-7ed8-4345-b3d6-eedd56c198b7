package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Groups;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.vo.ComponentOptionForm;
import com.goodsogood.ows.model.vo.MoveComponentForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.ComponentOptionService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @create 2019-07-25 08:59
 **/
@RestController
@RequestMapping("/com_option")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
@Log4j2
public class ComponentOptionController {

     private final Errors errors;
     private final ComponentOptionService componentOptionService;

     @Autowired
     public ComponentOptionController(Errors errors, ComponentOptionService componentOptionService) {
          this.errors = errors;
          this.componentOptionService = componentOptionService;
     }


     /**
      * 添加组件列表选项信息
      */
     @HttpMonitorLogger
     @PostMapping("/add")
     public ResponseEntity<Result<?>> add(HttpServletRequest request,
                                          @Validated(value = Groups.Insert.class)
                                          @RequestBody ComponentOptionForm componentOptionForm,
                                          BindingResult br) {
          log.debug("添加组件列表选项信息：componentOptionForm = {}", componentOptionForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Map<String, Integer> componentList = componentOptionService.add(componentOptionForm,request);
          Result<Map<String, Integer> > result = new Result<>(componentList, errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }


     /**
      * 编辑组件列表选项信息
      */
     @HttpMonitorLogger
     @PostMapping("/edit")
     public ResponseEntity<Result<?>> edit(HttpServletRequest request,
                                           @Validated(value = Groups.Update.class)
                                           @RequestBody ComponentOptionForm componentOptionForm,
                                           BindingResult br) {
          log.debug("编辑组件列表选项信息：componentOptionForm = {}", componentOptionForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Integer editResult = componentOptionService.edit(componentOptionForm,request);
          if(editResult<1){
               throw new ApiException("修改组库选项失败！", new Result<>(errors, 21012, HttpStatus.OK.value()));
          }
          Result<String > result = new Result<>("", errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }

     /**
      * 删除组件列表选项信息
      */
     @HttpMonitorLogger
     @PostMapping("/del")
     public ResponseEntity<Result<?>> del(HttpServletRequest request,
                                          @Validated(value = {Groups.PrimaryKey.class,Groups.Del.class})
										  @RequestBody ComponentOptionForm componentOptionForm,
                                          BindingResult br) {
          log.debug("删除组件列表选项信息：componentOptionForm = {}", componentOptionForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Integer delResult = componentOptionService.del(componentOptionForm,request);
          if(delResult<1){
               throw new ApiException("删除组库选项失败！", new Result<>(errors, 21013, HttpStatus.OK.value()));
          }
          Result<String> result = new Result<>("", errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }


     /**
      * 移动组件列表选项信息
      */
     @HttpMonitorLogger
     @PostMapping("/getDetail")
     public ResponseEntity<Result<?>> move(@Validated @RequestBody
                                           MoveComponentForm moveComponentForm,
                                           BindingResult br) {
          log.debug("移动组件列表选项信息：moveComponentForm = {}", moveComponentForm);
          if(br.hasErrors()){
               handlerParameterError(br);
          }
          Boolean delResult = componentOptionService.move(moveComponentForm);
          if(!delResult){
               throw new ApiException("移动组库选项失败！",
                       new Result<>(errors, 21014, HttpStatus.OK.value()));
          }
          Result<String> result = new Result<>("", errors);
          return new ResponseEntity<>(result, HttpStatus.OK);
     }


     /*
      *处理请求异常
      * @param br
      */
     public  void  handlerParameterError(BindingResult br){
          String message= br.getFieldError().getDefaultMessage();
          throw new ApiException(message ,new Result<>(errors, 200000, HttpStatus.OK.value(),message));
     }
}
