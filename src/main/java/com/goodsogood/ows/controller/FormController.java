package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.QueryConfigEntity;
import com.goodsogood.ows.model.db.TableConfigEntity;
import com.goodsogood.ows.model.vo.QueryConfigForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.TableConfigForm;
import com.goodsogood.ows.service.FormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

/**
 * 表单管理
 *
 * <AUTHOR>
 * @date 2020-08-18 11:13
 * @since 3.0.1
 **/
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "表单", tags = {"表单管理"})
@RequestMapping("/form")
@Validated
public class FormController {

     private final Errors errors;
     private final FormService formService;

     @Autowired
     public FormController(Errors errors, FormService formService) {
          this.errors = errors;
          this.formService = formService;
     }

     /**
      * 查询框结构查询
      *
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/query-config-list")
     @ApiOperation("查询框结构查询")
     public ResponseEntity<Result<?>> queryConfigList(@RequestParam(value = "own", defaultValue = "1")
                                                      Integer own,
                                                      @RequestParam(value = "op_type", defaultValue = "1")
                                                      Integer opType,
                                                      @RequestParam(value = "level", defaultValue = "1")
                                                      Integer level,
                                                      @RequestParam(value = "org_type",required = false)
                                                                   Integer orgType,
                                                      @RequestHeader HttpHeaders headers) {
         HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
         if(null!=orgType){
             header.setOrgType(orgType);
         }
         return new ResponseEntity<>(new Result<>(this.formService.
                  queryConfigList(own, header,level,opType), errors), HttpStatus.OK);
     }

    /**
     * 查询框结构查询
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/query-config-update")
    @ApiOperation("查询框结构查询")
    public ResponseEntity<Result<?>> queryConfigUpdate(@RequestParam(value = "own", defaultValue = "1")
                                                             Integer own,
                                                     @RequestParam(value = "level", defaultValue = "1")
                                                             Integer level,
                                                     @RequestParam(value = "org_type",required = false)
                                                             Integer orgType,
                                                     @RequestBody  List<QueryConfigForm> list,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if(null!=orgType){
            header.setOrgType(orgType);
        }
        return new ResponseEntity<>(new Result<>(this.formService.
                queryConfigUpdate(own, header,level,list), errors), HttpStatus.OK);
    }


    /**
     * 查询框结构设置
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/query-config-set")
    @ApiOperation("查询框结构设置")
    public ResponseEntity<Result<?>> queryConfigSet(@RequestParam(value = "own", defaultValue = "1")
                                                    Integer own,
                                                    @RequestBody QueryConfigEntity queryConfig,
                                                    @RequestHeader HttpHeaders headers) throws Exception{
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.formService.
                queryConfigSet(queryConfig,own, header), errors), HttpStatus.OK);
    }

     /**
      * 表格表头结构查询
      *
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/table-config-list")
     @ApiOperation("表格表头结构查询")
     public ResponseEntity<Result<?>> tableConfigList(@RequestParam(value = "own", defaultValue = "1")
                                                      Integer own,
                                                      @RequestParam(value = "type", defaultValue = "1")
                                                      Integer type,
                                                      @RequestParam(value = "org_type",required = false)
                                                      Integer orgType,
                                                      @RequestHeader HttpHeaders headers) {
         HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
         if(Objects.isNull(orgType)){
             orgType = header.getOrgType();
         }
         return new ResponseEntity<>(new Result<>(this.formService.
                  tableConfigList(own, type, orgType, header.getRegionId()), errors), HttpStatus.OK);
     }

    /**
     * 表格表头结构查询
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/table-config-update")
    @ApiOperation("表格表头结构更新")
    public ResponseEntity<Result<?>> tableConfigUpdate(@RequestParam(value = "own", defaultValue = "1")
                                                             Integer own,
                                                       @RequestParam(value = "type", defaultValue = "1")
                                                               Integer type,
                                                       @RequestParam(value = "org_type",required = false)
                                                             Integer orgType,
                                                       @RequestBody LinkedList<TableConfigForm> list,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if(null!=orgType){
            header.setOrgType(orgType);
        }
        return new ResponseEntity<>(new Result<>(this.formService.
                tableConfigUpdate(own, header,list,type), errors), HttpStatus.OK);
    }

    /**
     * 表格表头结构查询设置
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/table-config-set")
    @ApiOperation("表格表头结构查询设置")
    public ResponseEntity<Result<?>> tableConfigSet(@RequestParam(value = "own", defaultValue = "1")
                                                    Integer own,
                                                    @RequestBody TableConfigEntity tableConfig,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.formService.
                tableConfigSet(tableConfig,own, header), errors), HttpStatus.OK);
    }

     /**
      * 用户/组织表单列表查询
      * op_type=1 可以作为插入信息 备用 2：就是只查询key
      * type 表示这个字段可以共用 数据库用逗号分开
      * @return
      */
     @HttpMonitorLogger
     @GetMapping("/field-list")
     @ApiOperation("用户/组织表单列表查询")
     public ResponseEntity<Result<?>> fieldList(@RequestParam(value = "own", defaultValue = "1") Integer own,
                                                @RequestParam(value = "op_type", defaultValue = "1")
                                                Integer opType,
                                                @RequestParam(value = "org_type",required = false)
                                                Integer orgType,
                                                @RequestParam(value = "type") Integer type,
                                                @RequestHeader HttpHeaders headers) {
         HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
         if(null!=orgType){
             header.setOrgType(orgType);
         }
         List<QueryConfigForm> listForm=this.formService.fieldList(own, header,opType,type);
         return new ResponseEntity<>(new Result<>(listForm, errors),HttpStatus.OK);
     }


    /**
     * 用户/组织表单列表查询
     * op_type=1 可以作为插入信息 备用 2：就是只查询key
     * type 表示这个字段可以共用 数据库用逗号分开
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/field-list-update")
    @ApiOperation("用户/组织表单列表查询")
    public ResponseEntity<Result<?>> fieldListUpdate(@RequestParam(value = "own", defaultValue = "1")
                                                                 Integer own,
                                                     @Valid @RequestBody LinkedList<QueryConfigForm> listForm,
                                                     @RequestParam(value = "type") Integer type,
                                                     @RequestHeader HttpHeaders headers)
                                                     throws JsonProcessingException {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.formService.fieldListUpdate(own,type, header,listForm);
        return new ResponseEntity<>(new Result<>("suc", errors),HttpStatus.OK);
    }

    public static void main(String[] args) throws UnsupportedEncodingException {
        // 使用基本编码
        String base64encodedString = Base64.getEncoder().encodeToString("ows-test/water/logo.png?x-image-process=image/resize,P_50".getBytes(StandardCharsets.UTF_8));
        //String base64encodedString = Base64.getEncoder().encodeToString("ows-test/water/logo.png".getBytes("utf-8"));
        System.out.println("Base64 编码字符串 (基本) :" + base64encodedString);
    }
}
