package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgPeriodMemberAddForm;
import com.goodsogood.ows.model.vo.OrgPeriodMemberUpdateForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgPeriodMemberService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @program: ows-user-center
 * @description: 委员会成员
 * @author: taiqian.Luo
 * @create: 2019-04-11 12:53
 **/
@RestController
@RequestMapping("/org-period-member")
@CrossOrigin(origins = "*", maxAge = 3600)
@Log4j2
@Validated
public class OrgPeriodMemberController {

    private final OrgPeriodMemberService orgPeriodMemberService;
    private final Errors errors;

    @Autowired
    public OrgPeriodMemberController(OrgPeriodMemberService orgPeriodMemberService, Errors errors) {
        this.orgPeriodMemberService = orgPeriodMemberService;
        this.errors = errors;
    }

    /**
     * 添加委员会成员
     *
     * @param orgPeriodMemberAddForm
     * @param bindingResult
     * @param headers
     * @return
     */
    @PostMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody OrgPeriodMemberAddForm orgPeriodMemberAddForm,
                                         BindingResult bindingResult, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgPeriodMemberService.add(orgPeriodMemberAddForm, header);
    }

    /**
     * 修改委员会成员
     *
     * @param orgPeriodMemberUpdateForm
     * @param bindingResult
     * @param headers
     * @return
     */
    @PostMapping("/update")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> update(@Valid @RequestBody OrgPeriodMemberUpdateForm orgPeriodMemberUpdateForm,
                                            BindingResult bindingResult, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgPeriodMemberService.update(orgPeriodMemberUpdateForm, header);
    }

    /**
     * 委员会成员 查询
     *
     * @param periodId
     * @return
     */
    @GetMapping("/list")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> list(@RequestParam("period_id") @Range(min = 1, message = "Range.id") long periodId,
                                          @RequestParam("type") @Range(min = 1, max = 3, message = "{Range.OrgPeriodMemberAddForm.type}") byte type,
                                          @RequestParam(name = "page_size", defaultValue = "10") @Range(min = 1, max = 500) Integer pageSize,
                                          @RequestParam(name = "page_num", defaultValue = "1") @Range(min = 1, max = Integer.MAX_VALUE) Integer pageNum,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgPeriodMemberService.list(periodId, type, pageSize, pageNum, header);
    }

    /**
     * 委员会成员 删除
     *
     * @param periodMemberId
     * @param headers
     * @return
     */
    @PostMapping("/delete/{period_member_id}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delete(@Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                            @PathVariable("period_member_id") long periodMemberId, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgPeriodMemberService.delete(periodMemberId, header);
    }

    /**
     * 获取所有有效支委会关联的组织 并添加标签
     */
    @GetMapping("/boss")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> boss(@RequestParam(name = "period_id") Long regionId) {
        return orgPeriodMemberService.boss(regionId);
    }

    @PostMapping("/repay-period-score")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> repayPeriodScore(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.orgPeriodMemberService.repayPeriodScore(header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
