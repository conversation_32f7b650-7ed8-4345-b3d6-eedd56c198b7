//package com.goodsogood.ows.controller;
//
//import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
//import com.goodsogood.ows.component.Errors;
//import com.goodsogood.ows.helper.HeaderHelper;
//import com.goodsogood.ows.model.db.ExaminationConfigEntity;
//import com.goodsogood.ows.model.vo.ExamRegionConfig;
//import com.goodsogood.ows.model.vo.ExamSynConfig;
//import com.goodsogood.ows.model.vo.OrgSynInfoForm;
//import com.goodsogood.ows.model.vo.Result;
//import com.goodsogood.ows.service.ExaminationService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.log4j.Log4j2;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.validation.Valid;
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * @program: ows-user-center
// * @description: 考试系统controller
// * @author: Mr.LiGuoYongc
// * @create: 2020-12-24 15:41
// **/
//@RestController
//@Log4j2
//@Validated
//@CrossOrigin(origins = "*", maxAge = 3600)
//@Api(value = "考试系统", tags = {"考试系统"})
//@RequestMapping("/examination")
//public class ExaminationController {
//
//
//    private final Errors errors;
//    private final ExaminationService examinationService;
//
//    @Autowired
//    public ExaminationController(Errors errors, ExaminationService examinationService){
//        this.errors = errors;
//        this.examinationService = examinationService;
//    }
//
//    /**
//     * 同步组织信息到考试系统
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-org")
//    @ApiOperation("同步组织信息到考试系统")
//    public ResponseEntity<Result<?>> synOrg(@Valid @RequestBody List<OrgSynInfoForm> orgSynInfoForm,
//                                                @RequestParam(value = "is_init",required = false) Integer isInit,
//                                                @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        boolean result=examinationService.synOrgInfo(orgSynInfoForm,isInit,header);
//        if (result) {
//            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//        } else {
//            return new ResponseEntity<>(new Result<>("error", errors), HttpStatus.OK);
//        }
//    }
//
//
//    /**
//     * 同步组织用户信息到考试系统
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-user")
//    @ApiOperation("同步组织用户信息到考试系统")
//    public ResponseEntity<Result<?>> synUserInfo(
//                                                @RequestParam(value = "org_ids") List<Long> orgId,
//                                                @RequestParam(value = "is_init",required = false,
//                                                        defaultValue = "0") Integer isInit,
//                                                @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synUserInfo(orgId,isInit,header);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//    /**
//     * 验证是不是已经通过考试
//     */
//    @HttpMonitorLogger
//    @PostMapping("/valExamPass")
//    @ApiOperation("验证是不是已经通过考试")
//    public ResponseEntity<Result<?>> synUserInfo(
//            @RequestParam(value = "eid") Integer eid,
//            @RequestParam(value = "val_rule",defaultValue = "1") Integer valRule,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        Boolean aBoolean = examinationService.valExam(eid, header,valRule);
//        return new ResponseEntity<>(new Result<>(aBoolean, errors), HttpStatus.OK);
//    }
//
//    /**
//     * 添加或者移动用户到多个组里面
//     * 前提这个用户已经存在系统里面
//     * 然后才可以对用户进行复制 或 移动操作
//     * 复制过后  在总用户系统里面 还是只有一个用户，但是已经跟相关组织进行关联
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-user-group")
//    @ApiOperation("添加或者移动用户到多个组里面")
//    public ResponseEntity<Result<?>> synUserGroup(
//            @RequestParam(value = "org_id") Integer orgId,
//            @RequestParam(value = "user_ids") List<Long> userIds,
//            @RequestParam(value = "action") String action,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synUserGroup(orgId,userIds,action,header);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//
//    /**
//     *同步删除用户
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-del-user")
//    @ApiOperation("同步删除用户到考试系统")
//    public ResponseEntity<Result<?>> synDelUser(
//            @RequestParam(value = "user_ids") List<Long> user_ids,
//            @RequestParam(value = "org_id") Integer orgId,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synDelUser(user_ids,orgId,header);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//    /**
//     * 同步试卷信息
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-paper")
//    @ApiOperation("同步得到配置试卷信息")
//    public ResponseEntity<Result<?>> synPaper(
//            @RequestParam(value = "is_init",required = false,defaultValue = "0") Integer isInit,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synPaper(isInit,header);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//    /**
//     * 同步试卷信息
//     */
//    @HttpMonitorLogger
//    @PostMapping("/syn-paper-result")
//    @ApiOperation("同步考试结果")
//    public ResponseEntity<Result<?>> synPaperResult(
//            @RequestParam(value = "eid",defaultValue = "0") Integer eid,
//            @RequestParam(value = "sid",defaultValue = "0") Integer sid,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synPaperResult(eid,sid,header);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//
//    /**
//     * 更新区域考试配置信息
//     * 在t_examination_config
//     */
//    @HttpMonitorLogger
//    @PostMapping("/update-region-config")
//    @ApiOperation("更新区域配置信息")
//    public ResponseEntity<Result<?>> updateRegionConfig(
//            @Valid @RequestBody List<ExamRegionConfig> examRegionConfigs,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.updateRegionConfig(examRegionConfigs,header);
//        LinkedList<ExamRegionConfig> listPaper= examinationService.getRegionConfig(header);
//        return new ResponseEntity<>(new Result<>(listPaper, errors), HttpStatus.OK);
//    }
//
//    /**
//     * 更新区域考试配置信息
//     * 在t_examination_config
//     */
//    @HttpMonitorLogger
//    @PostMapping("/get-region-config")
//    @ApiOperation("得到区域配置信息")
//    public ResponseEntity<Result<?>> getRegionConfig(
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        LinkedList<ExamRegionConfig> listPaper= examinationService.getRegionConfig(header);
//        return new ResponseEntity<>(new Result<>(listPaper, errors), HttpStatus.OK);
//    }
//
//
//    /**
//     * 更新同步到配置信息
//     * 在t_examination_config
//     */
//    @HttpMonitorLogger
//    @PostMapping("/update-syn-config")
//    @ApiOperation("配置考试系统到积分配置")
//    public ResponseEntity<Result<?>> updateSynConfig(
//            @Valid @RequestBody List<ExamSynConfig> examRegionConfigs,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        ExaminationConfigEntity exam=examinationService.updateSynConfig(examRegionConfigs,header);
//        return new ResponseEntity<>(new Result<>(exam, errors), HttpStatus.OK);
//    }
//
//
//    /**
//     * 手动调用刷新考试 配置到考试 并且 到考试 系统
//     * 在t_examination_config
//     */
//    @HttpMonitorLogger
//    @PostMapping("/test-syn-config")
//    @ApiOperation("配置考试系统到积分配置")
//    public ResponseEntity<Result<?>> testSynConfig(
//            @RequestParam(value = "eid",defaultValue = "0") Integer eid,
//            @RequestParam(value = "preDay",defaultValue = "1") Integer preDay,
//            @RequestParam(value = "isSynExamResult",defaultValue = "0") Integer isSynExamResult,
//            @RequestParam(value = "isPullAll",defaultValue = "0") Integer isPullAll,
//            @RequestHeader HttpHeaders headers){
//        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
//        examinationService.synScore(eid,preDay,isSynExamResult,isPullAll);
//        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
//    }
//
//
//
//}
