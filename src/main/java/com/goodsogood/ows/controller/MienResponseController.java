package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.MienResponseEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MienResponseService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author: Stone
 * @CreateTime: 2023/10/14 09:47
 * @Description:  mienResponse--责任区 fc:党员风采
 *
 */

@RestController
@RequestMapping("/fc")
@Log4j2
@CrossOrigin(origins = "*",maxAge = 3600)
@Validated
public class MienResponseController {
    private final MienResponseService mienResponseService;

    private final Errors errors;
    @Autowired
    public MienResponseController(MienResponseService mienResponseService, Errors errors) {
        this.mienResponseService = mienResponseService;
        this.errors = errors;
    }

    /**
     * 新增一个责任区
     * @param headers
     * @param entity
     * @param result
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/addResponseMien")
    @ApiOperation("新增责任区")
    @RepeatedCheck
    public ResponseEntity<Result<MienResponseEntity>> addResponseMien(@RequestHeader HttpHeaders headers,
                                                                      @Valid @RequestBody MienResponseEntity entity,
                                                                      BindingResult result){
        MienResponseEntity mienResponseEntity = mienResponseService.addResponseMien( headers, entity);
        return new ResponseEntity<>(new Result<>(mienResponseEntity,errors), HttpStatus.OK);
    }

    /**
     * 删除一个责任区
     */
    @HttpMonitorLogger
    @GetMapping("/deleteResponseMienById")
    @ApiOperation("删除责任区")
    @RepeatedCheck
    public  ResponseEntity<Result<Integer>> deleteResponseMienById(@RequestHeader HttpHeaders headers,
                                                                               @RequestParam Long[] id){
        return new ResponseEntity<>(new Result<>(mienResponseService.deleteResponseMienById(id),errors),HttpStatus.OK);
    }


    /**
     *更新一个责任区
     */
    @HttpMonitorLogger
    @PostMapping("/updateResponseMien")
    @ApiOperation("更新责任区")
    @RepeatedCheck
    public ResponseEntity<Result<Integer>> updateResponseMien(@RequestHeader HttpHeaders headers,
                                                              @Valid @RequestBody MienResponseEntity entity,
                                                              BindingResult result){
        return new ResponseEntity<>(new Result<>(mienResponseService.updateResponseMien(headers,entity),errors),HttpStatus.OK);
    }

    /**
     * 查询责任区
     */
    @HttpMonitorLogger
    @PostMapping("/getResponseMienByCondition")
    @ApiOperation("查询责任区")
    public ResponseEntity<Result<List<MienResponseEntity>>> getResponseMienByCondition(@RequestHeader HttpHeaders headers,
                                                                                       @Valid @RequestBody MienResponseEntity entity,
                                                                                       @RequestParam(required = false) Integer page,
                                                                                       @RequestParam(value = "page_size",required = false) Integer pageSize,
                                                                                       BindingResult result){
        page = page == null ? 1:page;
        pageSize = pageSize == null ? 20:pageSize;
        return new ResponseEntity<>(new Result<>(mienResponseService.getResponseMienByCondition(headers,entity,page,pageSize),errors),HttpStatus.OK);

    }

    /**
     * 查询责任区详情页面
     */
    @HttpMonitorLogger
    @GetMapping("/getResponseMienById")
    @ApiOperation("查询责任区详情")
    public ResponseEntity<Result<MienResponseEntity>> getResponseMienById(@RequestHeader HttpHeaders headers, @RequestParam Long id){
        var data = mienResponseService.getResponseMienById(id);
        if(data == null){
            throw new ApiException("责任区[" + id+ "]找不到", new Result<>(errors, 9404, HttpStatus.OK.value(), "责任区[" + id + "]"));
        }
        return new ResponseEntity<>(new Result<>(data,errors),HttpStatus.OK);
    }

}
