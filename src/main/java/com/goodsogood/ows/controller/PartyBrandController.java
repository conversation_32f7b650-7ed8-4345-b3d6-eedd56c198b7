package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.mongo.PartyBrandBase;
import com.goodsogood.ows.model.vo.PartyBrandForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.service.OpenService;
import com.goodsogood.ows.service.PartyBrandService;
import com.goodsogood.ows.service.ScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/partyBrand")
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "党建品牌", tags = {"党建品牌"})
@Validated
public class PartyBrandController {
    private final Errors errors;
    private final PartyBrandService partyBrandService;
    private final ScoreService scoreService;
    private final OpenService openService;

    public PartyBrandController(Errors errors, PartyBrandService partyBrandService, ScoreService scoreService, OpenService openService) {
        this.errors = errors;
        this.partyBrandService = partyBrandService;
        this.scoreService = scoreService;
        this.openService = openService;
    }

    /**
     * 新建党建品牌
     */
    @HttpMonitorLogger
    @PostMapping(value = "/insert")
    @ApiOperation(value = "新建党建品牌")
    public ResponseEntity<Result<?>> insert(@RequestBody PartyBrandBase partyBrandBase,
                                            @RequestHeader HttpHeaders headers) throws JsonProcessingException {
        if (partyBrandService.insert(partyBrandBase)) {

            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            //从请求头里获取区县编号
            Long regionId = sysHeader.getRegionId();
            //从请求头里获取用户编号
            Long userId = sysHeader.getUserId();
            //异步判断是否给所属党组加分
            scoreService.addScoreByAsync(regionId,partyBrandBase.getOrgId(),16,userId,"1970-01", headers,74);
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        } else {
            throw new ApiException("每个组织仅能拥有一个党建品牌", new Result<>(errors, 33007, HttpStatus.OK.value()));
        }
    }

    /**
     * 删除党建品牌
     */
    @HttpMonitorLogger
    @GetMapping(value = "/delete")
    @ApiOperation(value = "删除党建品牌")
    public ResponseEntity<Result<?>> delete(@RequestParam(value = "org_id") Long orgId) {
        partyBrandService.delete(orgId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 修改党建品牌
     */
    @HttpMonitorLogger
    @PostMapping(value = "/update")
    @ApiOperation(value = "修改党建品牌")
    public ResponseEntity<Result<?>> update(@RequestBody PartyBrandBase partyBrandBase) throws JsonProcessingException {
        partyBrandService.update(partyBrandBase);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 查询党建品牌详情
     */
    @HttpMonitorLogger
    @GetMapping(value = "/selectSelf")
    @ApiOperation(value = "/查询党建品牌详情")
    public ResponseEntity<Result<?>> selectSelf(@RequestParam(value = "org_id") Long orgId) throws Exception {
        PartyBrandBase result = partyBrandService.selectSelf(orgId);
        if(result!=null){
            //查询组织名称
            OrganizationBase orgInfo = openService.findOrgById(orgId,null,null);
            if(StringUtils.isNotBlank(orgInfo.getName())){
                result.setOrgName(orgInfo.getName());
            }
        }
        result = result!=null?result:new PartyBrandBase();
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 查询所有下级党建品牌列表
     */
    @HttpMonitorLogger
    @GetMapping(value = "/selectSub")
    @ApiOperation(value = "查询所有下级党建品牌列表")
    public ResponseEntity<Result<?>> selectSub(@RequestParam(value = "org_id") Long orgId) throws Exception {
        List<PartyBrandBase> result = partyBrandService.selectSub(orgId);
        result.parallelStream().forEach(re->{
            if(re!=null&&re.getOrgId()!=null){
                //查询组织名称
                OrganizationBase orgInfo = openService.findOrgById(re.getOrgId(),null,null);
                if(StringUtils.isNotBlank(orgInfo.getName())){
                    re.setOrgName(orgInfo.getName());
                }
            }
        });

        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 品牌推优
     */
    @HttpMonitorLogger
    @PostMapping(value = "/recommend")
    @ApiOperation(value = "品牌推优")
    public ResponseEntity<Result<?>> recommend(@RequestBody List<Long> orgList) {
        for (Long orgId : orgList) {
            //查询组织名称
            OrganizationBase orgInfo = openService.findOrgById(orgId,null,null);
            partyBrandService.recommend(orgId,orgInfo!=null?orgInfo.getName():"");
        }
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/quitRecommend")
    @ApiOperation(value = "取消品牌推优")
    public ResponseEntity<Result<?>> quitRecommend(@RequestParam(value = "org_id") Long orgId) {
        partyBrandService.quitRecommend(orgId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/selectExcellent")
    @ApiOperation(value = "查询本单位和推优党建品牌列表")
    public ResponseEntity<Result<?>> selectExcellent(@RequestParam(value = "org_id") Long orgId) throws Exception{
        List<PartyBrandForm> result = partyBrandService.selectExcellent(orgId);
        result.parallelStream().forEach(re -> {
            if (re != null && re.getOrgId() != null) {
                //查询组织名称
                OrganizationBase orgInfo = openService.findOrgById(re.getOrgId(),null,null);
                if (StringUtils.isNotBlank(orgInfo.getName())) {
                    re.setOrgName(orgInfo.getName());
                }
            }
        });
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

}
