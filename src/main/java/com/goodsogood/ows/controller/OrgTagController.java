package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgTagForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgService;
import com.goodsogood.ows.service.OrgTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 组织标签关联关系
 * <AUTHOR>
 */
@RestController
@RequestMapping("/org/tag")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "组织标签管理", tags = {"org-tag"})
@Validated
public class OrgTagController {

    private final Errors errors;
    private final OrgTagService orgTagService;

    @Autowired
    public OrgTagController(Errors errors, OrgTagService orgTagService) {
        this.errors = errors;
        this.orgTagService = orgTagService;
    }

    @HttpMonitorLogger
    @PostMapping("/insert")
    @ApiOperation("组织管理批量新增组织标签")
    public ResponseEntity<Result<?>> insert(@Valid @RequestBody OrgTagForm orgTagForm,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            int i = this.orgTagService.updateOrgTag(orgTagForm, header.getUserId(), header.getRegionId(), headers,1);
            if (i == 1) {
                return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
            } else {
                return new ResponseEntity<>(new Result<>(errors, i, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("组织管理批量新增组织标签 报错 ->",  e);
            return new ResponseEntity<>(new Result<>(errors, 9903, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @PostMapping("/delete")
    @ApiOperation("组织管理批量删除组织标签")
    public ResponseEntity<Result<?>> delete(@Valid @RequestBody OrgTagForm orgTagForm,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            int i = this.orgTagService.updateOrgTag(orgTagForm, header.getUserId(), header.getRegionId(), headers, 2);
            if (i == 1) {
                return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
            } else {
                return new ResponseEntity<>(new Result<>(errors, i, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("组织管理批量删除组织标签 报错 ->",  e);
            return new ResponseEntity<>(new Result<>(errors, 9903, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }
}
