package com.goodsogood.ows.controller

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.helper.HeaderHelper.SysHeader
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.UserOrgService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import javax.validation.constraints.NotNull

@RestController
@RequestMapping("/user-org")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "用户组织", tags = ["user"])
class UserOrgController(@Autowired val errors: Errors,
                        @Autowired val userOrgService: UserOrgService) {


    @GetMapping("/get-user-org-list")
    @ApiOperation("查询用户所在组织ID")
    fun getUserOrg(@RequestParam("user_id") userId: Long) : ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(Result<Any>(this.userOrgService.getUserOrgList(userId), errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-third-info")
    @ApiOperation("查询人员第三方信息")
    fun getUserThirdInfo(@RequestParam("th_type") thType: Int,
                         @RequestParam("org_id", required = false) orgId: Long?,
                         @RequestParam("user_ids") userIds: MutableList<Long>,
                         @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        val header: SysHeader = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(Result<Any>(this.userOrgService.getUserThirdInfo(thType, orgId, header.regionId, userIds), errors), HttpStatus.OK)
    }

    @GetMapping("/get-user-statistical-data")
    @ApiOperation("查询人员第三方信息")
    fun getUserStatisticalData(@RequestParam("org_id") @NotNull(message = "\${NotNull.org.id}") orgId: Long,
                               @RequestHeader headers: HttpHeaders) : ResponseEntity<Result<Any>> {
        val header: SysHeader = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity<Result<Any>>(Result<Any>(this.userOrgService.calculateUser(orgId, header.regionId), errors), HttpStatus.OK)
    }
}