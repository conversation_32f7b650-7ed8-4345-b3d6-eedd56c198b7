package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgGroupAddForm;
import com.goodsogood.ows.model.vo.OrgGroupUpdateForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgGroupService;
import lombok.extern.log4j.Log4j2;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @program: ows-user-center
 * @description: 党小组
 * @author: taiqian.Luo
 * @create: 2019-04-09 10:00
 **/
@RestController
@RequestMapping("/org-group")
@CrossOrigin(origins = "*", maxAge = 3600)
@Log4j2
@Validated
public class OrgGroupController {

    private final OrgGroupService orgGroupService;
    private final Errors errors;

    @Autowired
    public OrgGroupController(OrgGroupService partyGroupService,
                              Errors errors) {
        this.orgGroupService = partyGroupService;
        this.errors = errors;
    }

    /**
     * 添加党小组
     *
     * @param partyGroupAddForm
     * @param bindingResult
     * @param headers
     * @return
     */
    @PostMapping("/add")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> add(@Valid @RequestBody OrgGroupAddForm partyGroupAddForm,
                                         BindingResult bindingResult, @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupService.add(partyGroupAddForm, header);
    }

    @GetMapping("/list/{org_id}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> list(@Range(min = 1, max = 99999999999L, message = "{Range.org.id}")
                                          @PathVariable("org_id") long orgId) {

        return orgGroupService.list(orgId);
    }

    @PostMapping("/delete/{org_group_id}/{org_id}")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> delete(@Range(min = 1, max = 99999999999L, message = "{Range.org.id}")
                                            @PathVariable("org_id") long orgId, @Range(min = 1, max = 99999999999L, message = "{Range.id}")
                                            @PathVariable("org_group_id") long orgGroupId, @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupService.delete(orgId, orgGroupId, header);
    }

    @PostMapping("/update")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> update(@Valid @RequestBody OrgGroupUpdateForm partyGroupUpdateForm,
                                            BindingResult bindingResult, @RequestHeader HttpHeaders headers) {

        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgGroupService.update(partyGroupUpdateForm, header);
    }

    /**
     * 查询党小组 组织 根据党小组组织id
     */
    @GetMapping("/find-leader-users")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findUserByOrganization(@RequestParam(value = "org_id",required = false) List<Long> orgId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<Long, Long> map = orgGroupService.findLeaderUser(orgId, header);
        return new ResponseEntity<>(new Result<>(map, errors), HttpStatus.OK);
    }

    /**
     * 根据党小组编号获取所属支部编号
     */
    @GetMapping("/find/branchId/by/orgId")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findBranchIdByOrgId(@RequestParam(value = "org_id",required = false) Long orgId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long id = orgGroupService.findBranchIdByOrgId(header.getRegionId(),orgId);
        return new ResponseEntity<>(new Result<>(id, errors), HttpStatus.OK);
    }

    /**
     * 根据支部编号获取下属党小组编号
     */
    @GetMapping("/find/orgId/by/branchId")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgIdByBranchId(@RequestParam(value = "branch_id",required = false) Long branchId,
                                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> ids = orgGroupService.findOrgIdByBranchId(header.getRegionId(),branchId);
        return new ResponseEntity<>(new Result<>(ids, errors), HttpStatus.OK);
    }

    /**
     * 根据支部编号批量获取下属党小组编号
     */
    @GetMapping("/find/orgId/by/branchIds")
    @HttpMonitorLogger
    public ResponseEntity<Result<?>> findOrgIdByBranchIds(@RequestParam(value = "branch_ids",required = false) List<Long> branchIds,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> ids = orgGroupService.findOrgIdByBranchIds(header.getRegionId(),branchIds);
        return new ResponseEntity<>(new Result<>(ids, errors), HttpStatus.OK);
    }
}
