package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.UnsupportedEncodingException;

/**
 * 微信中心回调转发
 *
 * <AUTHOR>
 * @date 2020/6/5
 */
@Api(tags = "微信相关页面跳转")
@Controller
@RequestMapping("redirect")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
public class WechatCallbackController {

    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;

    @Autowired
    public WechatCallbackController(SimpleApplicationConfigHelper simpleApplicationConfigHelper) {
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
    }

    @ApiOperation("公众号授权第三方平台-回调")
    @HttpMonitorLogger
    @GetMapping("official_accounts_authorization")
    public String officialAccountsAuthorization(@RequestParam(name = "org_id") Long orgId,
                                                @RequestParam(value = "auth_code") String authCode,
                                                @RequestParam(value = "expires_in") Integer expiresIn) {
        String pcDomain = simpleApplicationConfigHelper.getRegionByOrgId(orgId).getPcDomain();
        String redirectUrl = "redirect:" + pcDomain + "/weixin-bind?auth_code=" + authCode + "&expires_in=" + expiresIn;
        log.info("公众号授权回调：跳转地址：{}", redirectUrl);
        return redirectUrl;
    }

    @ApiOperation("微信网页授权-回调")
    @HttpMonitorLogger
    @GetMapping("web_authorization")
    public String webAuthorization(String code,
                                   String appid,
                                   @RequestParam(name = "org_id") Long orgId,
                                   @RequestParam Long routerType) {
        String wechatDomain = simpleApplicationConfigHelper.getRegionByOrgId(orgId).getWechatDomain();
        String redirectUrl = "redirect:" + wechatDomain + "/ssr/get-key?code=" + code + "&appid=" + appid + "&org_id=" + orgId + "&routerType=" + routerType;
        log.info("网页授权回调：跳转地址：{}", redirectUrl);
        return redirectUrl;
    }

    @ApiOperation("微信网页登陆-回调")
    @HttpMonitorLogger
    @GetMapping("web_login")
    public String webLogin(@RequestParam(name = "target_url") String targetUrl,
                           @RequestParam(name = "target_org_id") Long orgId) throws UnsupportedEncodingException {
        String wechatDomain = simpleApplicationConfigHelper.getRegionByOrgId(orgId).getWechatDomain();
        String redirectUrl = "redirect:" + wechatDomain + targetUrl;
        log.info("网页登录授权回调：跳转地址：{}", redirectUrl);
        return redirectUrl;
    }
}
