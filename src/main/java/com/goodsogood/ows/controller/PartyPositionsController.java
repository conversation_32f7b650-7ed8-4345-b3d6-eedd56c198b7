package com.goodsogood.ows.controller;

import cn.hutool.core.date.DateUtil;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.mongo.PartyPositions;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.PartyPositionsService;
import com.goodsogood.ows.service.ScoreService;
import com.goodsogood.ows.service.VrScenesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : PartyPositionsController
 * <AUTHOR> tc
 * @Date: 2021/9/7 16:38
 * @Description : 党建阵地
 */
@RestController
@RequestMapping("/partyPositions")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "党建阵地", tags = {"党建阵地"})
@Validated
public class PartyPositionsController {

    private final Errors errors;

    private final PartyPositionsService partyPositionsService;
    private final ScoreService scoreService;

    private final VrScenesService vrScenesService;

    @Autowired
    public PartyPositionsController(Errors errors, PartyPositionsService partyPositionsService, ScoreService scoreService, VrScenesService vrScenesService) {
        this.errors = errors;
        this.partyPositionsService = partyPositionsService;
        this.scoreService = scoreService;
        this.vrScenesService = vrScenesService;
    }

    /**
     * 新建党建阵地
     *
     */
    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新建党建阵地")
    public ResponseEntity<Result<?>> addPartyPositions(@Valid @RequestBody PartyPositions partyPositions,
                                             BindingResult bindingResult,
                                             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //从请求头里获取区县编号
        Long regionId = sysHeader.getRegionId();
        //从请求头里获取用户编号
        Long userId = sysHeader.getUserId();
        if (regionId==null) {
            throw new ApiException("<新增>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        if (StringUtils.isEmpty(partyPositions.getPartyPositionName())) {
            throw new ApiException("<新增>阵地名称不能为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "阵地名称不能为空"));
        }
        if (partyPositions.getPictures()==null||partyPositions.getPictures().size()<1) {
            throw new ApiException("<新增>阵地图片不能为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "阵地图片不能为空"));
        }
        //填充最后改动时间和最后改动人
        partyPositions.setLastUpdateTime(DateUtil.now());
        partyPositions.setLastUpdateUser(userId);
        if (Objects.equals(partyPositions.getClassify(), "VR阵地")){
            VrAddForm vrAddForm = new VrAddForm();
            vrAddForm.setOrgId(partyPositions.getOrgId());
            vrAddForm.setName(partyPositions.getPartyPositionName());
            vrAddForm.setUrl(partyPositions.getUrl());
            vrAddForm.setDescription(partyPositions.getDescription());
            vrAddForm.setCover(partyPositions.getCover());
            Integer i = vrScenesService.addPositions(vrAddForm,headers);
            partyPositions.setScenesId(i);
        }
        String id = partyPositionsService.addPartyPositions(regionId,partyPositions);
        //异步判断是否给所属党组加分
        scoreService.addScoreByAsync(regionId,partyPositions.getOrgId(),17,userId,"1970-01",headers,75);
        return new ResponseEntity<>(new Result<>(id, errors), HttpStatus.OK);
    }

    /**
     * 党建阵地详情
     *
     */
    @HttpMonitorLogger
    @GetMapping("/details")
    @ApiOperation("党建阵地详情")
    public ResponseEntity<Result<?>> partyPositionsDetails(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(value = "org_id") Long orgId,
                                                           @RequestParam(value = "party_position_id") String partyPositionId,
                                                           @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //从请求头里获取区县编号
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<详情>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        PartyPositions partyPositions = partyPositionsService.findPartyPositionsDetails(regionId,orgId,partyPositionId);
        return new ResponseEntity<>(new Result<>(partyPositions, errors), HttpStatus.OK);
    }

    /**
     * 编辑党建阵地
     *
     */
    @HttpMonitorLogger
    @PostMapping("/edit")
    @ApiOperation("编辑党建阵地")
    public ResponseEntity<Result<?>> editPartyPositions(@Valid @RequestBody PartyPositions partyPositions,
                                                       BindingResult bindingResult,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        //从请求头里获取区县编号
        Long regionId = sysHeader.getRegionId();
        //从请求头里获取用户编号
        Long userId = sysHeader.getUserId();
        if (regionId==null) {
            throw new ApiException("<编辑>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        if (StringUtils.isEmpty(partyPositions.getPartyPositionId())) {
            throw new ApiException("<编辑>阵地编号不能为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "阵地编号不能为空"));
        }
        if (StringUtils.isEmpty(partyPositions.getPartyPositionName())) {
            throw new ApiException("<编辑>阵地名称不能为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "阵地名称不能为空"));
        }
        if (partyPositions.getPictures()==null||partyPositions.getPictures().size()<1) {
            throw new ApiException("<编辑>阵地图片不能为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "阵地图片不能为空"));
        }
        //填充最后改动时间和最后改动人
        log.debug("填充最后改动时间和最后改动人");
        partyPositions.setLastUpdateTime(DateUtil.now());
        partyPositions.setLastUpdateUser(userId);
        log.debug("开始编辑");
        String id = partyPositionsService.editPartyPositions(regionId,partyPositions,sysHeader);
        return new ResponseEntity<>(new Result<>(id, errors), HttpStatus.OK);
    }

    /**
     * 党建阵地详情
     *
     */
    @HttpMonitorLogger
    @GetMapping("/findOne")
    @ApiOperation("党建阵地组织下第一条阵地信息")
    public ResponseEntity<Result<?>> partyPositionsFirst(HttpServletRequest request, HttpServletResponse response,
                                                           @RequestParam(value = "org_id") Long orgId,
                                                           @RequestHeader HttpHeaders headers) {
        //从请求头里获取区县编号
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<查询第一条>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        PartyPositions partyPositions = partyPositionsService.findPartyPositionsFirst(regionId,orgId);
        return new ResponseEntity<>(new Result<>(partyPositions, errors), HttpStatus.OK);
    }

    /**
     * 党建阵地列表
     *
     */
    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("党建阵地列表")
    public ResponseEntity<Result<?>> editPartyPositions(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "org_id") Long orgId,
                                                        @RequestParam(value = "page",required = false,defaultValue = "1") Long page,
                                                        @RequestParam(value = "page_size",required = false,defaultValue = "10") Long pageSize,
                                                        @RequestHeader HttpHeaders headers) throws IOException {
        //从请求头里获取区县编号
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<列表>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        PartyPositionsVo vo = partyPositionsService.findPartyPositionsAndChild(regionId,orgId,page,pageSize,sysHeader);
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/selectExcellent")
    @ApiOperation(value = "查询本单位和推优党建阵地列表")
    public ResponseEntity<Result<?>> selectExcellent(HttpServletRequest request, HttpServletResponse response,
                                                     @RequestParam(value = "org_id") Long orgId,
                                                     @RequestParam(value = "type") Long type,
                                                     @RequestParam(value = "page",required = false,defaultValue = "1") Long page,
                                                     @RequestParam(value = "page_size",required = false,defaultValue = "10") Long pageSize,
                                                     @RequestHeader HttpHeaders headers) throws Exception{
        //从请求头里获取区县编号
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<查询第一条>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        PartyPositionsForm result = partyPositionsService.selectExcellent(regionId,orgId,type,page,pageSize);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }


    /**
     * 党建阵地推优列表
     *
     */
    @HttpMonitorLogger
    @GetMapping("/list-recommend")
    @ApiOperation("党建阵地推优列表-点推优弹出列表")
    public ResponseEntity<Result<?>> listPartyPositions(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "org_id") Long orgId,
                                                        @RequestHeader HttpHeaders headers) {
        //从请求头里获取区县编号
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<列表>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        PartyPositionsVo vo = partyPositionsService.findRecommendPartyPositionsAndChild(regionId,orgId);
        return new ResponseEntity<>(new Result<>(vo, errors), HttpStatus.OK);
    }


    /**
     * 确认推优
     *
     */
    @HttpMonitorLogger
    @GetMapping("/recommend")
    @ApiOperation("确认推优")
    public ResponseEntity<Result<?>> recommendPartyPositions(HttpServletRequest request,
                                                        @RequestParam(value = "party_position_ids") List<String> partyPositionIds,
                                                        @RequestHeader HttpHeaders headers) {
        partyPositionsService.recommendPartyPositions(headers, partyPositionIds);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    /**
     * 取消推优
     *
     */
    @HttpMonitorLogger
    @GetMapping("/quit-recommend")
    @ApiOperation("取消推优")
    public ResponseEntity<Result<?>> quitRecommendPartyPositions(HttpServletRequest request,
                                                             @RequestParam(value = "party_position_id") String partyPositionId,
                                                             @RequestHeader HttpHeaders headers) {
        partyPositionsService.quitRecommendPartyPositions(headers, partyPositionId);
        return new ResponseEntity<>(new Result<>(true, errors), HttpStatus.OK);
    }

    /**
     * 删除党建阵地
     */
    @HttpMonitorLogger
    @GetMapping("/del")
    @ApiOperation("删除党建阵地")
    public ResponseEntity<Result<?>> editPartyPositions(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "org_id") Long orgId,
                                                        @RequestParam(value = "party_position_id") String partyPositionId,
                                                        @RequestHeader HttpHeaders headers) {
        //从请求头里获取区县编号
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        if (regionId==null) {
            throw new ApiException("<删除>区县编号为空", new Result<>(errors, 5630, HttpStatus.OK.value(), "区县编号不能为空"));
        }
        partyPositionsService.delPartyPositions(regionId,orgId,partyPositionId);
        return new ResponseEntity<>(new Result<>(partyPositionId, errors), HttpStatus.OK);
    }

    /**
     * 分页获取所有党建阵地(SAS中心在使用)
     */
    @HttpMonitorLogger
    @GetMapping("/generate/message/for/sas")
    @ApiOperation("分页获取所有党建阵地")
    public ResponseEntity<Result<?>> generateMessage(HttpServletRequest request, HttpServletResponse response,
                                                        @RequestParam(value = "region_id") Long regionId,
                                                        @RequestParam(value = "page_no",required = false) Integer pageNo,
                                                        @RequestParam(value = "page_size",required = false) Integer pageSize,
                                                        @RequestHeader HttpHeaders headers) {
        //设置默认页码
        if(pageNo==null){
            pageNo=1;
        }
        if(pageSize==null){
            pageSize=10;
        }
        log.debug("分页获取所有党建阵地(SAS中心在使用) 参数:  regionId={} pageNo={} pageSize={}",regionId,pageNo,pageSize);
        //查询结果
        List<MessageVO> re = partyPositionsService.generateMessage(regionId,pageNo,pageSize);
        log.debug("分页获取所有党建阵地(SAS中心在使用) 返回结果:  regionId={} pageNo={} pageSize={} re={}",regionId,pageNo,pageSize,re);
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }

}
