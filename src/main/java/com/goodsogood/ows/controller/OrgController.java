package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.AddDynamicFields;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.annotation.OwsValidation;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.common.idUntils.CertNumberUtil;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldType;
import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.ExcludeOrgConfig;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.configuration.SaasConfig;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 组织单位操作类
 *
 * <AUTHOR>
 * @date 2018-05-08 10:14
 **/
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "组织", tags = {"组织管理"})
@RequestMapping("/org")
public class OrgController {

    @Value("${org-menu-id}")
    @NotBlank
    private String orgMenuId;

    private final Errors errors;
    private final OrgService orgService;
    private final OpenService openService;
    private final OptionService optionService;
    private final StringRedisTemplate redisTemplate;
    private final OrgUserService orgUserService;
    private final OrganizationService organizationService;
    private final OrgTagService orgTagService;
    private final BranchHighlightService branchHighlightService;
    private final SimpleApplicationConfigHelper simpleApplicationConfigHelper;
    private final SaasConfig saasConfig;
    private final OrgMongoService orgMongoService;
    private final ExcludeOrgConfig excludeOrgConfig;


    @Autowired
    public OrgController(Errors errors, OrgService orgService, OpenService openService,
                         OptionService optionService, StringRedisTemplate redisTemplate, OrgUserService orgUserService,
                         OrganizationService organizationService, OrgTagService orgTagService,
                         BranchHighlightService branchHighlightService, SimpleApplicationConfigHelper simpleApplicationConfigHelper,
                         SaasConfig saasConfig, OrgMongoService orgMongoService, ExcludeOrgConfig excludeOrgConfig) {
        this.errors = errors;
        this.orgService = orgService;
        this.openService = openService;
        this.optionService = optionService;
        this.redisTemplate = redisTemplate;
        this.orgUserService = orgUserService;
        this.organizationService = organizationService;
        this.orgTagService = orgTagService;
        this.branchHighlightService = branchHighlightService;
        this.simpleApplicationConfigHelper = simpleApplicationConfigHelper;
        this.saasConfig = saasConfig;
        this.orgMongoService = orgMongoService;
        this.excludeOrgConfig = excludeOrgConfig;
    }

    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgForm.class)
    @HttpMonitorLogger
    @GetMapping("/list")
    @ApiOperation("获取单位组织的列表")
    @DoLog(type = LogType.QUERY,content = "获取单位组织的列表")
    public ResponseEntity<Result<Page<?>>> getOrgList(@RequestHeader HttpHeaders headers,
                                                      @RequestParam(value = "name", required = false) String name,
                                                      @RequestParam(value = "page", required = false,
                                                              defaultValue = "1") Integer page,
                                                      @RequestParam(value = "pagesize", required = false,
                                                              defaultValue = "10") Integer pageSize,
                                                      @RequestParam(value = "oid") Long oid) {
        log.debug("参数：name = {}, page = {}, pagesize = {}, oid = {}", name, page, pageSize, oid);
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (oid == null) {
            log.error("oid不能为空");
            throw new ApiException("获取单位组织列表出错", new Result<>(errors, Global.Errors.VALID_ERROR, HttpStatus.BAD_REQUEST.value(), "获取单位组织列表出错"));
        }
        Page<OrgForm> list;
        try {
            list = orgService.findOrgList(name, oid, new PageNumber(page, pageSize), header.getRegionId());
        } catch (Exception e) {
            log.error("查询单位组织列表出错", e);
            throw new ApiException("查询单位组织列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 激活组织
     *
     * @param form form
     */
    @HttpMonitorLogger
    @PostMapping("/enable")
    @ApiOperation("激活组织")
    public ResponseEntity<Result<?>> enableOrg(@Valid @RequestBody EnableForm form,
                                               BindingResult bindingResult,
                                               @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (form.getParentId().equals(form.getOrganizationId())) {
            log.debug("所选上级组织不能为自己 orgId:{}", form.getOrganizationId());
            return new ResponseEntity<>(new Result<>(errors, 605, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (null != form.getOwnerId() && form.getOrganizationId().equals(form.getOwnerId())) {
            log.debug("激活组织 - 所属组织不能选择当前组织 orgId:{}", form.getOrganizationId());
            return new ResponseEntity<>(new Result<>(errors, 221, HttpStatus.OK.value()), HttpStatus.OK);
        }
        int flag = this.orgService.enableOrg(form, header.getUserId(), header.getRegionId());
        if (flag == 3) {
            log.error("系统异常，激活组织失败");
            return new ResponseEntity<>(new Result<>(errors, 182, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (flag == 2) {
            log.debug("所选上级组织，不能为当前组织的下级");
            return new ResponseEntity<>(new Result<>(errors, 604, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (flag != 1) {
            log.debug("该组织已经被激活或者状态为禁用 errorCode -> {}", flag);
            return new ResponseEntity<>(new Result<>(errors, flag, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("激活成功", errors), HttpStatus.OK);
    }


    /**
     * 查询组织树
     *
     * <AUTHOR>
     * @date 2018-6-19
     **/
    @HttpMonitorLogger
    @GetMapping("/tree")
    @ApiOperation("查询组织树")
    @DoLog(type = LogType.QUERY,content = "查询组织树")
    public ResponseEntity<Result<?>> tree(
            @RequestParam(value = "org_id", required = false) Long orgId,
            @RequestParam(value = "tree_type") @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
            @RequestParam(value = "show_code", required = false, defaultValue = "0") Integer showUniqueCode,
            @RequestParam(value = "load_root", required = false, defaultValue = "1") Integer loadRoot,
            @RequestParam(value = "is_filter", required = false, defaultValue = "0") Integer isFilter,
            @RequestParam(value = "org_type", required = false) Integer orgType,
            @RequestParam(value = "is_all", required = false, defaultValue = "2") Integer isAll,
            @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (null == orgId && CollectionUtils.isEmpty(orgIds)) {
            log.error("组织ID不能为空");
            return new ResponseEntity<>(new Result<>(errors, 601, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (treeType == 2 && orgType == null) { //子树
            log.error("组织类型不能为空");
            return new ResponseEntity<>(new Result<>(errors, 607, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 如果查询所有父树
        List<Long> corpList = new ArrayList<>();
        // 父树对应regionId
        List<Long> regionList = new ArrayList<>();
        // 行政单位id
        Long corpId = null;
        if (isAll == 1) {
            // 如果是超级管理员，则查询所有父树
            if (header.getUserId().equals(1L) && Constants.TREE_TYPE_PARENT == treeType) {
                Region region = this.simpleApplicationConfigHelper.getRegions();
                List<Region.RegionData> corpData = region.getRegions();
                corpData.forEach(corp -> {
                    corpList.add(corp.getCorpData().getOrgId());
                    regionList.add(corp.getRegionId());
                });
            } else {
                // 根据区县ID查询顶级父树ID
                Region.OrgData region = this.simpleApplicationConfigHelper.getCorpByRegionId(saasConfig.getRegionLabel(),
                        header.getRegionId());
                corpList.add(region.getOrgId());
                regionList.add(header.getRegionId());
            }
        } else {
            if (!CollectionUtils.isEmpty(orgIds)) {
                corpList.addAll(orgIds);
                corpId = orgId;
            } else {
                corpList.add(orgId);
            }
            regionList.add(header.getRegionId());
        }
        List<List<OrgTreeForm>> resultList = new ArrayList<>();
        try {
            for (int i = 0, size = corpList.size(); i < size; i++) {
                Long cid = corpList.get(i);
                List<OrgTreeForm> otf = orgService.tree(cid, showUniqueCode, treeType, orgType, loadRoot,
                        isFilter, header.getRegionId(), corpId);
                if (!CollectionUtils.isEmpty(otf)) {
                    // 如果是超级管理员
                    if (header.getUserId().equals(1L)
                            && Constants.TREE_TYPE_PARENT == treeType) {
                        for (OrgTreeForm form : otf) {
                            List<OrgTreeForm> childList = new ArrayList<>();
                            for (OrgTreeForm child : form.getChildren()) {
                                if (child.getOrganizationId().equals(cid)) {
                                    childList.add(child);
                                }
                            }
                            form.setChildren(childList);
                            resultList.add(otf);
                        }
                    } else {
                        // 如果查询单位下所有组织，则所有组织列表平级展示，无下级子节点
                        if (null != corpId) {
                            otf.forEach(tree -> tree.setChildOrgNum(0));
                        }
                        resultList.add(otf);
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询组织树出错", e);
            throw new ApiException("查询组织树出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询组织树出错"));
        }
        List<OrgTreeForm> list = new ArrayList<>();
        resultList.forEach(orgTreeForms -> {
            if (!CollectionUtils.isEmpty(orgIds)) {
                orgTreeForms.forEach(orgTreeForm -> orgTreeForm.setChildren(null));
            }
            list.addAll(orgTreeForms);
        });
        return new ResponseEntity<>(new Result<>(list.stream().distinct().collect(Collectors.toList()), errors), HttpStatus.OK);
    }


    /**
     * 新建组织
     *
     * <AUTHOR>
     * @date 2018-6-19
     **/
    @OwsValidation(own = 2)
    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新建组织")
    @DoLog(type = LogType.INSERT,content = "新建组织")
    public ResponseEntity<Result<String>> add(@Valid @RequestBody Map<String, Object> orgMap,
                                              BindingResult bindingResult,
                                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        try {
            OrganizationEntity org = this.orgService.getOrgByMap(null, orgMap);
            Map<String, Object> dynamicField = ReflectUtil.getDynamicField(orgMap, OrganizationEntity.class);
            OrganizationEntity result = this.orgService.add(org, dynamicField, headers);
            BranchHighlightForm form = new BranchHighlightForm();
            form.setType(2);
            form.setOrgId(result.getOrganizationId());
            form.setNodeTime(DateUtils.toFormatDate(result.getOrgCreateTime(), "yyyy-MM-dd"));
            form.setDesc("支部成立");
            this.branchHighlightService.addOrgHighlight(form, sysHeader);
            return new ResponseEntity<>(new Result<>(result.getOrganizationId() != null ? "success" : "fail", errors),
                    HttpStatus.OK);
        } catch (ApiException e) {
            log.error("新建组织出错", e);
            throw e;
        } catch (Exception e) {
            log.error("新建组织出错", e);
            throw new ApiException("新建组织出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "添加组织出错"));
        }
    }

    /**
     * 格尔木环保管家-新建组织
     *
     * @param orgForm       orgForm
     * @param bindingResult bindingResult
     * @param headers       headers
     * @return OrganizationBase
     */
    @HttpMonitorLogger
    @PostMapping("/eps/add")
    @ApiOperation("格尔木环保管家-新建组织")
    public ResponseEntity<Result<?>> epsAdd(@Valid @RequestBody OrgAddForm orgForm,
                                            BindingResult bindingResult,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        if (orgService.isNameExists(null, orgForm.getName(), sysHeader.getRegionId())) {
            log.debug("新建组织 - 该组织名称已存在 name:{},regionId:[{}]", orgForm.getName(), sysHeader.getRegionId());
            return new ResponseEntity<>(new Result<>(errors, 600, HttpStatus.OK.value()), HttpStatus.OK);
        }
        if (StringUtils.isNotBlank(orgForm.getOrgCode()) && orgService.isOrgCodeExists(null, orgForm.getOrgCode())) {
            log.debug("新建组织 - 该组织代码已存在 code:{}", orgForm.getOrgCode());
            return new ResponseEntity<>(new Result<>(errors, 603, HttpStatus.OK.value()), HttpStatus.OK);
        }
        try {
            OrganizationEntity org = new OrganizationEntity();
            BeanUtils.copyProperties(orgForm, org);
            OrganizationEntity result = orgService.add(org, null, headers);
            OrganizationBase base = new OrganizationBase();
            BeanUtils.copyProperties(result, base);
            return new ResponseEntity<>(new Result<>(base, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("新建组织出错", e);
            throw new ApiException("新建组织出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "添加组织出错"));
        }
    }


    /**
     * 更新组织
     *
     * <AUTHOR>
     * @date 2018-6-19
     **/
    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation("更新组织")
    @DoLog(type = LogType.UPDATE,content = "更新组织")
    public ResponseEntity<Result<String>> update(@Valid @RequestBody Map<String, Object> orgMap,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        log.debug("更新组织[{}]", Utils.toJson(this.simpleApplicationConfigHelper));
        Long organizationId = Convert.toLong(orgMap.get(FieldNameConfig.ORG_ID));
        Integer orgTypeChild = Convert.toInt(orgMap.get(FieldNameConfig.ORG_TYPE_CHILD));
        // 党组织和第二党支部不允许相互更改
        OrganizationEntity oldOrg = this.orgService.getOrgById(organizationId, sysHeader.getRegionId());
        // 从第二党支部改为其他党组织类型
        if (Objects.nonNull(oldOrg.getOrgTypeChild()) && this.excludeOrgConfig.getNotChangeOrgType().contains(oldOrg.getOrgTypeChild()) &&
                !this.excludeOrgConfig.getNotChangeOrgType().contains(orgTypeChild)) {
            String name = this.optionService.getNameByKey(oldOrg.getOrgTypeChild(), Constants.ORG_TYPE_COMMUNIST.toString());
            return new ResponseEntity<>(new Result<>(errors, 30005, HttpStatus.OK.value(), name), HttpStatus.OK);
        }
        // 从党组织改为第二党支部
        if (Objects.nonNull(orgTypeChild) && this.excludeOrgConfig.getNotChangeOrgType().contains(orgTypeChild) &&
                !this.excludeOrgConfig.getNotChangeOrgType().contains(oldOrg.getOrgTypeChild())) {
            String name = this.optionService.getNameByKey(orgTypeChild, Constants.ORG_TYPE_COMMUNIST.toString());
            return new ResponseEntity<>(new Result<>(errors, 30004, HttpStatus.OK.value(), name), HttpStatus.OK);
        }
        try {
            OrganizationEntity org = this.orgService.getOrgByMap(oldOrg, orgMap);
            org.setLastChangeUser(sysHeader.getUserId());
            Map<String, Object> dynamicField = ReflectUtil.getDynamicField(orgMap, OrganizationEntity.class);
            Boolean b = this.orgService.update(org, sysHeader.getRegionId(), oldOrg, dynamicField, headers);
            return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
        } catch (ApiException e) {
            log.error("新建组织出错", e);
            throw e;
        } catch (Exception e) {
            log.error("更新组织出错" + e.getMessage(), e);
            throw new ApiException("更新组织出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "更新组织出错"));
        }
    }

    /**
     * 修改组织排序
     *
     * <AUTHOR>
     * @date 2019-06-26 TangLilin
     **/
    @HttpMonitorLogger
    @PostMapping("/sort")
    @ApiOperation("修改组织排序")
    @DoLog(type = LogType.UPDATE,content = "修改组织排序")
    public ResponseEntity<Result<String>> sort(@Valid @RequestBody OrgSortForm sortForm,
                                               BindingResult bindingResult,
                                               @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            Boolean b = orgService.updateSort(sortForm, sysHeader.getUserId());
            return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("修改组织排序出错:{}", e.getMessage(), e);
            throw new ApiException("修改组织排序出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "修改组织排序出错"));
        }
    }

    /**
     * 根据名称模糊匹配组织列表
     *
     * <AUTHOR>
     * @date 2018-6-19
     **/
    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrgListForm.class)
    @HttpMonitorLogger
    @GetMapping("/org-name-list")
    @ApiOperation("根据名称模糊匹配组织列表")
    public ResponseEntity<Result<List<OrgListForm>>> orgListByName(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                                   @RequestParam(value = "name") @NotNull(message = "{NotBlank.name}") String name,
                                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        try {
            List<OrgListForm> list = orgService.getListByName(orgId, name, sysHeader.getRegionId());
            return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据名称模糊匹配组织列表出错", e);
            throw new ApiException("根据名称模糊匹配组织列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "根据名称模糊匹配组织列表出错"));
        }
    }


    /**
     * 删除组织
     *
     * <AUTHOR>
     * @date 2018-6-19
     **/
    @HttpMonitorLogger
    @DeleteMapping("/delete")
    @ApiOperation("删除组织")
    @DoLog(type = LogType.DELETE,content = "删除组织")
    public ResponseEntity<Result<String>> delete(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                 @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            int b = orgService.delete(orgId, sysHeader.getUserId(), sysHeader.getRegionId(), headers);
            if (b > 1) {
                return new ResponseEntity<>(new Result<>(errors, b, HttpStatus.OK.value()), HttpStatus.OK);
            }
            this.orgMongoService.remove(orgId);
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除组织出错{}", e.getMessage(), e);
            throw new ApiException("删除组织出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "删除组织出错"));
        }
    }


    /**
     * 组织标识码下载
     *
     * @param orgId   orgId
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/downCode")
    @ApiOperation("组织标识码下载")
    public ResponseEntity<Result<String>> downCode(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                   @RequestParam(value = "tree_type") @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
                                                   @RequestParam(value = "org_type", required = false) Integer orgType,
                                                   @RequestHeader HttpHeaders headers, HttpServletResponse response) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Long regionId = sysHeader.getRegionId();
        try {
            int i = openService.checkDownloadAuth(sysHeader.getUserId(), orgId, orgMenuId, sysHeader.getRegionId());
            if (i <= 0) {
                log.debug("组织标识码下载 - 当前用户{}，无权限下载组织Id :{}", sysHeader.getUserId(), orgId);
                return new ResponseEntity<>(new Result<>(errors, 606, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("组织标识码下载出错", e);
            throw new ApiException("组织标识码下载出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "用户未登录或登录已过期"));
        }
        // 如果是超级管理员，regionID为null
        if (sysHeader.getUserId().equals(1L) && sysHeader.getOid().equals(1L)) {
            regionId = null;
        }
        try (Workbook wb = orgService.createWorkbook(orgId, treeType, orgType, regionId)) {
            if (wb != null) {
                // 设置响应头导出文件格式
                String contentDisposition = "attachment;filename=" + URLEncoder.encode("组织标识码", "UTF-8") + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss") + "." + ExcelUtils.XLSX_SUFFIX;
                // 设置响应头的文件名称信息
                response.setHeader("Content-Disposition", contentDisposition);
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                // 设置响应头导出文件格式vnd.ms-excel
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                // 使用响应的输出流导出excel文件
                wb.write(response.getOutputStream());
            } else {
                return new ResponseEntity<>(new Result<>(errors, 602, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("组织标识码下载出错", e);
            throw new ApiException("组织标识码下载出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "组织标识码下载出错"));
        }
        return null;
    }


    /**
     * 组织数据统计
     *
     * @param orgId   orgId
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/count")
    @ApiOperation("组织数据统计")
    public ResponseEntity<Result<OrgCountForm>> count(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                      @RequestParam(value = "tree_type") @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
                                                      @RequestParam(value = "org_type", required = false) Integer orgType,
                                                      @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
            Long regionId = sysHeader.getRegionId();
            // 如果是超级管理员，设置regionID为null
            if (sysHeader.getUserId().equals(1L) && sysHeader.getOid().equals(1L)) {
                regionId = null;
            }
            OrgCountForm ocf = orgService.count(orgId, treeType, orgType, regionId);
            return new ResponseEntity<>(new Result<>(ocf, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("组织数据统计出错", e);
            throw new ApiException("组织数据统计出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "组织数据统计出错"));
        }
    }

    /**
     * 查看组织信息
     *
     * @param orgId   orgId
     * @param headers headers
     */
//    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrgInfoForm.class)
    @HttpMonitorLogger
    @GetMapping("/info")
    @ApiOperation("查看组织信息")
    @DoLog(type = LogType.QUERY,content = "查看组织信息")
    public ResponseEntity<Result<OrgInfoForm>> info(@RequestParam(value = "org_id")
                                                    @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                    @RequestParam(value = "tree_type", defaultValue = "2") @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        try {
            Map<String, Object> resultMap = orgService.info(orgId, treeType, sysHeader.getRegionId());
            int code = (int) resultMap.get("code");
            if (1 != code) {
                return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
            }
            return new ResponseEntity<>(new Result<>((OrgInfoForm) resultMap.get("org"), errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("查看组织信息出错" + e.getMessage(), e);
            throw new ApiException("查看组织信息出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查看组织信息出错"));
        }
    }

    /**
     * 移交管理员
     *
     * @param form    form
     * @param headers headers
     */
    @HttpMonitorLogger
    @PostMapping("/trans-manager")
    @ApiOperation("移交管理员")
    public ResponseEntity<Result<?>> transManager(@Valid @RequestBody TransferManagerForm form,
                                                  BindingResult bindingResult,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String redisKey = MD5Helper.getMD5(form.getPhone());
        // redis不存在该用户的验证码
        if (Boolean.FALSE.equals(this.redisTemplate.hasKey(redisKey))) {
            log.debug("验证码已失效 -> content:{}", form.getCode());
            return new ResponseEntity<>(new Result<>(errors, 124, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            String oldContent = this.redisTemplate.opsForValue().get(redisKey);
            if (!form.getCode().equals(oldContent)) {
                log.debug("验证码输入有误，请重新输入 -> content:{}", form.getCode());
                return new ResponseEntity<>(new Result<>(errors, 201, HttpStatus.OK.value()), HttpStatus.OK);
            }
            // 删除手机验证码
            this.redisTemplate.delete(redisKey);
        }
        int flag = this.orgUserService.transferManager(form, header);
        if (flag != 1) {
            log.debug("移交管理员异常信息 code：{}", flag);
            return new ResponseEntity<>(new Result<>(errors, flag, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 移交管理员校验
     *
     * @param user_id user_id
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/trans-manager-check")
    @ApiOperation("移交管理员校验")
    public ResponseEntity<Result<?>> transManagerCheck(@RequestParam @NotNull(message = "{NotBlank.user.id}") Long user_id,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int flag = this.orgUserService.transManagerCheck(user_id, header);
        if (flag != 1) {
            log.debug("移交管理员异常信息 code：{}", flag);
            return new ResponseEntity<>(new Result<>(errors, flag, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询组织-当前组织以及下级组织范围
     *
     * @param orgName  组织名称-模糊匹配
     * @param treeType 树类型 1-父树 2-子树
     * @param headers  headers
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-by-name")
    @ApiOperation("根据名称模糊查询组织")
    public ResponseEntity<Result<?>> findByName(@RequestParam(required = false, value = "org_id") Long orgId,
                                                @RequestParam(required = false, value = "org_name") String orgName,
                                                @RequestParam(required = false, defaultValue = "1") Integer page,
                                                @RequestParam(required = false, value = "org_type") Integer orgType,
                                                @RequestParam(value = "tree_type")
                                                @NotNull(message = "{NotNull.tree.type}") Integer treeType,
                                                @RequestParam(value = "is_page", defaultValue = "1", required = false) Integer isPage,
                                                @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (null == orgId) {
            orgId = header.getOid();
        }
        // 如果是超级管理员则查询所有
        Long regionId = header.getRegionId();
        if (header.getUserId().equals(1L) && header.getOid().equals(1L)) {
            regionId = null;
        }
        // 如果是子树，orgType不能为空
        if (Constants.TREE_TYPE_CHILD == treeType && orgType == null) {
            log.debug("查询子树时组织类型不能为空");
            return new ResponseEntity<>(new Result<>(errors, 217, HttpStatus.OK.value()), HttpStatus.OK);
        }
        Map<String, Object> resultMap = this.orgService.findByName(new PageNumber(page), orgName, orgId, treeType,
                orgType, isPage, regionId, orgIds);
        return new ResponseEntity<>(new Result<>(null == resultMap ? new ArrayList<OrgNameResultForm>() : resultMap.get("page"), errors), HttpStatus.OK);
    }

    /**
     * 查询人员-当前组织以及下级组织范围
     *
     * @param headers headers
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = OrgUserInfoResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-by-user")
    @ApiOperation("根据姓名、电话、证件号码查询组织人员")
    public ResponseEntity<Result<?>> findByUser(@RequestParam(required = false, value = "param") String param,
                                                @RequestParam(value = "tree_type")
                                                @NotNull(message = "{NotNull.tree.type}") Integer treeType,
                                                @RequestParam(required = false, value = "org_type") Integer orgType,
                                                @RequestParam(required = false, value = "exclude_target_assessment") Integer excludeTargetAssessment,
                                                @RequestParam(required = false, defaultValue = "1") Integer page,
                                                @RequestParam(required = false, value = "page_size", defaultValue = "10") Integer pageSize,
                                                @RequestParam(required = false, defaultValue = "1") Integer version,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 如果是超级管理员则查询所有
        Long regionId = header.getRegionId();
        if (header.getUserId().equals(1L) && header.getOid().equals(1L)) {
            regionId = null;
        }
        // 如果是子树，orgType不能为空
        if (Constants.TREE_TYPE_CHILD == treeType && orgType == 0) {
            log.debug("查询子树时组织类型不能为空");
            return new ResponseEntity<>(new Result<>(errors, 217, HttpStatus.OK.value()), HttpStatus.OK);
        }
        String certNumber = "";
        int type = 1;
        if (param != null && param.length() == 11 && Utils.checkPhone(param)) {
            // 手机号码
            param = NumEncryptUtils.encrypt(param, 2);
            type = 2;
        } else if (param != null && Utils.checkCertNumber(Constants.CERT_NUMBER_IDENTITY, param)) {
            // 身份证
            param = NumEncryptUtils.encrypt(param, 1); // 自有加密方式
            certNumber = CertNumberUtil.encrypt(param); // 12371加密方式
            type = 3;
        }
        Page<OrgUserInfoResultForm> resultList = this.orgService.findByUser(new PageNumber(page, pageSize), param,
                type, certNumber, Utils.getUoid(header), treeType, null == orgType ? header.getOrgType() : orgType,
                null == excludeTargetAssessment ? 0 : excludeTargetAssessment, regionId, version);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 定位组织树
     *
     * @param rootOrgId      rootOrgId
     * @param orgId          orgId
     * @param treeType       treeType
     * @param showUniqueCode showUniqueCode
     * @param orgType        orgType
     * @param corpId         corpId
     */
    @HttpMonitorLogger
    @GetMapping("/locate")
    @ApiOperation("组织定位")
    public ResponseEntity<Result<?>> locate(@RequestParam(value = "root_org_id")
                                            @NotNull(message = "{NotBlank.org.id}") Long rootOrgId,
                                            @RequestParam(value = "org_id", required = false) Long orgId,
                                            @RequestParam(value = "tree_type")
                                            @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
                                            @RequestParam(value = "show_code", required = false, defaultValue = "0") Integer showUniqueCode,
                                            @RequestParam(value = "org_type", required = false) Integer orgType,
                                            @RequestParam(value = "is_filter", required = false, defaultValue = "0") Integer isFilter,
                                            @RequestParam(value = "load_root", required = false, defaultValue = "0") Integer loadRoot,
                                            @RequestParam(value = "org_ids", required = false) List<Long> orgIds,
                                            @RequestParam(value = "corp_id", required = false) Long corpId,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 子树时组织类型不能为空
        if (Constants.TREE_TYPE_CHILD == treeType && null == orgType) {
            log.error("组织类型不能为空");
            return new ResponseEntity<>(new Result<>(errors, 607, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (Constants.TREE_TYPE_PARENT == treeType && null == orgType) {
            orgType = header.getOrgType();
        }
        List<Long> corpList = new ArrayList<>();
        Region region = this.simpleApplicationConfigHelper.getRegions();
        List<Region.RegionData> corpData = region.getRegions();
        corpData.forEach(corp -> {
            corpList.add(corp.getCorpData().getOrgId());
        });
        // 如果是父树
        if (Constants.TREE_TYPE_PARENT == treeType && Constants.YES == isFilter) {
            for (Region.RegionData topCorp : corpData) {
                if (topCorp.getRegionId().equals(header.getRegionId())) {
                    rootOrgId = topCorp.getCorpData().getOrgId();
                    break;
                }
            }
            // 不加载根节点
            loadRoot = Constants.NO;
        }
        OrgTreeForm form = orgService.locate(rootOrgId, orgId, treeType, orgType, showUniqueCode,
                header.getRegionId(), rootOrgId, isFilter, orgIds, corpId);
        if (null == form) {
            return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
        }
        List<OrgTreeForm> resultList = new ArrayList<>();
        // 加载root节点
        if (Constants.LOAD_ROOT_YES == loadRoot) {
            if (!CollectionUtils.isEmpty(orgIds)) {
                resultList.addAll(form.getChildren());
                form.setChildren(null);
                resultList.add(form);
            } else {
                resultList.add(form);
            }
            return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(form.getChildren(), errors), HttpStatus.OK);
        }
    }

    /**
     * 重新计算子节点数
     */
    @HttpMonitorLogger
    @GetMapping("/recountChildNum")
    @ApiOperation("从新计算子节点数")
    public ResponseEntity<Result<?>> recountChild(@RequestParam(required = false, value = "org_id") Long orgId,
                                                  @RequestParam(required = false, value = "region_id") Long regionId) {
        orgService.recountChild(orgId, regionId);
        return new ResponseEntity<>(new Result<>(null, errors), HttpStatus.OK);
    }

    /**
     * 选择组织人员（选择人数/总人数）-H5端-子树
     *
     * @param form          form
     * @param bindingResult bindingResult
     * <AUTHOR>
     */
    @HttpMonitorLogger
    @PostMapping("/find-count-by-tree")
    @ApiOperation("选择组织人员（选择人数/总人数）")
    public ResponseEntity<Result<?>> findCountByTree(@Valid @RequestBody SelectCountForm form,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("选择组织人员参数：{}", form);
        List<ResultCountTreeForm> resultList = this.orgService.findCountByTree(form, header);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 查询组织（党费返还）
     *
     * @param orgId    组织ID
     * @param pageNo   页码
     * @param pageSize 每页条数
     * @Edit 2018-11-21 更改为查询组织直接下级
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-all-org")
    @ApiOperation("查询组织（党费返还）")
    public ResponseEntity<Result<?>> findAllOrg(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                @RequestParam(required = false, value = "page", defaultValue = "1") Integer pageNo,
                                                @RequestParam(required = false, value = "page_size", defaultValue = "9999") Integer pageSize,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrgNameResultForm> resultList = this.orgService.findAllOrg(orgId, pageNo, pageSize, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询上级顶级党委组织ID
     *
     * @param orgId orgId
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = PartyResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-party-id")
    @ApiOperation("根据组织ID查询上级顶级党委组织ID")
    public ResponseEntity<Result<?>> findPartyId(@RequestParam(value = "org_id")
                                                 @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        PartyResultForm result = this.orgService.findPartyId(orgId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询管辖组织列表
     *
     * @param userId 用户ID
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = ManageOrgForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-manage-list")
    @ApiOperation("根据用户ID查询管辖组织列表")
    public ResponseEntity<Result<?>> findManageList(@RequestParam(value = "user_id")
                                                    @NotNull(message = "{NotNull.user.id}") Long userId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<ManageOrgForm> resultList = this.orgService.findManageList(userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询所有下级组织
     *
     * @param orgId      组织ID
     * @param filterType 1-党委 2-党总支 3-党支部，默认0不筛选
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-all-child-org")
    @ApiOperation("根据组织ID查询所有下级组织")
    public ResponseEntity<Result<?>> findAllChildOrg(@RequestParam(value = "org_id")
                                                     @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                     @RequestParam(value = "is_include", required = false,
                                                             defaultValue = "0") Integer isInclude,
                                                     @RequestParam(value = "include_del", required = false,
                                                             defaultValue = "0") Integer includeDel,
                                                     @RequestParam(value = "filter_type", required = false,
                                                             defaultValue = "0") Integer filterType,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrgNameResultForm> resultList;
        try {
            resultList = this.orgService.findAllChildOrg(orgId, header.getOrgType(), isInclude,
                    header.getRegionId(), filterType, includeDel);
        } catch (IOException e) {
            log.error("系统异常 -->{}", e.getMessage());
            return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询所有上级组织
     *
     * @param orgId 组织ID
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-all-parent-org")
    @ApiOperation("根据组织ID查询所有上级组织")
    public ResponseEntity<Result<?>> findAllParentOrg(@RequestParam(value = "org_id")
                                                      @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrgNameResultForm> resultList = this.orgService.findAllParentOrg(orgId, header.getOrgType(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID批量查询组织信息
     *
     * @param form          form
     * @param bindingResult bindingResult
     */
    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-org-by-list")
    @ApiOperation("根据组织ID批量查询组织信息")
    public ResponseEntity<Result<?>> findOrgByList(@Valid @RequestBody FindOrgListForm form,
                                                   BindingResult bindingResult,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrganizationBase> resultList = this.orgService.findOrgByList(form.getIdList(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 重置组织所有上级orgLevel
     *
     * @param orgId 组织ID
     */
    @HttpMonitorLogger
    @PostMapping("/reset-org-level")
    @ApiOperation("重置orgLevel")
    public ResponseEntity<Result<?>> resetOrgLevel(@RequestParam(value = "org_id", required = false) Long orgId,
                                                   @RequestParam(value = "region_id", required = false) Long regionId) {
        this.orgService.resetOrgLevel(orgId, regionId);
        return new ResponseEntity<>(new Result<>("", errors), HttpStatus.OK);
    }

    /**
     * 工作纪实「监督预警」获取组织概况
     *
     * @param orgId   orgId
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/getOrgProfile")
    @ApiOperation("工作纪实「监督预警」获取组织概况")
    public ResponseEntity<Result<?>> getOrgProfile(@RequestParam(value = "org_id", required = false) Long orgId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (null == orgId || orgId == 0) {
            orgId = header.getUoid();
        }
        return this.orgService.getOrgProfile(orgId, header.getRegionId());
    }

    /**
     * 根据12371用户序列号和组织序列号查询用户信息
     *
     * @param oId oId
     * @param uId uId
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = OrgPartyResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-by-id")
    @ApiOperation("根据12371用户序列号和组织序列号查询用户信息")
    public ResponseEntity<Result<?>> findUserById(@RequestParam(value = "o_id")
                                                  @NotBlank(message = "{NotBlank.o.id}") String oId,
                                                  @RequestParam(value = "u_id")
                                                  @NotBlank(message = "{NotBlank.u.id}") String uId) {
        return new ResponseEntity<>(new Result<>(this.orgService.findUserById(uId, oId), errors), HttpStatus.OK);
    }

    /**
     * 查询所有组织
     */
    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-all")
    @ApiOperation("查询所有组织")
    public ResponseEntity<Result<?>> findOrgByAll(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgService.findOrgByAll(header), errors), HttpStatus.OK);
    }

    /**
     * 模糊查询组织所有下级（新增查处单）
     *
     * @param orgId   orgId
     * @param orgName orgName
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-name")
    @ApiOperation("模糊查询组织所有下级（新增查处单）")
    public ResponseEntity<Result<?>> findOrgByName(@RequestParam(required = false, value = "org_id") Long orgId,
                                                   @RequestParam(required = false, value = "org_name") String orgName,
                                                   @RequestParam(required = false, value = "tree_type", defaultValue = "2") Integer treeType,
                                                   @RequestParam(required = false, value = "org_ids") List<Long> ids,
                                                   @RequestParam(required = false, value = "corp_id") Long corpId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> orgIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            // 默认查询当前登录组织
            orgIds.add(orgId == null ? header.getOid() : orgId);
        } else {
            orgIds.addAll(ids);
        }
        List<OrgNameResultForm> resultList = this.orgService.findOrgByName(orgIds, orgName, treeType, header.getRegionId(), corpId);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询所在组织
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgNameResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-org-by-id")
    @ApiOperation("根据用户ID查询所在组织")
    public ResponseEntity<Result<?>> findUserOrgById(@RequestParam(value = "user_id")
                                                     @NotNull(message = "{NotNull.user.id}") Long userId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrgNameResultForm> resultList = this.orgService.findUserOrgById(userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(null == resultList ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织类型查询有效组织列表
     *
     * @param orgType      orgType
     * @param orgTypeChild orgTypeChild
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = SasOrganizationForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-type")
    @ApiOperation("根据组织类型查询有效组织列表")
    public ResponseEntity<Result<?>> findOrgByType(@RequestParam(value = "org_type", required = false) Long orgType,
                                                   @RequestParam(value = "org_type_child", required = false) Long orgTypeChild,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<SasOrganizationForm> orgList = this.orgService.findOrgByType(orgType, orgTypeChild, header.getRegionId());
        return new ResponseEntity<>(new Result<>(null == orgList ? new ArrayList<>() : orgList, errors), HttpStatus.OK);
    }

    /**
     * 查询所有考核组织
     *
     * @param isPage   是否分页 1-是 2-否，默认2
     * @param pageNum  当前页码
     * @param pageSize 每页条数
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-all-eval-org")
    @ApiOperation("查询所有考核组织")
    public ResponseEntity<Result<?>> findAllEvalOrg(@RequestParam(value = "is_page", required = false, defaultValue = "2") Integer isPage,
                                                    @RequestParam(value = "page_num", required = false, defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "page_size", required = false,
                                                            defaultValue = "10") Integer pageSize,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询所有考核组织findAllEvalOrg 参数isPage：[{}],pageNum:[{}],pageSize:[{}]", isPage, pageNum, pageSize);
        Map<String, Object> resultMap = this.orgService.findAllEvalOrg(isPage, pageNum, pageSize, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultMap.get("result"), errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询下属所有考核组织
     *
     * @param orgId 组织ID
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-eval-org-by-id")
    @ApiOperation("根据组织ID查询下属所有考核组织")
    public ResponseEntity<Result<?>> findEvalOrgById(@RequestParam(value = "org_id") Long orgId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据组织ID查询下属所有考核组织findEvalOrgById orgId：[{}]", orgId);
        List<OrganizationBase> orgList = this.orgService.findEvalOrgByOrgId(orgId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(orgList, errors), HttpStatus.OK);
    }

    /**
     * 设置考核组织
     *
     * @param form 参数
     */
    @HttpMonitorLogger
    @PostMapping("/batch-deal-eval-org")
    @ApiOperation("设置考核组织")
    public ResponseEntity<Result<?>> batchDealEvalOrg(@Valid @RequestBody OrgMgrForm form,
                                                      BindingResult bindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("设置考核组织batchDealEvalOrg 参数:[{}]", form);
        try {
            this.orgService.batchDealEvalOrg(form, header);
        } catch (Exception e) {
            log.error("系统异常,设置考核组织出错 -->{}", e.getMessage());
            return new ResponseEntity<>(new Result<>(errors, 9901, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 组织用户数据统计-v1.0.5
     *
     * @param orgId   orgId
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/total-count")
    @ApiOperation("组织用户数据统计")
    public ResponseEntity<Result<OrgTotalCountForm>> totalCount(@RequestParam(value = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                                @RequestParam(value = "tree_type")
                                                                @Range(min = 1, max = 2, message = "{Range.tree.type}") Integer treeType,
                                                                @RequestParam(value = "org_type", required = false) Integer orgType,
                                                                @RequestHeader HttpHeaders headers) {
        try {
            HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
            OrgTotalCountForm total = this.orgService.totalCount(orgId, treeType, orgType, header.getRegionId());
            return new ResponseEntity<>(new Result<>(total, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("组织数据统计异常：{}", e.getMessage());
            throw new ApiException("组织数据统计出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
    }

    /**
     * 批量查询组织党员数量
     *
     * @param form 参数
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-count-by-list")
    @ApiOperation("批量查询组织党员数量")
    public ResponseEntity<Result<?>> findUserCountByList(@Valid @RequestBody OrgUserCountQueryForm form,
                                                         BindingResult bindingResult,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("批量查询组织党员 参数:[{}]", form);
        return new ResponseEntity<>(new Result<>(this.orgService.findUserCountByList(form, header), errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询指定党组织
     *
     * @param form 参数
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = ChildOrgResultForm.class)
    @HttpMonitorLogger
    @PostMapping("/find-child-org-by-list")
    @ApiOperation("根据条件查询指定党组织")
    public ResponseEntity<Result<?>> findChildOrgByList(@Valid @RequestBody ChildOrgQueryForm form,
                                                        BindingResult bindingResult,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据条件查询指定党组织 参数:[{}]", form);
        List<ChildOrgResultForm> resultList = this.orgService.findChildOrgByList(form, header);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 查询组织以及下级列表
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgWhereResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-where")
    @ApiOperation("查询组织以及下级列表")
    public ResponseEntity<Result<?>> findOrgByWhere(@RequestParam(value = "org_id", required = false) Long orgId,
                                                    @RequestParam(value = "org_name", required = false) String orgName,
                                                    @RequestParam(value = "is_retire", required = false) Integer isRetire,
                                                    @RequestParam(value = "is_flow", required = false) Integer isFlow,
                                                    @RequestParam(value = "org_type_child", required = false) Integer orgTypeChild,
                                                    @RequestParam(value = "tag_id", required = false) String tagId,
                                                    @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                    @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("查询组织以及下级列表 参数 --> orgId:[{}],orgName:[{}],isRetire:[{}],isFLow:[{}],tagId:[{}],orgTypeChild:[{}],header:[{}]",
                orgId, orgName, isRetire, isFlow, tagId, orgTypeChild, header);
        if (orgId == null) {
            orgId = header.getOid();
        }
        List<OrgWhereResultForm> resultList = this.organizationService.findOrgByWhere(orgId, orgName, isRetire, isFlow, tagId,
                page, pageSize, Constants.YES, header.getRegionId(), orgTypeChild);
        log.debug("查询组织以及下级列表 结果 --> result:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * 导出查询组织以及下级列表
     */
    @HttpMonitorLogger
    @GetMapping("/export-org-info")
    @ApiOperation("导出查询组织以及下级列表")
    public void exportOrgInfo(HttpServletResponse response,
                              @RequestParam(value = "org_id", required = false) Long orgId,
                              @RequestParam(value = "org_name", required = false) String orgName,
                              @RequestParam(value = "is_retire", required = false) Integer isRetire,
                              @RequestParam(value = "is_flow", required = false) Integer isFlow,
                              @RequestParam(value = "tag_id", required = false) String tagId,
                              @RequestParam(value = "org_type_child", required = false) Integer orgTypeChild,
                              @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("导出查询组织以及下级列表 参数 --> orgId:[{}],orgName:[{}],isRetire:[{}],isFLow:[{}],tagId:[{}],header:[{}]",
                orgId, orgName, isRetire, isFlow, tagId, header);
        try {
            if (orgId == null) {
                orgId = header.getOid();
            }
            List<OrgWhereResultForm> resultList = this.organizationService.findOrgByWhere(orgId, orgName, isRetire,
                    isFlow, tagId, null, null, Constants.NO, header.getRegionId(), orgTypeChild);
            OrganizationEntity org = this.organizationService.getOrgById(orgId);
            Workbook wb = this.organizationService.createOrgInfoWorkbook(resultList, org.getName());
            try {
                if (wb != null) {
                    // 设置响应头导出文件格式
                    String contentDisposition = "attachment;filename=" + URLEncoder.encode(org.getName(), "UTF-8") + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd_HH:mm:ss") + "." + ExcelUtils.XLS_SUFFIX;
                    // 设置响应头的文件名称信息
                    response.setHeader("Content-Disposition", contentDisposition);
                    response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                    // 设置响应头导出文件格式vnd.ms-excel
                    response.setContentType("application/vnd.ms-excel");
                    response.setCharacterEncoding("UTF-8");
                    // 使用响应的输出流导出excel文件
                    wb.write(response.getOutputStream());
                }
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (null != wb) {
                    try {
                        wb.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            log.error("导出查询组织以及下级列表: " + e.getMessage(), e);
            throw new ApiException("导出查询组织以及下级列表", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
    }

    /**
     * 根据条件查询考核组织和管理组织列表
     *
     * @param orgId   组织ID
     * @param tagType 组织标签类型 6-考核组织 7-管理组织
     * @return 组织列表
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-tag-by-where")
    @ApiOperation("根据条件查询考核组织和管理组织列表")
    public ResponseEntity<Result<?>> findOrgTagByWhere(@RequestParam(value = "org_id", required = false) Long orgId,
                                                       @RequestParam(value = "tag_type", required = false) Integer tagType,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据条件查询考核组织和管理组织列表 参数 --> orgId:[{}],tagTyep:[{}],header:[{}]", orgId, tagType, header);
        if (orgId == null) {
            orgId = header.getOid();
        }
        List<OrganizationBase> resultList = this.organizationService.findOrgTagByWhere(orgId, tagType, header.getRegionId());
        log.debug("根据条件查询考核组织和管理组织列表 结果 --> result:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询直属下级组织
     *
     * @param orgId   组织ID
     * @param orgName 组织名称
     * @return 组织列表
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = OrgWarningResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-warning")
    @ApiOperation("根据条件查询考核组织和管理组织列表")
    public ResponseEntity<Result<?>> findOrgTagByWarning(@RequestParam(value = "org_id") Long orgId,
                                                         @RequestParam(value = "org_name", required = false) String orgName,
                                                         @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                         @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 根据regionID查询顶级党委组织
        Region.OrgData orgData = simpleApplicationConfigHelper.getOrgByRegionId(header.getRegionId());
        if (null == orgData || orgData.getOrgId() == null) {
            log.error("系统异常,配置区县ID不存在 -->orgData[{}]", orgData);
            return new ResponseEntity<>(new Result<>(errors, 30000, HttpStatus.OK.value()), HttpStatus.OK);
        }
        List<OrgWarningResultForm> resultList = this.organizationService.findOrgTagByWarning(orgId, orgName, page,
                pageSize, orgData.getOrgId());
        return new ResponseEntity<>(new Result<>(CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }


    /**
     * 根据用户ID查询组织党委，所在单位信息
     *
     * @param form 参数
     * @Date 2020-01-07
     */
    @AddDynamicFields(fieldType = FieldType.ORG_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-party-by-list")
    @ApiOperation("根据用户ID查询组织党委，所在单位信息")
    public ResponseEntity<Result<?>> findUserPartyByList(@Valid @RequestBody OrgUserCountQueryForm form,
                                                         BindingResult bindingResult,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询组织党委，所在单位信息 参数:[{}]", form);
        return new ResponseEntity<>(new Result<>(this.orgService.findUserPartyByList(form.getIdList(), header.getRegionId()),
                errors), HttpStatus.OK);
    }

    /**
     * 更新组织的考核标签状态
     *
     * @param updateEvalOrgForm updateEvalOrgForm
     * @param headers           headers
     */
    @HttpMonitorLogger
    @PostMapping("/update-eval-tag-by-eval-org")
    @ApiOperation("更新组织的考核标签状态")
    public ResponseEntity<Result<?>> updateEvalTagByEvalOrg(@Valid @RequestBody UpdateEvalOrgForm updateEvalOrgForm,
                                                            @RequestHeader HttpHeaders headers) {
        log.debug("更新组织的考核标签状态，参数 -> [{}]", updateEvalOrgForm);
        int i = this.orgTagService.updateEvalOrgTag(updateEvalOrgForm, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据区域ID和组织类型获取顶级组织ID
     *
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/get-root-org-id-by-region-id")
    @ApiOperation("根据区域ID和组织类型获取顶级组织ID")
    public ResponseEntity<Result<?>> getRootOrgByRegionId(@RequestParam(value = "region_id", required = false) Long regionId,
                                                          @RequestParam(value = "org_type", required = false) Integer orgType,
                                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long resultId = this.orgService.getRootIdByOrgType(Objects.nonNull(regionId) ? regionId : header.getRegionId(), Objects.nonNull(orgType) ? orgType : header.getOrgType());
        return new ResponseEntity<>(new Result<>(resultId, errors), HttpStatus.OK);
    }


    /**
     * 根据顶级组织id 查询它下面所有组织层级关系
     * 一次性查询出所有 不用一次一次点击查询他的下级
     *
     * @Date 2020-01-07
     */
    @HttpMonitorLogger
    @PostMapping("/org-tree-by-top-org-id")
    @ApiOperation("根据顶级组织id 查询它下面所有组织层级关系")
    public ResponseEntity<Result<?>> orgTreeByTopOrgId(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询组织党委，所在单位信息 参数:[{}]", header.getOid());
        return new ResponseEntity<>(new Result<>(orgService.getStructure(header.getOid(), header.getRegionId()),
                errors), HttpStatus.OK);
    }

    /**
     * 筛选考核/管理组织
     *
     * @param form    form
     * @param headers headers
     */
    @HttpMonitorLogger
    @PostMapping("/filter-org-by-list")
    @ApiOperation("筛选考核/管理组织")
    public ResponseEntity<Result<?>> filterOrgByList(@RequestBody FindOrgListForm form, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        form.setRegionId(header.getRegionId());
        log.debug("筛选考核/管理组织 参数:[{}]", form);
        return new ResponseEntity<>(new Result<>(orgService.filterOrgByList(form), errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询考核组织和管理组织列表
     *
     * @param form    form
     * @param headers headers
     */
    @HttpMonitorLogger
    @PostMapping("/find-mgr-eval-org-by-id")
    @ApiOperation("根据组织ID查询考核组织和管理组织列表")
    public ResponseEntity<Result<?>> findMgrEvalOrgById(@RequestBody MgrEvalForm form,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        form.setRegionId(header.getRegionId());
        log.debug("根据组织ID查询考核组织和管理组织列表 参数:[{}]", form);
        return new ResponseEntity<>(new Result<>(orgService.findMgrEvalOrgById(form), errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询组织直接下级，查询之前时间数据则查询快照数据
     *
     * @param headers headers
     */
    @HttpMonitorLogger
    @GetMapping("/find-child-org-by-id")
    @ApiOperation("根据组织ID查询组织直接下级")
    public ResponseEntity<Result<?>> findChildOrgById(@RequestParam(value = "org_id")
                                                      @NotNull(message = "{NotNull.org.id}") Long orgId,
                                                      @RequestParam(value = "year")
                                                      @NotNull(message = "{NotNull.year}") Integer year,
                                                      @RequestParam(value = "month")
                                                      @NotNull(message = "{NotNull.month}") Integer month,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgService.findChildOrgById(orgId, year, month, header.getRegionId()), errors),
                HttpStatus.OK);
    }


    /**
     * 根据组织权重倒序
     *
     * @param form          组织ID列表
     * @param bindingResult bindingResult
     */
    @HttpMonitorLogger
    @PostMapping("/sort-org-by-list")
    @ApiOperation("根据组织权重倒序")
    public ResponseEntity<Result<?>> sortOrgByList(@Valid @RequestBody FindOrgListForm form,
                                                   BindingResult bindingResult,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> resultList = this.orgService.sortOrgByList(form.getIdList(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/corp-expand-info")
    @ApiOperation("查询单位拓展信息")
    public ResponseEntity<Result<?>> corpExpandInfo(@RequestParam(value = "corp_id") Long corpId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgService.getCorpExpandInfo(header.getRegionId(), corpId), errors), HttpStatus.OK);
    }

    /**
     * 根据单位id查询党组织信息
     *
     * @param corpId  单位id
     * @param headers header
     */
    @HttpMonitorLogger
    @GetMapping("/find-org-by-corp")
    @ApiOperation("根据单位id查询党组织信息")
    public ResponseEntity<Result<?>> findOrgByCorp(@RequestParam(value = "corpId")
                                                   @NotNull(message = "{NotNull.query.oid}") Long corpId,
                                                   @RequestParam(value = "type", required = false, defaultValue = "0") Integer type,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgService.findOrgByCorp(corpId, header, type), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-grade-by-org-id")
    @ApiOperation("根据组织id查询平级组织id")
    public ResponseEntity<Result<?>> findGradeByOrgId(@RequestParam(value = "orgId")
                                                      @NotNull(message = "{NotNull.org.id}") Long orgId,
                                                      @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                      @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.organizationService.findGradeByOrgId(orgId, header, page, pageSize), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-org-by-unit")
    @ApiOperation("根据行政单位id获取一级党组织")
    public ResponseEntity<Result<?>> findOrgByUnit(@RequestParam(value = "org_id") Long orgId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.organizationService.getOrgByUnit(orgId, header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-org-by-owner")
    @ApiOperation("根据党组织获取同级党支部列表")
    public ResponseEntity<Result<?>> findOrgByOwner(@RequestParam(value = "org_id") Long orgId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.organizationService.findOrgByOwner(orgId, header), errors), HttpStatus.OK);
    }

    /**
     * 获取所有党委列表
     *
     */
    @HttpMonitorLogger
    @GetMapping("/find-all-party")
    @ApiOperation("获取所有党委列表")
    public ResponseEntity<Result<?>> findAllParty(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OrganizationTree> organizationEntities= this.orgService.findAllParty();
        return new ResponseEntity<>(new Result<>(organizationEntities, errors), HttpStatus.OK);
    }
}
