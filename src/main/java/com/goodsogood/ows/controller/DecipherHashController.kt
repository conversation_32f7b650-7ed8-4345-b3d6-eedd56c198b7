package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HashAlgorithms
import com.goodsogood.ows.helper.SM3Utils
import com.goodsogood.ows.model.vo.HashForm
import com.goodsogood.ows.model.vo.Result
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.io.IOException
import javax.validation.Valid
import kotlin.math.abs

/**
 * <AUTHOR>
 * @date 2023/6/12
 * @description class DecipherHashController
 */
@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "hash计算", tags = ["内部调用"])
@RequestMapping("/show_me_the_money")
@Validated
class DecipherHashController(val errors: Errors) {
    private val log = org.slf4j.LoggerFactory.getLogger(javaClass)

    @PostMapping("/hash")
    @HttpMonitorLogger
    @ApiOperation("show me the money")
    fun showHash(@Valid @RequestBody hashForm: HashForm): ResponseEntity<Result<HashForm>> {
        val str = hashForm.path + hashForm.content?.replace("\n", "")
            ?.replace("\r", "")
            ?.replace(Regex("\\s+"), "")
        var myHash: String? = ""
        if (hashForm.hashAlgo == "SDBM") {
            myHash = abs(HashAlgorithms.SDBMHash(str)).toString()
            // str to uri encode and sdbm it
        } else if (hashForm.hashAlgo == "SM3") {
            try {
                myHash = SM3Utils.hashToHexString(str)
            } catch (e: IOException) {
                log.error("计算hash时候，SM3 hash失败", e)
            }
        }

        return ResponseEntity<Result<HashForm>>(
            Result<HashForm>(
                hashForm.also {
                    it.money = myHash
                    it.str = str
                }, errors
            ), HttpStatus.OK
        )
    }
}