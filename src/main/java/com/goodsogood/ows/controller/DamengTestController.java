package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.util.SqlCompatibilityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 达梦数据库测试控制器
 * 用于测试达梦数据库连接和SQL转换功能
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@RestController
@RequestMapping("/dameng-test")
@Api(value = "达梦数据库测试", tags = {"达梦数据库测试"})
@Log4j2
public class DamengTestController {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private Errors errors;

    /**
     * 测试数据库连接
     */
    @GetMapping("/connection")
    @ApiOperation("测试达梦数据库连接")
    public ResponseEntity<Result<Map<String, Object>>> testConnection() {
        log.info("开始测试达梦数据库连接...");
        
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            result.put("connected", true);
            result.put("databaseProductName", metaData.getDatabaseProductName());
            result.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            result.put("driverName", metaData.getDriverName());
            result.put("driverVersion", metaData.getDriverVersion());
            result.put("url", metaData.getURL());
            result.put("userName", metaData.getUserName());
            result.put("status", "连接成功");
            
            log.info("达梦数据库连接成功: {} {}", 
                    metaData.getDatabaseProductName(), 
                    metaData.getDatabaseProductVersion());
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (SQLException e) {
            log.error("达梦数据库连接失败: {}", e.getMessage(), e);
            result.put("connected", false);
            result.put("error", e.getMessage());
            result.put("status", "连接失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 执行简单查询测试
     */
    @GetMapping("/query")
    @ApiOperation("执行简单查询测试")
    public ResponseEntity<Result<Map<String, Object>>> testQuery() {
        log.info("开始执行达梦数据库查询测试...");
        
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            // 执行简单的查询
            try (ResultSet rs = connection.createStatement().executeQuery("SELECT 1 FROM DUAL")) {
                if (rs.next()) {
                    result.put("queryResult", rs.getInt(1));
                    result.put("status", "查询成功");
                    
                    DatabaseMetaData metaData = connection.getMetaData();
                    result.put("databaseType", metaData.getDatabaseProductName());
                    
                    log.info("达梦数据库查询测试成功");
                    return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
                }
            }
        } catch (SQLException e) {
            log.error("达梦数据库查询测试失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("status", "查询失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
        
        result.put("status", "未知错误");
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 测试SQL转换功能
     */
    @PostMapping("/sql-conversion")
    @ApiOperation("测试SQL转换功能")
    public ResponseEntity<Result<Map<String, Object>>> testSqlConversion(@RequestBody Map<String, String> request) {
        log.info("开始测试SQL转换功能...");
        
        String originalSql = request.get("sql");
        if (originalSql == null || originalSql.trim().isEmpty()) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", "SQL语句不能为空");
            return new ResponseEntity<>(new Result<>(errorResult, errors), HttpStatus.BAD_REQUEST);
        }
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查是否需要转换
            boolean needsConversion = SqlCompatibilityUtil.needsConversion(originalSql);
            
            // 转换SQL
            String convertedSql = SqlCompatibilityUtil.convertSql(originalSql);
            
            // 获取SQL类型
            SqlCompatibilityUtil.SqlType sqlType = SqlCompatibilityUtil.getSqlType(originalSql);
            
            result.put("originalSql", originalSql);
            result.put("convertedSql", convertedSql);
            result.put("needsConversion", needsConversion);
            result.put("sqlType", sqlType.toString());
            result.put("changed", !originalSql.equals(convertedSql));
            result.put("status", "转换成功");
            
            log.info("SQL转换测试完成 - 原SQL: {}, 转换后: {}", originalSql.trim(), convertedSql.trim());
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("SQL转换测试失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            result.put("status", "转换失败");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取常见SQL转换示例
     */
    @GetMapping("/conversion-examples")
    @ApiOperation("获取常见SQL转换示例")
    public ResponseEntity<Result<Map<String, Object>>> getConversionExamples() {
        Map<String, Object> result = new HashMap<>();
        Map<String, String> examples = new HashMap<>();
        
        // 添加常见的转换示例
        examples.put("LIMIT语法", "SELECT * FROM users LIMIT 10, 20 -> SELECT * FROM users LIMIT 20 OFFSET 10");
        examples.put("反引号", "SELECT `name` FROM `users` -> SELECT name FROM users");
        examples.put("NOW函数", "SELECT NOW() -> SELECT SYSDATE");
        examples.put("IFNULL函数", "SELECT IFNULL(name, 'Unknown') -> SELECT NVL(name, 'Unknown')");
        examples.put("UNIX_TIMESTAMP", "SELECT UNIX_TIMESTAMP(created_at) -> SELECT EXTRACT(EPOCH FROM created_at)");
        examples.put("DATE_FORMAT", "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') -> SELECT TO_CHAR(created_at, 'YYYY-MM-DD')");
        examples.put("AUTO_INCREMENT", "CREATE TABLE users (id INT AUTO_INCREMENT) -> CREATE TABLE users (id INT IDENTITY(1,1))");
        
        result.put("examples", examples);
        result.put("description", "这些是MySQL到达梦数据库的常见SQL语法转换示例");
        
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 获取数据库系统信息
     */
    @GetMapping("/system-info")
    @ApiOperation("获取数据库系统信息")
    public ResponseEntity<Result<Map<String, Object>>> getSystemInfo() {
        log.info("获取达梦数据库系统信息...");
        
        Map<String, Object> result = new HashMap<>();
        
        try (Connection connection = dataSource.getConnection()) {
            DatabaseMetaData metaData = connection.getMetaData();
            
            result.put("databaseProductName", metaData.getDatabaseProductName());
            result.put("databaseProductVersion", metaData.getDatabaseProductVersion());
            result.put("databaseMajorVersion", metaData.getDatabaseMajorVersion());
            result.put("databaseMinorVersion", metaData.getDatabaseMinorVersion());
            result.put("driverName", metaData.getDriverName());
            result.put("driverVersion", metaData.getDriverVersion());
            result.put("driverMajorVersion", metaData.getDriverMajorVersion());
            result.put("driverMinorVersion", metaData.getDriverMinorVersion());
            result.put("jdbcMajorVersion", metaData.getJDBCMajorVersion());
            result.put("jdbcMinorVersion", metaData.getJDBCMinorVersion());
            result.put("url", metaData.getURL());
            result.put("userName", metaData.getUserName());
            result.put("supportsTransactions", metaData.supportsTransactions());
            result.put("supportsStoredProcedures", metaData.supportsStoredProcedures());
            result.put("maxConnections", metaData.getMaxConnections());
            result.put("maxStatements", metaData.getMaxStatements());
            
            log.info("达梦数据库系统信息获取成功");
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (SQLException e) {
            log.error("获取达梦数据库系统信息失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
