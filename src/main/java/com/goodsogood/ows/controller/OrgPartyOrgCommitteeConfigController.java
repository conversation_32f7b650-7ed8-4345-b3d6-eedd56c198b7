package com.goodsogood.ows.controller;

import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.OrgPartyOrgCommitteeConfigForm;
import com.goodsogood.ows.service.OrgPartyOrgCommitteeConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2019/11/20
 * @Description: 党组织委员会参数 配置
 **/
@RestController
@RequestMapping("/party-org-committee")
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class OrgPartyOrgCommitteeConfigController {
    private final OrgPartyOrgCommitteeConfigService orgPartyOrgCommitteeConfigService;

    @Autowired
    public OrgPartyOrgCommitteeConfigController(OrgPartyOrgCommitteeConfigService orgPartyOrgCommitteeConfigService) {
        this.orgPartyOrgCommitteeConfigService = orgPartyOrgCommitteeConfigService;
    }

    @PostMapping("/update")
    public ResponseEntity<?> update(@RequestBody @Valid OrgPartyOrgCommitteeConfigForm.UpdateForm updateForm,
                                    BindingResult bindingResult, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(orgPartyOrgCommitteeConfigService.update(updateForm, header), HttpStatus.OK);
    }

    @GetMapping("/list")
    public ResponseEntity<?> list() {
        return new ResponseEntity<>(orgPartyOrgCommitteeConfigService.list(), HttpStatus.OK);
    }

    /**
     *
     * @return  查看 选中组织 纪律检查委员会、党务办公室这两个字段是否可见
     */
    @GetMapping("/is-visible/{org_id}")
    public ResponseEntity<?> isVisible(@PathVariable("org_id") long orgId) {
        return new ResponseEntity<>(orgPartyOrgCommitteeConfigService.isVisible(orgId,null), HttpStatus.OK);
    }

}
