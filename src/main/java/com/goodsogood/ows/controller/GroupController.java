package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.common.LogType;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.RoleEntity;
import com.goodsogood.ows.model.vo.BitchGroupForm;
import com.goodsogood.ows.model.vo.GroupDetailForm;
import com.goodsogood.ows.model.vo.GroupForm;
import com.goodsogood.ows.model.vo.GroupOrgForm;
import com.goodsogood.ows.model.vo.GroupQueryForm;
import com.goodsogood.ows.model.vo.GroupRoleForm;
import com.goodsogood.ows.model.vo.GroupUserForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.RoleAddForm;
import com.goodsogood.ows.model.vo.UserOrgResultForm;
import com.goodsogood.ows.service.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "用户组", tags = {"用户组管理"})
@RequestMapping("/uc/group")
public class GroupController {

    private final Errors errors;
    private final GroupService groupService;

    @Autowired
    public GroupController(Errors errors, GroupService groupService){
        this.errors = errors;
        this.groupService = groupService;
    }

    /**
     * 创建用户组
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("创建用户组")
    public ResponseEntity<Result<?>> creatGroup(@Valid @RequestBody GroupForm form,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.createGroup(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 更新用户组
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation("更新用户组")
    public ResponseEntity<Result<?>> updateGroup(@Valid @RequestBody GroupForm form,
                                                BindingResult bindingResult,
                                                @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.updateGroup(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 修改用户组状态(支持批量)
     * @param form
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/change-status")
    @ApiOperation("修改用户组状态(支持批量)")
    public ResponseEntity<Result<?>> bitchChangeStatus(@Valid @RequestBody BitchGroupForm form,
                                                       BindingResult bindingResult,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.bitchChangeStatus(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 根据用户组查询用户列表
     * @param groupId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-user-by-group")
    @ApiOperation("根据用户组查询用户列表")
    public ResponseEntity<Result<?>> findUserByGroup(@RequestParam("group_id") @NotNull(message = "{NotNull.group.groupId}") Long groupId){
        List<UserOrgResultForm> userList = this.groupService.findUserByGroup(groupId);
        return new ResponseEntity<>(new Result<>(userList, errors), HttpStatus.OK);
    }

    /**
     * 查询用户组列表
     * @param form
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-group-by-org")
    @ApiOperation("查询用户组列表")
    public ResponseEntity<Result<?>> findGroupByOrg(@Valid @RequestBody GroupQueryForm form,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<GroupOrgForm> groupForms = this.groupService.findGroupByOrg(form, header);
        return new ResponseEntity<>(new Result<>(groupForms, errors), HttpStatus.OK);
    }

    /**
     * 用户组新增或移除用户(支持批量)
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/handle-user")
    @ApiOperation("用户组新增或移除用户(支持批量)")
    public ResponseEntity<Result<?>> handleUser(@Valid @RequestBody GroupUserForm form,
                                                  BindingResult bindingResult,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.handleUser(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-role-by-group")
    @ApiOperation("根据用户组查询角色列表")
    public ResponseEntity<Result<?>> findRoleByGroup(@RequestParam("group_id") @NotNull(message = "{NotNull.group.groupId}") Long groupId,
                                                     @RequestHeader HttpHeaders headers){
        List<RoleEntity> roleList = this.groupService.findRoleByGroup(groupId);
        return new ResponseEntity<>(new Result<>(roleList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织查询可选择角色列表
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-role-by-org")
    @ApiOperation("根据组织查询可选择角色列表")
    public ResponseEntity<Result<?>> findRoleByOrg( @RequestParam(value = "org_id", required = false) Long orgId,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long oid = header.getOid();
        List<Long> orgIds = new ArrayList<>(2);
        orgIds.add(oid);
        if (orgId != null & !orgId.equals(oid)) {
            orgIds.add(orgId);
        }
        List<RoleAddForm> roleList = this.groupService.findRoleByOrg(orgIds);
        return new ResponseEntity<>(new Result<>(roleList, errors), HttpStatus.OK);
    }

    /**
     * 用户组新增或移除角色(支持批量)
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/handle-role")
    @ApiOperation("用户组新增或移除角色(支持批量)")
    public ResponseEntity<Result<?>> handleRole(@Valid @RequestBody GroupRoleForm form,
                                                  BindingResult bindingResult,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.handleRole(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 编辑用户组详情页面
     * @param groupId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/select-group-by-id")
    @ApiOperation("编辑用户组详情页面")
    public ResponseEntity<Result<?>> selectGroupById(@RequestParam("group_id") @NotNull(message = "{NotNull.group.groupId}") Long groupId){
        GroupDetailForm form = this.groupService.selectGroupById(groupId);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    /**
     * 删除下发组织
     * @param groupId
     * @param headers
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/del-lssue-org")
    @ApiOperation("判断删除下发组织是否存在用户")
    public ResponseEntity<Result<?>> delLssueOrg(@RequestParam("group_id") @NotNull(message = "{NotNull.group.groupId}") Long groupId,
                                                 @RequestHeader HttpHeaders headers){
        int result = this.groupService.delLussueOrg(groupId);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    /**
     * 新增用户组和组织关系
     * @param form
     * @param bindingResult
     * @param headers
     * @return
     */
  /*  @HttpMonitorLogger
    @PostMapping("/add-group-org")
    @ApiOperation("新增用户组和组织关系")
    public ResponseEntity<Result<?>> addGroupOrg(@Valid @RequestBody GroupAddOrgForm form,
                                                 BindingResult bindingResult,@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int result = this.groupService.addGroupOrg(form, header);
        if (result == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, result, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }*/
}
