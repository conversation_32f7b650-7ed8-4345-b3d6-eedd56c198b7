package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.FieldForm;
import com.goodsogood.ows.model.vo.FieldVO;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.FieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 字段控制层
 * <AUTHOR>
 */
@RestController
@RequestMapping("/field")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "字段控制层", tags = {"field"})
@Validated
public class FieldController {

    private static final int USER = 1;
    private static final int ORG = 2;

    private final Errors errors;
    private final FieldService fieldService;

    @Autowired
    public FieldController(Errors errors, FieldService fieldService) {
        this.errors = errors;
        this.fieldService = fieldService;
    }

    @HttpMonitorLogger
    @PostMapping("/org/add")
    @ApiOperation("新增组织字段")
    public ResponseEntity<Result<?>> addOrgField(@Valid @RequestBody FieldForm form,
                                              BindingResult bindingResult,
                                              @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.addField(form, ORG,header);
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/user/add")
    @ApiOperation("新增用户字段")
    public ResponseEntity<Result<?>> addUserField(@Valid @RequestBody FieldForm form,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.addField(form, USER,header);
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新增用户字段")
    public ResponseEntity<Result<?>> addField(@Valid @RequestBody FieldForm form,
                                                  BindingResult bindingResult,
                                              @RequestParam(value = "own") Integer own,
                                              @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.addField(form,own,header);
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/org/update")
    @ApiOperation("更新组织字段")
    public ResponseEntity<Result<?>> updateOrgField(@Valid @RequestBody FieldForm form,
                                                 BindingResult bindingResult,
                                                 @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.updateField(form, ORG, header.getUserId());
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/user/update")
    @ApiOperation("更新用户字段")
    public ResponseEntity<Result<?>> updateUserField(@Valid @RequestBody FieldForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.updateField(form, USER, header.getUserId());
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/delete")
    @ApiOperation("删除动态字段")
    public ResponseEntity<Result<?>> deleteField(@RequestParam(value = "field_id")
                                                 @NotNull(message = "{NotNull.field.id}") Long fieldId,
                                                 @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.fieldService.deleteField(fieldId, header.getUserId());
        return new ResponseEntity<>(new Result<>("SUCCESS", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/get")
    @ApiOperation("删除动态字段")
    public ResponseEntity<Result<?>> getField(@RequestParam(value = "field_id")
                                              @NotNull(message = "{NotNull.field.id}") Long fieldId,
                                              @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        FieldForm field = this.fieldService.getField(fieldId);
        return new ResponseEntity<>(new Result<>(field, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/org/list")
    @ApiOperation("查询组织字段列表")
    public ResponseEntity<Result<?>> getOrgFieldList(@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<FieldVO> fieldList = this.fieldService.getFieldList(ORG, header);
        return new ResponseEntity<>(new Result<>(fieldList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/user/list")
    @ApiOperation("查询用户字段列表")
    public ResponseEntity<Result<?>> getUserFieldList(@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<FieldVO> fieldList = this.fieldService.getFieldList(USER, header);
        return new ResponseEntity<>(new Result<>(fieldList, errors), HttpStatus.OK);
    }


}
