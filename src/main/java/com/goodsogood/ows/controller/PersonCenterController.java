package com.goodsogood.ows.controller;


import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.ExclExportUtils;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.BranchHighlightEntity;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.UserHighlightEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.service.BranchHighlightService;
import com.goodsogood.ows.service.UserHighlightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "烟草个人中心", tags = {"烟草个人中心"})
@RequestMapping("/person-center")
public class PersonCenterController {

    private final BranchHighlightService branchHighlightService;

    private final UserHighlightService userHighlightService;

    private final Errors errors;

    public PersonCenterController(BranchHighlightService branchHighlightService,
                                  UserHighlightService userHighlightService,
                                  Errors errors) {
        this.branchHighlightService = branchHighlightService;
        this.userHighlightService = userHighlightService;
        this.errors = errors;
    }

    /**
     * 添加支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @PostMapping("/org/highlight/add")
    @ApiOperation("添加支部风采或者支部发展史")
    public ResponseEntity<Result<?>> addOrgHighlight(@Valid @RequestBody BranchHighlightForm branchHighlightForm,
                                            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long resultId = branchHighlightService.addOrgHighlight(branchHighlightForm, header);
        return new ResponseEntity<>(new Result<>(resultId, errors), HttpStatus.OK);
    }


    /**
     * 删除支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @PostMapping("/org/highlight/del")
    @ApiOperation("删除支部风采或者支部发展史")
    public ResponseEntity<Result<?>> delOrgHighlight(@Valid @RequestBody BranchHighlightForm branchHighlightForm,
                                         @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        branchHighlightService.delOrgHighlight(branchHighlightForm, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 修改支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @PostMapping("/org/highlight/edit")
    @ApiOperation("修改支部风采或者支部发展史")
    public ResponseEntity<Result<?>> editOrgHighlight(@Valid @RequestBody BranchHighlightForm branchHighlightForm,
                                            @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        branchHighlightService.editOrgHighlight(branchHighlightForm, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 得到支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @GetMapping("/org/highlight/get")
    @ApiOperation("修改支部风采或者支部发展史")
    public ResponseEntity<Result<?>> getOrgHighlight(@RequestParam(value="highlight_id",required = true)
                                                                 Long highlightId,
                                                      @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        BranchHighlightEntity orgHighlight = branchHighlightService.getOrgHighlight(highlightId, header);
        return new ResponseEntity<>(new Result<>(orgHighlight, errors), HttpStatus.OK);
    }

    /**
     * 得到本单位支部风采和推优列表
     */
    @HttpMonitorLogger
    @GetMapping("/org/highlight/selectExcellent")
    @ApiOperation("得到本单位支部风采和推优列表")
    public ResponseEntity<Result<?>> selectExcellent(@RequestParam(value = "org_id") Long orgId,
                                                     @RequestParam(value = "page",required = false,defaultValue = "1") Integer page,
                                                     @RequestParam(value = "page_size",required = false,defaultValue = "10") Integer pageSize){
        List<HighlightForm>  orgHighlight = branchHighlightService.selectExcellent(orgId,page,pageSize);
        return new ResponseEntity<>(new Result<>(orgHighlight, errors), HttpStatus.OK);
    }

    /**
     * 个人中心我的支部
     */
    @HttpMonitorLogger
    @GetMapping("/my_branch")
    @ApiOperation("个人中心我的支部支部风采或者支部发展史")
    public ResponseEntity<Result<?>> myBranch( @RequestParam(value="type")Integer type,
                                               @RequestParam(value="org_id") Long orgId,
                                               @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return   branchHighlightService.myBranch(type,orgId, header);
    }


    /**
     * 得到支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @GetMapping("/org/highlight/list")
    @ApiOperation("修改支部风采或者支部发展史")
    public ResponseEntity<Result<?>> listOrgHighlight(@RequestParam(value="org_id") Long orgId,
                                                      @RequestParam(value="type")Integer type,
                                                      @RequestParam(required = false)
                                                                  Integer page,
                                                      @RequestParam(required = false, name = "page_size")
                                                                  Integer pageSize,
                                                      @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        List<BranchHighlightEntity> orgHighlight = branchHighlightService.listOrgHighlight(orgId, type,
                new PageNumber(page, pageSize), header);
        return new ResponseEntity<>(new Result<>(orgHighlight, errors), HttpStatus.OK);
    }


    /**
     * 得到支部风采或者支部发展史
     */
    @HttpMonitorLogger
    @GetMapping("/org/highlight/export")
    @ApiOperation("导出支部风采或者支部发展史")
    public ResponseEntity<Result<?>> listOrgHighExport(HttpServletResponse response,
                                                       HttpServletRequest request,
                                                        @RequestParam(value="org_id") Long orgId,
                                                        @RequestParam(value="type")Integer type,
                                                        @RequestParam(required = false) Integer page,
                                                        @RequestParam(required = false, name = "page_size") Integer pageSize,
                                                        @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10000;
        }
        try {
            List<BranchHighlightEntity> orgHighlight = branchHighlightService.listOrgHighlight(orgId, type,
                    new PageNumber(page, pageSize), header);

            if(CollectionUtils.isEmpty(orgHighlight)){
                throw new ApiException(
                        "没有查询到数据，无法展出！", new Result<>(errors, 4712, HttpStatus.OK.value()));
            }
            String excelName = type == 1 ? "支部风采" : "支部发展史";
            String[] colName = {"时间","标题", "简介"};
            HSSFWorkbook workbook = branchHighlightService.exportPortExcel(orgHighlight,null, excelName, colName);
            boolean flag = ExclExportUtils.export03(request, response, workbook, excelName);
            log.info("导出支部风采或者支部发展史失败结果：" + flag);
            return null;
        } catch (Exception ex) {
            log.error("导出支部风采或者支部发展史异常",ex);
            throw new ApiException(
                    "导出支部风采或者支部发展史失败！", new Result<>(errors, 4712, HttpStatus.OK.value()));
        }
    }


    /**
     * 得到支部风采待推优列表
     */
    @HttpMonitorLogger
    @GetMapping("/org/wait_excellent/list")
    @ApiOperation("得到支部风采待推优列表")
    public ResponseEntity<Result<?>> listOrgWaitExcellentHighlight(@RequestParam(value="org_id") Long orgId,
                                                      @RequestParam(value="type",defaultValue = "1")Integer type,
                                                      @RequestParam(required = false)
                                                              Integer page,
                                                      @RequestParam(required = false, name = "page_size")
                                                              Integer pageSize,
                                                      @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        List<BranchHighlightEntity> orgHighlight = branchHighlightService.listOrgWaitExcellentHighlight(orgId, type,
                new PageNumber(page, pageSize), header);
        //对封面单独处理
        orgHighlight.forEach(item->{
            //封面单独处理
            if(null!=item.getFileType()&&item.getFileType()==2) {
                item.setUrl(item.getCover());
            }else {
                item.setUrl(item.getUrl());
            }

            //简介单独判断
            if(StringUtils.isNotBlank(item.getTitle())) {
                item.setDesc(item.getTitle());
            }else {
                item.setDesc(item.getDesc());
            }
        });
        return new ResponseEntity<>(new Result<>(orgHighlight, errors), HttpStatus.OK);
    }

    /**
     * 确定推优
     */
    @HttpMonitorLogger
    @PostMapping("/sure_excellent")
    @ApiOperation("确定推优")
    public ResponseEntity<Result<?>> orgExcellent(@Valid @RequestBody ExcellentForm excellentForm,
                                                       @RequestHeader HttpHeaders headers){
        if(CollectionUtils.isEmpty(excellentForm.getOrgHighlightId())){
            return new ResponseEntity<>(new Result<>(-1, errors), HttpStatus.OK);
        }
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return branchHighlightService.orgExcellent(excellentForm, header);
    }


    /**
     * 取消推优
     */
    @HttpMonitorLogger
    @PostMapping("/cancel_excellent")
    @ApiOperation("确定推优")
    public ResponseEntity<Result<?>> cancelExcellent(@Valid @RequestBody CancelExcellentForm cancelExcellentForm,
                                                     @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return branchHighlightService.cancelExcellent(cancelExcellentForm, header);
    }

    /**
     * 添加个人成长轨迹
     */
    @HttpMonitorLogger
    @PostMapping("/user/highlight/add")
    @ApiOperation("添加个人成长轨迹")
    public ResponseEntity<Result<?>> addUserHighlight(@Valid @RequestBody UserHighlightForm userHighlightForm,
                                                     @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Integer resultId = userHighlightService.addUserHighlight(userHighlightForm, header);
        return new ResponseEntity<>(new Result<>(resultId, errors), HttpStatus.OK);
    }


    /**
     * 删除个人成长轨迹
     */
    @HttpMonitorLogger
    @PostMapping("/user/highlight/del")
    @ApiOperation("删除个人成长轨迹")
    public ResponseEntity<Result<?>> delUserHighlight(@Valid @RequestBody UserHighlightForm userHighlightForm,
                                                      @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        userHighlightService.delUserHighlight(userHighlightForm, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }



    /**
     * 修改个人成长轨迹
     */
    @HttpMonitorLogger
    @PostMapping("/user/highlight/edit")
    @ApiOperation("修改个人成长轨迹")
    public ResponseEntity<Result<?>> editUserHighlight(@Valid @RequestBody UserHighlightForm userHighlightForm,
                                                      @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        userHighlightService.editUserHighlight(userHighlightForm, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }


    /**
     * 修改个人成长轨迹
     */
    @HttpMonitorLogger
    @GetMapping("/user/highlight/get")
    @ApiOperation("修改个人成长轨迹")
    public ResponseEntity<Result<?>> getUserHighlight(@RequestParam(value="highlight_id",required = true)
                                                                  Long highlightId,
                                                       @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UserHighlightEntity userHighlight = userHighlightService.getUserHighlight(highlightId, header);
        return new ResponseEntity<>(new Result<>(userHighlight, errors), HttpStatus.OK);
    }

    /**
     * 个人成长轨迹列表
     */
    @HttpMonitorLogger
    @GetMapping("/user/highlight/list")
    @ApiOperation("个人成长轨迹列表")
    public ResponseEntity<Result<?>> listUserHighlight( @RequestParam(value="type", required = false,
                defaultValue = "1" )Integer type,
                @RequestParam(required = false) Integer page,
                @RequestParam(required = false, name = "page_size") Integer pageSize,
                @RequestHeader HttpHeaders headers){
            HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
            if (page == null || page < 1) {
                page = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }
            List<UserHighlightEntity> userHighlight = userHighlightService.listUserHighlight(type,
            new PageNumber(page, pageSize),header);
            return new ResponseEntity<>(new Result<>(userHighlight, errors), HttpStatus.OK);
    }



    /**
     * 个人成长轨迹列表导出
     */
    @HttpMonitorLogger
    @GetMapping("/user/highlight/export")
    @ApiOperation("个人成长轨迹列表导出")
    public ResponseEntity<Result<?>> listUserHighExport( HttpServletResponse response,
                                                         HttpServletRequest request,
                                                        @RequestParam(value="type", required = false,
                                                        defaultValue = "1" )Integer type,
                                                        @RequestParam(required = false) Integer page,
                                                        @RequestParam(required = false, name = "page_size") Integer pageSize,
                                                        @RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 1000;
        }
        try{
            List<UserHighlightEntity> userHighlight = userHighlightService.listUserHighlight(type,
                    new PageNumber(page, pageSize),header);
            if(CollectionUtils.isEmpty(userHighlight)){
                throw new ApiException(
                        "没有查询到数据，无法展出！", new Result<>(errors, 4712, HttpStatus.OK.value()));
            }
            String excelName = "个人成长轨迹";
            String[] colName = {"时间", "简介"};
            HSSFWorkbook workbook = branchHighlightService.exportPortExcel(null,userHighlight, excelName, colName);
            boolean flag = ExclExportUtils.export03(request, response, workbook, excelName);
            log.info("个人成长轨迹列表导出结果：" + flag);
            return null;
        } catch (Exception ex) {
            log.error("个人成长轨迹列表导出错误",ex);
            throw new ApiException(
                    "个人成长轨迹列表导出失败！", new Result<>(errors, 4712, HttpStatus.OK.value()));
        }
    }

    /**
     * 个人中心(个人成长轨迹)
     */
    @HttpMonitorLogger
    @GetMapping("/user/highlight/person_center")
    @ApiOperation("个人成长轨迹列表")
    public ResponseEntity<Result<?>> personCenter(@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(userHighlightService.personCenter(header.getUserId()), errors), HttpStatus.OK);
    }

    /**
     * 首页党建风采获取所有党委的推优党建品牌和组织风采
     */
    @HttpMonitorLogger
    @GetMapping("/home/<USER>")
    @ApiOperation("首页党建风采获取所有党委的推优党建阵地和组织风采")
    public ResponseEntity<Result<?>> getPartyBuildingBrand(@RequestHeader HttpHeaders headers){
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return branchHighlightService.getPartyBuildingBrand(header);
    }

}
