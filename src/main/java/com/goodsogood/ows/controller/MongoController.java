package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.FieldNameConfig;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.UserFieldEnum;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.OrgMongoService;
import com.goodsogood.ows.service.UserMongoService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/mongo")
@Log4j2
public class MongoController {

	private final Errors errors;
	private final UserMongoService userMongoService;
	private final OrgMongoService orgMongoService;

	@Autowired
	public MongoController(Errors errors,
						   UserMongoService userMongoService,
						   OrgMongoService orgMongoService) {
		this.errors = errors;
		this.userMongoService = userMongoService;
		this.orgMongoService = orgMongoService;
	}

	@HttpMonitorLogger
	@PostMapping(value = "/getUserList")
	@ApiOperation("查询人员列表")
	public ResponseEntity<Result<?>> getUserList(@RequestParam(value = "own", required = false) Integer own,
												 @RequestParam(value = "type", required = false) Integer type,
												 @RequestParam(value = "orderFields", required = false) String[] orderFields,
												 @RequestParam(value = "is_only_belong_org", required = false) Integer isOnlyBelongOrg,
												 @RequestBody Map<String, Object> queryMap,
												 @RequestHeader HttpHeaders headers) {
		HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
		// 判断是否需要分页
		Object isPageObj = queryMap.get(FieldNameConfig.IS_PAGE);
		// 是否仅查询所属组织
		if (Objects.isNull(isOnlyBelongOrg)) {
			Object isOnlyBelongOrgObj = queryMap.get(FieldNameConfig.IS_ONLY_BELONG_ORG);
			if (Objects.nonNull(isOnlyBelongOrgObj)) {
				isOnlyBelongOrg = Convert.toInt(isOnlyBelongOrgObj);
			}
		}
		// 区域ID
		Long regionId = header.getRegionId();
		Object regionIdObj = queryMap.get(FieldNameConfig.REGION_ID);
		if (Objects.isNull(regionIdObj)) {
			queryMap.put(FieldNameConfig.REGION_ID, regionId);
		}
		// 组织类型
		Object o = queryMap.get(FieldNameConfig.ORG_TYPE);
		Integer orgType = header.getOrgType();
		if (Objects.nonNull(o)) {
			orgType = Convert.toInt(o);
		}
		queryMap.put(FieldNameConfig.ORG_TYPE, orgType);
		boolean isOnlyOrg = Objects.nonNull(isOnlyBelongOrg) && (isOnlyBelongOrg.equals(Constants.STATUS_YES));
		// 分页逻辑
		Integer isPage = Convert.toInt(isPageObj);
		if (Objects.nonNull(isPageObj) && isPage.equals(Constants.STATUS_YES)) {
			int page = queryMap.get(FieldNameConfig.PAGE) == null ? 1 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE));
			int pageSize = queryMap.get(FieldNameConfig.PAGE_SIZE) == null ? 10 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE_SIZE));
			PageNumber pageNumber = new PageNumber(page, pageSize);
			return new ResponseEntity<>(new Result<>(this.userMongoService.getUserList(queryMap, own, type, pageNumber, header, isOnlyOrg, orderFields), errors), HttpStatus.OK);
		} else {
			return new ResponseEntity<>(new Result<>(this.userMongoService.getUserList(queryMap, own, type, header, isOnlyOrg, orderFields), errors), HttpStatus.OK);
		}
	}

	@HttpMonitorLogger
	@GetMapping(value = "/delMongoRedundantUser")
	@ApiOperation("删除MongoDB多余的人")
	public ResponseEntity<Result<?>> deleteMongoErrorData(@RequestParam(value = "region_id") Long regionId){
		this.userMongoService.synchronizeUserData(regionId);
		return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
	}

}
