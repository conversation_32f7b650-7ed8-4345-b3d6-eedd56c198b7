package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.CorpService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import lombok.extern.log4j.Log4j2
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*

@RestController
@Log4j2
@Validated
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "单位", tags = ["单位"])
@RequestMapping("/corp")
class CorpController @Autowired constructor(val errors: Errors,
                                            val corpService: CorpService) {

    @HttpMonitorLogger
    @GetMapping("/query-list")
    @ApiOperation("查询单位列表")
    fun ami(
        @RequestParam(name = "page", required = false, defaultValue = "1") page: Int = 1,
        @RequestParam(name = "page_size", required = false, defaultValue = "10") pageSize: Int = 10,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity<Result<Any>>(
            Result<Any>(
                this.corpService.getCorpList(page, pageSize, headers)
                , errors
            ), HttpStatus.OK
        )
    }
}