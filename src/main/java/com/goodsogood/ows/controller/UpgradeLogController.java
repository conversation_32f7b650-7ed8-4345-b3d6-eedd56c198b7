package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UpgradeLogEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.UpgradeLogEditForm;
import com.goodsogood.ows.model.vo.UpgradeLogForm;
import com.goodsogood.ows.service.UpgradeLogService;

import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 升级日志管理
 *
 * <AUTHOR>
 * @date 2019/4/8 17:48
 */
@RestController
@RequestMapping("/uc/upgrade-log")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "升级日志管理", tags = {"upgradeLog"})
@Validated
public class UpgradeLogController {

    private final Errors errors;
    private final UpgradeLogService upgradeLogService;

    public UpgradeLogController(Errors errors, UpgradeLogService upgradeLogService) {
        this.errors = errors;
        this.upgradeLogService = upgradeLogService;
    }

    /**
     * 新增升级日志记录
     *
     * <AUTHOR>
     * @date 2019/4/9 8:34
     */

    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("新增升级日志记录")
    public ResponseEntity<Result<?>> addUpgradeLog(@RequestHeader HttpHeaders headers,
                                                   @Valid @RequestBody UpgradeLogForm form,
                                                   BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UpgradeLogEntity upgradeLogEntity = new UpgradeLogEntity();
        upgradeLogEntity.setVer(form.getVer());
        upgradeLogEntity.setContent(form.getContent());
        if (StringUtils.isBlank(form.getSummary())) {
            upgradeLogEntity.setSummary("");
        } else {
            upgradeLogEntity.setSummary(form.getSummary());
        }
        upgradeLogEntity.setUpgradeTime(form.getUpgradeTime());
        upgradeLogEntity.setUpdateTime(new Date());
        upgradeLogEntity.setCreateTime(new Date());
        upgradeLogEntity.setStatus(Constants.LOG_STATUS_NORMAL);
        upgradeLogEntity.setLastChangeUser(header.getUserId());
        int flag = this.upgradeLogService.addUpgradeLog(upgradeLogEntity);
        if (flag == 2) {
            log.debug("该版本号已经存在,{}", form.getVer());
            return new ResponseEntity<>(new Result<>(errors, 4017, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("升级日志添加成功");
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 更新升级日志记录
     *
     * <AUTHOR>
     * @date 2019/4/9 9:15
     */

    @HttpMonitorLogger
    @PostMapping("/edit")
    @ApiOperation("更新升级日志记录")
    public ResponseEntity<Result<?>> editUpgradeLog(
        @RequestHeader HttpHeaders headers,
        @Valid @RequestBody UpgradeLogEditForm form,
        BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UpgradeLogEntity upgradeLogEntity = new UpgradeLogEntity();
        if (StringUtils.isBlank(form.getSummary())) {
            upgradeLogEntity.setSummary("");
        } else {
            upgradeLogEntity.setSummary(form.getSummary());
        }
        upgradeLogEntity.setUpgradeLogId(form.getUpgradeLogId());
        upgradeLogEntity.setContent(form.getContent());
        upgradeLogEntity.setLastChangeUser(header.getUserId());
        upgradeLogEntity.setUpdateTime(new Date());
        int flag = this.upgradeLogService.editUpgradeLog(upgradeLogEntity);
        if (flag == 2) {
            log.debug("当前升级日志记录不存在，id={}", form.getUpgradeLogId());
            return new ResponseEntity<>(new Result<>(errors, 4018, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("升级日志修改成功id={}", form.getUpgradeLogId());
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 查询升级日志记录详情
     *
     * <AUTHOR>
     * @date 2019/4/9 10:30
     */

    @HttpMonitorLogger
    @ApiOperation(value = "升级日志记录详情")
    @PostMapping("/details/{upgrade_log_id}")
    public ResponseEntity<Result<UpgradeLogEntity>> detailUpgradeLog(@RequestHeader HttpHeaders headers,
                                                                     @PathVariable("upgrade_log_id")
                                                                     @NotNull(message = "{NotBlank.ver.id}")
                                                                         long upgradeLogId) {
        log.info("升级日志记录详情.upgrade_log_id:{}", upgradeLogId);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Result<UpgradeLogEntity> result = new Result<>(
            upgradeLogService.detailUpgradeLog(upgradeLogId), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    /**
     * 删除升级日志记录
     *
     * <AUTHOR>
     * @date 2019/4/9 10:37
     */

    @HttpMonitorLogger
    @ApiOperation(value = "删除升级日志记录")
    @DeleteMapping("/del/{upgrade_log_id}")
    @RepeatedCheck
    public ResponseEntity<Result<Boolean>> delUpgradeLog(@RequestHeader HttpHeaders headers,
                                                         @PathVariable("upgrade_log_id") @NotNull(message = "{NotBlank.ver.id}") long upgradeLogId) {
        log.info("删除日志记录.upgradeLogId:{}", upgradeLogId);
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Result<Boolean> result = new Result<>(upgradeLogService.deleteUpgradeLog(upgradeLogId, sysHeader) > 0, errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * 查询升级日志列表
     *
     * <AUTHOR>
     * @date 2019/4/9 16:04
     */

    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("升级日志记录列表")
    public ResponseEntity<Result<Page<List<UpgradeLogEntity>>>> list(
        @RequestHeader HttpHeaders headers,
        @RequestBody UpgradeLogEditForm form
    ) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        UpgradeLogForm upgradeLogForm = new UpgradeLogForm();
        upgradeLogForm.setVer(form.getVer());
        upgradeLogForm.setSummary(form.getSummary());
        upgradeLogForm.setUpgradeTime(form.getUpgradeTime());
        Page<List<UpgradeLogEntity>> listPage = this.upgradeLogService.pageupgradeLogList(upgradeLogForm, form.getPageSize(), form.getPageNo(), sysHeader);
        return new ResponseEntity<>(new Result<>(listPage, errors), HttpStatus.OK);
    }

    /**
     * 查询最新的日志记录
     *
     * <AUTHOR>
     * @date 2019/4/9 16:14
     */

    @HttpMonitorLogger
    @ApiOperation(value = "查询最新升级日志记录")
    @PostMapping("/last-details")
    public ResponseEntity<Result<UpgradeLogEntity>> newUpgradeLog(@RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader sysHeader = HeaderHelper.buildMyHeader(headers);
        Result<UpgradeLogEntity> result = new Result<>(
            upgradeLogService.findNewUpgradeLog(), errors);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
