package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Config;
import com.goodsogood.ows.common.ErrorCode;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.model.db.ConsigneeAddressEntity;
import com.goodsogood.ows.model.vo.ConsigneeAddressForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.ConsigneeAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 收货人地址管理
 *
 * <AUTHOR>
 * @create 2019/12/10
 **/
@RestController
@RequestMapping("/consignee")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "收货人地址管理", tags = {"收货人地址管理"})
@Validated
public class ConsigneeAddressController {

    private final Errors errors;

    private final ConsigneeAddressService consigneeAddressService;

    @Autowired
    public ConsigneeAddressController(Errors errors, ConsigneeAddressService consigneeAddressService) {
        this.errors = errors;
        this.consigneeAddressService = consigneeAddressService;
    }


    /**
     * 编辑用户收货地址(积分中心调用)
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/cr/edit")
    @ApiOperation("编辑用户收货地址(积分中心调用)")
    public ResponseEntity<Result<?>> editConsigneeAddress(@Valid @RequestBody ConsigneeAddressForm form,
                                                          BindingResult bindingResult,
                                                          @RequestHeader HttpHeaders headers) {
        log.debug("编辑用户收货地址(积分中心调用) 开始！form={}", form);
        Long re = -1L;
        //检查参数
        consigneeAddressService.checkConsigneeAddress(form);
        try {
            //根据操作类型选择处理方式
            Integer operationType = form.getOperationType();
            switch (operationType) {
                case Config.ConsigneeAddress.OPERATION_TYPE_ADD:
                    if (form.getDefaultFlag() != 1) {
                        //非默认地址的添加
                        re = consigneeAddressService.addConsigneeAddress(form);
                    } else {
                        //默认地址的添加
                        re = consigneeAddressService.addConsigneeAddressForDefaultFlag(form);
                    }

                    break;
                case Config.ConsigneeAddress.OPERATION_TYPE_UPD:
                    //先判断是否为已删除的数据，如果是已删除数据就放弃编辑
                    if(form.getAddressType()!=1){
                        //如果不是党建的地址，通过数据库查找addressId
                        form.setAddressId(consigneeAddressService.findAddressId(form));
                    }
                    //获取地址信息
                    ConsigneeAddressEntity cae = consigneeAddressService.findConsigneeAddressInfo(form);
                    if(cae!=null&&cae.getStatus()==1){
                        if (form.getDefaultFlag() != 1) {
                            //非默认地址的修改
                            consigneeAddressService.updConsigneeAddress(form);
                        } else {
                            //默认地址的修改
                            consigneeAddressService.updConsigneeAddressForDefaultFlag(form);
                        }
                        re = 0L;
                    }else{
                        log.error("修改的地址信息不存在或者已经为删除状态！form={}",form);
                        throw new RuntimeException("修改的地址信息不存在或者已经为删除状态！");
                    }
                    break;
                case Config.ConsigneeAddress.OPERATION_TYPE_DEL:
                    //非默认地址的删除
                    consigneeAddressService.delConsigneeAddress(form);
                    re = 0L;
                    break;
                default:
                    log.debug("编辑用户收货地址(积分中心调用) 操作类型不在预定的范围 operationType={} form={}", operationType, form);
            }
        } catch (Exception e) {
            //处理失败删除重复验证token
            consigneeAddressService.delToken(form.getAccessKey(),form.getToken());
            log.error("编辑用户收货地址(积分中心调用)出错！form={}", form, e);
            throw new ApiException("编辑用户收货地址(积分中心调用)出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "编辑用户收货地址(积分中心调用)出错"));
        }
        return new ResponseEntity<>(new Result<>(re, errors), HttpStatus.OK);
    }


    /**
     * 添加外部用户收货人地址关联信息(积分中心调用)
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/cr/add/outAddressInfo")
    @ApiOperation("添加外部用户收货人地址关联信息(积分中心调用)")
    public ResponseEntity<Result<?>> addOutAddressInfo(@Valid @RequestBody ConsigneeAddressForm form,
                                                     BindingResult bindingResult,
                                                     @RequestHeader HttpHeaders headers) {
        String logText = "添加外部用户收货人地址关联信息(积分中心调用)";
        log.debug(logText + " 开始！form={}", form);
        //检查参数
        if (StringUtils.isEmpty(form.getAddressType())) {
            log.error(logText + "：参数校验： address_type(地址来源) 不能为空");
            throw new ApiException(logText + "：参数校验： address_type(地址来源) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "address_type(地址来源) 不能为空"));
        }
        if (StringUtils.isEmpty(form.getAccessKey())) {
            log.error(logText + "：参数校验： access_key(外部渠道标识) 不能为空");
            throw new ApiException(logText + "：参数校验： access_key(外部渠道标识) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "access_key(外部渠道标识) 不能为空"));
        }
        if (StringUtils.isEmpty(form.getAddressId())) {
            log.error(logText + "：参数校验： address_id(地址编号) 不能为空");
            throw new ApiException(logText + "：参数校验： address_id(地址编号) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "address_id(地址编号) 不能为空"));
        }
        if (StringUtils.isEmpty(form.getOutAddressId())) {
            log.error(logText + "：参数校验： out_address_id(外部地址编号) 不能为空");
            throw new ApiException(logText + "：参数校验： out_address_id(外部地址编号) 不能为空", new Result<>(errors, ErrorCode.PARAM_ERROR, HttpStatus.OK.value(), "out_address_id(外部地址编号) 不能为空"));
        }
        try {
            //添加外部用户收货地址关联信息
            consigneeAddressService.addOutAddressInfo(form);
        } catch (Exception e) {
            log.error(logText + "出错！form={}", form, e);
            throw new ApiException(logText + "出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), logText + "出错"));
        }
        return new ResponseEntity<>(new Result<>(ErrorCode.SUCCESS, errors), HttpStatus.OK);
    }
}
