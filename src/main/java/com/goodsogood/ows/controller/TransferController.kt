package com.goodsogood.ows.controller

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger
import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.exception.ApiException
import com.goodsogood.ows.helper.HeaderHelper
import com.goodsogood.ows.model.vo.*
import com.goodsogood.ows.service.InnerTransferService
import com.goodsogood.ows.service.TransferService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.hibernate.validator.constraints.Length
import org.hibernate.validator.constraints.Range
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 * <AUTHOR> ruoyu
 * @date : 2021/8/31
 */
@RestController
@RequestMapping("/transfer")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "组织关系转接相关", tags = ["组织关系转接相关"])
@Validated
class TransferController @Autowired constructor(
    private val errors: Errors,
    private val transferService: TransferService,
    private val  innerTransferService: InnerTransferService
) {

    @HttpMonitorLogger
    @PostMapping("/list")
    @ApiOperation("转入转出列表")
    fun list(
        @Valid @RequestBody transferListForm: TransferListForm,
        bindingResult: BindingResult, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(Result(transferService.list(transferListForm, header), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/detail")
    @ApiOperation("查看详情")
    fun detail(
        @RequestParam("master_id") masterId: Long,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        return ResponseEntity(Result(transferService.detail(masterId, header), errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/p-join")
    @ApiOperation("转入办理")
    fun join(
        @RequestParam("master_id") masterId: Long,
        @RequestParam("proposal") @Length(max = 500, message = "办理意见最多只能填写500字") proposal: String,
        @RequestParam(name = "files", required = false) files: String?,
        @RequestParam("type") @Range(min = 1, max = 2, message = "办理意见条件错误") type: Int,
        @RequestParam(name = "process_time", required = false) processTime: String?,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val join = transferService.join(masterId, header, proposal, files, type, processTime, true)
        if (join > 0) throw ApiException(
            join.toString(),
            Result<Any>(errors, join, HttpStatus.INTERNAL_SERVER_ERROR.value())
        )
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @PostMapping("/add")
    @ApiOperation("提交转接申请")
    fun add(
        @Valid @RequestBody transferAddForm: TransferAddForm,
        bindingResult: BindingResult, @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        transferService.add(transferAddForm, header)
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @GetMapping("/cancel")
    @ApiOperation("取消申请")
    fun cancel(
        @RequestParam("master_id") masterId: Long,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val join = transferService.cancel(masterId, header)
        if (join > 0) throw ApiException(
            join.toString(),
            Result<Any>(errors, join, HttpStatus.INTERNAL_SERVER_ERROR.value())
        )
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }

    @HttpMonitorLogger
    @PostMapping("/add-new")
    @ApiOperation("录入新党员")
    fun addNew(
        @RequestBody transferAddNewForm: TransferNewUserForm,
        @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        val header = HeaderHelper.buildMyHeader(headers)
        val join = transferService.addNew(transferAddNewForm, header, true)
        if (join.equals(-1L)) throw ApiException(
            "手机号码已经存在",
            Result<Any>(errors, 125, HttpStatus.INTERNAL_SERVER_ERROR.value())
        )
        return ResponseEntity(Result(join, errors), HttpStatus.OK)
    }


    @HttpMonitorLogger
    @PostMapping("/inner-transfer")
    @ApiOperation("组织关系内部调整转出")
    fun innerTransfer(
            @RequestBody form: InnerTransferForm,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<*>> {
        innerTransferService.innerTransfer(form,headers);
        return ResponseEntity(Result("success", errors), HttpStatus.OK)
    }
}