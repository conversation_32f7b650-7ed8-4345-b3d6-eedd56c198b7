package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.RepeatedCheck;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.SecretaryMailboxEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SecretaryMailboxVo;
import com.goodsogood.ows.service.SecretaryMailboxService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/secretary-mailbox")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Validated
public class SecretaryMailboxController {
    private final Errors errors;

    private final SecretaryMailboxService secretaryMailboxService;

    @Autowired
    public SecretaryMailboxController(Errors errors, SecretaryMailboxService secretaryMailboxService) {
        this.errors = errors;
        this.secretaryMailboxService = secretaryMailboxService;
    }

    /**
     * 查询增书记信箱记录
     */
    @HttpMonitorLogger
    @ApiOperation(value = "查询增书记信箱记录")
    @PostMapping("/getSecretaryMailboxList")
    @Validated
    public ResponseEntity<Result<Long>> getSecretaryMailboxList(@RequestHeader HttpHeaders headers,
                                                                @Valid @RequestBody SecretaryMailboxVo secretaryMailboxVo) {

        return secretaryMailboxService.getSecretaryMailboxList(headers, secretaryMailboxVo, new PageNumber(secretaryMailboxVo.getPage(),secretaryMailboxVo.getPage_size()));
    }

    /**
     * 新增书记信箱
     */
    @HttpMonitorLogger
    @ApiOperation(value = "新增书记信箱")
    @PostMapping("/addSecretaryMailbox")
    @RepeatedCheck
    @Validated
    public ResponseEntity<?> addSecretaryMailbox(@RequestHeader HttpHeaders headers, @Valid @RequestBody SecretaryMailboxEntity secretaryMailboxEntity) {

        return secretaryMailboxService.addSecretaryMailbox(headers,secretaryMailboxEntity);
    }

    /**
     * 获取书记信箱详情
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取书记信箱详情")
    @GetMapping("/getSecretaryMailboxDetail")
    @Validated
    public ResponseEntity<?> getSecretaryMailboxDetail(@RequestParam(required = false) Long id) {

        return secretaryMailboxService.getSecretaryMailboxDetail(id);
    }

    /**
     * 修改书记信箱
     */
    @HttpMonitorLogger
    @ApiOperation(value = "修改书记信箱")
    @PostMapping("/updateSecretaryMailbox")
    @RepeatedCheck
    @Validated
    public ResponseEntity<?> updateSecretaryMailbox(@RequestHeader HttpHeaders headers, @Valid @RequestBody SecretaryMailboxEntity secretaryMailboxEntity) {
        return secretaryMailboxService.updateSecretaryMailbox(headers,secretaryMailboxEntity);
    }

    /**
     * 删除书记信箱
     */
    @HttpMonitorLogger
    @ApiOperation(value = "删除书记信箱")
    @GetMapping("/deleteSecretaryMailbox")
    @RepeatedCheck
    @Validated
    public ResponseEntity<?> deleteSecretaryMailbox(@RequestParam(required = false) Long id) {
        return secretaryMailboxService.deleteSecretaryMailbox(id);
    }

    /**
     * 获取书记信箱草稿
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取书记信箱草稿")
    @GetMapping("/getDraftSecretaryMailbox")
    @Validated
    public ResponseEntity<?> getDraftSecretaryMailbox(@RequestHeader HttpHeaders headers) {
        return secretaryMailboxService.getDraftSecretaryMailbox(headers);
    }

    /**
     * 获取书记信箱管理列表
     */
    @HttpMonitorLogger
    @ApiOperation(value = "获取书记信箱管理列表")
    @PostMapping("/getSecretaryMailboxManageList")
    @Validated
    public ResponseEntity<?> getSecretaryMailboxManageList(@RequestHeader HttpHeaders headers,
                                                           @Valid @RequestBody SecretaryMailboxVo secretaryMailboxVo) {
        return secretaryMailboxService.getSecretaryMailboxManageList(headers,secretaryMailboxVo,new PageNumber(secretaryMailboxVo.getPage(),secretaryMailboxVo.getPage_size()));
    }

    /**
     * 回复接口
     */
    @HttpMonitorLogger
    @ApiOperation(value = "回复接口")
    @PostMapping("/updateSecretaryMailboxManage")
    @RepeatedCheck
    @Validated
    public ResponseEntity<?> updateSecretaryMailboxManage(@RequestHeader HttpHeaders headers,
                                                          @Valid @RequestBody SecretaryMailboxEntity secretaryMailboxEntity) {
        return secretaryMailboxService.updateSecretaryMailboxManage(headers,secretaryMailboxEntity);
    }
}
