package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.pageUtils.PageList;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.PushExcellenCriterionEntity;
import com.goodsogood.ows.model.vo.CheckListForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.PushExcellenCriterionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 风采、阵地、品牌推优
 * @author: zhangtao
 * @create: 2022-03-16 17:44
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "风采、阵地、品牌推优", tags = {"风采、阵地、品牌推优"})
@RequestMapping("/push-excellent")
public class PushExcellentController {

    private final Errors errors;
    private final PushExcellenCriterionService pushExcellenCriterionService;

    @Autowired
    public PushExcellentController(Errors errors, PushExcellenCriterionService pushExcellenCriterionService) {
        this.errors = errors;
        this.pushExcellenCriterionService = pushExcellenCriterionService;
    }

    @HttpMonitorLogger
    @GetMapping(value = "/list")
    @ApiOperation(value = "/查看推优标准")
    public ResponseEntity<Result<?>> list(@RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "type") Integer type) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<PushExcellenCriterionEntity> list = this.pushExcellenCriterionService.list(header, type);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping(value = "/edit")
    @ApiOperation(value = "/编辑推优标准")
    public ResponseEntity<Result<?>> edit(@RequestHeader HttpHeaders headers,
                                          @RequestBody PushExcellenCriterionEntity pushEntity) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.pushExcellenCriterionService.edit(header, pushEntity);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/check/list")
    @ApiOperation(value = "/审核列表")
    public ResponseEntity<Result<?>> checkList(@RequestHeader HttpHeaders headers,
                                               @RequestParam(value = "status") Integer status,
                                               @RequestParam(value = "type", required = false) Integer type,
                                               @RequestParam(value = "title", required = false) String title,
                                               @RequestParam(value = "org_name", required = false) String orgName,
                                               @RequestParam(value = "excellent_status", required = false) Integer excellentStatus,
                                               @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                               @RequestParam(value = "pageSize", required = false, defaultValue = "6") Integer pageSize) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        PageList pageList = this.pushExcellenCriterionService.checkList(header, status, type, title, orgName, excellentStatus, page, pageSize);
        return new ResponseEntity<>(new Result<>(pageList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping(value = "/check")
    @ApiOperation(value = "/推优-审核")
    public ResponseEntity<Result<?>> check(@RequestHeader HttpHeaders headers,
                                           @RequestBody CheckListForm form) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.pushExcellenCriterionService.check(header, form);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/check/top")
    @ApiOperation(value = "/置顶、取消置顶")
    public ResponseEntity<Result<?>> top(@RequestHeader HttpHeaders headers,
                                         @RequestParam(value = "check_id") Long checkId,
                                         @RequestParam(value = "top_status") Integer topStatus) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.pushExcellenCriterionService.setTop(header, checkId, topStatus);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/check/index")
    @ApiOperation(value = "/首页展示设置和取消")
    public ResponseEntity<Result<?>> index(@RequestHeader HttpHeaders headers,
                                           @RequestParam(value = "check_id") Long checkId,
                                           @RequestParam(value = "index_status") Integer indexStatus) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.pushExcellenCriterionService.setIndex(header, checkId, indexStatus);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping(value = "/check/move")
    @ApiOperation(value = "/移动端分类列表展示")
    public ResponseEntity<Result<?>> move(@RequestHeader HttpHeaders headers,
                                          @RequestParam(value = "type") Integer type) throws Exception {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.pushExcellenCriterionService.moveList(header, type), errors), HttpStatus.OK);
    }
}

