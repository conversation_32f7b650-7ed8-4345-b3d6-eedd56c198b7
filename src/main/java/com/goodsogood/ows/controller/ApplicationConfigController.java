package com.goodsogood.ows.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.ApplicationConfigEntity;
import com.goodsogood.ows.model.vo.ApplicationConfigForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.ApplicationConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/4/13
 */
@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "应用配置", tags = {"应用配置管理"})
@RequestMapping("/app/config")
public class ApplicationConfigController {

    private final Errors errors;
    private final ApplicationConfigService applicationConfigService;
    private final SimpleApplicationConfigHelper applicationConfigHelper;

    @Autowired
    public ApplicationConfigController(Errors errors,
                                       ApplicationConfigService applicationConfigService,
                                       SimpleApplicationConfigHelper applicationConfigHelper) {
        this.errors = errors;
        this.applicationConfigService = applicationConfigService;
        this.applicationConfigHelper = applicationConfigHelper;
    }

    @ApiOperation("新增应用配置（表单上传方式）")
    @PostMapping("/saveByUpload")
    public ResponseEntity<Result<?>> saveByUpload(@RequestParam("config_file") MultipartFile file) {
        boolean result = applicationConfigService.saveByUpload(file);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @ApiOperation("修改应用配置（表单上传方式）")
    @PostMapping("/updateByUpload")
    public ResponseEntity<Result<?>> updateByUpload(@RequestParam("config_file") MultipartFile file,
//                                                    @RequestParam("id") Long id,
                                                    String commit) {
        boolean result = applicationConfigService.updateByUpload(file, commit);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @ApiOperation("新增应用配置（json参数方式）")
    @PostMapping("/saveByForm")
    public ResponseEntity<Result<?>> saveByForm(@Validated(value = {ApplicationConfigForm.AddApplicationConfig.class}) ApplicationConfigForm applicationConfigForm,
                                                BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ApiException("参数错误", new Result<>(Global.Errors.VALID_ERROR.getCode(), bindingResult.getFieldErrors().get(0).getDefaultMessage(), HttpStatus.BAD_REQUEST.value(), null));
        }
        boolean result = applicationConfigService.saveByForm(applicationConfigForm);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @ApiOperation("修改应用配置（json参数方式）")
    @PostMapping("/updateByForm")
    public ResponseEntity<Result<?>> updateByForm(@Validated(value = {ApplicationConfigForm.UpdateApplicationConfig.class}) ApplicationConfigForm applicationConfigForm,
                                                  BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ApiException("参数错误", new Result<>(Global.Errors.VALID_ERROR.getCode(), bindingResult.getFieldErrors().get(0).getDefaultMessage(), HttpStatus.BAD_REQUEST.value(), null));
        }
        boolean result = applicationConfigService.updateByForm(applicationConfigForm);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @ApiOperation("查询应用配置")
    @GetMapping("/getConfig")
    public ResponseEntity<Result<?>> getConfig(@RequestParam String application, @RequestParam String label) {
        ApplicationConfigEntity config = applicationConfigService.getApplicationConfig(application, label);
        return new ResponseEntity<>(new Result<>(config, errors), HttpStatus.OK);
    }

    @ApiOperation("查询前端应用配置")
    @GetMapping("/getConfig/pc")
    public ResponseEntity<Result<?>> getConfig(@RequestHeader HttpHeaders httpHeaders) {
        List<String> ids = httpHeaders.get(HeaderHelper.OPERATOR_REGION);
        Long regionId = null;
        for (String id : ids) {
            if (id != null) {
                regionId = Long.valueOf(id);
            }
            break;
        }
        if (regionId == null) {
            return null;
        }
        Map<Long, Map<String, Object>> map = applicationConfigHelper.getConfigByType("pc", new TypeReference<Map<Long, Map<String, Object>>>() {
        });
        if (map == null) {
            return null;
        }
        Map<String, Object> objectMap = map.get(regionId);
        if (Objects.nonNull(objectMap)) {
            objectMap.put("region_id", regionId);
        }
        return new ResponseEntity<>(new Result<>(objectMap, errors), HttpStatus.OK);
    }

    @ApiOperation("查询前端应用配置")
    @GetMapping("/refresh")
    public ResponseEntity<Result<?>> refresh(@RequestParam(required = false) String application) {
        return new ResponseEntity<>(new Result<>(applicationConfigService.refresh(application), errors), HttpStatus.OK);
    }

//    @GetMapping("/test")
//    public void test() {
//        Region regions = applicationConfigHelper.getRegions();
//        Region.OrgData orgByRegionId = applicationConfigHelper.getOrgByRegionId(3);
//        Region.RegionData regionByOrgId = applicationConfigHelper.getRegionByOrgId(3);
//        Map<String, Object> global = applicationConfigHelper.getConfigByMap("GLOBAL");
//        System.out.println("111");
//    }
}
