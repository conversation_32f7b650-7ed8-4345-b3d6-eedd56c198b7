package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SnapshotParamForm;
import com.goodsogood.ows.service.SnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2020-11-04 19:10
 * @since 3.0.1
 **/
@RestController
@RequestMapping("/snapshot")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "数据快照", tags = {"数据快照"})
@Validated
public class SnapshotController {

     private final Errors errors;
     private final SnapshotService snapshotService;

     @Autowired
     public SnapshotController(Errors errors, SnapshotService snapshotService) {
          this.errors = errors;
          this.snapshotService = snapshotService;
     }

     /**
      * 初始化组织快照数据
      *
      * @param form
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/deal-org-snapshot")
     @ApiOperation("初始化组织快照数据")
     public ResponseEntity<Result<?>> dealOrgSnapshot(@RequestBody SnapshotParamForm form) {
          this.snapshotService.dealOrgSnapshot(form);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 初始化用户快照数据
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/deal-user-snapshot")
     @ApiOperation("初始化用户快照数据")
     public ResponseEntity<Result<?>> dealUserSnapshot(@RequestBody SnapshotParamForm form) {
          this.snapshotService.dealUserSnapshot(form);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 初始化单位快照数据
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/deal-corp-snapshot")
     @ApiOperation("初始化单位快照数据")
     public ResponseEntity<Result<?>> dealCorpSnapshot(@RequestBody SnapshotParamForm form) {
          this.snapshotService.dealCorpSnapshot(form);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

     /**
      * 处理单位更新时间
      *
      * @return
      */
     @HttpMonitorLogger
     @PostMapping("/deal-corp-create-time")
     @ApiOperation("处理单位更新时间")
     public ResponseEntity<Result<?>> dealCorpCreateTime() {
          this.snapshotService.dealCorpCreateTime();
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }


}
