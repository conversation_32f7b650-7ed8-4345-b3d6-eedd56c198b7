package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.AddDynamicFields;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.annotation.OwsValidation;
import com.goodsogood.ows.common.*;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldType;
import com.goodsogood.ows.component.UserFieldEnum;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.model.db.MembershipEntity;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.mongo.User;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: tc
 * @Description 组织人员
 * @Date 16:40 2018/6/11
 */
@SuppressWarnings("ALL")
@RestController
@RequestMapping("/org/user")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "组织人员", tags = {"组织人员"})
@Validated
public class OrgUserController {

    @Value("${org-menu-id}")
    private String orgMenuId;

    private final String phoneRegexp = "^1\\d{10}$";

    private static final String REDIS_REPEAT_DOWN_USER = "REDIS_REPEAT_DOWN_USER_";

    private final Errors errors;
    private final UserService userService;
    private final OrgUserService orgUserService;
    private final OpenService openService;
    private final OptionService optionService;
    private final DictAreaService dictAreaService;
    private final UserTagService userTagService;
    private final OrgUserAddService orgUserAddService;
    private final StringRedisTemplate redisTemplate;
    private final ImportUserService importUserService;
    private final OrganizationService organizationService;

    @Autowired
    public OrgUserController(Errors errors,
                             UserService userService,
                             OrgUserService orgUserService,
                             OpenService openService,
                             OptionService optionService,
                             DictAreaService dictAreaService,
                             UserTagService userTagService,
                             OrgUserAddService orgUserAddService,
                             StringRedisTemplate redisTemplate,
                             ImportUserService importUserService,
                             OrganizationService organizationService) {
        this.errors = errors;
        this.userService = userService;
        this.orgUserService = orgUserService;
        this.openService = openService;
        this.optionService = optionService;
        this.dictAreaService = dictAreaService;
        this.userTagService = userTagService;
        this.orgUserAddService = orgUserAddService;
        this.redisTemplate = redisTemplate;
        this.importUserService = importUserService;
        this.organizationService = organizationService;
    }


    /**
     * 用户新增时检查手机号重复
     *
     * @param phone       phone
     * @param developType 1-发展党员校验手机号码 ,默认0-其他
     */
    @HttpMonitorLogger
    @GetMapping("/addCheckPhone")
    @ApiOperation("用户新增时检查手机号码重复")
    public ResponseEntity<Result<?>> addCheckPhone(@RequestParam
                                                   @NotBlank(message = "{NotBlank.user.phone}")
                                                   @javax.validation.constraints.Pattern(regexp = phoneRegexp, message = "{Pattern.user.phone}")
                                                   String phone,
                                                   @RequestParam @NotNull(message = "{NotNull.user.org.id}") Long oid,
                                                   @RequestParam(value = "developType", required = false, defaultValue = "0") Integer developType,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String p = NumEncryptUtils.encrypt(phone, 2);//加密后的电话号码
        if (developType == 1) {
            log.debug("发展党员校验手机号码是否存 -> phone:[{}],header:[{}]", phone, header);
            return new ResponseEntity<>(new Result<>(this.buildUserForm(p, header.getRegionId()), errors), HttpStatus.OK);
        } else {
            UserForm form = this.userService.addCheckPhone(p, Constants.DEP_TYPE_ORG, oid);
            if (form != null && form.getUserId() != null) {
                log.debug("该手机号码已经在该组织下存在，提示 用户已存在 -> phone:{}", phone);
                return new ResponseEntity<>(new Result<>(errors, 125, HttpStatus.OK.value()), HttpStatus.OK);
            } else {
                return new ResponseEntity<>(new Result<>(this.buildUserForm(p, header.getRegionId()), errors), HttpStatus.OK);
            }
        }
    }

    /**
     * @param encryptPhone encryptPhone
     * @param regionId     regionId
     */
    private UserForm buildUserForm(String encryptPhone, Long regionId) {
        UserForm existForm = new UserForm();
        // 在其他组织是否存在
        int exist = this.userService.checkPhone(encryptPhone, null);
        // 存在
        if (exist > 0) {
            existForm = this.userService.findIdByPhone(encryptPhone);
            // 构建form
            this.orgUserAddService.buildForm(existForm, regionId);
        }
        return existForm;
    }

    /**
     * 身份证实名验证及重复检查
     *
     * @param name   name
     * @param idCard idCard
     */
    @HttpMonitorLogger
    @GetMapping("/checkNameIdCard")
    @ApiOperation("身份证实名验证及重复检查")
    public ResponseEntity<Result<?>> checkNameIdCard(
            @RequestParam @NotBlank(message = "{NotBlank.user.name}") String name,
            @RequestParam @NotBlank(message = "{NotBlank.user.certNumber}")
            @javax.validation.constraints.Pattern(regexp = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$)", message = "{Pattern.user.certNumber}")
            String idCard,
            @RequestParam(required = false) Long userId) {
        String idCardRepetitionError = errors.errors.get(191);
        return orgUserService.checkNameIdCard(idCard, name, userId, idCardRepetitionError, openService);
    }


    /**
     * 新增组织人员
     *
     * @param userMap              userMap
     * @param ignoredBindingResult bindingResult
     */
    @OwsValidation(own = 1, type = 1)
    @HttpMonitorLogger
    @PostMapping("/orgUserAdd")
    @ApiOperation("新增组织党员")
    @DoLog(type = LogType.INSERT,content = "新增组织党员")
    public ResponseEntity<Result<?>> addOrgUser(@Valid @RequestBody Map<String, Object> userMap,
                                                BindingResult ignoredBindingResult,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 提交的证件号码值
        String ucn = Convert.toStr(userMap.get(FieldNameConfig.USER_CERT_NUMBER));
        // 提交上来的用户电话号码值
        String up = Convert.toStr(userMap.get(FieldNameConfig.USER_PHONE));
        // 用户ID
        Long userId = Convert.toLong(userMap.get(FieldNameConfig.USER_ID));
        // 用户姓名
        String name = Convert.toStr(userMap.get(FieldNameConfig.USER_NAME));
        // 修改标识
        String flag = Convert.toStr(userMap.get("flag"));
        // 政治面貌
        Integer politicalType = Convert.toInt(userMap.get(FieldNameConfig.USER_POLITICAL_TYPE), 0);

        // 姓名更改标记
        int nameChangeFlag = -1;
        // 电话更改标记
        int phoneChangeFlag = -1;
        // 身份证更改标记
        int idcardChangeFlag = -1;
        // 政治面貌标记
        int politicalTypeFlag = -1;

        // 基本信息为修改标记时，判断用户名是否变更
        if (Constants.EDIT_NO.equals(flag)) {
            // 根据用户ID查询用户信息
            UserEntity ue = this.userService.selectOne(userId);
            if (ue != null && !ue.getName().equals(name)) {
                nameChangeFlag = 1;//修改了用户名
            }
            if (ue != null && !ue.getCertNumber().equalsIgnoreCase(NumEncryptUtils.encrypt(ucn,
                    Constants.CERT_TYPE))) {
                idcardChangeFlag = 1;
            }
            // 政治面貌
            if (!politicalType.equals(Objects.requireNonNull(ue).getPoliticalType())) {
                politicalTypeFlag = 1;
            }
        }
        // 如果手机号码中不包含*号，则是修改后的号码
        if (Objects.nonNull(up) && !up.contains("*")) {
            String p = NumEncryptUtils.encrypt(up, 2);
            // 验证通过后设置加密后的手机号值
            userMap.put(FieldNameConfig.USER_PHONE, p);
            userMap.put(FieldNameConfig.USER_PHONE_SECRET, NumEncryptUtils.numSecret(up, 2));
            // 基本信息为修改标记时，号码没有星号为变更了
            if (Constants.EDIT_NO.equals(flag)) {
                // 标记手机号码的修改
                phoneChangeFlag = 1;
            }
        } else {
            userMap.remove(FieldNameConfig.USER_PHONE);
            userMap.remove(FieldNameConfig.USER_PHONE_SECRET);
        }
        // 如果身份证不包含*号，则为修改后的身份证号码
        int certType = Convert.toInt(userMap.get(FieldNameConfig.USER_CERT_TYPE));
        if (StringUtils.isNotEmpty(ucn) && !ucn.contains("*")) {
            // 验证通过后设置加密后的证件号值
            userMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(ucn, certType));
            userMap.put(FieldNameConfig.USER_CERT_NUMBER_SECRET, NumEncryptUtils.numSecret(ucn, certType));
        } else {
            userMap.remove(FieldNameConfig.USER_CERT_NUMBER);
            userMap.remove(FieldNameConfig.USER_CERT_NUMBER_SECRET);
        }

        // 校验是否存在其他党组织，有则提示先删除其他党组织信息
        boolean isExistOrg = this.orgUserAddService.isExistOther(userId, up, header.getRegionId(), header.getOrgType());
        if (isExistOrg) {
            log.debug("新增党员-该用户存在其他组织关系，请先转出name[{}],phone[{}],certNubmer[{}]", name, up, ucn);
            return new ResponseEntity<>(new Result<>(errors, 30007, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 校验发展党员的政治面貌是否是预备党员
        boolean isPreMember = this.orgUserAddService.prePartyMember(politicalType, Convert.toInt(userMap.get(FieldNameConfig.USER_JOIN_PARTY_TYPE)));
        if (isPreMember) {
            log.debug("新增党员-发展党员时政治面貌只能是预备党员name[{}],phone[{}],certNubmer[{}],politicalType[{}]",
                    name, up, ucn, politicalType);
            return new ResponseEntity<>(new Result<>(errors, 30008, HttpStatus.OK.value()), HttpStatus.OK);
        }
        // 新增党员
        OrgUserForm user = this.orgUserService.getOrgUserByMap(userMap);
        userId = this.orgUserService.addOrgUser(user, Constants.STATUS_YES, header);
        if (Objects.isNull(userId)) {
            log.debug("新增党员-系统异常，新增组织人员失败");
            return new ResponseEntity<>(new Result<>(errors, 134, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            // 调用后续操作
            Map<String, Object> dynamicField = ReflectUtil.getDynamicField(userMap, User.class);
            this.orgUserService.afterDealWithUser(userId, user.getOrgId(), dynamicField, 1, false, header);
            // 修改姓名回调
            if (nameChangeFlag == 1) {
                orgUserService.userChangeNameCallback(userId, name, header.getRegionId());
            }
            // 修改电话回调
            if (phoneChangeFlag == 1) {
                orgUserService.userChangePhoneCallback(userId, up, header.getRegionId());
            }
            // 修改身份证回调
            if (idcardChangeFlag == 1) {
                orgUserService.userChangeIdcardCallback(userId, ucn, header.getRegionId());
            }
            // 修改政治面貌回调
            if (politicalTypeFlag == 1) {
                orgUserService.userChangePoliticalTypeCallBack(userId, politicalType, header.getRegionId());
            }
        }
        //新增用户成功过后 同步数据到考试系统
        //examinationService.synUserInfo(Collections.singletonList(user.getOrgId()), 2, header);
        log.debug("新增党员-完成user[{}]", userMap);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);

    }

    /**
     * 修改用户信息
     *
     * @param userMap              userMap
     * @param ignoredBindingResult bindingResult
     */
    @OwsValidation(own = 1, type = 1)
    @HttpMonitorLogger
    @PostMapping("/orgUserUpdate")
    @ApiOperation("修改组织人员信息")
    @DoLog(type = LogType.UPDATE,content = "修改组织人员信息")
    public ResponseEntity<Result<?>> orgUserUpdate(@Valid @RequestBody Map<String, Object> userMap,
                                                   BindingResult ignoredBindingResult,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);

        //提交的证件号码值
        String ucn = Convert.toStr(userMap.get(FieldNameConfig.USER_CERT_NUMBER));
        //提交上来的用户电话号码值
        String up = Convert.toStr(userMap.get(FieldNameConfig.USER_PHONE));
        Long userId = Convert.toLong(userMap.get(FieldNameConfig.USER_ID));
        String name = Convert.toStr(userMap.get(FieldNameConfig.USER_NAME));
        Integer politicalType = Convert.toInt(userMap.get(FieldNameConfig.USER_POLITICAL_TYPE));
        String flag = Convert.toStr(userMap.get("flag"));

        //姓名更改标记
        int nameChangeFlag = -1;
        //电话更改标记
        int phoneChangeFlag = -1;
        // 身份证更改标记
//        int idcardChangeFlag = -1;
        int politicalTypeFlag = -1;

        // 根据用户ID查询用户信息
        UserEntity ue = this.userService.selectOne(userId);
        if (!ue.getName().equals(name)) {
            //修改了用户名
            nameChangeFlag = 1;
        }
        if (Objects.nonNull(politicalType) && Objects.nonNull(ue.getPoliticalType())) {
            if (!politicalType.equals(ue.getPoliticalType())) {
                politicalTypeFlag = 1;
            }
        } else {
            politicalTypeFlag = (politicalType == null ? (ue.getPoliticalType() == null ? -1 : 1) : 1);
        }
        // 如果电话不包含*（即更改了电话）
        if (Objects.nonNull(up) && !up.contains("*")) {
            String p = NumEncryptUtils.encrypt(up, 2);
            // 验证通过后设置加密后的手机号值
            userMap.put(FieldNameConfig.USER_PHONE, p);
            userMap.put(FieldNameConfig.USER_PHONE_SECRET, NumEncryptUtils.numSecret(up, 2));
            // 基本信息为修改标记时，号码没有星号为变更了
            if (Constants.EDIT_NO.equals(flag)) {
                // 标记手机号码的修改
                phoneChangeFlag = 1;
            }
        } else {
            userMap.remove(FieldNameConfig.USER_PHONE);
            userMap.remove(FieldNameConfig.USER_PHONE_SECRET);
        }
        // 如果身份证不包含*号，则为修改后的身份证号码
        Integer certType = Convert.toInt(userMap.get(FieldNameConfig.USER_CERT_TYPE));
        if (StringUtils.isNotEmpty(ucn) && !ucn.contains("*")) {
            // 验证通过后设置加密后的证件号值
            userMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(ucn, certType));
            userMap.put(FieldNameConfig.USER_CERT_NUMBER_SECRET, NumEncryptUtils.numSecret(ucn, certType));
        } else {
            userMap.remove(FieldNameConfig.USER_CERT_NUMBER);
            userMap.remove(FieldNameConfig.USER_CERT_NUMBER_SECRET);
        }
        // 编辑党员信息
        OrgUserForm form = this.orgUserService.getOrgUserByMap(userMap);
        if (!this.orgUserService.editOrgUser(form, header)) {
            log.debug("编辑党员-系统异常，修改组织人员信息失败form[{}]", form);
            return new ResponseEntity<>(new Result<>(errors, 135, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            // 调用后续操作
            Map<String, Object> dynamicField = ReflectUtil.getDynamicField(userMap, UserEntity.class);
            this.orgUserService.afterDealWithUser(userId, form.getOrgId(), dynamicField, 2, false, header);
            // 判断是否修改了姓名或电话
            if (nameChangeFlag == 1) {
                orgUserService.userChangeNameCallback(userId, name, header.getRegionId());
            }
            if (phoneChangeFlag == 1) {
                orgUserService.userChangePhoneCallback(userId, up, header.getRegionId());
            }
            // 修改身份证回调
//            if (idcardChangeFlag == 1) {
//                orgUserService.userChangeIdcardCallback(userId, ucn, header.getRegionId());
//            }
            // 修改政治面貌回调
            if (politicalTypeFlag == 1) {
                orgUserService.userChangePoliticalTypeCallBack(userId, politicalType, header.getRegionId());
            }
        }
        log.debug("编辑党员-完成form[{}]", userMap);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据人员编号查询人员信息-修改时回显基本信息
     *
     * @param userId userId
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserForm.class)
    @HttpMonitorLogger
    @GetMapping("/info")
    @ApiOperation("根据人员编号查询人员信息")
    public ResponseEntity<Result<?>> info(@RequestParam("user_id") @NotNull(message = "{NotBlank.user.id}") Long userId,
                                          @RequestParam("org_id") @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            UserForm form = orgUserService.orgUserInfo(orgId, userId, header.getRegionId());
            return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据人员编号查询人员信息失败, ", e);
            throw new ApiException("根据人员编号查询人员信息失败", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "根据人员编号查询人员信息失败"));
        }
    }


    /**
     * 查询组织人员列表
     *
     * @param headers headers
     * @param form    form
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserOrgResultForm.class)
    @HttpMonitorLogger
    @PostMapping("/orgUserList")
    @ApiOperation("查询组织人员列表")
    @DoLog(type = LogType.QUERY,content = "查询组织人员列表")
    public ResponseEntity<Result<Page<?>>> getOrgUserList(@Valid @RequestBody OrgUserQueryForm form,
                                                          BindingResult ignoredBindingResult,
                                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long queryUserId = header.getUserId();
        // edit by TangLilin 2018-12-13
        Long queryUserOid = Utils.getUoid(header);
        Integer appointAble = queryUserOid.equals(form.getOrgId()) ? 1 : 0;
        if (form.getPage() == null) {
            form.setPage(1);
        }
        if (form.getPagesize() == null) {
            form.setPagesize(10);
        }
        log.debug("参数：orgUserQueryForm = {}, page = {}, pagesize = {}", form.toString(), form.getPage(), form.getPagesize());

        Page<UserOrgResultForm> list;
        try {
            //替换证件号码和电话为密文
            if (form.getCertNumber() != null) {
                form.setCertNumber(NumEncryptUtils.encrypt(form.getCertNumber(), Constants.CERT_NUMBER_IDENTITY));
            }
            if (form.getPhone() != null) {
                form.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
            }
            list = orgUserService.getOrgUserList(form, new PageNumber(form.getPage(), form.getPagesize()), header.getRegionId());
            list.forEach(ouser -> {
                if (ouser.getUserId().equals(queryUserId)) {
                    ouser.setAppointAble(0);
                } else {
                    ouser.setAppointAble(appointAble);
                }
                // 设置用户职务
                if (Constants.ORG_TYPE_COMMUNIST.equals(ouser.getOrgType()) && ouser.getCommunist() != null) {
                    // 党组织
                    ouser.setPosition(this.optionService.getNameByKey(ouser.getCommunist(), Constants.COMMUNIST_CODE));
                } else if (Constants.ORG_TYPE_UNION_MEMBER == ouser.getOrgType() && ouser.getUnionMember() != null) {
                    // 工会组织
                    ouser.setPosition(this.optionService.getNameByKey(ouser.getUnionMember(), Constants.UNION_MEMBER_CODE));
                } else if (Constants.ORG_TYPE_YOUTH_LEAGUE == ouser.getOrgType() && ouser.getYouthLeague() != null) {
                    // 团组织
                    ouser.setPosition(this.optionService.getNameByKey(ouser.getYouthLeague(), Constants.YOUTH_LEAGUE_CODE));
                } else if (Constants.ORG_TYPE_WOMEN_LEAGUE == ouser.getOrgType() && ouser.getWomenLeague() != null) {
                    // 妇女组织
                    ouser.setPosition(this.optionService.getNameByKey(ouser.getWomenLeague(), Constants.WOMEN_LEAGUE_CODE));
                }
            });
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
            throw new ApiException("查询组织人员列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 删除组织人员
     */
    @HttpMonitorLogger
    @GetMapping("/del")
    @ApiOperation("删除组织人员")
    @DoLog(type = LogType.DELETE,content = "删除组织人员")
    public ResponseEntity<Result<?>> delete(@RequestHeader HttpHeaders headers,
                                            @RequestParam @NotNull(message = "{NotBlank.user.id}") Long userId,
                                            @RequestParam @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                            @RequestParam @NotNull(message = "{NotNull.leave.type}") Integer leaveType) {
        log.debug("删除党员-参数：userId = {}, orgId = {},leaveType = {}", userId, orgId, leaveType);
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            if (!orgUserService.delOrgUser(userId, orgId, header.getRegionId(), leaveType, header, 1)) {
                log.debug("删除党员-系统异常，删除组织人员信息失败！userId = " + userId + "  orgId = " + orgId);
                return new ResponseEntity<>(new Result<>(errors, 184, HttpStatus.OK.value()), HttpStatus.OK);
            }
        } catch (Exception e) {
            log.error("删除党员-异常{}", e.getMessage(), e);
            throw new ApiException("删除党员", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR,
                    HttpStatus.INTERNAL_SERVER_ERROR.value(), "删除组织人员出错"));
        }
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 下载所有查询结果
     *
     * @param form form
     */
    @HttpMonitorLogger
    @GetMapping("/downExcel")
    @ApiOperation("下载组织人员查询结果")
    public String downloadExcel(HttpServletResponse response, @Valid OrgUserQueryForm form, BindingResult ignoredBindingResult, @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("下载组织人员查询结果参数：orgUserQueryForm = {}", form.toString());
        //添加下载权限判断
        //获取下载用户编号
        Long userId = header.getUserId();
        Long orgId = header.getOid();
        //验证是否有下载权限
        int n = openService.checkDownloadAuth(userId, orgId, orgMenuId, header.getRegionId());
        if (n <= 0) {
            log.debug("<下载组织人员查询结果> 用户下载权限不足：userId = {} orgId = {}  ", userId, form.getOrgId());
            return null;
        }
        //替换证件号码和电话为密文
        if (form.getCertNumber() != null) {
            form.setCertNumber(NumEncryptUtils.encrypt(form.getCertNumber(), Constants.CERT_NUMBER_IDENTITY));
        }
        if (form.getPhone() != null) {
            form.setPhone(NumEncryptUtils.encrypt(form.getPhone(), 2));
        }
        List<UserOrgResultForm> orgUserList = orgUserService.getOrgUserListForExport(form, header.getRegionId());
        String cName = StringUtils.deleteWhitespace(orgUserService.getNameByOid(form.getOrgId()));
        Workbook wb = orgUserService.creatExcel(orgUserList, cName);
        try {
            if (wb != null) {
                // 设置响应头导出文件格式
//                String contentDisposition = "attachment;filename=" + new String(cName.getBytes("UTF-8"),"ISO-8859-1")+"." + ExcelUtils.XLSX_SUFFIX;
                String contentDisposition = "attachment;filename=" + URLEncoder.encode(cName, "UTF-8") + DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd_HH:mm:ss") + "." + ExcelUtils.XLS_SUFFIX;
                // 设置响应头的文件名称信息
                response.setHeader("Content-Disposition", contentDisposition);
                response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
                // 设置响应头导出文件格式vnd.ms-excel
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("UTF-8");
                // 使用响应的输出流导出excel文件
                wb.write(response.getOutputStream());
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != wb) {
                try {
                    wb.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 下载批量导入模板路径
     */
    @HttpMonitorLogger
    @GetMapping("/excelTemplate")
    @ApiOperation("下载批量导入模板路径")
    public void excelTemplate(@RequestParam("org_id") Long orgId,
                              @RequestParam("field_ids") String fieldIds,
                              @RequestHeader HttpHeaders headers,
                              HttpServletResponse response) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        String[] split = fieldIds.split(",");
        List<Long> fieldList = Arrays.stream(split).map(Convert::toLong).collect(Collectors.toList());
        this.importUserService.exportUserTemplate(orgId, fieldList, header, response);
    }

    @HttpMonitorLogger
    @PostMapping("/import")
    @ApiOperation("导入数据")
    @DoLog(type = LogType.INSERT,content = "批量导入党员")
    public ResponseEntity<Result<?>> importUser(@RequestParam("org_id") Long orgId,
                                                @RequestParam("file") MultipartFile file,
                                                @RequestHeader HttpHeaders headers) {
        return new ResponseEntity<>(new Result<>(this.importUserService.importUser(file, orgId, headers), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/import-rate")
    @ApiOperation("人员上传进度查询")
    public ResponseEntity<Result<?>> importRate(@RequestParam("uuid") String uuid) {
        return new ResponseEntity<>(new Result<>(this.importUserService.getImportSchedule(uuid), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/import-fail")
    @ApiOperation("导入数据")
    public void importFail(@RequestParam("uuid") String uuid,
                           HttpServletResponse response) {
        this.importUserService.ExportErrorList(uuid, response);
    }

    /**
     * 获取异步批量上传组织人员结果
     */
    @HttpMonitorLogger
    @GetMapping("/getOperKey")
    @ApiOperation("获取异步批量上传组织人员结果")
    public ResponseEntity<Result<?>> getOperKey(@RequestHeader HttpHeaders ignoredHeaders,
                                                @RequestParam(value = "result_key")
                                                @NotBlank(message = "{NotBlank.org.user.resultKey}") String resultKey) {
        return orgUserService.getOperKey(resultKey);
    }

    /**
     * 上传组织人员数据
     */
    @HttpMonitorLogger
    @GetMapping("/importInit")
    @ApiOperation("上传组织人员数据")
    public ResponseEntity<Result<?>> importInit(HttpServletRequest request, @RequestHeader HttpHeaders ignoredHeaders,
                                                @RequestParam @NotBlank(message = "{NotBlank.user.org.fileId}") String fileId) {
        String path = request.getSession().getServletContext().getRealPath("/") + "tempFile/";
        return orgUserService.uploadOrgUser(fileId, path, openService);
    }


    /**
     * 分页显示组织人员待导入信息
     */
    @HttpMonitorLogger
    @GetMapping("/importData")
    @ApiOperation("分页显示组织人员待导入信息")
    public ResponseEntity<Result<?>> importData(
            @RequestHeader HttpHeaders ignoredHeaders,
            @RequestParam @NotNull(message = "{NotBlank.user.org.operkey}") String operKey,
            @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
            @RequestParam(value = "pagesize", defaultValue = "10", required = false) Integer pageSize) {
        log.debug("参数：operKey = {}, page = {}, pagesize = {}", operKey, page, pageSize);

        return orgUserService.findImportData(operKey, page, pageSize);
    }

    /**
     * 批量导入组织人员，返回导入失败信息
     *
     * @param operKey operKey
     */
    @HttpMonitorLogger
    @GetMapping("/importOrgUser")
    @ApiOperation("开始导入组织人员")
    public ResponseEntity<Result<?>> importOrgUser(@RequestParam @NotNull(message = "{NotBlank.user.org.operkey}") String operKey,
                                                   @RequestParam @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                   @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                                   @RequestParam(value = "pagesize", defaultValue = "10", required = false) Integer pageSize,
                                                   @RequestHeader HttpHeaders headers) {
        log.debug("<批量导入组织人员> 参数：operKey = {},orgId = {}, page = {}, pagesize = {}", operKey, orgId, page, pageSize);
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgUserService.importOrgUser(operKey, orgId, header, this.openService, page, pageSize);
    }

    /**
     * 导入失败后单独提交人员信息
     *
     * @param name       name
     * @param phone      phone
     * @param certNumber certNumber
     * @param orgId      orgId
     * @param headers    headers
     */
    @HttpMonitorLogger
    @GetMapping("/submitData")
    @ApiOperation("导入失败后单独提交人员信息")
    public ResponseEntity<Result<?>> submitData(@RequestParam @NotNull(message = "{NotBlank.user.name}") String name,
                                                @RequestParam @NotBlank(message = "{NotBlank.user.phone}")
                                                @javax.validation.constraints.Pattern(regexp = "^1\\d{10}$", message = "{Pattern.user.phone}") String phone,
                                                @RequestParam @NotBlank(message = "{NotBlank.user.certNumber}") String certNumber,
                                                @RequestParam @NotNull(message = "{NotNull.user.org.id}") Long orgId,
                                                @RequestParam @NotNull(message = "{NotBlank.user.certType}") Integer certType,
                                                @RequestParam(required = false) Integer politicalType,
                                                @RequestParam(required = false) Integer jobGrade,
                                                @RequestParam(required = false) String position,
                                                @RequestParam(required = false) Integer communist,
                                                @RequestParam(required = false) Integer youthLeague,
                                                @RequestParam(required = false) Integer unionMember,
                                                @RequestParam(required = false) Integer womenLeague,
                                                @RequestParam(required = false) String joiningTime,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return orgUserService.submitData(header, orgId, name, phone, certType, certNumber,
                politicalType, jobGrade, position, communist, youthLeague, unionMember, womenLeague, joiningTime,
                openService);
    }

    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = OrgUserForm.class)
    @HttpMonitorLogger
    @PostMapping("/orgUserInfo")
    @ApiOperation("查询组织里人员信息")
    public ResponseEntity<Result<Page<OrgUserForm>>> orgUserInfo(@RequestHeader HttpHeaders headers,
                                                                 @RequestParam @NotBlank(message = "{NotNull.user.org.id}") String orgIds,
                                                                 @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                                                 @RequestParam(value = "pagesize", defaultValue = "10", required = false) Integer pageSize) {
        log.debug("参数：orgIds = {}, page = {}, pagesize = {}", orgIds, page, pageSize);
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Page<OrgUserForm> list;
        try {
            list = orgUserService.selectOrgUserInfo(orgIds, new PageNumber(page, pageSize), header.getRegionId());
        } catch (Exception e) {
            log.error("查询组织人员信息出错!", e);
            throw new ApiException("查询组织人员信息出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询组织人员信息出错"));
        }
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/nativeArea")
    @ApiOperation("根据父级编号查询籍贯的区域")
    public ResponseEntity<Result<?>> nativeArea(@RequestHeader HttpHeaders ignoredHeaders, @RequestParam(required = false) Integer parentid) {
        log.debug("参数：parentid = {} ", parentid);

        List<DictAreaForm> list;
        try {
            list = dictAreaService.selectNativeArea(parentid);
        } catch (Exception e) {
            log.error("根据父级编号查询籍贯的区域出错!", e);
            throw new ApiException("根据父级编号查询籍贯的区域出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "根据父级编号查询籍贯的区域"));
        }
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 查询所有用户-领导班子
     *
     * @param page    page
     * @param orgType orgType
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = OrgUserInfoResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-all-user")
    @ApiOperation("查询所有用户-领导班子")
    public ResponseEntity<Result<?>> findAllUser(@RequestParam(required = false, value = "param") String param,
                                                 @RequestParam(required = false, defaultValue = "1") Integer page,
                                                 @RequestParam(required = false, value = "org_type") Integer orgType,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (page == null) {
            page = 1;
        }
        // 默认为姓名
        int type = 1;
        if (param.length() == 11 && Utils.checkPhone(param)) {
            // 手机号码
            param = NumEncryptUtils.encrypt(param, 2);
            type = 2;
        }
        Page<OrgUserInfoResultForm> result = this.orgUserService.findAllUser(new PageNumber(page), param,
                type, orgType, header.getRegionId());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询用户
     *
     * @param form 查询条件
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-by-list")
    @ApiOperation("根据条件查询用户")
    public ResponseEntity<Result<?>> findUserByIdList(@Valid @RequestBody UserIdForm form,
                                                      BindingResult ignoredBindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据条件查询用户findUserByIdList 参数[{}]，header[{}]", form, header);
        Map<String, Object> resultMap = this.orgUserService.findUserByIdList(form, header);
        return new ResponseEntity<>(new Result<>(resultMap.get("result"), errors), HttpStatus.OK);
    }

    /**
     * 根据组织查询管理员
     *
     * @param form 查询条件
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-org-mgr-by-list")
    @ApiOperation("根据条件查询用户")
    public ResponseEntity<Result<?>> findOrgMgrByList(@Valid @RequestBody OrgMgrForm form,
                                                      BindingResult ignoredBindingResult,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据组织查询管理员findOrgMgrByList 参数：[{}]", form);
        if (CollectionUtils.isEmpty(form.getIdList())) {
            return new ResponseEntity<>(new Result<>(errors, 601, HttpStatus.OK.value()), HttpStatus.OK);
        }
        Map<String, Object> resultMap = this.orgUserService.findOrgMgrByList(form, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultMap.get("result"), errors), HttpStatus.OK);
    }

    /**
     * 成员管理根据条件查询成员列表
     *
     * @param queryMap queryMap
     * @param headers  headers
     */
    @HttpMonitorLogger
    @PostMapping("/getUserList")
    @ApiOperation("根据条件查询成员列表")
    public ResponseEntity<Result<?>> getUserList(@RequestBody Map<String, Object> queryMap,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("参数：queryMap = {}", queryMap);

        Page<Map<String, Object>> list = new Page<>();
        try {
            String certNumber = Convert.toStr(queryMap.get(FieldNameConfig.USER_CERT_NUMBER));
            String phone = Convert.toStr(queryMap.get(FieldNameConfig.USER_PHONE));
            //替换证件号码和电话为密文
            if (!StringUtils.isEmpty(certNumber)) {
                queryMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY));
            }
            if (!StringUtils.isEmpty(phone)) {
                queryMap.put(FieldNameConfig.USER_PHONE, NumEncryptUtils.encrypt(phone, Constants.PHONE_IDENTITY));
            }
            int page = queryMap.get(FieldNameConfig.PAGE) == null ? 1 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE));
            int pageSize = queryMap.get(FieldNameConfig.PAGE_SIZE) == null ? 10 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE_SIZE));
            queryMap.remove(FieldNameConfig.PAGE);
            queryMap.remove(FieldNameConfig.PAGE_SIZE);
            list = orgUserService.getUserList(queryMap, new PageNumber(page, pageSize), header);
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
        }
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @RequestMapping(value = "/exportOrgUser", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation("导出人员查询列表")
    public ResponseEntity<Result<?>> exportUserOrgExcel(@RequestBody Map<String, Object> queryMap,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long userId = header.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_USER + userId;
        if (Boolean.TRUE.equals(this.redisTemplate.hasKey(redisKey))) {
            throw new ApiException("请不要重复提交下载", new Result<>(errors, Global.Errors.REPEAT_DATA_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "请不要重复提交下载"));
        } else {
            this.redisTemplate.opsForValue().set(redisKey, Convert.toStr(userId), 5L, TimeUnit.MINUTES);
        }
        try {
            log.debug("参数：orgUserQueryForm = {}", queryMap.toString());
            //替换证件号码和电话为密文
            String certNumber = Convert.toStr(queryMap.get(FieldNameConfig.USER_CERT_NUMBER));
            String phone = Convert.toStr(queryMap.get(FieldNameConfig.USER_PHONE));
            //替换证件号码和电话为密文
            if (!StringUtils.isEmpty(certNumber)) {
                queryMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY));
            }
            if (!StringUtils.isEmpty(phone)) {
                queryMap.put(FieldNameConfig.USER_PHONE, NumEncryptUtils.encrypt(phone, Constants.PHONE_IDENTITY));
            }

            Long orgId = Convert.toLong(queryMap.get(UserFieldEnum.ORG_ID.getKey()));
            String cName = StringUtils.deleteWhitespace(this.orgUserService.getNameByOid(orgId));

            String uuid = UUID.randomUUID().toString();

            queryMap.put(UserFieldEnum.ORG_REGION_ID.getKey(), header.getRegionId());
            queryMap.put(UserFieldEnum.ORG_TYPE.getKey(), header.getOrgType());

            this.orgUserService.exportUserList(queryMap, cName, uuid, redisKey, headers);

            return new ResponseEntity<>(new Result<>(uuid, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
            throw new ApiException("查询组织人员列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
    }

    @HttpMonitorLogger
    @RequestMapping(value = "/exportOrgUser", method = RequestMethod.GET)
    @ApiOperation("导出人员查询列表")
    public ResponseEntity<Result<?>> exportUserOrgExcel(@RequestParam(value = "uuid") String uuid,
                                                        @RequestHeader HttpHeaders headers,
                                                        HttpServletResponse response) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Long userId = header.getUserId();
        String redisKey = REDIS_REPEAT_DOWN_USER + userId;
        try {
            Object rate = this.orgUserService.downloadUserList(response, uuid, redisKey);
            if (Objects.nonNull(rate)) {
                return new ResponseEntity<>(new Result<>(rate, errors), HttpStatus.OK);
            }
            log.debug("下载人员列表 uuid -> {}, userId -> {}", uuid, header.getUserId());
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
            throw new ApiException("查询组织人员列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
        return new ResponseEntity<>(new Result<>(0, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/tag/insert")
    @ApiOperation("成员管理批量新增人员标签")
    public ResponseEntity<Result<?>> insertUserTag(@Valid @RequestBody UserTagForm form,
                                                   BindingResult ignoredBindingResult,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int i = this.userTagService.updateUserTag(form, 1, header);
        if (i == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, i, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @PostMapping("/tag/delete")
    @ApiOperation("成员管理批量删除人员标签")
    public ResponseEntity<Result<?>> deleteUserTag(@Valid @RequestBody UserTagForm form,
                                                   BindingResult ignoredBindingResult,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int i = this.userTagService.updateUserTag(form, 0, header);
        if (i == 1) {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>(errors, i, HttpStatus.OK.value()), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @PostMapping("/tag/getUserTagList")
    @ApiOperation("查询人员标签列表")
    public ResponseEntity<Result<?>> getUserTagList(@Valid @RequestBody UserTagQuestForm form,
                                                    BindingResult ignoredBindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserOrgTagVO> userTagList = this.userTagService.getUserTagList(form, header.getRegionId());
        return new ResponseEntity<>(new Result<>(userTagList, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/get-user-by-condition")
    @ApiOperation("人员选择器查询人员列表")
    public ResponseEntity<Result<?>> getUserByCondition(@Valid @RequestBody ChooseUserQueryForm form,
                                                        BindingResult ignoredBindingResult,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("参数：ChooseUserQueryForm = {}, page = {}, pagesize = {}", form.toString(), form.getPage(), form.getPagesize());
        Page<UserOrgResultForm> list = this.orgUserService.getUserByCondition(form, new PageNumber(form.getPage(), form.getPagesize()), header.getRegionId(), null);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/get-user-by-condition-v2")
    @ApiOperation("人员选择器查询人员列表")
    public ResponseEntity<Result<?>> getUserByCondition(@Valid @RequestBody Map<String, Object> form,
                                                        BindingResult ignoredBindingResult,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("参数：ChooseUserQueryForm = {}, page = {}, pagesize = {}", form.toString(), form.get(FieldNameConfig.PAGE), form.get(FieldNameConfig.PAGE_SIZE));
        Page<Map<String, Object>> list = this.orgUserService.getUserByCondition(form, header);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/get-user-by-condition-v3")
    @ApiOperation("人员选择器查询人员列表-组织生活-查询所有党员")
    public ResponseEntity<Result<?>> getUserByConditionV3(@Valid @RequestBody ChooseUserQueryForm form,
                                                          BindingResult ignoredBindingResult,
                                                          @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("参数[getUserByConditionV3]：ChooseUserQueryForm = {}, page = {}, pagesize = {}", form.toString(), form.getPage(), form.getPagesize());
        Page<UserOrgResultForm> list = this.orgUserService.getUserByCondition(form,
                new PageNumber(form.getPage(), form.getPagesize()), header.getRegionId(), "v3");
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 通过Excel导入获取用户信息
     *
     * @param form form
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-by-import")
    @ApiOperation("通过Excel导入获取用户信息")
    public ResponseEntity<Result<?>> findUserByImport(@Valid @RequestBody BatchUserInfoForm form,
                                                      @RequestHeader HttpHeaders headers,
                                                      BindingResult ignoredBindingResult) {
        if (CollectionUtils.isEmpty(form.getDataList())) {
            log.debug("通过Excel导入用户信息为空:{}", form);
            return new ResponseEntity<>(new Result<>(errors, 5600, HttpStatus.OK.value()), HttpStatus.OK);
        }
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<String, Object> resultMap = this.orgUserService.findUserByImport(form, header.getOrgType(), header.getRegionId());
        int code = (int) resultMap.get("code");
        if (code == 1) {
            return new ResponseEntity<>(new Result<>(resultMap.get("form"), errors), HttpStatus.OK);
        } else {
            log.debug("通过Excel导入获取用户信息 错误码：[{}]", code);
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value(), (String) resultMap.get("name"),
                    (String) resultMap.get("phone"), (String) resultMap.get("cert")), HttpStatus.OK);
        }
    }

    /**
     * 批量获取用户信息-消息发送功能使用
     *
     * @param includeLevel  是否包含下级 1:包含 2：不包含 3：仅包含 默认1
     * @param includeRetire 是否包含离退休组织 1:包含 2：不包含 3：仅包含 默认1
     * @param includeAll    是否包含所有人 1-是 2-否，默认2
     * @param filterStatus  是否包含删除状态信息 1-是 2-否，默认1
     * @return List<UserPartyResultForm>
     */
    @HttpMonitorLogger
    @GetMapping("/find-party-user-by-where")
    @ApiOperation("批量获取用户信息-消息发送功能使用")
    public ResponseEntity<Result<?>> findPartyUserByWhere(@RequestParam(value = "org_id") @NotNull(message = "{NotNull.org.id}") Long orgId,
                                                          @RequestParam(value = "include_retire", required = false, defaultValue = "1") Integer includeRetire,
                                                          @RequestParam(value = "include_level", required = false, defaultValue = "1") Integer includeLevel,
                                                          @RequestParam(value = "include_all", required = false, defaultValue = "2") Integer includeAll,
                                                          @RequestParam(value = "filter_status", required = false, defaultValue = "1") Integer filterStatus,
                                                          @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                          @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
                                                          @RequestHeader HttpHeaders headers,
                                                          HttpServletRequest request) {
        log.debug("请求来源 -> [{}]", Utils.getRemoteHost(request));
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        UserPartyQueryForm form = new UserPartyQueryForm();
        form.setOrgId(orgId);
        form.setIncludeAll(includeAll);
        form.setIncludeLevel(includeLevel);
        form.setIncludeRetire(includeRetire);
        form.setFilterStatus(filterStatus);
        List<UserPartyResultForm> resultList = this.orgUserService.findPartyUserByWhere(form, page, pageSize,
                header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 获取用户第三方用户信息
     *
     * @param userIds  党建系统用户ID集合
     * @param unionIds 钉钉用户在企业唯一标识集合(对应党建系统openId)
     * @param ids      钉钉用户ID集合(对应党建系统第三方ID)
     * @param regionId 区域ID
     * @param headers  headers
     */
    @HttpMonitorLogger
    @GetMapping("/get-user-third")
    @ApiOperation("获取用户第三方信息ID")
    public ResponseEntity<Result<?>> getUserThirdInfo(@RequestParam(value = "user_id", required = false) List<Long> userIds,
                                                      @RequestParam(value = "union_id", required = false) List<String> unionIds,
                                                      @RequestParam(value = "id", required = false) List<String> ids,
                                                      @RequestParam(value = "region_id", required = false) Long regionId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        regionId = Objects.isNull(regionId) ? header.getRegionId() : regionId;
        return new ResponseEntity<>(new Result<>(this.userService.getUserThirdInfo(userIds, unionIds, ids, regionId), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/find-user-by-ids")
    @ApiOperation("根据用户id列表查询信息")
    public ResponseEntity<Result<?>> findUserByIds(@Valid @RequestBody ChildOrgQueryForm form,
                                                   @RequestHeader HttpHeaders headers,
                                                   BindingResult ignoredBindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (CollectionUtils.isEmpty(form.getIdList())) {
            return new ResponseEntity<>(new Result<>(errors, 198, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(this.organizationService.findUserByIds(form.getIdList(), header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-user-by-phone")
    @ApiOperation("根据电话号码查询用户信息")
    public ResponseEntity<Result<?>> findUserByPhone(@RequestParam(value = "phone") String phone,
                                                     @RequestParam(value = "date_month") Integer dateMonth,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.organizationService.findUserByPhone(phone, dateMonth, header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/find-user-by-batch")
    @ApiOperation("根据电话和姓名查询用户")
    public ResponseEntity<Result<?>> findUserByBatch(@Valid @RequestBody List<BatchForm> list,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (CollectionUtils.isEmpty(list)) {
            return new ResponseEntity<>(new Result<>(errors, 33101, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(this.orgUserAddService.findUserByBatch(list, header), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/find-org-by-precise")
    @ApiOperation("根据组织名称精确查询组织")
    public ResponseEntity<Result<?>> findOrgByPrecise(@Valid @RequestBody List<String> list,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (CollectionUtils.isEmpty(list)) {
            return new ResponseEntity<>(new Result<>(errors, 33101, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(this.orgUserAddService.findOrgByPrecise(list, header), errors), HttpStatus.OK);
    }

    /**
     * 通过Excel导入获取用户信息
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-by-new-import")
    @ApiOperation("通过Excel导入获取用户信息")
    public ResponseEntity<Result<?>> findUserByNewImport(@Valid @RequestBody List<UserInfoBase> dataList,
                                                         @RequestHeader HttpHeaders headers,
                                                         BindingResult ignoredBindingResult) {
        if (CollectionUtils.isEmpty(dataList)) {
            log.debug("通过Excel导入用户信息为空:{}", dataList);
            return new ResponseEntity<>(new Result<>(errors, 5600, HttpStatus.OK.value()), HttpStatus.OK);
        }
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgUserService.findUserByNewImport(dataList, header.getOrgType(), header.getRegionId()), errors), HttpStatus.OK);
    }


    /**
     * 根据人员查询人员组织等信息-积分中心调用
     */
    @HttpMonitorLogger
    @GetMapping("/find-user-org_info")
    @ApiOperation("查询所有党员")
    public ResponseEntity<Result<?>> findUserOrgInfo(@RequestParam("page") Integer page,
                                                     @RequestParam("page_size") Integer pageSize,
                                                     @RequestHeader HttpHeaders headers) {
        log.debug("mid-score开始");
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("mid-score用户中心" + header);
        return new ResponseEntity<>(new Result<>(this.orgUserService.findUserOrgInfo(header.getRegionId(), page, pageSize), errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询支部党员明文数据
     */
    @HttpMonitorLogger
    @GetMapping("/find-user-plaintext")
    @ApiOperation("查询所有党员")
    public ResponseEntity<Result<?>> findUserPlaintext(@RequestParam("orgId") Long orgId,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(this.orgUserService.findUserPlaintext(header, orgId), errors), HttpStatus.OK);
    }
    /**
     * 查询党籍管理列表
     */
    @HttpMonitorLogger
    @GetMapping("/membership")
    @ApiOperation("查询党籍管理列表")
    public ResponseEntity<Result<List<MembershipEntity>>> findMembership(@RequestHeader HttpHeaders headers,
                                                                         @RequestParam(value = "name",required = false) String name,
                                                                         @RequestParam(value = "political_type",required = false) Integer politicalType,
                                                                         @RequestParam(value = "leave_type",required = false) Integer leaveType,
                                                                         @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize){
        return new ResponseEntity<>(new Result<>(this.orgUserService.findMembership(headers,name,politicalType,leaveType,page,pageSize), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/membership/details")
    @ApiOperation("查询党籍管理详情")
    public ResponseEntity<Result<UserForm>> findMembershipDetails(@RequestHeader HttpHeaders headers,
                                                          @RequestParam(value = "user_id",required = true) Long userId){
        return new ResponseEntity<>(new Result<>(this.orgUserService.findMembershipDetails(headers,userId), errors), HttpStatus.OK);
    }
}

