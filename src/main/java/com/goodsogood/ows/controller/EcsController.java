package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.EcsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2021-08-11 9:04
 * @description
 */
@RestController
@RequestMapping("/ecs")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "移动密评", tags = {"移动密评"})
@Validated
public class EcsController {

    private final Errors errors;
    private final EcsService ecsService;

    @Autowired
    public EcsController(Errors errors, EcsService ecsService) {
        this.errors = errors;
        this.ecsService = ecsService;
    }

    @HttpMonitorLogger
    @GetMapping("/update-hmac")
    @ApiOperation("更新HMAC值")
    public ResponseEntity<Result<?>> updateHmac(@RequestParam(name = "userId",required = false) Long userId,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.ecsService.updateHmac(userId, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/update-user-role-hmac")
    @ApiOperation("更新HMAC值")
    public ResponseEntity<Result<?>> updateHmac(@RequestParam(name = "userId",required = false) Long userId) {
        this.ecsService.userRoleHmac(userId);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/update-user-info")
    @ApiOperation("更新用户密码-电话-身份证")
    public ResponseEntity<Result<?>> updateUserInfo(@RequestParam(name = "userId", required = false) Long userId,
                                                    @RequestParam(name = "type", required = false) Integer type,
                                                    @RequestParam(name = "max", required = false) Integer max,
                                                    @RequestParam(name = "min", required = false) Integer min) {
        this.ecsService.updateUserInfo(userId, type, max, min);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

}
