package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.DoLog;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.LogType;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.vo.MenuForm;
import com.goodsogood.ows.model.vo.MenuInfoForm;
import com.goodsogood.ows.model.vo.MenuUrlAddForm;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.MenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 菜单controller
 *
 * <AUTHOR>
 * @date 2018-04-08
 */
@RestController
@RequestMapping("/uc/menu")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "菜单管理", tags = {"menu"})
@Validated
public class MenuController {

    private final Errors errors;
    private final MenuService menuService;

    @Autowired
    public MenuController(Errors errors, MenuService menuService) {
        this.errors = errors;
        this.menuService = menuService;
    }

    /**
     * 查询所有菜单
     *
     * @return 菜单
     */
    @HttpMonitorLogger
    @GetMapping("/all")
    @ApiOperation("查询所有菜单")
    @DoLog(type = LogType.QUERY,content = "查询所有菜单")
    public ResponseEntity<Result<List<MenuForm>>> getAll(@RequestParam(name = "belong", required = false) Integer belong,
                                                         @RequestParam(name = "region_id", required = false) Long regionId,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        List<MenuForm> formList = this.menuService.getAll(Constants.STATUS_YES, belong, regionId);
        return new ResponseEntity<>(new Result<>(formList, errors), HttpStatus.OK);
    }

    /**
     * 为菜单添加uri
     *
     * @param menuUrlAddForm 请求对象
     *      * @param bindingResult  bindingResult
     * @return Result
     */
    @HttpMonitorLogger
    @PostMapping("/uri/add")
    @ApiOperation("为菜单添加uri")
    public ResponseEntity<Result<?>> addMenuUrl(@RequestBody @Valid MenuUrlAddForm menuUrlAddForm, BindingResult bindingResult) {
        if (this.menuService.findOne(menuUrlAddForm.getMenuId()) == null) {
            throw new ApiException("not found", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "菜单[" + menuUrlAddForm.getMenuId() + "]"));
        }
        this.menuService.addMenuUrl(menuUrlAddForm.getMenuId(), menuUrlAddForm.getMenuUrls());
        return ResponseEntity.ok(new Result<>(null, errors));
    }

    @HttpMonitorLogger
    @DeleteMapping("/uri/remove/{menuId}/{menuUrlId}")
    @ApiOperation("为菜单添加uri")
    public ResponseEntity<Result<?>> removeMenuUrl(@PathVariable String menuId, @PathVariable Long menuUrlId) {
        this.menuService.removeMenUrl(menuId, menuUrlId);
        return ResponseEntity.ok(new Result<>(null, errors));
    }

    @HttpMonitorLogger
    @PostMapping("/insert")
    @ApiOperation("添加菜单")
    public ResponseEntity<Result<?>> insert(@RequestBody @Valid MenuInfoForm menuInfoForm,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.menuService.insert(menuInfoForm, header);
        return ResponseEntity.ok(new Result<>("success", errors));
    }

    @HttpMonitorLogger
    @PostMapping("/update")
    @ApiOperation("更新菜单")
    public ResponseEntity<Result<?>> update(@RequestBody @Valid MenuInfoForm menuInfoForm,
                                            @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.menuService.update(menuInfoForm, header);
        return ResponseEntity.ok(new Result<>("success", errors));
    }

    @HttpMonitorLogger
    @GetMapping("/query")
    @ApiOperation("查询菜单")
    public ResponseEntity<Result<?>> query(@RequestParam(name = "menu_id") String menuId,
                                           @RequestParam(name = "region_id", required = false) Long regionId,
                                           @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        return ResponseEntity.ok(new Result<>(this.menuService.getMenuInfo(menuId, regionId), errors));
    }

    @HttpMonitorLogger
    @GetMapping("/del")
    @ApiOperation("查询菜单")
    public ResponseEntity<Result<?>> del(@RequestParam(name = "menu_id") String menuId,
                                         @RequestParam(name = "region_id", required = false) Long regionId,
                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        this.menuService.del(menuId, regionId, header);
        return ResponseEntity.ok(new Result<>("success", errors));
    }

    @HttpMonitorLogger
    @GetMapping("/query-list")
    @ApiOperation("查询菜单")
    public ResponseEntity<Result<?>> queryList(@RequestParam(name = "parent_id", required = false, defaultValue = "0") String parentId,
                                               @RequestParam(name = "region_id", required = false) Long regionId,
                                               @RequestParam(name = "belong") Integer belong,
                                               @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        if (Objects.isNull(regionId)) {
            regionId = header.getRegionId();
        }
        return ResponseEntity.ok(new Result<>(this.menuService.getMenuList(parentId, regionId, belong), errors));
    }

    @HttpMonitorLogger
    @GetMapping("/get-user-menu-list")
    @ApiOperation("查询人员所有菜单列表")
    public ResponseEntity<Result<?>> queryList(@RequestParam(name = "parent_id", required = false, defaultValue = "0") String parentId,
                                               @RequestParam(name = "user_id") Long userId,
                                               @RequestParam(name = "org_id") Long orgId,
                                               @RequestHeader HttpHeaders headers) {
        return ResponseEntity.ok(new Result<>(this.menuService.getUserMenuList(parentId,userId, orgId), errors));
    }
}
