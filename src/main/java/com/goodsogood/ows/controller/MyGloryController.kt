package com.goodsogood.ows.controller

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.DelForm
import com.goodsogood.ows.model.vo.MyGloryForm
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.service.MyGloryService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime
import java.util.UUID
import javax.validation.Valid
import javax.validation.constraints.NotNull

@RestController
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "你是我的荣耀", tags = ["你是我的荣耀"])
@RequestMapping("/my-glory")
@Validated
class MyGloryController(@Autowired val errors: Errors,
                        @Autowired val myGloryService: MyGloryService) {

    @PostMapping("/add")
    @ApiOperation("新增我的荣耀")
    fun add(@Valid @RequestBody form: MyGloryForm,
            bindingResult: BindingResult,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(myGloryService.add(form, headers) , errors), HttpStatus.OK)
    }

    @GetMapping("/del/{glory_id}")
    @ApiOperation("删除我的荣耀")
    fun del(@PathVariable("glory_id") @NotNull(message = "主键不能为空") gloryId: Long,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(myGloryService.del(gloryId, headers) , errors), HttpStatus.OK)
    }

    @PostMapping("/del-by-example")
    @ApiOperation("删除我的荣耀")
    fun delByExample(@Valid @RequestBody form: DelForm,
                     bindingResult: BindingResult,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(myGloryService.delByExample(form, headers) , errors), HttpStatus.OK)
    }

    @GetMapping("/list")
    @ApiOperation("查询我的荣耀")
    fun list(@RequestParam("user_id") @NotNull(message = "用户ID不能为空") userId: Long,
             @RequestParam("page", required = false) page: Int = 1,
             @RequestParam("page_size", required = false) pageSize: Int = 5,
             @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(myGloryService.list(userId, page, pageSize, headers) , errors), HttpStatus.OK)
    }

    @PostMapping("/export")
    @ApiOperation("构建导出我的荣耀")
    fun export(@RequestParam("user_id") @NotNull(message = "用户ID不能为空") userId: Long,
               @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        val uuid = UUID.randomUUID().toString()
        myGloryService.exportMyGlory(userId, uuid, headers)
        return ResponseEntity(Result(uuid , errors), HttpStatus.OK)
    }

    @GetMapping("/export")
    @ApiOperation("返回我的荣耀文件地址")
    fun export(@RequestParam("uuid") @NotNull(message = "UUID不能为空") uuid: String,
               @RequestHeader headers: HttpHeaders): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(myGloryService.getMyGloryFile(uuid) , errors), HttpStatus.OK)
    }
}