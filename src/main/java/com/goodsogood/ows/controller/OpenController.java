package com.goodsogood.ows.controller;

import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.annotation.AddDynamicFields;
import com.goodsogood.ows.annotation.DynamicDesc;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.JsonUtils;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.FieldType;
import com.goodsogood.ows.component.PartPushScheduler;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.model.db.CorporationEntity;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.TagEntity;
import com.goodsogood.ows.model.vo.*;
import com.goodsogood.ows.model.vo.activity.OrganizationBase;
import com.goodsogood.ows.model.vo.activity.UserInfoBase;
import com.goodsogood.ows.model.vo.user.DepartmentBase;
import com.goodsogood.ows.model.vo.user.TokenBase;
import com.goodsogood.ows.model.vo.user.UserChangeForm;
import com.goodsogood.ows.service.OfficialAccountService;
import com.goodsogood.ows.service.OpenService;
import com.goodsogood.ows.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.IOException;
import java.util.*;

/**
 * 对外接口controller
 *
 * <AUTHOR>
 * @date 2018-04-27
 */
@RestController
@RequestMapping("")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "对外接口", tags = {"对外接口"})
@Validated
public class OpenController {

    private final Errors errors;
    private final OpenService openService;
    private final StringRedisTemplate redisTemplate;
    private final OfficialAccountService officialAccountService;
    protected final PartPushScheduler partPushScheduler;
    private final UserService userService;

    @Autowired
    public OpenController(Errors errors, OpenService openService,
                          StringRedisTemplate redisTemplate,
                          OfficialAccountService officialAccountService,
                          PartPushScheduler partPushScheduler,
                          UserService userService) {
        this.errors = errors;
        this.openService = openService;
        this.redisTemplate = redisTemplate;
        this.officialAccountService = officialAccountService;
        this.partPushScheduler = partPushScheduler;
        this.userService = userService;
    }

    /**
     * 单位、组织列表
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/corp-org/list")
    @ApiOperation("单位或组织下拉列表")
    public ResponseEntity<Result<Page<?>>> list(@RequestParam @NotNull(message = "{NotNull.tag.type}") int type,
                                                @RequestParam(required = false) Integer page,
                                                @RequestParam(required = false) Long oId,
                                                @RequestParam(required = false) Integer pageSize) {
        if (page == null) {
            page = 1;
        }
        Page<OrgCorpForm> listPage = this.openService.findList(new PageNumber(page, pageSize), type, oId);
        return new ResponseEntity<>(new Result<>(listPage, errors), HttpStatus.OK);
    }

    /**
     * 更新部门状态
     *
     * @param depId
     * @param status
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/dep-status/{depId}/{status}")
    @ApiOperation("更新部门状态")
    public ResponseEntity<Result<?>> depStatus(@PathVariable Long depId, @PathVariable int status) {

        this.openService.updateStatus(depId, status);
        log.debug("更新部门状态成功 -> depId:{}", depId);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据部门ID查询所有上级部门ID
     *
     * @param depId
     * @param type
     * @param oId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/dep/id-list/{depId}/{type}/{oId}")
    @ApiOperation("根据部门ID查询所有上级部门ID")
    public ResponseEntity<Result<?>> depIdList(@PathVariable Long depId,
                                               @PathVariable int type,
                                               @PathVariable Long oId) {
        List<Long> list = this.openService.getDepList(depId, type, oId);
        Collections.reverse(list);
        return new ResponseEntity<>(new Result<>(list, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询所在部门列表
     *
     * @param userId
     * @param type
     * @param oId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/dep/exist-list/{userId}/{type}/{oId}")
    @ApiOperation("根据用户ID查询所在部门列表")
    public ResponseEntity<Result<?>> getExistDepList(@PathVariable
                                                     @NotNull(message = "{NotNull.user.id}") Long userId,
                                                     @PathVariable
                                                     @NotNull(message = "{NotNull.login.type}") int type,
                                                     @PathVariable
                                                     @NotNull(message = "{NotNull.login.oId}") Long oId) {
        List<DepartmentBase> formList = this.openService.getExistDepList(userId, oId, type);
        return new ResponseEntity<>(new Result<>(formList, errors), HttpStatus.OK);
    }

    /**
     * 根据ID查询所有下级组织ID列表（包括查询ID）
     *
     * @param id
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/org/child-list/{id}")
    @ApiOperation("根据ID查询所有下级组织ID列表（包括查询ID）")
    public ResponseEntity<Result<?>> childList(@PathVariable @NotNull(message = "{NotNull.org.id}") Long id,
                                               @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> resultList = this.openService.childList(id, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /*@HttpMonitorLogger
    @PostMapping("/import")
    @ApiOperation("导入数据")
    public ResponseEntity<Result<?>> importUser() throws Exception {

        this.openService.importUser();
        return new ResponseEntity<>(new Result<>("success",errors),HttpStatus.OK);
    }*/

    /**
     * 查询参加人员限制条件
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/uc/org/get-user-limit")
    @ApiOperation("查询参加人员限制条件")
    public ResponseEntity<Result<?>> getUserLimit(@RequestParam(required = false) Long oid,
                                                  @RequestParam @NotNull(message = "{NotNull.limit.userId}") Long userId,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserInfoBase> resultList = this.openService.getUserLimit(userId, oid, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询用户信息-内部接口（辅助决策sas模块有相同方法 2020-11-20）
     *
     * @param user_id
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/uc/org/user-base-info")
    @ApiOperation("根据用户ID查询基本信息")
    public ResponseEntity<Result<?>> userBaseInfo(@RequestParam(required = false) Long user_id,
                                                  @RequestParam(required = false) Long oid,
                                                  @RequestParam(value = "open_id", required = false) String openId,
                                                  @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            UserInfoBase result = this.openService.userBaseInfo(user_id, oid, openId, header.getRegionId());
            log.debug("根据用户ID查询基本信息成功 userId：{},openID： {}", user_id, openId);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据用户ID查询基本信息失败:" + e.getMessage(), e);
            throw new ApiException("根据用户ID查询基本信息失败", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "根据用户ID查询基本信息失败"));
        }
    }

    /**
     * 根据用户ID查询用户信息-内部接口（辅助决策sas模块有相同方法 2020-11-20）
     *
     * @param userId
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/uc/org/user-base-info-v2")
    @ApiOperation("根据用户ID查询基本信息")
    public ResponseEntity<Result<?>> userBaseInfoV2(@RequestParam(name = "user_id", required = false) Long userId,
                                                    @RequestParam(required = false) Long oid,
                                                    @RequestParam(value = "open_id", required = false) String openId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        // 判断token与请求头里的userId是否一直
        final String token = header.getToken();
        userId = header.getUserId();
        String redisKey = Constants.LOGIN_TOKEN + token;
        final TokenBase tokenBase = JsonUtils.fromJson(redisTemplate.opsForValue().get(redisKey), TokenBase.class);
        if (tokenBase != null) {
            if (!tokenBase.getUserId().equals(userId)) {
                log.error("请求头Token包含UserId[{}], 请求头里UserId[{}]", tokenBase.getUserId(), userId);
                throw new ApiException("请求头_uid和_tk不对应", new Result<>(errors, 220, HttpStatus.NOT_FOUND.value()));
            }
        }
        try {
            UserInfoBase result = this.openService.userBaseInfo(userId, oid, openId, header.getRegionId());
            log.debug("根据用户ID查询基本信息成功 userId：{},openID： {}", userId, openId);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据用户ID查询基本信息失败:" + e.getMessage(), e);
            throw new ApiException("根据用户ID查询基本信息失败", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "根据用户ID查询基本信息失败"));
        }
    }

    /**
     * 根据用户ID查询用户脱敏信息-外部接口
     *
     * @param userId user_id 可以为空
     * @param oid    org_id 可以为空
     * @param openId open id 可以为空
     * @return data or error
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/uc/org/user-base-info-safe")
    @ApiOperation("根据用户ID/openid查询脱敏基本信息")
    public ResponseEntity<Result<?>> userBaseInfoSafe(@RequestParam(required = false) Long userId,
                                                      @RequestParam(required = false) Long oid,
                                                      @RequestParam(value = "open_id", required = false) String openId,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        try {
            UserInfoBase result = this.openService.userBaseInfo(userId, oid, openId, header.getRegionId());
            // 移除非脱敏数据
            result.setPhone(null);
            result.setCertNumber(null);
            result.setCertType(null);
            if (userId == null) {
                result.setUserId(null);
            }
            result.setAllIdList(null);
            result.setOrgIdList(null);
            log.debug("根据用户ID/openid查询脱敏基本信息成功 userId：{},openID： {}", userId, openId);
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据用户ID/openid查询脱敏基本信息失败:" + e.getMessage(), e);
            throw new ApiException("根据ID/openid查询脱敏基本信息失败", new Result<>(errors, Global.Errors.NOT_FOUND, HttpStatus.NOT_FOUND.value(), "根据用户ID查询基本信息失败"));
        }
    }

    /**
     * 添加报名用户
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/uc/org/create-user-info")
    @ApiOperation("添加报名用户")
    public ResponseEntity<Result<?>> createUser(@Valid @RequestBody UserInfoBase form, @RequestHeader HttpHeaders headers,
                                                BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<String, Object> map = this.openService.createUser(form, header.getOid(), header.getRegionId());
        UserInfoBase resultBase;
        int code = (int) map.get("code");
        if (0 == code) {
            log.debug("系统异常，添加报名用户失败，参数--> name:{},phone:{},certNumber:{}", form.getName(), form.getPhone(), form.getCertNumber());
            return new ResponseEntity<>(new Result<>(errors, 174, HttpStatus.OK.value()), HttpStatus.OK);
        } else if (1 == code) {
            resultBase = (UserInfoBase) map.get("form");
        } else if (192 == code) {
            log.debug("报名用户电话已经存在,参数--> name:{},phone:{},certNumber:{}", form.getName(), form.getPhone(), form.getCertNumber());
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value(), form.getPhone()), HttpStatus.OK);
        } else {
            log.debug("身份证合法校验失败,参数--> name:{},phone:{},certNumber:{}", form.getName(), form.getPhone(), form.getCertNumber());
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
        }
        log.debug("新增报名用户成功 userId：{}", resultBase.getUserId());
        return new ResponseEntity<>(new Result<>(resultBase, errors), HttpStatus.OK);
    }

    /**
     * 批量添加报名用户
     *
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/uc/org/batch-create-user-info")
    @ApiOperation("批量添加报名用户")
    public ResponseEntity<Result<?>> batchCreateUserInfo(@Valid @RequestBody BatchUserInfoForm form, @RequestHeader HttpHeaders headers,
                                                         BindingResult bindingResult) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<String, Object> map = this.openService.batchCreateUserInfo(form, header.getOid(), header.getRegionId());
        BatchUserInfoForm resultForm;
        int code = (int) map.get("code");
        if (code == 1) {
            resultForm = (BatchUserInfoForm) map.get("form");
        } else if (192 == code) {
            log.debug("报名用户电话已经存在");
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value(), map.get("phone").toString()), HttpStatus.OK);
        } else {
            log.debug("批量添加报名用户失败");
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(resultForm, errors), HttpStatus.OK);
    }

    /**
     * 根据单位ID查询单位信息-内部接口
     *
     * @param oid
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/uc/org/corp-info")
    @ApiOperation("根据单位ID查询单位信息")
    public ResponseEntity<Result<?>> corpInfo(@RequestParam @NotNull(message = "{NotNull.query.oid}") Long oid) {
        CorporationEntity result = this.openService.corpInfo(oid);
        log.debug("根据单位ID查询单位信息成功 oid：{}", oid);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 验证组织标识码
     *
     * @param orgUniqueCode
     * @param captcha
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/uc/org/check-unique-code")
    @ApiOperation("验证组织标识码")
    public ResponseEntity<Result<?>> checkUniqueCode(@RequestParam
                                                     @NotBlank(message = "{NotBlank.query.orgUniqueCode}") String orgUniqueCode,
                                                     @RequestParam @NotBlank(message = "{NotBlank.query.uuid}") String uuid,
                                                     @RequestParam @NotBlank(message = "{NotBlank.query.captcha}") String captcha) {
        if (!this.redisTemplate.hasKey(Constants.LOGIN_CAPTCHA_PREFIX.concat(uuid))) {
            log.debug("验证码已失效，请重新获取 captcha -> {}", captcha);
            return new ResponseEntity<>(new Result<>(errors, 176, HttpStatus.OK.value()), HttpStatus.OK);
        }
        OrganizationEntity entity = this.openService.checkUniqueCode(orgUniqueCode);
        if (entity == null) {
            log.debug("组织标识码不存在，请重新输入 orgUniqueCode -> {}", orgUniqueCode);
            return new ResponseEntity<>(new Result<>(errors, 181, HttpStatus.OK.value()), HttpStatus.OK);
        } else {
            if (entity.getStatus() == Constants.STATUS_NO) {
                log.debug("该组织已经被禁用，请重新输入 orgUniqueCode -> {}", orgUniqueCode);
                return new ResponseEntity<>(new Result<>(errors, 190, HttpStatus.OK.value()), HttpStatus.OK);
            }
            if (entity.getActivate() == 1) {
                log.debug("该组织已经被激活，请重新输入 orgUniqueCode -> {}", orgUniqueCode);
                return new ResponseEntity<>(new Result<>(errors, 187, HttpStatus.OK.value()), HttpStatus.OK);
            }
            String redisCaptcha = this.redisTemplate.opsForValue().get(Constants.LOGIN_CAPTCHA_PREFIX.concat(uuid));
            if (!redisCaptcha.equalsIgnoreCase(captcha)) {
                log.debug("验证码错误，请重新输入 captcha -> {}", captcha);
                return new ResponseEntity<>(new Result<>(errors, 177, HttpStatus.OK.value()), HttpStatus.OK);
            }
            // 删除图片验证码
            this.redisTemplate.delete(Constants.LOGIN_CAPTCHA_PREFIX.concat(uuid));
            OrgResultForm targetForm = new OrgResultForm();
            BeanUtils.copyProperties(entity, targetForm);
            targetForm.setParentTreeName(openService.getParentTreeName(entity.getRegionId()));
            if (null != targetForm.getOwnerId()) {
                targetForm.setOwnerName(this.openService.getNameByOrgId(targetForm.getOwnerId()));
            }
            // 根据组织ID查询组织组织名称
            log.debug("验证组织标识码成功");
            return new ResponseEntity<>(new Result<>(targetForm, errors), HttpStatus.OK);
        }
    }

    /**
     * 查询所有标签
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-all-tag")
    @ApiOperation("查询所有标签")
    public ResponseEntity<Result<?>> findAllTag() {
        List<TagEntity> tagList = this.openService.findAllTag();
        return new ResponseEntity<>(new Result<>(tagList, errors), HttpStatus.OK);
    }

    /**
     * 实名认证
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/check-real-name")
    @ApiOperation("实名认证")
    public ResponseEntity<Result<?>> checkRealName(@NotBlank(message = "{NotBlank.user.name}")
                                                   @RequestParam String name,
                                                   @NotBlank(message = "{NotBlank.user.certNumber}")
                                                   @javax.validation.constraints.Pattern(regexp = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$)", message = "{Pattern.user.certNumber}")
                                                   @RequestParam(name = "id_card") String idCard
    ) {
        String rs = this.openService.validateCertNumber(idCard, name);
        OpenForm openForm = Utils.fromJson(rs, OpenForm.class);
        return new ResponseEntity<>(new Result<>(openForm, errors), HttpStatus.OK);
    }

    /**
     * 群团实名认证
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/bind-check-real-name")
    @ApiOperation("群团实名认证")
    public ResponseEntity<Result<?>> bindCheckRealName(@NotBlank(message = "{NotBlank.user.name}")
                                                       @RequestParam String name,
                                                       @NotBlank(message = "{NotBlank.user.certNumber}")
                                                       @javax.validation.constraints.Pattern(regexp = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$)", message = "{Pattern.user.certNumber}")
                                                       @RequestParam(name = "cert_number") String certNumber,
                                                       @NotBlank(message = "{NotBlank.openId}")
                                                       @RequestParam(value = "open_id") String openId,
                                                       @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        Map<String, Object> resultMap = this.openService.bindCheckRealName(certNumber.toUpperCase(), name, openId, header.getOid());
        int code = (int) resultMap.get("code");
        if (code != 1) {
            log.debug("群团绑定失败 name:{},certNumber:{},code:{}", name, certNumber, code);
            return new ResponseEntity<>(new Result<>(errors, code, HttpStatus.OK.value()), HttpStatus.OK);
        }
        UserForm userForm = new UserForm();
        userForm.setUserId((Long) resultMap.get("userId"));
        return new ResponseEntity<>(new Result<>(userForm, errors), HttpStatus.OK);
    }


    /**
     * @Author: tc
     * @Description 导出时做权限检查，只有拥有
     * 当前组织菜单权限的用户才能导出，返回值大于0表示有权限
     * @Date 10:29 2018/7/11
     */
    @HttpMonitorLogger
    @GetMapping("/uc/org/user/check-download-auth")
    @ApiOperation("导出权限验证")
    public ResponseEntity<Result<?>> checkDownloadAuth(
            @RequestHeader HttpHeaders headers,
            @RequestParam @NotNull(message = "{NotBlank.user.id}") Long userId,
            @RequestParam @NotNull(message = "{NotNull.user.org.id}") Long orgId,
            @RequestParam @NotBlank(message = "{NotBlank.user.menu.id}") String menuId) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        int rs = this.openService.checkDownloadAuth(userId, orgId, menuId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(rs, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询所在组织列表
     *
     * @param userId
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("uc/org/list-by-id")
    @ApiOperation("根据用户ID查询所在组织列表")
    public ResponseEntity<Result<?>> orgListByUserId(@RequestParam(name = "user_id")
                                                     @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<Long> resultList = this.openService.orgListByUserId(userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 获取所有用户openId
     *
     * @param page
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/open-id-list")
    @ApiOperation("获取所有用户openId")
    public ResponseEntity<Result<?>> openIdList(@RequestParam(required = false) Integer page,
                                                @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<OpenResultForm> resultList = this.openService.openIdList(page, header.getRegionId());
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据用户id 得到用户所在组织信息
     * 包括组织id 组织名称 单位id 单位类型
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/get_user_org_info")
    @ApiOperation("根据用户获取用户的组织信息")
    public ResponseEntity<Result<?>> getUserOrgInfo(@RequestParam(name = "user_id")
                                                    @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        TbcBaseVo userOrgInfo = this.openService.getUserOrgInfo(userId);
        return new ResponseEntity<>(new Result<>(userOrgInfo, errors), HttpStatus.OK);
    }


    /**
     * @param phone
     * @param certNumber
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/get-secret")
    @ApiOperation("加密")
    public ResponseEntity<Result<?>> getSecret(
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "cert_number", required = false) String certNumber) {
        ResultSecretForm form = this.openService.getSecret(phone, certNumber);
        return new ResponseEntity<>(new Result<>(form, errors), HttpStatus.OK);
    }

    /**
     * @param form
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/decode-secret")
    @ApiOperation("解密")
    public ResponseEntity<Result<?>> encryptSecret(@RequestBody DecodeForm form) throws Exception {
        String result = this.openService.decodeSecret(form.getSecret(), form.getNum_secret());
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    /**
     * 用户添加菜单权限-对外接口
     *
     * @param form
     * @param bindingResult
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/change-user-role")
    @ApiOperation("用户添加菜单权限")
    public ResponseEntity<Result<?>> changeUserRole(@Valid @RequestBody ChangeUserRoleForm form,
                                                    BindingResult bindingResult,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        boolean b = this.openService.changeUserRole(form, header.getUserId(), header.getRegionId());
        return new ResponseEntity<>(new Result<>(b ? "success" : "fail", errors), HttpStatus.OK);
    }

    /**
     * 根据菜单查询该组织下拥有该权限的用户-对外接口
     *
     * @param orgId  组织ID
     * @param menuId 菜单ID
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserMenuResultForm.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-by-menu")
    @ApiOperation("根据菜单查询该组织下拥有该权限的用户")
    public ResponseEntity<Result<?>> findUserByMenu(@RequestParam(name = "org_id")
                                                    @NotNull(message = "{NotNull.org.id}") Long orgId,
                                                    @RequestParam(name = "menu_id")
                                                    @NotBlank(message = "{NotBlank.menu.id}") String menuId,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        List<UserMenuResultForm> formList = this.openService.findUserByMenu(orgId, menuId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(formList, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @PostMapping("/test-user-change")
    @ApiOperation("用户组织变化回调接口")
    public ResponseEntity<Result<?>> testUserChange() {

        UserChangeForm form = new UserChangeForm();
        form.setUserId(55885L);
        form.setUserName("谢章元");
        form.setOldOrgId(3957L);
        form.setNewOrgId(3952L);
        form.setNewOrgName("新组织");
        form.setStatus(1);
        form.setTime(new Date());
        // this.callbackService.userOrgChangeCallback(form);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/test-add-mgr")
    @ApiOperation("生成高级管理员权限")
    public ResponseEntity<Result<?>> addMgr() {
        // this.openService.addMgr();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/test-update-certNumber")
    @ApiOperation("生成更新用户身份证脚本")
    public ResponseEntity<Result<?>> updateCertNumber() throws IOException {
        // this.openService.updateCertNumber();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/update-org-user-num")
    @ApiOperation("刷新组织用户数量")
    public ResponseEntity<Result<String>> flushOrgUserNum(@RequestBody FindOrgListForm form) {
        this.openService.updateOrgUserNum(form);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据组织类型获取树类型 1-父树 2-子树
     *
     * @param orgType 组织类型字典码
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-tree-type-by-op")
    @ApiOperation("根据组织类型获取树类型 1-父树 2-子树")
    public ResponseEntity<Result<?>> findTreeTypeByOp(@RequestParam(name = "org_type")
                                                      @NotNull(message = "{NotBlank.org.type}") Integer orgType) {
        int treeType = this.openService.findTreeTypeByOp(orgType);
        log.debug("根据组织类型获取树类型 orgType:[{}],treeType:[{}]", orgType, treeType);
        return new ResponseEntity<>(new Result<>(treeType, errors), HttpStatus.OK);
    }

    /**
     * 手动执行政治生日和自然生日定时任务-供测试使用
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/joinTimeScheduler")
    @ApiOperation("手动执行政治生日定时任务")
    public ResponseEntity<Result<?>> joinTimeScheduler() {
        this.partPushScheduler.joinTimeScheduler();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 手动执行自然生日定时任务-供测试使用
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/birthdayScheduler")
    @ApiOperation("手动执行自然生日定时任务")
    public ResponseEntity<Result<?>> birthdayScheduler() {
        this.partPushScheduler.birthdayScheduler();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 校验用户是否存在公众号权限
     *
     * @param form 参数
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/check-org-permission")
    @ApiOperation("校验用户是否存在公众号权限")
    public ResponseEntity<Result<?>> checkOrgPermission(@RequestBody CheckPermissionForm form) {
        boolean isPermission = this.officialAccountService.checkOrgPermission(form.getOrgId(), form.getOrgIdList());
        log.debug("校验用户是否存在公众号权限 isPermission:[{}]", isPermission);
        return new ResponseEntity<>(new Result<>(isPermission, errors), HttpStatus.OK);
    }

    /**
     * 手动执行更新人员年龄定时任务-供测试使用
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/resetAgeScheduler")
    @ApiOperation("手动执行更新人员年龄定时任务")
    public ResponseEntity<Result<?>> resetAgeScheduler() {
        this.userService.resetAge();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询用户信息
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-by-where")
    @ApiOperation("根据条件查询用户信息")
    public ResponseEntity<Result<?>> findUserByWhere(@RequestParam(name = "name")
                                                     @NotBlank(message = "{NotBlank.user.name}") String name,
                                                     @RequestParam(name = "phone", required = false) String phone,
                                                     @RequestParam(name = "cert_number", required = false)
                                                     @Pattern(regexp = "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}[0-9Xx]$)",
                                                             message = "{Pattern.user.certNumber}")
                                                     String certNumber) {
        log.debug("根据条件查询用户信息参数 name:[{}],phone:[{}],certNumber:[{}]", name, phone, certNumber);
        if (StringUtils.isBlank(phone) && StringUtils.isBlank(certNumber)) {
            throw new ApiException("手机号和身份证不能同时为空", new Result<>(errors, 32004, HttpStatus.INTERNAL_SERVER_ERROR.value()));
        }
        return new ResponseEntity<>(new Result<>(this.openService.findUserByWhere(name, phone, certNumber), errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询政治生日，自然生日党员
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-member-by-date")
    @ApiOperation("根据条件查询政治生日，自然生日党员")
    public ResponseEntity<Result<?>> findMemberByDate(@RequestParam(name = "org_id")
                                                      @NotNull(message = "{NotBlank.org.id}") Long orgId) {
        log.debug("根据条件查询政治生日，自然生日党员参数 orgId:[{}]", orgId);
        return new ResponseEntity<>(new Result<>(this.openService.findMemberByDate(orgId), errors), HttpStatus.OK);
    }

    /**
     * 根据条件查询组织管理员
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-manager-by-where")
    @ApiOperation("根据条件查询组织管理员")
    public ResponseEntity<Result<?>> findManagerByWhere(@RequestParam(name = "org_id")
                                                        @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                        @RequestParam(name = "role_type", defaultValue = "9") Integer roleType,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据条件查询组织管理员参数 orgId:[{}],roleType:[{}]", orgId, roleType);
        List<UserInfoBase> resultList = this.openService.findManagerByWhere(orgId, roleType, header.getRegionId());
        log.debug("根据条件查询组织管理员返回结果 resultList:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询用户列表
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-by-org-id")
    @ApiOperation("根据组织ID查询用户列表")
    public ResponseEntity<Result<?>> findUserByOrgId(@RequestBody FindOrgListForm form,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据组织ID查询用户列表参数 from:[{}]", form);
        List<UserInfoBase> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(form.getIdList())) {
            resultList = this.openService.findUserByOrgId(form, header.getRegionId());
        }
        log.debug("根据组织ID查询用户列表结果 resultList:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询用户列表
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoCommentVO.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-info-by-org-id")
    @ApiOperation("根据组织ID查询用户列表")
    public ResponseEntity<Result<?>> findUserInfoByOrgId(@RequestBody FindUserListByOrgForm form,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据组织ID查询用户列表参数 from:[{}]", form);
        Page<UserInfoCommentVO> resultList = this.openService.findUserListByOrgId(form, header.getRegionId());
        log.debug("根据组织ID查询用户列表结果 resultList:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }

    /**
     * 根据组织ID查询组织基本信息
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-org-by-id")
    @ApiOperation("根据组织ID查询组织基本信息")
    public ResponseEntity<Result<?>> findOrgById(@RequestParam(name = "org_id")
                                                 @NotNull(message = "{NotBlank.org.id}") Long orgId,
                                                 @RequestParam(name = "year", required = false) Integer year,
                                                 @RequestParam(name = "month", required = false) Integer month) {
        log.debug("根据组织ID查询组织基本信息 orgId:[{}]", orgId);
        return new ResponseEntity<>(new Result<>(this.openService.findOrgById(orgId, year, month), errors), HttpStatus.OK);
    }

    @AddDynamicFields(fieldType = FieldType.ORGANIZATION_ID, clazzType = OrganizationBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-org-by-ids")
    @ApiOperation("根据组织ID查询组织基本信息")
    public ResponseEntity<Result<?>> findOrgByIds(@RequestBody List<Long> orgIds,
                                                  @RequestParam(name = "year", required = false) Integer year,
                                                  @RequestParam(name = "month", required = false) Integer month) {
        log.debug("根据组织ID查询组织基本信息 orgId:[{}]", orgIds);
        return new ResponseEntity<>(new Result<>(orgIds.stream().filter(Objects::nonNull).map(orgId -> this.openService.findOrgById(orgId, year, month)), errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询用户组织关系基本信息
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @GetMapping("/find-user-by-key")
    @ApiOperation("根据用户ID查询用户组织关系基本信息")
    public ResponseEntity<Result<?>> findUserByKey(@RequestParam(name = "user_id")
                                                   @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                   @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询用户组织关系基本信息 userId:[{}]", userId);
        List<UserInfoBase> resultList = this.openService.findUserByKey(userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(CollectionUtils.isEmpty(resultList) ? new ArrayList<>() : resultList, errors), HttpStatus.OK);
    }


    /**
     * 根据用户ID 查询用户概要信息
     * 包括 组织id 用户名称 头像 部门名称 职务名称
     *
     * @return
     */
    @HttpMonitorLogger
    @GetMapping("/find-user-summary")
    @ApiOperation("根据用户ID查询用户概要信息")
    @DynamicDesc(dynamicObj = 1, dynamicNum = 2, dynamicField = "type")
    public ResponseEntity<Result<?>> findUserSummary(@RequestParam(name = "user_id")
                                                     @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                     @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询用户概要信息 userId:[{}]", userId);
        UserSummaryVo userSummary = this.openService.findUserSummary(userId, header.getRegionId());
        return new ResponseEntity<>(new Result<>(userSummary, errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID 查询用户概要信息
     * 包括 组织id 用户名称 头像 部门名称 职务名称
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/find-user-summary2")
    @ApiOperation("根据用户ID查询用户概要信息")
    @DynamicDesc(dynamicObj = 1, dynamicNum = 2, dynamicField = "title")
    public ResponseEntity<Result<?>> findUserSummary2(@RequestParam(name = "user_id")
                                                      @NotNull(message = "{NotBlank.user.id}") Long userId,
                                                      @RequestBody FindUserListByOrgForm form,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询用户概要信息 userId:[{}]", userId);
        return new ResponseEntity<>(new Result<>("SUC", errors), HttpStatus.OK);
    }

    /**
     * 根据用户ID查询用户组织以及党委信息
     *
     * @return
     */
    @AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-org-by-batch")
    @ApiOperation("根据用户ID查询用户组织以及党委信息")
    public ResponseEntity<Result<?>> findUserOrgByBatch(@RequestBody FindOrgListForm form,
                                                        @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询用户组织以及党委信息 form:[{}]", form);
        if (CollectionUtils.isEmpty(form.getIdList())) {
            return new ResponseEntity<>(new Result<>(errors, 5600, HttpStatus.OK.value()), HttpStatus.OK);
        }
        return new ResponseEntity<>(new Result<>(this.openService.findUserOrgByBatch(form, header.getRegionId()), errors),
                HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/find-party-number-by-org")
    @ApiOperation("根据用户ID查询及其下级所有组织的党员数量")
    public ResponseEntity<Result<?>> findPartyNumberByOrg
            (@RequestParam(name = "org_id") @NotNull(message = "{NotBlank.org.id}") Long orgId,
             @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据用户ID查询及其下级所有组织的党员数量 orgId:[{}]", orgId);
        return new ResponseEntity<>(new Result<>(this.openService.findPartyNumberByOrg(orgId, header.getRegionId()), errors),
                HttpStatus.OK);
    }

    /**
     * 导入用户数据
     *
     * @return
     * @throws Exception
     */
    @HttpMonitorLogger
    @PostMapping("/import-user")
    @ApiOperation("导入用户信息")
    public ResponseEntity<Result<?>> importUser() throws Exception {

        this.openService.importUser();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/initOrgPeriod")
    @ApiOperation("初始化届次以及领导班子")
    public ResponseEntity<Result<?>> initOrgPeriod() {
        this.openService.initOrgPeriod();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/select-org-secretary")
    @ApiOperation("获取组织数据")
    public ResponseEntity<Result<?>> selectOrgSecretary(@RequestBody SelectOrgSecretaryQueryForm queryForm) {
        List<OrgSecretaryForm> orgSecretaryForms = this.openService.selectOrgSecretary(queryForm);
        return new ResponseEntity<>(new Result<>(orgSecretaryForms, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @GetMapping("/dealSM4Info")
    @ApiOperation("获取国密4加密/解密串")
    public ResponseEntity<Result<?>> dealSM4Info(@RequestParam(name = "content") String content,
                                                 @RequestParam(name = "type") Integer type) {
        return new ResponseEntity<>(new Result<>(this.openService.dealSM4Info(content, type), errors), HttpStatus.OK);
    }

    /**
     * 服务端调用统一密码服务平台验证接口
     *
     * @return 验证结果
     */
    @HttpMonitorLogger
    @PostMapping("/verify-sign-request")
    @ApiOperation("服务端调用统一密码服务平台验证接口")
    public ResponseEntity<Result<?>> verifySignRequest(@RequestBody VerifySignRequestForm form) {
        return new ResponseEntity<>(new Result<>(this.openService.vefitySignRequest(form), errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/get-system-path")
    @ApiOperation("获取系统路径")
    public ResponseEntity<Result<?>> getSystemPath() {
        String path = System.getProperty("user.dir");
        String result = NumEncryptUtils.encrypt("15810548399", 1);
        return new ResponseEntity<>(new Result<>(path + "/" + result, errors), HttpStatus.OK);
    }

    /**
     * 根据单位/组织ID查询用户列表
     *
     * @return
     */
    //@AddDynamicFields(fieldType = FieldType.USER_ID, clazzType = UserInfoBase.class)
    @HttpMonitorLogger
    @PostMapping("/find-user-by-org-or-corp-id")
    @ApiOperation("根据单位/组织ID查询用户列表")
    public ResponseEntity<Result<?>> findUserByOrgCorpId(@RequestBody FindOrgListForm form,
                                                         @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("根据单位/组织ID查询用户列表 from:[{}]", form);
        Set<UserInfoBase> resultList = new HashSet<>();
        if (!CollectionUtils.isEmpty(form.getIdList())) {
            resultList = this.openService.findUserByOrgCorpId(form, header.getRegionId());
        }
        log.debug("根据单位/组织ID查询用户列表 resultList:[{}]", resultList);
        return new ResponseEntity<>(new Result<>(resultList, errors), HttpStatus.OK);
    }


    @HttpMonitorLogger
    @GetMapping("/find-corp-by-org")
    @ApiOperation("根据组织ID查询单位对应的一级组织")
    public ResponseEntity<Result<?>> getOrgByCorp(@RequestParam("org_id") Long orgId) {
        final OrganizationBase result = this.openService.findCorpByOrg(orgId);
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/batch-user-by-phone")
    @ApiOperation("根据手机号批量查询人员")
    public ResponseEntity<Result<?>> getUserOrgInfo(@Valid @RequestBody UserPhoneForm form,
                                                    @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(
                this.openService.getUserListByPhone(form.getPhoneList(), header.getRegionId())
                , errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @PostMapping("/find-user-corp-org-id")
    @ApiOperation("根据组织列表查询人员列表 可包含行政单位")
    public ResponseEntity<Result<?>> getUserByOrgCorp(@RequestBody FindOrgListForm form,
                                                      @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        return new ResponseEntity<>(new Result<>(
                this.openService.getUserByOrgCorp(form.getIdList(), header.getRegionId())
                , errors), HttpStatus.OK);
    }
}
