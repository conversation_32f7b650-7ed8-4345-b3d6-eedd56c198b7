package com.goodsogood.ows.controller;

import com.goodsogood.ows.config.Region;
import com.goodsogood.ows.config.SimpleApplicationConfigHelper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @date 2020/9/16
 */
@RestController
@Log4j2
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
public class LinkOutController {

    private final SimpleApplicationConfigHelper applicationConfigHelper;

    @Autowired
    public LinkOutController(SimpleApplicationConfigHelper applicationConfigHelper) {
        this.applicationConfigHelper = applicationConfigHelper;
    }

    static String V1 = "{wechat_center}/ssr/check-page/{openId}/{random}?redirect={redirect}";

    static String V3 = "{wechat_center}/redirect/login_pre_router" +
            "?project_name={projectName}" +
            "&appid={appId}" +
            "&scope=snsapi_base" +
            "&version=4" +
            "&org_id={orgId}" +
            "&routerType=-2" +
            "&redirect={redirect}";
    static String V3_REDIRECT = "/v2/check-page?oid={oid}&login_type=wxcode&region_id={regionId}&appid={appId}&redirect_url={redirect}";

    @GetMapping("/buildLinkOut")
    public String buildLinkOut(@RequestParam("org_id") long ogrId, @RequestParam String redirect) throws UnsupportedEncodingException {
        log.debug("接收到参数，org_id = {}，redirect = {}", ogrId, redirect);
        Region.RegionData regionData = applicationConfigHelper.getRegionByOrgId(ogrId);
        String linkOut;
        if (regionData.wechatIsSaasVersion()) {
            linkOut = getLinkOutV3(ogrId, redirect);
        } else {
            linkOut = getLinkOutV1(ogrId, redirect);
        }
        log.debug("生成外链 = {}", linkOut);
        return linkOut;
    }

    /**
     * 生成v1.0.6外链
     *
     * @param ogrId
     * @param redirect
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getLinkOutV1(long ogrId, String redirect) throws UnsupportedEncodingException {
        Region.RegionData regionData = applicationConfigHelper.getRegionByOrgId(ogrId);
        String linkOutUrl = V1.replace("{wechat_center}", regionData.getWechatDomain())
                .replace("{redirect}", URLEncoder.encode(redirect, "UTF-8"));
        return linkOutUrl;
    }

    /**
     * 生成v3.0.0外链
     *
     * @param ogrId
     * @param redirect
     * @return
     * @throws UnsupportedEncodingException
     */
    public String getLinkOutV3(long ogrId, String redirect) throws UnsupportedEncodingException {
        Region.RegionData regionData = applicationConfigHelper.getRegionByOrgId(ogrId);
        String resultRedirect = V3_REDIRECT.replace("{oid}", regionData.getOrgData().getOrgId() + "")
                .replace("{regionId}", regionData.getRegionId() + "")
                .replace("{appId}", regionData.getAppId())
                .replace("{redirect}", URLEncoder.encode(redirect, "UTF-8"));
        String linkOutUrl = V3.replace("{wechat_center}", applicationConfigHelper.getRegions().getWechatCenterUrl())
                .replace("{projectName}", applicationConfigHelper.getRegions().getProjectName())
                .replace("{appId}", regionData.getAppId())
                .replace("{orgId}", regionData.getOrgData().getOrgId() + "")
                .replace("{redirect}", URLEncoder.encode(resultRedirect, "UTF-8"));
        return linkOutUrl;
    }
}
