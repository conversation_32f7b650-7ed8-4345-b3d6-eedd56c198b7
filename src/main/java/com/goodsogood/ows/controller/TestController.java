package com.goodsogood.ows.controller;

import cn.hutool.core.convert.Convert;
import com.github.pagehelper.Page;
import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.ExcelUtils;
import com.goodsogood.ows.common.FieldNameConfig;
import com.goodsogood.ows.component.CallbackScheduler;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.component.InitMongoBean;
import com.goodsogood.ows.component.UserFieldEnum;
import com.goodsogood.ows.configuration.Global;
import com.goodsogood.ows.exception.ApiException;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.helper.NumEncryptUtils;
import com.goodsogood.ows.model.db.PageNumber;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.service.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/convert")
@Log4j2
public class TestController {

    private final Errors errors;
    private final UserThirdService userThirdService;
    private final TestService testService;
    private final UserMongoService userMongoService;
    private final CallbackScheduler callbackScheduler;
    private final OpenService openService;

    private final OrgService orgService;

    @Autowired
    public TestController(Errors errors, UserThirdService userThirdService,
                          TestService testService, UserMongoService userMongoService, CallbackScheduler callbackScheduler, OpenService openService, OrgService orgService) {
        this.errors = errors;
        this.userThirdService = userThirdService;
        this.testService = testService;
        this.userMongoService = userMongoService;
        this.callbackScheduler = callbackScheduler;
        this.openService = openService;
        this.orgService = orgService;
    }

    @HttpMonitorLogger
    @ApiOperation("用户信息转换")
    @PostMapping(value = "/userInfo")
    public void convert(HttpServletRequest request, HttpServletResponse response, @RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("上传文件为空");
        }
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!(suffix.equals("xlsx") || suffix.equals("xls") || suffix.equals("csv"))) {
            throw new Exception("请上传Excel文件");
        }
        analysisExcel(file.getInputStream(), response);
    }

    @HttpMonitorLogger
    @ApiOperation("身份证加密")
    @PostMapping(value = "/encryptionCert")
    public void encryptionCert(HttpServletRequest request, HttpServletResponse response, @RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("上传文件为空");
        }
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!(suffix.equals("xlsx") || suffix.equals("xls"))) {
            throw new Exception("请上传Excel文件");
        }
        encryptionCertNumberExcel(file.getInputStream(), response);
    }

    @HttpMonitorLogger
    @ApiOperation("用户信息转换")
    @PostMapping(value = "/getPhoneByOpenId")
    public void getPhoneByOpenId(HttpServletRequest request, HttpServletResponse response, @RequestParam("file") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw new Exception("上传文件为空");
        }
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if (!(suffix.equals("xlsx") || suffix.equals("xls"))) {
            throw new Exception("请上传Excel文件");
        }
        analysisOpenIdExcel(file.getInputStream(), response);
    }

    @HttpMonitorLogger
    @ApiOperation("用户更新标签")
    @GetMapping(value = "/update-new-tag")
    public String updateNewTag(HttpServletRequest request) {
        this.testService.updateNewTag();
        return "success";
    }

    @HttpMonitorLogger
    @PostMapping("/getUserList")
    @ApiOperation("根据条件查询成员列表")
    public ResponseEntity<Result<?>> getUserList(@RequestBody Map<String, Object> queryMap,
                                                 @RequestHeader HttpHeaders headers) {
        HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        log.debug("参数：queryMap = {}", queryMap);

        Page<Map<String, Object>> pageData;
        try {
            Object certNumberObj = queryMap.get(FieldNameConfig.USER_CERT_NUMBER);
            Object phoneObj = queryMap.get(FieldNameConfig.USER_PHONE);
            //替换证件号码和电话为密文
            if (certNumberObj != null) {
                String certNumber = certNumberObj.toString();
                queryMap.put(FieldNameConfig.USER_CERT_NUMBER, NumEncryptUtils.encrypt(certNumber, Constants.CERT_NUMBER_IDENTITY));
            }
            if (phoneObj != null) {
                String phone = phoneObj.toString();
                queryMap.put(FieldNameConfig.USER_PHONE, NumEncryptUtils.encrypt(phone, Constants.PHONE_IDENTITY));
            }
            int page = queryMap.get(FieldNameConfig.PAGE) == null ? 1 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE));
            int pageSize = queryMap.get(FieldNameConfig.PAGE_SIZE) == null ? 10 : Convert.toInt(queryMap.get(FieldNameConfig.PAGE_SIZE));
            queryMap.put("regionId", header.getRegionId());
            queryMap.put(UserFieldEnum.STATUS.getKey(), Constants.STATUS_YES);
            queryMap.remove(FieldNameConfig.PAGE);
            queryMap.remove(FieldNameConfig.PAGE_SIZE);
            pageData = this.userMongoService.getUserList(queryMap, 1, 1, new PageNumber(page, pageSize), header, true, "name~1");
        } catch (Exception e) {
            log.error("查询组织人员列表出错:{}" + e.getMessage(), e);
            throw new ApiException("查询组织人员列表出错", new Result<>(errors, Global.Errors.REMOTE_SERVER_ERROR, HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询单位组织列表出错"));
        }
        return new ResponseEntity<>(new Result<>(pageData, errors), HttpStatus.OK);
    }

    /**
     * 将Ecel解析出来
     */
    private void analysisExcel(InputStream inputStream, HttpServletResponse response) throws Exception {
        Workbook workbook = WorkbookFactory.create(inputStream);
        inputStream.close();
        //第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        //总行数
        int rowLength = sheet.getLastRowNum() + 1;
        //总列数
        int colLength = sheet.getRow(0).getLastCellNum();

        //得到单元格样式
        log.debug("行数 -> [{}],列数 -> [{}]", rowLength, colLength);
        for (int i = 1; i < rowLength; i++) {
            Row row = sheet.getRow(i);
            if (null != row) {
                String cipherText = row.getCell(0).getStringCellValue();
                String secretNum = row.getCell(1).getStringCellValue();
                log.debug("解密密文 -> [{}], 脱敏数据 -> [{}]", cipherText, secretNum);
                String content = NumEncryptUtils.decrypt(cipherText, secretNum);
                log.debug("解密数据 -> [{}]", content);
                String PHONE_NUMBER_REG = "^(1[3-9])\\d{9}$";
                if (content.matches(PHONE_NUMBER_REG)) {
                    row.createCell(2).setCellValue(content);
                }
                //row.createCell(2).setCellValue(content);
            }
        }

        if (workbook != null) {
            //设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=phone" + ExcelUtils.XLSX_SUFFIX);
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/vnd.ms-excel;charset=gb2312");
            //将excel写入到输出流中
            workbook.write(os);
            os.flush();
            os.close();
        } else {
            throw new Exception("导出失败");
        }
    }


    /**
     * 将Ecel解析出来
     */
    private void analysisOpenIdExcel(InputStream inputStream, HttpServletResponse response) throws Exception {
        Workbook workbook = WorkbookFactory.create(inputStream);
        inputStream.close();
        //第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        //总行数
        int rowLength = sheet.getLastRowNum() + 1;
        //总列数
        int colLength = sheet.getRow(0).getLastCellNum();

        //得到单元格样式
        log.debug("行数 -> [{}],列数 -> [{}]", rowLength, colLength);
        for (int i = 0; i < rowLength; i++) {
            Row row = sheet.getRow(i);
            if (null != row) {
                String openId = row.getCell(1).getStringCellValue();
                if (StringUtils.isNotBlank(openId)) {
                    UserEntity user = this.userThirdService.getUserByOpenId(openId);
                    if (null != user) {
                        String cipherText = user.getPhone();
                        String secretNum = user.getPhoneSecret();
                        log.debug("解密密文 -> [{}], 脱敏数据 -> [{}]", cipherText, secretNum);
                        String content = NumEncryptUtils.decrypt(cipherText, secretNum);
                        log.debug("解密数据 -> [{}]", content);
                        String PHONE_NUMBER_REG = "^(1[3-9])\\d{9}$";
                        if (content.matches(PHONE_NUMBER_REG)) {
                            row.createCell(2).setCellValue(content);
                        }
                    }
                }
                //row.createCell(2).setCellValue(content);
            }
        }

        if (workbook != null) {
            //设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=phone." + ExcelUtils.XLSX_SUFFIX);
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/vnd.ms-excel;charset=gb2312");
            //将excel写入到输出流中
            workbook.write(os);
            os.flush();
            os.close();
        } else {
            throw new Exception("导出失败");
        }
    }

    /**
     * 将Ecel解析出来
     */
    private void encryptionCertNumberExcel(InputStream inputStream, HttpServletResponse response) throws Exception {
        Workbook workbook = WorkbookFactory.create(inputStream);
        inputStream.close();
        //第一个sheet
        Sheet sheet = workbook.getSheetAt(0);
        //总行数
        int rowLength = sheet.getLastRowNum() + 1;
        //总列数
        int colLength = sheet.getRow(0).getLastCellNum();

        //得到单元格样式
        log.debug("行数 -> [{}],列数 -> [{}]", rowLength, colLength);
        for (int i = 1; i < rowLength; i++) {
            Row row = sheet.getRow(i);
            if (null != row) {
                String certNumber = row.getCell(9).getStringCellValue();
                if (StringUtils.isNotBlank(certNumber)) {
                    log.debug("加密数据 -> [{}]", certNumber);
                    String secretNum = NumEncryptUtils.encrypt(certNumber, 1);
                    String cipherText = NumEncryptUtils.numSecret(certNumber, 1);
                    log.debug("解密密文 -> [{}], 脱敏数据 -> [{}]", cipherText, secretNum);

                    row.createCell(19).setCellValue(cipherText);
                    row.createCell(20).setCellValue(secretNum);
                }
                //row.createCell(2).setCellValue(content);
            }
        }

        //设置response的Header
        response.addHeader("Content-Disposition", "attachment;filename=phone." + ExcelUtils.XLSX_SUFFIX);
        OutputStream os = new BufferedOutputStream(response.getOutputStream());
        response.setContentType("application/vnd.ms-excel;charset=gb2312");
        //将excel写入到输出流中
        workbook.write(os);
        os.flush();
        os.close();
    }

    public static boolean isRowEmpty(Row row) {
        for (int c = row.getFirstCellNum(); c < row.getLastCellNum(); c++) {
            Cell cell = row.getCell(c);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                return false;
            }
        }
        return true;
    }

    @HttpMonitorLogger
    @ApiOperation("测试")
    @GetMapping(value = "/getAttrPath")
    public ResponseEntity<Result<?>> testMongoAttr(@RequestParam(value = "type") int type,
                                                   @RequestParam(value = "field_name", required = false) String fieldName) {
        if (StringUtils.isNotBlank(fieldName)) {
            return new ResponseEntity<>(new Result<>(InitMongoBean.getAttrPath(type, fieldName), errors), HttpStatus.OK);
        } else if (type == 1) {
            return new ResponseEntity<>(new Result<>(InitMongoBean.userMap, errors), HttpStatus.OK);
        } else if (type == 2) {
            return new ResponseEntity<>(new Result<>(InitMongoBean.orgMap, errors), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
        }
    }

    @HttpMonitorLogger
    @ApiOperation("测试处理用户动态字段")
    @GetMapping(value = "/testDealUserDynamic")
    public ResponseEntity<Result<?>> testDealUserDynamic(@RequestParam(value = "user_id", required = false) Long userId) {
        this.testService.dealUserDynamic(userId);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("同步用户微信头像和昵称")
    @GetMapping(value = "/pullThirdInfo")
    public ResponseEntity<Result<?>> pullThirdInfo(@RequestParam(value = "region_id") Long regionId,
                                                   @RequestParam(value = "is_all", defaultValue = "0") boolean isAll) {
        this.userThirdService.pullThirdInfo(regionId, isAll);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("单人同步用户微信头像和昵称")
    @GetMapping(value = "/pullThirdInfoByUserId")
    public ResponseEntity<Result<?>> pullThirdInfoByUserId(@RequestParam(value = "region_id") Long regionId,
                                                           @RequestParam(value = "user_ids") List<Long> userIds) {
        this.userThirdService.pullThirdInfoByUserId(userIds, regionId);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("手动执行回调定时任务")
    @GetMapping(value = "/submit-callback")
    public ResponseEntity<Result<?>> submitCallback(@RequestParam(value = "pageNo") Integer pageNo) {
        this.callbackScheduler.submitCallback(pageNo);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("上传烟草党员用户")
    @PostMapping(value = "/import-cqyc-user")
    public ResponseEntity<Result<?>> importCqycUser(@RequestParam(value = "region_id") Long regionId,
                                                    @RequestParam("file") MultipartFile file) {
        this.testService.importTabaccoUser(file, regionId);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("下载错误数据")
    @PostMapping(value = "/downloadError")
    public void downloadError(@RequestParam("file") MultipartFile file,
                              HttpServletResponse response) {
        this.testService.saveError(file, response);
    }

    @HttpMonitorLogger
    @ApiOperation("手动刷新缓存")
    @GetMapping(value = "/flush-cache")
    public ResponseEntity<Result<?>> flushCache() {
        this.openService.saveUserBaseInfoInRedis();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("手动刷新党员成长轨迹")
    @GetMapping(value = "/flush-user-highlight")
    public ResponseEntity<Result<?>> flushUserHighlight(@RequestHeader HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.testService.flushUserHighlight(header.getRegionId(), header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("刷新身份证")
    @GetMapping(value = "/data-process")
    public ResponseEntity<Result<?>> dataProcess(@RequestParam(value = "type") Integer type,
                                                 @RequestParam(value = "user_id", required = false) Long userId,
                                                 @RequestHeader HttpHeaders headers) {
        this.testService.dataProcess(type, userId, headers);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("转移数据")
    @GetMapping(value = "/transfer-data")
    public ResponseEntity<Result<?>> transferData(@RequestParam(value = "dynamic_field") String dynamicField,
                                                  @RequestParam(value = "field_name") String fieldName,
                                                  @RequestParam(value = "type") Integer type,
                                                  @RequestParam(value = "user_id", required = false) Long userId,
                                                  @RequestHeader HttpHeaders headers) {
        final HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
        this.testService.transferData(dynamicField, fieldName, type, userId, header);
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("手动广播所有党员到学习系统")
    @GetMapping(value = "/learn-user-push")
    public ResponseEntity<Result<?>> learnUserPush() {
        orgService.pushAllUserAndOrgToLearn();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }

    @HttpMonitorLogger
    @ApiOperation("手动广播所有组织到学习系统")
    @GetMapping(value = "/learn-org-push")
    public ResponseEntity<Result<?>> learnOrgPush() {
        orgService.pushAllOrgToLearn();
        return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
    }
}
