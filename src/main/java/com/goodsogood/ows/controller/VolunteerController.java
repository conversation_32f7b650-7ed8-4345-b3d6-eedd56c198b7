package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.common.Utils;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.helper.HeaderHelper;
import com.goodsogood.ows.model.db.UserEntity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.VolunteerInfoForm;
import com.goodsogood.ows.service.MessageService;
import com.goodsogood.ows.service.OpenService;
import com.goodsogood.ows.service.UserOrgCacheCallService;
import com.goodsogood.ows.service.UserService;
import com.goodsogood.ows.service.UserTagService;
import com.goodsogood.ows.service.VolunteerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

@RestController
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "志愿者", tags = {"志愿者"})
@Validated
@RequestMapping("/volunteer")
public class VolunteerController {

     private final Errors errors;
     private final UserService userService;
     private final VolunteerService volunteerService;
     private final MessageService messageService;
     private final UserOrgCacheCallService userOrgCacheCallService;
     private final UserTagService userTagService;
     private final OpenService openService;

     @Autowired
     public VolunteerController(Errors errors, UserService userService,
                                VolunteerService volunteerService, MessageService messageService,
                                UserOrgCacheCallService userOrgCacheCallService, UserTagService userTagService,
                                OpenService openService) {
          this.errors = errors;
          this.userService = userService;
          this.volunteerService = volunteerService;
          this.messageService = messageService;
          this.userOrgCacheCallService = userOrgCacheCallService;
          this.userTagService = userTagService;
          this.openService = openService;
     }

     @HttpMonitorLogger
     @GetMapping("/echoInfo")
     @ApiOperation("用户信息回显")
     public ResponseEntity<Result<?>> echoInfo(@RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          Long userId = header.getUserId();
          VolunteerInfoForm infoForm = new VolunteerInfoForm();
          if (null != userId) {
               UserEntity user = this.userService.selectNotVolunteerUser(userId);
               if (null != user) {
                    infoForm.setName(user.getName());
                    infoForm.setContactNumber(user.getPhoneSecret());
                    infoForm.setCertNumber(user.getCertNumberSecret());
               }
          }
          return new ResponseEntity<>(new Result<>(infoForm, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @GetMapping("/isVolunteer")
     @ApiOperation("是否是志愿者")
     public ResponseEntity<Result<?>> isVolunteer(@RequestHeader HttpHeaders headers) {
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          Long userId = header.getUserId();
          boolean isVolunteer = this.volunteerService.isVolunteer(userId);
          return new ResponseEntity<>(new Result<>(isVolunteer, errors), HttpStatus.OK);
     }

     @HttpMonitorLogger
     @PostMapping("/volunteer_register")
     @ApiOperation("志愿者注册")
     public ResponseEntity<Result<?>> registerVolunteer(@Valid @RequestBody VolunteerInfoForm infoForm,
                                                        BindingResult bindingResult,
                                                        @RequestHeader HttpHeaders headers) {
          int result = this.messageService.checkCode(infoForm.getContactNumber(), infoForm.getCode(), null);
          if (result != 1) {
               return ResponseEntity.ok(new Result<>(errors, result, HttpStatus.OK.value()));
          }
          HeaderHelper.SysHeader header = HeaderHelper.buildMyHeader(headers);
          Long userId = header.getUserId();
          boolean isVolunteer = this.volunteerService.isVolunteer(userId);
          if (isVolunteer) {
               return new ResponseEntity<>(new Result<>(errors, 5640, HttpStatus.OK.value()), HttpStatus.OK);
          }
          // 实名认证
          String str = this.openService.validateCertNumber(infoForm.getCertNumber(), infoForm.getName());
          Map<String, Object> rsMap = Utils.fromJson(str, Map.class);
          if (rsMap == null) {
               return new ResponseEntity<>(new Result<>(errors, 152, HttpStatus.OK.value()), HttpStatus.OK);
          }
          if (String.valueOf(rsMap.get(Constants.CERT_NUMBER_RS_CODE)).equals(Constants.CERT_NUMBER_RS_ERROR)) {
               return new ResponseEntity<>(new Result<>(errors, 151, HttpStatus.OK.value()), HttpStatus.OK);
          }
          // 保存字段
          this.userService.updateVolunteerInfo(infoForm, userId);
          // 绑定志愿者标签
          this.userTagService.addUserVolunteerTag(userId, header.getRegionId());
          // 刷新用户缓存
          this.userOrgCacheCallService.flushUserInfo(header.getRegionId(), userId, 1);
          return new ResponseEntity<>(new Result<>("success", errors), HttpStatus.OK);
     }

}
