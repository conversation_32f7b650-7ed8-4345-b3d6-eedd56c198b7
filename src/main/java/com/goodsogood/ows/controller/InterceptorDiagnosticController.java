package com.goodsogood.ows.controller;

import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 拦截器诊断控制器
 * 用于诊断MyBatis拦截器是否正确注册和工作
 * 
 * <AUTHOR>
 * @date 2024-08-07
 */
@RestController
@RequestMapping("/interceptor-diagnostic")
@Api(value = "拦截器诊断", tags = {"拦截器诊断"})
@Log4j2
public class InterceptorDiagnosticController {

    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;

    @Autowired
    private Errors errors;

    /**
     * 检查拦截器注册状态
     */
    @GetMapping("/check-registration")
    @ApiOperation("检查拦截器注册状态")
    public ResponseEntity<Result<Map<String, Object>>> checkInterceptorRegistration() {
        log.info("=== 开始检查拦截器注册状态 ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            result.put("sqlSessionFactoryCount", sqlSessionFactoryList.size());
            
            if (sqlSessionFactoryList.isEmpty()) {
                result.put("status", "ERROR");
                result.put("message", "没有找到SqlSessionFactory");
                return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            }
            
            for (int i = 0; i < sqlSessionFactoryList.size(); i++) {
                SqlSessionFactory factory = sqlSessionFactoryList.get(i);
                List<Interceptor> interceptors = factory.getConfiguration().getInterceptors();
                
                Map<String, Object> factoryInfo = new HashMap<>();
                factoryInfo.put("factoryClass", factory.getClass().getName());
                factoryInfo.put("interceptorCount", interceptors.size());
                
                // 检查每个拦截器
                for (int j = 0; j < interceptors.size(); j++) {
                    Interceptor interceptor = interceptors.get(j);
                    Map<String, Object> interceptorInfo = new HashMap<>();
                    interceptorInfo.put("class", interceptor.getClass().getName());
                    interceptorInfo.put("toString", interceptor.toString());
                    
                    // 检查是否是我们的拦截器
                    if (interceptor.getClass().getName().contains("SqlConversionAspect")) {
                        interceptorInfo.put("isOurInterceptor", true);
                        interceptorInfo.put("type", "SQL转换拦截器");
                    } else if (interceptor.getClass().getName().contains("SimpleTestInterceptor")) {
                        interceptorInfo.put("isOurInterceptor", true);
                        interceptorInfo.put("type", "简单测试拦截器");
                    } else {
                        interceptorInfo.put("isOurInterceptor", false);
                        interceptorInfo.put("type", "其他拦截器");
                    }
                    
                    factoryInfo.put("interceptor_" + j, interceptorInfo);
                }
                
                result.put("sqlSessionFactory_" + i, factoryInfo);
            }
            
            result.put("status", "SUCCESS");
            result.put("message", "拦截器注册状态检查完成");
            
            log.info("拦截器注册状态检查完成");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("检查拦截器注册状态时发生错误: {}", e.getMessage(), e);
            result.put("status", "ERROR");
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取MyBatis配置信息
     */
    @GetMapping("/mybatis-config")
    @ApiOperation("获取MyBatis配置信息")
    public ResponseEntity<Result<Map<String, Object>>> getMybatisConfig() {
        log.info("=== 开始获取MyBatis配置信息 ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (sqlSessionFactoryList.isEmpty()) {
                result.put("status", "ERROR");
                result.put("message", "没有找到SqlSessionFactory");
                return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            }
            
            SqlSessionFactory factory = sqlSessionFactoryList.get(0);
            org.apache.ibatis.session.Configuration config = factory.getConfiguration();
            
            result.put("databaseId", config.getDatabaseId());
            result.put("defaultExecutorType", config.getDefaultExecutorType().toString());
            result.put("mapUnderscoreToCamelCase", config.isMapUnderscoreToCamelCase());
            result.put("cacheEnabled", config.isCacheEnabled());
            result.put("lazyLoadingEnabled", config.isLazyLoadingEnabled());
            result.put("interceptorCount", config.getInterceptors().size());
            result.put("mapperRegistrySize", config.getMapperRegistry().getMappers().size());
            
            // 列出所有已注册的Mapper
            Map<String, String> mappers = new HashMap<>();
            config.getMapperRegistry().getMappers().forEach(mapperClass -> {
                mappers.put(mapperClass.getSimpleName(), mapperClass.getName());
            });
            result.put("registeredMappers", mappers);
            
            result.put("status", "SUCCESS");
            
            log.info("MyBatis配置信息获取完成");
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("获取MyBatis配置信息时发生错误: {}", e.getMessage(), e);
            result.put("status", "ERROR");
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 检查Spring容器中的拦截器Bean
     */
    @GetMapping("/check-beans")
    @ApiOperation("检查Spring容器中的拦截器Bean")
    public ResponseEntity<Result<Map<String, Object>>> checkInterceptorBeans() {
        log.info("=== 开始检查Spring容器中的拦截器Bean ===");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里可以通过ApplicationContext来检查Bean
            // 但由于我们没有直接注入ApplicationContext，先返回基本信息
            result.put("status", "INFO");
            result.put("message", "需要通过ApplicationContext来检查Bean状态");
            result.put("note", "请检查SqlConversionAspect和SimpleTestInterceptor是否被正确注册为Spring Bean");
            
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
            
        } catch (Exception e) {
            log.error("检查Spring容器中的拦截器Bean时发生错误: {}", e.getMessage(), e);
            result.put("status", "ERROR");
            result.put("error", e.getMessage());
            return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 获取诊断建议
     */
    @GetMapping("/diagnostic-suggestions")
    @ApiOperation("获取诊断建议")
    public ResponseEntity<Result<Map<String, Object>>> getDiagnosticSuggestions() {
        Map<String, Object> result = new HashMap<>();
        
        result.put("commonIssues", new String[]{
            "1. 拦截器类没有被Spring扫描到（缺少@Component注解）",
            "2. SqlConversionConfig配置类没有被执行（缺少@Configuration注解）",
            "3. SqlSessionFactory注入失败",
            "4. 拦截器注册时机不对（在SqlSessionFactory创建之后）",
            "5. 拦截器的@Intercepts注解配置错误",
            "6. MyBatis版本兼容性问题"
        });
        
        result.put("checkSteps", new String[]{
            "1. 检查应用启动日志，查看拦截器注册相关日志",
            "2. 访问 /interceptor-diagnostic/check-registration 检查注册状态",
            "3. 访问 /interceptor-diagnostic/mybatis-config 查看MyBatis配置",
            "4. 访问 /test/test-sql-interceptor 触发SQL执行",
            "5. 查看应用日志中是否有拦截器被调用的日志"
        });
        
        result.put("status", "INFO");
        
        return new ResponseEntity<>(new Result<>(result, errors), HttpStatus.OK);
    }
}
