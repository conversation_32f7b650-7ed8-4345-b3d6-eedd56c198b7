package com.goodsogood.ows.controller

import com.goodsogood.ows.component.Errors
import com.goodsogood.ows.model.vo.VrAddForm
import com.goodsogood.ows.service.VrScenesService
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import com.goodsogood.ows.model.vo.Result
import com.goodsogood.ows.model.vo.VrQueryForm
import com.goodsogood.ows.model.vo.VrUpdateForm
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.validation.BindingResult
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/vr")
@CrossOrigin(origins = ["*"], maxAge = 3600)
@Api(value = "VR场景控制层", tags = ["vr"])
@Validated
class VrScenesController (@Autowired val errors: Errors,
                          @Autowired val vrScenesService: VrScenesService) {

    @PostMapping("/add")
    @ApiOperation("新增VR场景")
    fun add(@Valid @RequestBody form: VrAddForm,
            bindingResult: BindingResult,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.add(form, headers) , errors), HttpStatus.OK)
    }

    @PostMapping("/update")
    @ApiOperation("更新VR场景")
    fun update(@Valid @RequestBody form: VrUpdateForm,
               bindingResult: BindingResult,
               @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.update(form, headers) , errors), HttpStatus.OK)
    }

    @GetMapping("/del/{scenes_id}")
    @ApiOperation("删除VR场景")
    fun del(@PathVariable("scenes_id") scenesId: Long,
               @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.del(scenesId, headers) , errors), HttpStatus.OK)
    }

    @GetMapping("/get/{scenes_id}")
    @ApiOperation("获取VR场景")
    fun get(@PathVariable("scenes_id") scenesId: Long,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.get(scenesId) , errors), HttpStatus.OK)
    }

    @GetMapping("/get-by-org/{org_id}")
    @ApiOperation("获取VR场景")
    fun getByOrg(@PathVariable("org_id") orgId: Long,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.getByOrg(orgId) , errors), HttpStatus.OK)
    }

    @PostMapping("/list")
    @ApiOperation("获取VR场景列表")
    fun post(@Valid @RequestBody form: VrQueryForm,
             bindingResult: BindingResult,
            @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.list(form) , errors), HttpStatus.OK)
    }

    @GetMapping("/list-by-ids")
    @ApiOperation("根据ids获取VR场景列表")
    fun listByIds(@RequestParam("ids") ids: List<Long>,
                bindingResult: BindingResult,
                @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.listByIds(ids) , errors), HttpStatus.OK)
    }

    @GetMapping("/statistical-data")
    @ApiOperation("统计VR数据")
    fun statisticalData(@RequestParam("org_id", required = false) orgId: Long? = null,
             @RequestHeader headers: HttpHeaders
    ): ResponseEntity<Result<Any>> {
        return ResponseEntity(Result(vrScenesService.statisticalData(orgId) , errors), HttpStatus.OK)
    }
}