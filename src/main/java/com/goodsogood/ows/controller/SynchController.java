package com.goodsogood.ows.controller;

import com.aidangqun.log4j2cm.annotation.HttpMonitorLogger;
import com.goodsogood.ows.component.Errors;
import com.goodsogood.ows.model.db.Org12371Entity;
import com.goodsogood.ows.model.db.User12371Entity;
import com.goodsogood.ows.model.vo.Result;
import com.goodsogood.ows.model.vo.SynchOrgForm;
import com.goodsogood.ows.model.vo.SynchUserForm;
import com.goodsogood.ows.service.SynchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName SynchController
 * @date 2018-08-20 14:36
 */
@RestController
@RequestMapping("")
@Log4j2
@CrossOrigin(origins = "*", maxAge = 3600)
@Api(value = "12371测试接口", tags = {"12371测试接口"})
@Validated
public class SynchController {

    private final Errors errors;
    private final SynchService synchService;
    private final RestTemplate jdgcRestTemplate;

    @Autowired
    public SynchController(Errors errors, SynchService synchService, RestTemplate jdgcRestTemplate) {
        this.errors = errors;
        this.synchService = synchService;
        this.jdgcRestTemplate = jdgcRestTemplate;
    }


    /**
     * 模拟同步12371用户数据
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/test/mem-list")
    @ApiOperation("模拟同步12371用户数据")
    public ResponseEntity<Result<?>> testMemList(){

        List<User12371Entity> list = this.synchService.getAllUser12371List();
        List<SynchUserForm> paramList = new ArrayList<>();
        for (User12371Entity entity : list) {
            SynchUserForm form = new SynchUserForm();
            BeanUtils.copyProperties(entity,form);
            paramList.add(form);
        }
        this.synchService.memList(paramList,1);
        return new ResponseEntity<>(new Result<>("success",errors), HttpStatus.OK);
    }

    /**
     * 模拟同步12371组织数据
     *
     * @return
     */
    @HttpMonitorLogger
    @PostMapping("/test/to-org-info")
    @ApiOperation("模拟同步12371组织数据")
    public ResponseEntity<Result<?>> testToOrgInfo(){

        List<Org12371Entity> list = this.synchService.getAllOrg12371List();
        List<SynchOrgForm> paramList = new ArrayList<>();
        for (Org12371Entity entity : list) {
            SynchOrgForm form = new SynchOrgForm();
            BeanUtils.copyProperties(entity,form);
            paramList.add(form);
        }
        this.synchService.toOrgInfo(paramList,1);
        return new ResponseEntity<>(new Result<>("success",errors), HttpStatus.OK);
    }


}
