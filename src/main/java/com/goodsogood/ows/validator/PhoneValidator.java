package com.goodsogood.ows.validator;
import com.goodsogood.ows.validator.constraints.CheckPhone;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 手机号自定义验证
 * <AUTHOR>
 * @date 2018/6/29
 */
public class PhoneValidator implements ConstraintValidator<CheckPhone, String> {

    @Override
    public void initialize(CheckPhone checkPhone) {

    }
    @Override
    public boolean isValid(String value, ConstraintValidatorContext constraintValidatorContext) {
        //手机号的正则，包括手机明文以及手机脱敏
        // String regex = "^(1([34578])\\d{9})|(1([34578])\\d(\\*){4}\\d{4})$";
        String regex = "^(1\\d{10})|(1\\d{2}(\\*){4}\\d{4})$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(value);
        return m.matches();
    }
}
