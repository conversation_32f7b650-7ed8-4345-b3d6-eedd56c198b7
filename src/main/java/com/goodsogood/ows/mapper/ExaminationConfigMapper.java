package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.ExaminationConfigEntity;
import com.goodsogood.ows.model.db.ExaminationOrganizationEntity;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2020-12-24 16:44
 **/
@Repository
@Mapper
public interface ExaminationConfigMapper extends MyMapper<ExaminationConfigEntity>{

    @Select("select op_key,op_value from t_examination_config where channel=#{channel}")
    Map<String,String> getConfigByChannel(String channel);


    @Select("select op_key as opKey,op_value as opValue from t_examination_config where channel=#{channel}")
    List<ExaminationConfigEntity> getByChannel(String channel);

    @Delete("delete from t_examination_organization where channel=#{channel}" +
            " and region_id=#{regionId} and (org_id=#{orgId} OR org_level LIKE CONCAT('%-',#{orgId},'-%')); ")
    Integer delOrgInfo(@Param(value = "channel") String channel,
                       @Param(value = "orgId")Long orgId,
                       @Param(value = "regionId")Long regionId);


    @Select("select org_id as orgId, examination_gid as examinationGid from t_examination_organization " +
            " where channel=#{channel}" +
            " and region_id=#{regionId} and org_id=#{orgId}  ")
    ExaminationOrganizationEntity getTopOrgInfo(@Param(value = "channel") String channel,
                                                @Param(value = "orgId") Long orgId,
                                                @Param(value = "regionId") Long regionId);



    @Update("update t_examination_config set op_value=#{opValue}  where op_key=#{opKey} and  channel=#{channel} ")
    Integer updateRegionConfig(@Param(value = "opKey") String opKey,
                               @Param(value = "opValue")  String content,
                               @Param(value = "channel") String channel);
}