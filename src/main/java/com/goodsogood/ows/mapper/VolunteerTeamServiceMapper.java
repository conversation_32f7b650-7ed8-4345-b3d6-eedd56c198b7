package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.VolunteerTeamServiceEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-06-23 15:37
 **/
@Repository
@Mapper
public interface VolunteerTeamServiceMapper extends MyMapper<VolunteerTeamServiceEntity> {

     @Insert("<script><foreach collection=\"list\" item=\"item\" index=\"index\" separator=\";\">" +
             "insert into t_volunteer_team_service (volunteer_team_id,service_id) values " +
             "(#{volunteerTeamId},#{item})</foreach></script>")
     void batchInsertTeamService(@Param("list") List<Integer> list, @Param("volunteerTeamId") Long volunteerTeamId);

     @Select("select service_id from t_volunteer_team_service where volunteer_team_id = #{volunteerTeamId}")
     List<Integer> findServices(@Param("volunteerTeamId") long volunteerTeamId);
}
