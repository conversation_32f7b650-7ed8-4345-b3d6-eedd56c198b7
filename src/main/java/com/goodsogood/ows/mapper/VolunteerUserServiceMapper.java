package com.goodsogood.ows.mapper;

import com.goodsogood.ows.model.db.VolunteerUserServiceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/23
 * Description:用户服务领域 服务类别
 */
@Repository
@Mapper
public interface VolunteerUserServiceMapper extends MyMapper<VolunteerUserServiceEntity> {

    @Select("select service_id from t_volunteer_user_service where volunteer_user_id=#{volunteerUserId} and type=#{volunteerUserServiceType}")
    List<Integer> findByVolunteerId(@Param("volunteerUserId") Long volunteerUserId, @Param("volunteerUserServiceType") int volunteerUserServiceType);
}
