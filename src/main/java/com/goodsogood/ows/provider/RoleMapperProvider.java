package com.goodsogood.ows.provider;

import com.goodsogood.ows.common.Constants;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.jdbc.SQL;

/**
 * 角色provider
 *
 * <AUTHOR>
 * @date 2018-04-08
 */
public class RoleMapperProvider {

    public String getRoleByName(String name, int status, int type, long oId, long regionId) {
        return new SQL() {
            {
                SELECT("role_id,name");
                FROM("t_role");
                WHERE("role_type != 3 and status = " + status + " and org_type = " + type + " and region_id = " + regionId);
                if (Constants.DEP_TYPE_CORP == type) {
                    AND();
                    WHERE("corporation_id = " + oId);
                }
                if (Constants.DEP_TYPE_ORG == type) {
                    AND();
                    WHERE("organization_id = " + oId);
                }
                if (!StringUtils.isBlank(name)) {
                    AND();
                    WHERE("name like '%" + name + "%'");
                }
            }
        }.toString();
    }
}
