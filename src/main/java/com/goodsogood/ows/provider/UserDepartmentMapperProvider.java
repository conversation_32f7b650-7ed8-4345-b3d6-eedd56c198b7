package com.goodsogood.ows.provider;

import com.goodsogood.ows.common.Constants;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.jdbc.SQL;

/**
 * 用户部门provider
 *
 * <AUTHOR>
 * @date 2018-03-26
 */
public class UserDepartmentMapperProvider {

    /**
     * 根据名称查询人员
     *
     * @param userName
     * @param
     *
     * @return
     *
     * @deprecated by 许力多 20220328 有SQL注入风险
     */
    @Deprecated()
    public String getUser(String userName, long oId, int type) {
        return new SQL() {
            {
                SELECT("a.user_id user_Id,a.name name,a.phone phone");
                FROM("t_user a,t_user_org_corp b");
                WHERE("a.user_id = b.user_id");
                AND();
                WHERE(" a.name like '%" + userName + "%'");
                if (type == Constants.DEP_TYPE_CORP) {
                    AND();
                    WHERE("b.corporation_id = " + oId);
                } else if (type == Constants.DEP_TYPE_ORG) {
                    AND();
                    WHERE("b.organization_id = " + oId);
                }
            }
        }.toString();
    }

    /**
     * 查询部门管理员
     *
     * @param did
     * @param duty
     *
     * @return
     */
    public String getDepManager(long did, int duty) {
        return new SQL() {
            {
                SELECT("a.user_id user_id,a.name name,a.phone phone,b.department_id department_id");
                FROM("t_user a,t_user_department b");
                WHERE("a.user_id = b.user_id");
                AND();
                WHERE("b.department_id = " + did);
                AND();
                WHERE("b.duty = " + duty);
            }
        }.toString();
    }

    private String fromStr(String userName, long oId, int type) {
        String sql = "t_user_department a,t_department b,(" +
                "SELECT u.user_id user_Id,u.name name,u.phone phone FROM t_user u,t_user_org_corp o WHERE u.user_id" +
                "= o.user_id ";
        if (!"".equals(userName) && userName != null) {
            sql += "and  u.name like '%" + userName + "%'";
        }
        if (type == Constants.DEP_TYPE_CORP) {
            sql += " and o.corporation_id = " + oId + ") c";
        } else if (type == Constants.DEP_TYPE_ORG) {
            sql += " and o.organization_id = " + oId + ") c";
        }
        return sql;
    }

    /**
     * 查询部门人员
     *
     * @param userName
     * @param oId
     * @param type
     *
     * @return
     *
     * @deprecated by 许力多 20220328 有SQL注入风险
     */
    @Deprecated
    public String getDepUser(String userName, long oId, int type, Long depId) {
        return new SQL() {
            {
                SELECT(" distinct u.user_id user_Id,u.name name,u.phone_secret phone, " +
                        "(CASE d.is_root WHEN 1 THEN null WHEN 0 THEN d.name END) `department_name`," +
                        "(CASE d.is_root WHEN 1 THEN null WHEN 0 THEN d.department_id END) `department_id`");
                FROM(" t_user u LEFT JOIN t_user_org_corp o on u.user_id = o.user_id " +
                        " LEFT JOIN t_user_department c on u.user_id = c.user_id " +
                        " LEFT JOIN t_department d on c.department_id = d.department_id ");
                if (type == Constants.DEP_TYPE_CORP) {
                    WHERE(" o.corporation_id = " + oId);
                } else if (type == Constants.DEP_TYPE_ORG) {
                    WHERE(" o.organization_id = " + oId);
                }
                if (!StringUtils.isBlank(userName)) {
                    AND();
                    WHERE(" u.name like '%" + userName + "%'");
                }
                if (depId != null) {
                    AND();
                    WHERE(" d.department_id = " + depId);
                } else {
                    GROUP_BY("u.user_id");
                }
            }
        }.toString();
    }
}
