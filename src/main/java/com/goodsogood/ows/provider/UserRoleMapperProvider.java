package com.goodsogood.ows.provider;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.model.db.UserOrgAndCorpEntity;
import org.apache.ibatis.jdbc.SQL;

import java.util.concurrent.ConcurrentMap;

/**
 * 用户角色provicder
 *
 * <AUTHOR>
 * @date 2018-03-27
 */
public class UserRoleMapperProvider {

    public String updateByWhere(ConcurrentMap<String,Object> map){
        return new SQL(){
            {
                UPDATE("t_user_role");
                SET("role_id = " + map.get("newRoleId"));
                WHERE("role_id = "+ map.get("oldRoleId"));
                if (Integer.parseInt(map.get("type").toString()) == Constants.DEP_TYPE_ORG){
                    AND();
                    WHERE("organization_id = " + map.get("oId"));
                }else if (Integer.parseInt(map.get("type").toString()) == Constants.DEP_TYPE_CORP){
                    AND();
                    WHERE("corporation_id = " + map.get("oId"));
                }
            }
        }.toString();
    }

    public String getSysList(ConcurrentMap<String,Object> map){
        return new SQL(){
            {
                SELECT("distinct a.user_id user_id,a.name name,a.phone_secret phone,b.duty duty,c.role_id `role_id`");
                FROM("t_user a RIGHT JOIN t_user_department b on a.user_id = b.user_id LEFT JOIN t_user_role c " +
                        "on a.user_id = c.user_id RIGHT JOIN t_user_org_corp d on a.user_id = d.user_id");
                if (Constants.DEP_TYPE_CORP == (int)map.get("type")) {
                    WHERE("d.corporation_id = #{oId}");
                } else if (Constants.DEP_TYPE_ORG == (int)map.get("type")) {
                    WHERE("d.organization_id = #{oId}");
                }
                AND();
                if ((int)map.get("sys") == 1) {
                    WHERE("b.duty <> #{duty}");
                } else {
                    WHERE("b.duty <> #{duty} and b.duty <> 99");
                }
                if (!"".equals(map.get("name")) && map.get("name") != null) {
                    AND();
                    WHERE("a.name like '%" + map.get("name") + "%'");
                }
            }
        }.toString();
    }

    public String getRoleList(UserOrgAndCorpEntity entity){
        return new SQL(){
            {
                SELECT("ur.role_id");
                FROM("t_user_role ur");
                WHERE("ur.user_id = " + entity.getUserId());
                AND();
                WHERE("ur.organization_id = " + entity.getOrganizationId());
                AND();
                WHERE("EXISTS(select 1 from t_role r where r.role_id = ur.role_id and r.`status` = 1)");
            }
        }.toString();
    }

}
