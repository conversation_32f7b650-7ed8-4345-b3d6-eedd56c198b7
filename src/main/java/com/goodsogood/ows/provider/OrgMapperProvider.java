package com.goodsogood.ows.provider;

import org.apache.ibatis.jdbc.SQL;

import java.util.List;

public class OrgMapperProvider {

    public String getOrgInformationIntegrity(List<String> fields, long orgId){
        return new SQL(){
            {
                StringBuffer selectSql = new StringBuffer();
                for (int i = 0; i < fields.size(); i++) {
                    selectSql.append(fields.get(i).concat("_v"));
                    if (i + 1 != fields.size()) {
                        selectSql.append(" + ");
                    }
                }
                SELECT(selectSql.toString());
                String tableSql = "(" + getOrgInformationTable(fields, orgId) +")a";
                FROM(tableSql);
            }
        }.toString();
    }

    public String getOrgInformationTable(List<String> fields, long orgId){
        return new SQL(){
            {
                StringBuffer selectSql = new StringBuffer();
                for (int i = 0; i < fields.size(); i++) {
                    String field = fields.get(i);
                    selectSql.append("case when (isnull(" + field + ") || LENGTH(trim(" + field + "))<1) then 0 else 1 end " + field + "_v");
                    if (i + 1 != fields.size()) {
                        selectSql.append(", ");
                    }
                }
                SELECT(selectSql.toString());
                FROM("t_organization");
                WHERE("organization_id = " + orgId);
            }
        }.toString();
    }
}
