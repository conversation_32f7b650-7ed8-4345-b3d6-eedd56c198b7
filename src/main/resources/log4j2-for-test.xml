<?xml version="1.0" encoding="UTF-8"?>
<!--<configuration status="TRACE">-->
<configuration static="OFF">
    <properties>
        <property name="LEVEL">DEBUG</property>
        <property name="APP_NAME">ows-user-center</property>
        <property name="LOG_HOME">/var/log/webapps/${APP_NAME}</property>
        <property name="isThreadContextMapInheritable">true</property>
    </properties>

    <appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>
        </Console>

<!--        <RocketMQ name="RocketMQAppender" producerGroup="ows-log-group-test" nameServerAddress="*************:9876"-->
<!--                  topic="ows-log-topic-test" tag="ows-log"  accessKey="LTAIB8LtE2CvPHC6"-->
<!--                  secretKey="9pMonbKn1UeX7RzJf6fKUys8xg4Z71">-->
<!--            <JsonLayout propertiesAsList="true" includeStacktrace="true" stacktraceAsString="true" includeTimeMillis="true">-->
<!--                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd HH:mm:ss.SSS}" />-->
<!--                <KeyValuePair key="server-name" value="$${APP_NAME}" />-->
<!--                <KeyValuePair key="start_time" value="$${ctx:start_time}" />-->
<!--                <KeyValuePair key="type" value="$${ctx:type}" />-->
<!--                <KeyValuePair key="name" value="$${ctx:name}" />-->
<!--                <KeyValuePair key="tracker_id" value="$${ctx:tracker_id}" />-->
<!--                <KeyValuePair key="span_id" value="$${ctx:span_id}" />-->
<!--                <KeyValuePair key="p_span_id" value="$${ctx:p_span_id}" />-->
<!--                <KeyValuePair key="env" value="$${ctx:env}" />-->
<!--            </JsonLayout>-->
<!--        </RocketMQ>-->

        <RabbitMQ name="RabbitMQ" host="*************" port="5672" username="gsg" password="gsg"
                  exchange="ows.log.dev.fanout" queue="ows-log-queue-test" virtualHost="/">
            <JsonLayout propertiesAsList="true" includeStacktrace="true" stacktraceAsString="true"
                        includeTimeMillis="true">
                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd HH:mm:ss.SSS}"/>
                <KeyValuePair key="server-name" value="$${APP_NAME}"/>
                <KeyValuePair key="start_time" value="$${ctx:start_time}"/>
                <KeyValuePair key="type" value="$${ctx:type}"/>
                <KeyValuePair key="name" value="$${ctx:name}"/>
                <KeyValuePair key="tracker_id" value="$${ctx:tracker_id}"/>
                <KeyValuePair key="span_id" value="$${ctx:span_id}"/>
                <KeyValuePair key="p_span_id" value="$${ctx:p_span_id}"/>
                <KeyValuePair key="env" value="$${ctx:env}"/>
            </JsonLayout>
        </RabbitMQ>

        <!-- 配置日志输出文件名字     追加读写 -->
        <RollingRandomAccessFile name="RollingRandomAccessFile" fileName="${LOG_HOME}/${APP_NAME}-${LEVEL}.log"
                                 filePattern="${LOG_HOME}/${APP_NAME}-${LEVEL}-%d{yyyy-MM-dd}.log">
            <!-- 输出格式 -->
            <PatternLayout pattern="[%d{ABSOLUTE}][%-5p][%-25c][%t] [%X{tracker_id}] [%X{span_id}] %m%n"/>
            <!-- 设置策略 -->
            <Policies>
                <!-- 基于时间的触发策略。该策略主要是完成周期性的log文件封存工作。有两个参数：
                interval，integer型，指定两次封存动作之间的时间间隔。单位:以日志的命名精度来确定单位，
                比如yyyy-MM-dd-HH 单位为小时，yyyy-MM-dd-HH-mm 单位为分钟
                modulate，boolean型，说明是否对封存时间进行调制。若modulate=true，
                则封存时间将以0点为边界进行偏移计算。比如，modulate=true，interval=4hours，
                那么假设上次封存日志的时间为03:00，则下次封存日志的时间为04:00，
                之后的封存时间依次为08:00，12:00，16:00
                -->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingRandomAccessFile>

    </appenders>
    <loggers>

        <!--
            使用了全局异步模式
            异步模式可能会造成日志顺序有问题，因此最好在业务log中带上日期或者相关的task
        -->
        <logger name="com.aidangqun.log4j2cm.aop.HttpLogAspect" level="INFO" includeLocation="true" additivity="false">
            <AppenderRef ref="RabbitMQ"/>
        </logger>

        <logger name="com.goodsogood.ows" level="DEBUG" includeLocation="true" additivity="false">
            <AppenderRef ref="RabbitMQ"/>
            <AppenderRef ref="Console"/>
        </logger>

        <!--过滤掉spring一些无用的DEBUG信息-->
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.apache" level="ERROR"/>
        <logger name="com.netflix" level="ERROR"/>
        <logger name="org.hibernate" level="ERROR"/>
        <logger name="org.elasticsearch" level="ERROR"/>
        <logger name="tk" level="ERROR"/>
        <logger name="io.lettuce" level="ERROR"/>
        <logger name="jdbc.sqltiming" level="OFF"/>
        <logger name="jdbc.resultset" level="OFF"/>
        <logger name="jdbc.connection" level="OFF"/>
        <logger name="jdbc.audit" level="OFF"/>

        <!-- 为sql跟踪配置一个logger -->
        <logger name="jdbc.sqlonly" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RabbitMQ"/>
        </logger>
        <logger name="jdbc.resultsettable" level="INFO" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="RabbitMQ"/>
        </logger>
        <logger name="log4jdbc.log4j2" level="INFO" additivity="false">
            <MarkerFilter marker="LOG4JDBC_OTHER" onMatch="DENY" onMismatch="NEUTRAL"/>
            <appender-ref ref="Console"/>
            <appender-ref ref="RabbitMQ"/>
        </logger>
        <logger level="DEBUG">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingRandomAccessFile"/>
        </logger>
    </loggers>
</configuration>
