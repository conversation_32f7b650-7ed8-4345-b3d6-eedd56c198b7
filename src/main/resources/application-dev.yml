#端口
sys-port: 8106
#节点
sys-id: 1
#渠道
sys-channel: cqyc.gray
#config dir
sys-config-dir: -gray

#数据库 - 达梦数据库（唯一数据源）
sys-dm-db:
  db-url: ***************:55237
  db-name: GS_OWS_ZY_GRAY
  db-user: SYSDBA
  db-password: Dameng@123456
#redis
sys-redis-config:
  database: 86
  password: 1234567890
  host: **************
  port: 16379


sys-mongodb.uri: **************
sys-mongodb.userName: zy_test_uto
sys-mongodb.password: Qwer1234..
sys-mongodb.databaseName: gs_ows_zy_test

#微信中心注册
sys-wechat:
  project-name: owsszf
  #渠道外网域名
  channel: cqjgdj.gov.com
  #回调微信H5的入口地址服务名
  #市值机关
  h5: owsswx

#初始化用户换成
sys-init: false
#是否开启定时任务
sys-scheduler: false
#同步12371接口服务器
sys-scheduler-12731: false
#定时任务的开关
sys-scheduler-committee: false
#区县label配置
regionLabel: V4.0.0
#回调定时任务开关
callbackRun: false
#生日定时任务
birth-scheduler: false
#快照定时任务
snapshotScheduler: false

#通用配置
login:
  auth-code:
    usable: false

error-message:
  errors:
    9999: 当前接口已废除
    9901: 未知错误
    9902: 当前请求过多或服务器繁忙，请稍后再试
    9903: 调用外部接口发生错误
    9904: 当前客户端版本已经过期
    9905: 登录失效请重新登录
    9404: ${0}找不到
    401: 该部门中有人员，不可删除
    402: 部门存在下级部门的不能删除
    403: 该部门不存在
    404: 该部门下有活动，不允许删除
    111: 权限分配失败
    112: 删除角色失败
    113: 修改角色失败
    114: 默认角色不能修改为自定义角色
    115: 操作管理员账号失败
    116: 移除管理员失败
    117: 移交管理员失败
    118: 账号或密码错误
    119: 登录超时,请重新登录
    120: JSON转换失败
    121: 获取缓存失败
    122: 您不是组织成员 或者 您的所在组织未激活
    123: 获取验证码失败
    124: 验证码已失效,请重新获取
    125: 手机号码已经存在
    126: 输入密码不一致
    127: 更新用户积分失败
    128: 该手机号未注册
    129: 默认权限不能删除
    130: 系统保留标签不能删除
    131: 删除标签失败
    132: 批量移动员工失败
    133: 该人员已经是超级管理员
    134: 系统异常，新增用户失败
    135: 系统异常，修改用户信息失败
    136: 系统异常，修改标签失败
    137: 该人员已经是管理员
    138: 未实名用户注册失败
    139: 验证码已失效，请刷新重新获取
    140: 验证码输入有误，请重新输入
    141: 系统异常，登录失败
    142: 系统异常，绑定用户信息失败
    143: 系统异常，离职操作失败
    144: 已经是超级管理员
    145: 已存在同名的标签
    146: 系统异常，编辑部门失败
    147: 不能选择待编辑部门为上级部门
    148: 不能选择待编辑部门的下级部门作为上级部门
    149: 身份证号码格式错误
    150: 超级管理员不允许离职
    151: 身份证合法校验失败，请填写正确的身份证号和姓名
    152: 身份证合法校验系统繁忙，请稍后再试
    170: 您已经不是组织成员，无法登录
    171: 您无法离职您自己
    174: 添加用户失败
    175: 上传的数据已失效，请重新上传组织会员数据
    176: 图片验证码已失效，请重新获取
    177: 图片验证码错误，请重新输入
    178: 系统异常，上传组织人员数据失败
    179: 批量导入组织人员失败，导入模板不匹配，请使用最新的导入模板
    180: 系统异常，设置密码失败
    181: 组织标识码不存在，请重新输入
    182: 系统异常，激活组织失败
    183: 系统异常，批量新增用户失败
    184: 系统异常，删除组织用户失败
    185: 手机号码格式错误
    186: 系统异常，身份证或者手机号码密文转明文失败
    187: 该组织已经被激活，无法重复激活
    188: ${0}组织未激活
    189: 系统异常，查询组织人员待导入信息失败
    190: 组织处于禁用状态，不允许激活
    191: 身份证号已存在
    192: 手机号码${0}已经存在，请重新输入
    193: 系统异常，获取异步批量上传组织人员结果失败
    194: 系统异常，修改用户昵称失败
    195: 公安部实名认证系统繁忙，请稍后再试或一次性提交少量证件
    196: 昵称限2~8个汉字
    197: 系统暂未查询到您的个人信息
    198: 用户ID不能为空
    199: 用户不存在
    200: 用户信息不完全
    201: 验证码输入有误，请重新输入
    202: 请重新输入自己正确的姓名或身份证号码
    205: 当前身份证号码已经被绑定，请重新输入
    208: 不允许删除管理员
    209: 只能将管理员权限移交给普通组织成员
    210: 所选组织人员不存在，请重新选择
    211: 所选人员信息不完整，请完善其必填信息后再试
    212: 普通组织成员无移交管理员权限
    216: 请稍后再试
    217: 组织类型不能为空
    218: 用户政治面貌格式错误
    219: ${0}组织不存在
    220: 您没有操作权限
    221: 所属组织不能选择当前组织
    222: 当前组织未绑定公众号
    228: 身份信息和手机号不匹配
    230: 通用权限不允许删除
    231: 已使用权限不允许删除
    232: 获取短信验证码时间间隔未到
    600: 组织名称已存在
    601: 组织Id不能为空
    602: 组织Id无效
    603: 组织代码已经存在
    604: 所选上级组织，不能为当前组织的下级
    605: 所选上级组织不能为自己
    606: 抱歉，您暂无权限下载该组织标识码
    607: 组织类型不能为空
    608: 组织树名称已存在
    609: 组织树Id不能为空
    610: 父树已经存在请勿重复创建
    700: 未绑定公众号
    901: 用户数据异常
    902: 操作失败, 该人员已在其他组织缴纳党费

    4000: 请选择角色
    4001: 您所在组织已被删除
    4002: 请选择组织
    4003: 选择组织无效
    4004: 请选择用户组
    4005: 请选择用户
    4006: 指令不存在
    4007: 新增失败,选择人员中包含其他组织成员
    4008: 重复加入人员
    4009: 请选择角色
    4010: 系统异常, 当前数据不一致
    4011: 当前用户重复添加组织权限
    4012: 请先移除用户组下的用户
    4013: 当前用户组已被禁用
    4014: 请重新选择用户组
    4015: 用户组角色重复
    4016: 请选择菜单
    4017: 已经存在该版本号
    4018: 当前升级日志记录不存在

    4600: 创建领导班子成员失败
    4601: 未查询到组织信息
    4602: 未查询到 ${0} 信息
    4603: ${0}不是下级组织
    4604: 修改领导班子成员失败
    4605: 领导班子主键id为空
    4606: 未查询到领导班子信息
    4607: 不能修改人员
    4608: 不能修改组织信息
    4609: 删除领导班子失败
    4610: 当前操作人所属组织类型不是单位
    4611: 不是上级单位不能操作
    4612: 请勿重复提交分管部门
    4613: 查询的组织类型不是事业单位
    4614: 不是单位组织无法创建领导班子
    4615: 未查询到操作人所属组织
    4616: 操作人所属组织不是新增领导班子所属组织的上级,不能操作
    4617: URL解码失败:${0}
    4618: 最少填写两个字
    4619: 查询的组织不能为空
    4620: 联系支部组织Id不能为空
    4621: 不能选择该领导所在的支部

    4650: 创建组织届次失败
    4651: 新增的换届信息所属组织类型不是党组织
    4652: 不是上级组织不能操作
    4653: 修改组织届次失败
    4654: 未查询到组织届次信息
    4655: 不能修改组织届次所属组织

    4500: 党小组名称已存在！
    4501: 新增党小组用户不能为空！
    4502: 没有对该组织的党小组操作的权限！
    4503: 该数据已经被删除过了！
    4504: 更新数据失败,数据已被删除！
    4505: 没有对该党小组成员操作的权限！
    4506: 党小组已被删除，不能执行操作！
    4507: 用户已是党小组成员,不能加人当前党小组:${0}！
    4508: 不属于当前组织及其所有下级组织的用户:${0}！
    4509: 非党组织没有党小组操作权限！
    4510: 党小组下级不能再创建组织

    4550: 委员会已被删除不能添加成员！
    4551: 委员会已被删除不能操作委员会成员！
    4552: 该数据已经被删除！
    4553: 没有对该委员会成员操作的权限！
    4554: 委员会已被删除不能修改成员信息！
    4555: 非党组织没有委员会操作权限！

    3601: 系统异常，人员新增标签失败
    3602: 出现异常标签，操作人员标签失败
    3603: 党费标签异常，操作人员标签失败
    3604: 系统标签，不能编辑
    3605: 最多设置20个标签

    5600: 用户数据不能为空
    5601: 姓名为[${0}]的电话号码[${1}]已被其他用户使用
    5602: 人员[${0}]不是组织成员
    5603: 人员[${0}][${2}]不存在
    5604: 未配置project_name

    5610: 党组织委员会成员必须添加
    5611: 纪律检查委员会成员必须添加
    5612: 党委办公室成员必须添加
    5613: 当前组织类型不可操作纪律检查委员会
    5614: 当前组织类型不可操作党委办公室
    5615: period_id不能为空
    5616: 该用户已存在不能重复添加！
    5617: 存在多个相同用户 ${0} 信息不一致
    5618: 不能添加纪律检查委员会成员
    5619: 不能添加党务办公室成员
    5620: 不能操作纪律检查委员会
    5621: 不能操作党务办公室
    5630: 参数错误：${0}   #******* 用户收货地址同步
    5631: 消息重复推送,addressId:${0}  #******* 用户收货地址同步
    5632: 所在组织关系与公众号绑定不匹配，无法绑定
    5633: 游客用户基本信息有误
    5640: 请勿重复注册志愿者
    5641: 未查询到有效同步组织
    5700: 数据已存在该字段名

    6000: 志愿团体名称已经存在
    6001: 不允许删除启用状态的团体
    6002: 该组织未注册志愿团体
    6003: 上级志愿团体不存在
    6201: 未查询到该志愿者
    6202: 未查询到志愿团队
    6203: 存在重复用户ID[${0}],请联系管理员
    6204: 存在重复手机号码[${0}],请联系管理员

    30000: 配置区县ID不存在
    30001: 系统异常，权限不足
    30002: 请先删除该组织的下级组织
    30003: 请先删除该组织下的人员
    30004: 基础党组织不能变更为${0}
    30005: ${0}不能变更为基础党组织
    30006: 请先删除该团体下的志愿者

    30007: 该用户存在其他组织关系，请先转出。
    30008: 发展党员的政治面貌只能是预备党员
    30009: 入党时间不能晚于转正时间
    30010: 查询集合中必须包含own，否则查询不出结果集
    30011: 手机号已绑定
    30013: 当前用户不需要绑定
    30014: 党员同志你好，请进入智慧党建平台进行绑定

    31000: 新增失败，${0}区域已存在该菜单
    31001: 新增失败，菜单名称重复
    31002: 区县列表为空
    31003: 当前菜单在${0}区域下存在多条
    31004: 当前菜单在${0}区域下不存在
    31005: 该菜单下存在子菜单，不能被删除

    32000: 导出表格出现严重异常
    32001: 导入失败，${0}
    32002: 下载失败，导入还未完成
    32003: 无错误数据
    32004: 身份证和手机号不能同时为空

    33001: 该用户已加入小区,无法再加入其他小区!
    33002: 用户不存在,请填写您的信息!
    33003: 只有游客才能编辑!
    33004: 您没有进小区管理权限!
    33005: 查询到管理了多个小区,请保证一个用户只管理一个小区!
    33006: 手机号码不存在,请补填号码!

    33007: 每个组织仅能拥有一个党建品牌

    34001: 未查询到该记录,或者已经被处理!
    34002: 未查询到原用户组织关系,请确认是否为原组织所属党员!
    34003: 存在该用户转接中的记录,请处理后再次提交!

    34201: 您编辑的党组不存在党组书记,请新增党组书记!
    34202: 党组中只能存在一个书记,请确认后再提交!
    34203: 该单位已存在党组信息,无法再创建新的党组!
    34204: 未查询到您编辑的党组信息!
    34205: 未查询到您编辑的党组成员信息:[${0}]!

    40100: 操作失败${0}
    3606: 标签所属不能修改
    35001: 该组织为用户所属组织，不能删除
    35002: 用户在该组织存在标签，不能删除
    35003: 角色列表不能为空
    35004: 支部名称已经存在，请重新输入
    35005: 请勿重复加入

    35007: 推荐时间不能为空
    35008: 确定积极分子日期不能为空
    35009: 培养联系人不能为空
    35010: 确定发展对象日期不能为空
    35011: 发展阶段有误
    35012: 申请人谈话内容不能为空
    35013: 申请人谈话时间不能为空
    35014: 谈话人不能为空
    35015: 推荐方式不能为空
    35016: 会议时间不能为空
    35017: 上级党委备案时间不能为空
    35018: 入党介绍人不能为空
    35019: 上级党委预审时间不能为空
    35020: 入党志愿书不能为空
    35021: 所在党组织不能为空
    35022: 加入中共组织类别不能为空
    35023: 工作岗位不能为空
    35024: 上级党委审批时间不能为空
    35025: 转正类型不能为空
    35026: 所在支部ID或支部名称不能为空
    35027: 组织级别选择错误
    35028: 名字限100字以内
    35029: 您没有权限执行此操作
    35030: 家庭住址字数超过最大限制
    35031: 积极分子时间不足一年
    35032: 预备党员时间不足一年
    35033: 上级党委备案意见不能为空
    35034: 字数限1000字以内
    35035: 用户姓名不能为空
    35036: 申请入党日期不能为空
    35037: 字数限200字以内
    35038: 取消原因不能为空

    33100: 该群组已被关联不允许修改！

#关闭endpoint的鉴权
management:
  health:
    elasticsearch:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: '/'
      path-mapping:
        health: ows-user-center/root/ping
        info: actuator/info
        metrics: actuator/metrics
        #env: actuator/env
        beans: actuator/beans
        configprops: actuator/configprops
        flyway: actuator/flyway
        logfile: actuator/logfile
        prometheus: actuator/prometheus
#mapper - 达梦数据库配置
mapper:
  mappers:
    - com.goodsogood.ows.mapper.MyMapper
    - com.goodsogood.ows.mapper.OrganizationMapper
  not-empty: true
  identity: ORACLE

#是否初始化缓存
init-redis:
  run: ${sys-init}

#数据字典code
init-option:
  codes: 1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,102801,102802,102803,102804,102805,102806,102807,102808,102809,102810,1029,1030,1031

init-org-user:
  tag_type: 3

#组织信息字段(数据库) 统计组织信息完整度
org-profile:
  information: org_type,parent_id,name,org_contacts,org_phone,is_retire

server:
  port: ${sys-port}
logging:
  .: DEBUG
  config: classpath:log4j2-dev.xml
spring:
  cloud:
    config:
      import-check:
        enabled: false
      enabled: false
  profiles:
    active: dev
  application:
    name: user-center-dev-lingjing
  jackson:
    #date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Chongqing

  #达梦数据库配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    username: ${sys-dm-db.db-user}
    url: jdbc:dm://${sys-dm-db.db-url}/${sys-dm-db.db-name}?stringtype=unspecified&currentSchema=GS_OWS_ZY_TEST
    password: ${sys-dm-db.db-password}
    driver-class-name: dm.jdbc.driver.DmDriver
    hikari:
      connection-test-query: SELECT 1 FROM DUAL
      minimum-idle: 1
      maximum-pool-size: 5
      pool-name: hikari-dm-pool
      idle-timeout: 60000
      max-lifetime: 2000000
      connection-timeout: 60000
      register-mbeans: true
      data-source-properties:
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        cachePrepStmts: true
        useServerPrepStmts: true

  #正式环境 redis
  redis:
    database: ${sys-redis-config.database}
    password: ${sys-redis-config.password}
    host: ${sys-redis-config.host}
    port: ${sys-redis-config.port}
  messages:
    basename: messages
    encoding: UTF-8

mongoConnection:
  host: ${sys-mongodb.uri}
  port: 27017
  userName: ${sys-mongodb.userName}
  password: ${sys-mongodb.password}
  databaseName: ${sys-mongodb.databaseName}
  collectionNames:
    com.goodsogood.ows.model.mongo.User: OWS-USER-SZF
    com.goodsogood.ows.model.mongo.Org: OWS-ORG-SZF
    com.goodsogood.ows.model.vo.DictionaryForm: Dictionary-SZF
    com.goodsogood.ows.model.mongo.PartyPositions: PartyPositions-cqyc
    com.goodsogood.ows.model.mongo.PartyBrandBase: PartyBrand


#正式环境 关闭 swagger
swagger:
  show: false

#正式环境 eureka客户端，服务自动发现
eureka:
  client:
    service-url: # 指定服务注册中心
      defaultZone: http://eurekaserver1.cqyc.online:8104/eureka/,http://eurekaserver2.cqyc.online:8104/eureka/
  instance:
    health-check-url-path: /ows-user-center/root/ping
    appname: user-center-gray
    hostname: usercenter${sys-id}.${sys-channel}
    #节点的名称
    #instance-id: ${spring.application.name}:${random.value}
    instance-id: 127.0.0.1


#正式环境 其他模块名称（eureka.client.instance.appname）
tog-services:
  #文件中心
  file-center: FILE-CENTER-TEST
  #网宣平台
  cms-plat: CMS-PLAT-TEST
  #工作流服务
  workflow: WORKFLOW-TEST
  #用户中心
  user-center: USER-CENTER-TEST
  #活动平台
  activity-plat: ACTIVITY-PLAT-TEST
  #积分中心
  credit-center: CREDIT-CENTER-TEST
  # 党费缴纳模块
  ppmd: PPMD-TEST
  # 纪实模块
  meeting: MEETING-TEST
  #推送中心
  push: PUSH-CENTER-TEST
  # 考核模块
  eval: EVAL-TEST
  #志愿服务
  volunteer: VOLUNTEER-TEST
  #云区项目
  ecp: ECP-TEST
  #sas中心
  sas: SAS-TEST

#负载策略
ribbon:
  eureka:
    enable: true
  NFLoadBalancerRuleClassName: com.netflix.loadbalancer.WeightedResponseTimeRule

#正式环境微信配置信息
wechat:
  #微信中心地址 例: https://wechat.goodsogood.com
  wechat-center-url: http://wechat.aidangqun.com
  #当前项目的名称 例: SZJG
  #project-name: owsyeyeku
  project-name:
    3: owsszf
  callback-version: 2
  get-access-token-url: /component/authorizer_access_token?project_name=%s&oid=%s
  get-wechat-user-info-url: https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=%s

#短信发送URL
send:
  url: https://gsmessage.goodsogood.com/gs-message/sms/code/sendText
  client-id: 100001
  channel-id: 1

download:
  orgUserImportModel: 28.xlsx

#学习系统广播接口
broadcast:
  run: true #是否推送到学习系统
  authUrl: http://10.11.166.170:8717/
  authCode: -1
  # 用户广播方法名
  userInfoMethod: /exterior/sync/user-info-broadcast
  # 组织广播方法名
  orgInfoMethod: /exterior/sync/org-info-broadcast
  # 用户组织广播方法名
  userOrgInfoMethod: /exterior/sync/user-dep-info-broadcast
  appId: 19 #重庆烟草
  key: "ZTO43BSX5CZON985"
  callback: #回调
    run: ${callbackRun} #与同步接口开关互斥
    initialDelay: 3000 #启动后多少毫秒开始执行
    fixedDelay: 100000 #每多少时间执行一次（等待上一次执行完以后）
    zuul-url: http://1


#需要回调服务
zuul-prefix:
  credit-center: CREDIT-CENTER-TEST # 积分中心
  ppmd: PPMD-TEST # 党费缴纳模块
  meeting: MEETING-TEST # 纪实模块
  eval: EVAL-TEST #考核模块

sync-jdgc-config: #12371同步定时任务配置
  run: ${sys-scheduler-12731} #是否启动定时器
  url: http://wx.cqjgdj.gov.cn/
  key: 3cpR75Psah7ujbJLkIXoMCrjf7CFsNvx
  pageSize: 100 #每次同步多少条，默认为20
  lastTimeId: 1 #t_last_time中对应的id 默认为1
  initialDelay: 5000 #启动后多少毫秒开始执行
  fixedDelay: 3600000 #每多少时间执行一次（等待上一次执行完以后）

# 组织权限
org-role:
  # 管理员权限
  managerMenuIds: pc01,pc03,pc04,pc05,01,0102,010201,010202,010203,0112,07,0109,010901,010902,12,1203,1301,1302,1303,15,1502,1503,04,0402,18,19
  # 普通用户权限
  userMenuIds: pc03,pc04,01,0102,010201,010202,010203,0112,07,0109,010901,010902
  # 微信权限
  wxMenuIds: 17,1701,1702,1703
  # 微信管理权限
  wxManagerMenuIds: 16,1601,1602,1603,1604,1605,1606,17,1701,1702,1703,1704
  # 高级管理员权限
  seniorMgrMenuIds: pc01,pc03,pc04,pc05,01,0102,010201,010202,010203,0112,07,0109,010901,010902,12,1201,1203,15,1502,1503,0402,18,19,04,21,2101,210101,210102,210105,20,2001,200102
  type: 3,9 #管理员角色类型

# 实名认证url
valid-certNumber:
  # 单个认证
  url: https://tool.goodsogood.com/gs_dicitem/gsRealName/checkRealName
  # 批量认证
  batch-url: http://tool.goodsogood.com/gs_dicitem/gsRealName/batchCheckRealName

# 离退休人员岗位code
user-option:
  retireCode: 49,50,51,52,53
  politicalCode: 1,5,17,18 #政治面貌code
  prePoliticalCode: 5,18 #预备党员
  joinPartyType: 104401 #发展党员
  joinType: 10440201 #市内转入
  leaveTypes:
    - 10450201
    - 10450202
  leaderTypes: #届次书记数据字典code
    - 6
    - 9
    - 12
    - 16
    - 19
  recusalType: 104506 #取消预备党员资格

# 生成组织概况定时任务配置
generate-org-profile-config:
  run: ${sys-scheduler} #是否启动定时器
  initialDelay: 5000 #启动后多少毫秒开始执行
  fixedDelay: 1800000 #每多少时间执行一次（等待上一次执行完以后）

# 组织大数据菜单ID 0401
org-menu-id: '0401'

org-type-child: 10280304,10280309,10280314,10280315,10280319 #党支部类型

tag-type: 3 #标签类型 3-党委标签

parent-tree-type: 102807 #父树组织类型

scheduler:
  count: ${sys-scheduler} #定时统计任务开关
  cron:
    count: ${sys-scheduler-committee}
    org-committee: 0 0 10 25 * ? #定时开启支委会换届通知 每月25日0时10分0秒执行　只在正式环境运行！！！！！
  result:
    org-committee: 0 10 0 * * ? #定时开启统计支委会换届相关结果 每天10分执行

role-type: 3,9 #管理员角色类型
evel-tag-type: 6 #是否考核组织标签类型

party-push-scheduler:
  run: true #定时统计任务开关
  cron: 0 30 10 * * * #每天早晨10点30分执行
  joinTemplateId: 208 #政治生日模板ID
  birthTemplateId: 156 #自然生日模板ID
  channelType: 4 #渠道类型 4-钉钉消息
  source: user-center #业务系统userCenter
  dataSize: 100 #每次推送最大条数
  maxRepeatCount: 3 #调用服务重试次数
  pushModel: 3 #推送模式 1-只推当前渠道，2-只推主题消息，3-全部推送
  includeRegionId: 19 #需要推送的区县ID

#发展党员到期提醒定时任务
party-member-scheduler:
  run: ${sys-scheduler} #是否开启定时任务
  cron: 0 0 9 25 * ?  #每个月25号9点执行
  activistId: 199 #积极分子模板ID
  partyMemberId: 200 #预备党员模板ID
  channelType: 4 #渠道类型 4-钉钉
  source: user-center #业务系统userCenter
  pushModel: 1 #推送模式 1-只推当前渠道，2-只推主题消息，3-全部推送
  maxRepeatCount: 3 #调用服务重试次数
  dataSize: 100 #每次推送最大条数
  includeRegionId: 19 #需要推送的区县ID

#人员重置年龄定时任务
reset-age-scheduler:
  run: ${sys-scheduler} #是否开启定时任务
  cron: 0 5 0 20 * ? #每个月20号0点执行

#党费标签名称
ppmd-tag-name: 在平台交党费

#短信中心配置文件
message:
  run: true #是否开启发送短信
  message-center-url: http://owsmsg.aidangqun.com/smsSend/batchSend #发送短信url
  secret-key: ce4e55bd7f4540689d25ce3fc95c63ca #认证key
  sms-code-template-id: 32 #短信验证码模板ID
#  41  49d219b3af2748d9ae01e573cf44877e 天津
#  42  6ff70a410a4e40e787075c76dbc6544e 云南
#  43  2e8c8a292c4e49db8293ba022fc96d3e 市值
#  32  ce4e55bd7f4540689d25ce3fc95c63ca 市直

#监督预警组织概况组织类型
org-type:
  #党委
  communist-child[0]: 10280301
  communist-child[1]: 10280310
  communist-child[2]: 10280322
  #党总支
  general-branch-child[0]: 10280303
  general-branch-child[1]: 10280308
  general-branch-child[2]: 10280311
  general-branch-child[3]: 10280318
  #党支部
  branch-child[0]: 10280304
  branch-child[1]: 10280309
  branch-child[2]: 10280314
  branch-child[3]: 10280315
  branch-child[4]: 10280319
  #党组
  party-group[0]: 10280312
  party-group[1]: 10280313
  #委员会
  committee[0]: 10280316
  committee[1]: 10280317
  #不统计党组织类型
  no-statistics-child[0]: 10280329
  no-statistics-child[1]: 10280330
  #党小组
  communist-group[0]: 10280306
  #顶级党委
  top-group: 10280307

# 考核组织所属单位类型
corp-type-child: 10280728,10280701,10280710,10280702,10280711,10280707,10280725,10280719,10280708,10280727,10280716,10280712,10280713,10280723,10280703,10280731,10280730,10280720,10280741,10280742

# 区县label配置
saas:
  region-label: V4.0.0
  label: V4.0.0
  third-id-prefix: gsg- # 第三方数据ID前缀
  generate-third-id: true #是否生成第三方数据ID
  org-type[0]: 102802 #行政单位
  regions: #需要交纳党费的区县ID
    - 6 #沙区
    - 10 #渝北
  partyTagId: 1 #党费标签ID

show-type:
  #需要展示标签类型
  tag-type:
    - 3
    - 6
    - 7
    - 8
    - 9
    - 10
    - 11
  #组织管理中需要过滤的管理标签类型
  filter-type:
    - 9

# 志愿团体推送消息
volunteer-team-message:
  #模板ID
  template-id: 48
  #服务类别
  service-type: "志愿服务"
  #渠道类型 2-微信
  channel-type: 2
  #来源
  source: "user-center"
  #重试次数
  max-repeat-count: 3
  #启用团体
  valid-comment:
    title: "%s 已被启用。"
    remark: "如有疑问，请联系上级志愿团体。"
  #停用团体
  invalid-comment:
    title: "非常抱歉，%s 已被停用。"

#统计时排除的组织ID
exclude-org:
  org-ids:
    - 3261
    - 1294
    - 5
    - 2409
    - 2417
    - 1222
    - 1255
    - 377
    - 3404
  not-change-org-type:
    - 10280329
    - 10280306

#用户组织快照数据统计月份
history:
  month:
    - "2021-08"
    - "2021-09"
    - "2021-10"
    - "2021-11"
    - "2021-12"
    - "2022-01"
    - "2022-02"
    - "2022-03"
    - "2022-04"
    - "2022-05"

#生成快照定时任务
snapshot-scheduler:
  run: ${snapshotScheduler} #是否开启定时任务
  corp-cron: 0 0 1 5 * ? #每个月5号1点执行
  org-cron: 0 0 2 5 * ? #每个月5号2点执行
  user-cron: 0 0 4 5 * ? #每个月5号4点执行
  region-id:
    - 19 #市直

# 电话号码加密方式true-> SM4 / false或者不配置 -> AES
num:
  encrypt:
    algo: false

#动态字段结果是否打开
dynamic-fields:
  open: true

# 刷新组织人员数量 - 目前支持党组织树
update-org-user-num:
  run: ${sys-scheduler}
  cron: 0 0 0/3 * * ?
  org-type:
    - 102803
pull-user-third-info:
  run: false
  cron: 0 14 10 * * ?
  is-all: false

#临时文件保存地址
temp-path: /home/<USER>/appdata/tmp/ows-user-center

# 积分中心
credit:
  run: ${sys-scheduler}
  access_key: ows
  #每天5点执行同步结果
  syn-exam-result-scheduler: 0 0 5 * * ?
  #每天10点 同步那些发送积分失败的考试人员信息
  syn-exam-result-reissue: 0 0 10 * * ?
  #每隔15分钟同步当天的开始结果
  syn-exam-today-scheduler: 0 */15 * * * ?
  #验证统计时间
  valTime: 120
  #组织风采发送积分配置
  branch-highlight-remark: 组织风采,组织发展史
  branch-highlight-type: 32,33
  branch-highlight-score: 1,1
  #每满5条发送积分
  branch-highlight-size: 5
  #个人成长轨迹
  user-highlight-remark: 积分任务_新增个人成长轨迹
  user-highlight-score: 1
  user-highlight-type: 34
  #每满5条发送积分
  user-highlight-size: 5
  #每月上限5条数据
  user-highlight-limit-count: 5

# 过滤那些字段不显示
field:
  name:
    - "phone"
    - "cert_number"

# 统一服务密码平台验证信息
verify:
  url: "https://olymtech.qicp.vip:3443/service_api/verify_sign_request"
  algo: "ECS_SM2_CHALLENGE"
  secretKey: "We2JZ7wHpHbtEEAO"

org:
  head-url-key: "head_url"

# 导入文件有效期
import-user:
  file-vild: 7

dingding:
  server: sas-szf
  #钉钉机器人的accessToken
  accessToken: 10480628a5202e29bef535e29e23f11fab899e5c19d7f99face2c140e7848d7e

# 资产管理层级关系
asset:
  level: 3

# 密评配置
ecs:
  run: false

  # 全量刷新缓存
flush-cache:
  run: ${sys-scheduler}
  cron: 0 14 0 * * ? #每天下午2检查是否为该月的最后一天

exclude-org-ids: 1294,5,2409,2417,1222,1255,3404,377,3619,3676

#动态字段
dynamic-field:
  fields:
    # 岗位分布
    job_distributed: "field_3515"
    # 岗位类别
    job_category: "field_3516"
    # 所属序列
    sequence: "field_3525"
    # 个性签名
    signature: "field_3527"
    # 参加工作时间
    join_work_time: "field_3523"
    # 是否失联
    lose_type: "is_lose"
    # 是否流动党员
    flow_type: "is_flow"
    # 我的承诺
    my_promise: "field_3517"
    # 党组织属地
    org_dependency: "field_3373"

#发展党员
develop:
  recusal: "取消"
  qualification: "资格"
  proposer: "入党申请人"
  activist: "积极分子"
  developmentobject: "发展对象"
  propartymember: "预备党员"
  deletepeople: "删除发展人员"
  deleteUser: "在党员管理中心已删除了该党员"
  #推荐方式
  partymemberrecommend: "党员推荐"
  teamrecommend: "群团组织推荐"
  elserecommend: "其他"
  #转正类型
  ontime: "按期转正"
  lengthen: "延长预备期满转正"
  postpone: "推迟讨论转正"

  addDesc: "新增录入\"%s\""
  confirm: "确定"
  receive: "接收"
  positive: "转正"
  recommentTime: "会议时间：%s"
  parentRecordTime: "上级党委备案时间: "
  branch: "支部"
  add: "新增"
  #公示
  publicityTitle: "关于拟将%s同志转为中共正式党员的公示"
  text: "%s同志，%s，%s出生，%s学历。%s申请入党，%s被确定为入党积极分子，%s被确定为发展对象。经%s审查，%s同志个人历史、直系亲属和主要社会关系清楚，现实表现符合共产党员的标准。经%s审查和研究，认为该同志已具备党员条件，拟在近期召开支部大会讨论%s预备党员转正事宜。现予以公示。公示从%s开始，至%s结束（共计5天）。"

#组织届次更改书记上报积分
org-period-credit:
  accessKey: "ows"
  prefixName: "积分任务_"
  branchRemark: "配齐组织书记"
  communistRemark: "健全组织机构，配强组织力量"
  creditTimes: 1 #上报积分次数
  scores: 5 #积分值
  scoreType: 24 #积分类型-组织建设
  operType: 0 #操作类型 0-新增 1-扣分

#排除的测试组织  区县-组织id
report-org:
  exclude_map:
    19:
      - 7
      - 8
      - 2570

# 高德地图
amap:
  # 代理地址
  url: https://git.aidangqun.com/amap/v3/geocode/geo