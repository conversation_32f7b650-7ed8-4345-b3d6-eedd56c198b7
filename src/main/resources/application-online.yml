
#online
spring:
  cloud:
    config:
      #失败就停止
      fail-fast: false
      uri: http://configserver.goodsogood.online:8194
      label: v1.0.3
  profiles:
    active: online
  application:
    name: user-center
logging:
  level: DEBUG

#端口
sys-port: 8106
#节点
sys-id: szf1
#局点
sys-channel: goodsogood.online
#config dir
sys-config-dir: -szf
#数据库
sys-db0:
    db-url: *************
    db-name: gs_ows_szf_online
    db-user: gszw_owsszf
    db-password: IyfNa#Li71GZ
#redis
sys-redis-config:
    database: 9
    password: d7f153db47f04b24
    host: *************
    port: 6379

#微信中心注册
sys-wechat:
    #市直机关
    project-name: owsszf
    #渠道外网域名
    channel: goodsogood.com
    #回调微信H5的入口地址服务名
    #市值机关
    h5: owsswx

#初始化用户换成
sys-init: true

#是否开启定时任务
sys-scheduler: true
#定时推送组织换届信息开关 在正式环境时为true  其他环境为false
sys-scheduler-committee: false

#同步12371接口服务器
sys-scheduler-12731: true