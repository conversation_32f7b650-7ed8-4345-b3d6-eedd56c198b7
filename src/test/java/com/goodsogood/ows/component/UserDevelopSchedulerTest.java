package com.goodsogood.ows.component;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("test")
@Log4j2
public class UserDevelopSchedulerTest {

    @Autowired
    private UserDevelopScheduler userDevelopScheduler;

    @Test
    public void findPushData() {
        //this.userDevelopScheduler.findPushData(2);
    }
}