package com.goodsogood.ows;

import cn.hutool.core.collection.CollectionUtil;
import com.goodsogood.ows.common.MD5Helper;
import com.goodsogood.ows.common.MD5Util;
import com.goodsogood.ows.model.mongo.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Test;

import java.awt.*;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * test
 *
 * <AUTHOR>
 * @date 2018-05-17
 */
@Log4j2
//@ExtendWith(SpringExtension.class)
//@SpringBootTest()
//@ActiveProfiles("dev")
public class DoTest {


    @Test
    public void someTest() throws Exception {

        //System.out.println(NumEncryptUtils.encrypt("51023019691204434X",1));
        //System.out.println(NumEncryptUtils.encrypt("18321011021",2));
        //System.out.println(NumEncryptUtils.numSecret("a68696869123",2));
        // System.out.println(System.currentTimeMillis());
        //System.out.println(NumEncryptUtils.encrypt("420984198505025651", 1));
        System.out.println(MD5Helper.getMD5("Sj930211"));
        //System.out.println(NumEncryptUtils.decrypt("ke2m+6Mt5QOnk3PnTF5Yag==",2));
        // "wtodYckOMvF+M6QK99Af/mgAl8R7BtZvZZhZ6ql9zRM=","510228********7231"));
        //System.out.println(Utils.getPhoneSuffix());
        //System.out.println(NumEncryptUtils.decrypt("5pmR2U+aF3A+LxbXHTzG/828IWqZ2ZEhf9iRN/1hLac=", "510202********5929"));
        //System.out.println(NumEncryptUtils.decrypt("XzAHuTB3yEfPa9Og5cxBow==", "183****9115"));
        //System.out.println(Utils.checkPhone("17723196077"));

        /*java.util.regex.Pattern regex = Pattern.compile("^[\u4e00-\u9fa5a-zA-Z]{4,16}+$");
        Matcher m = regex.matcher("dffdsa");
        System.out.println(m.matches());*/

//        String sign="current=1&pageSize=10&time=1533257385986&key=3cpR75Psah7ujbJLkIXoMCrjf7CFsNvx";
//        String sendSign = MD5Helper.getMD5(sign).toUpperCase();

        //System.out.println(Utils.checkPhone("100000000200"));

        //String str = "{\"birthday\": \"1973-01-07 00:00:00\",\"political_type\": \"正式党员\",\"ethnic\": \"汉族\",\"education\": \"男\",\"gender\": \"大学本科\",\"is_retire\": 2,\"joining_time\": \"2000-08-04 00:00:00\",\"cert_number\": \"957bedd3d122c9c3e3b79c39bd230e898a2327395c4498862d2b783faf5b29fd\",\"birthplace\": \"重庆市\",\"phone\": \"13983453810\",\"name\": \"张曦\",\"id\": \"U705525094696357958\",\"position\": \"\",\"org_name\": \"中共重庆市社会人才第一总支第62支部委员会\",\"org_code\": \"050289603005\"}";
    }

    @Test
    public void test01() {
        List<String> testList = new ArrayList<>();
        /*for (int i = 0; i < 100; i++) {
            testList.add(String.valueOf(i));
        }*/
        long stime1 = System.currentTimeMillis();
        System.out.println("开始时间: [" + stime1 + "]");
        testList.stream().forEach((item) -> {
            System.out.println("第一种循环 -> item : " + item);
        });
        long etime1 = System.currentTimeMillis();
        System.out.println("结束时间: [" + etime1 + "] , 总耗时: [" + (etime1 - stime1) + "]");

        System.out.println("----------------------------------");

        long stime2 = System.currentTimeMillis();
        System.out.println("开始时间: [" + stime2 + "]");
        for (int i = 0; i < testList.size(); i++) {
            System.out.println("第二种循环 -> item : " + testList.get(i));
        }

        long etime2 = System.currentTimeMillis();
        System.out.println("结束时间: [" + etime2 + "] , 总耗时: [" + (etime2 - stime2) + "]");

        System.out.println("----------------------------------");

        long stime3 = System.currentTimeMillis();
        System.out.println("开始时间: [" + stime3 + "]");
        for (String s : testList) {
            System.out.println("第三种循环 -> item : " + s);
        }

        long etime3 = System.currentTimeMillis();
        System.out.println("结束时间: [" + etime3 + "] , 总耗时: [" + (etime3 - stime3) + "]");


        /*String jobNumber = "091023";
        String s = Base64Utils.decompressData(jobNumber);


        System.out.println(s);*/
    }

    @Test
    public void test3() {

        String password = getPassword("aqwepioowo");
        System.out.println(password);
        System.out.println(MD5Util.toMD5(password));
        System.out.println(MD5Util.GetMD5Code(password));
        System.out.println(MD5Helper.getMD5(password));

    }

    @Test
    public void test4() {
        String password = "123456";
        System.out.println(password);
        System.out.println(MD5Util.toMD5(password));
        System.out.println(MD5Util.GetMD5Code(password));
        System.out.println(MD5Helper.getMD5(password));

    }

    private String getPassword(String jobNumber) {
        String s = jobNumber.substring(jobNumber.length() - (jobNumber.length() > 3 ? 3 : jobNumber.length()));
        byte[] sbytes = s.getBytes();
        int re = 0;
        for (int i = 0; i < sbytes.length; i++) {
            byte b1 = sbytes[i];
            re += b1;
        }
        s = String.valueOf(re);
        String password = "";
        byte[] bytes = jobNumber.getBytes();
        int length = jobNumber.length() > 2 ? 2 : jobNumber.length();
        for (int i = 0; i < length; i++) {
            char c = (char) (bytes[i]);
            System.out.println("第".concat(String.valueOf(i)).concat("几次循环: ") + c);
            // 判断是否为数字
            boolean digit = Character.isDigit(c);
            if (digit) {
                byte b = bytes[i];
                if (c == '0') {
                    c = (char) (b + 58);
                } else {
                    c = (char) (b + 48);
                }
            } else {
                // 判断是否为字母
                boolean letter = Character.isLetter(c);
                if (!letter) {
                    c = 'a';
                }
            }

            password += String.valueOf(c);
        }
        password = password.concat(s);
        if (password.length() < 5) {
            return getPassword(password);
        }
        if (password.length() > 5) {
            return password.substring(0, 5);
        }
        return password;
    }

    // 将字母转换成数字
    public static void letterToNum(String input) {
        for (byte b : input.getBytes()) {
            System.out.print(b - 96);
        }
    }

    // 将数字转换成字母
    public static void numToLetter(String input) {
        for (byte b : input.getBytes()) {
            System.out.print((char) (b + 48));
        }
    }

    @Test
    public void test02() throws UnsupportedEncodingException {
        String u = ":%7B%22type%22%3A1%2C%22appObject%22%3A%22%E5%9B%BA%E5%AE%88-%E6%B5%8B%E8%AF%95%E5%85%9A%E6%94%AF%E9%83%A8%3A %E6%B1%9F%E4%BC%9A%E9%9D%92%22%2C%22category%22%3Anull%2C%22level%22%3A%22%E5%85%B6%E4%BB%96%22%2C%22name%22%3Anull%7D";
        final String decode = URLDecoder.decode(u, "UTF-8");
        System.out.printf(decode);
    }

    @Test
    public void generatePass() {
        System.out.println(new PasswordGenerator(12, 2).generateRandomPassword());
    }

    @Test
    public void test03() {
        List<Student> list = Lists.newArrayList();
        list.add(new Student("测试", "男", 18));
        list.add(new Student("开发", "男", 20));
        list.add(new Student("运维", "女", 19));
        list.add(new Student("DBA", "女", 22));
        list.add(new Student("运营", "男", 24));
        list.add(new Student("产品", "女", 21));
        list.add(new Student("经理", "女", 25));
        list.add(new Student("产品", "女", 21));

        //求性别为男的学生集合
        List<Student> l1 = list.stream().filter(student -> student.sex.equals("男")).collect(toList());
        System.out.println("求性别为男的学生集合" + l1);

        //map的key值true为男，false为女的集合
        Map<Boolean, List<Student>> map = list.stream().collect(partitioningBy(student -> student.getSex().equals("男")));
        System.out.println("map的key值true为男，false为女的集合" + map);

        //求性别为男的学生总岁数
        Integer sum = list.stream().filter(student -> student.sex.equals("男")).mapToInt(Student::getAge).sum();
        Student st = list.stream().filter(student -> (student.sex.equals("男") && student.age > 26)).collect(maxBy(Comparator.comparing(Student::getAge))).get();
        System.out.println("求性别为男的学生总岁数" + sum);
        System.out.println("求性别为二的学生" + st);

        //按性别进行分组统计人数
        Map<String, Integer> map2 = list.stream().collect(Collectors.groupingBy(Student::getSex, Collectors.summingInt(p -> 1)));
        System.out.println("按性别进行分组统计人数" + map2);

        //判断是否有年龄大于25岁的学生
        boolean check = list.stream().anyMatch(student -> student.getAge() > 25);
        System.out.println("判断是否有年龄大于25岁的学生 " + check);

        //获取所有学生的姓名集合
        List<String> l2 = list.stream().map(Student::getName).collect(toList());
        System.out.println("获取所有学生的姓名集合 " + l2);

        //求所有人的平均年龄
        double avg = list.stream().collect(averagingInt(Student::getAge));
        System.out.println("求所有人的平均年龄" + avg);

        //求年龄最大的学生
        Student s = list.stream().reduce((student, student2) -> student.getAge() > student2.getAge() ? student : student2).get();
        System.out.println("求年龄最大的学生 " + s);

        Student stu = list.stream().collect(maxBy(Comparator.comparing(Student::getAge))).get();
        System.out.println(stu);

        //按照年龄从小到大排序
        List<Student> l3 = list.stream().sorted((s1, s2) -> s1.getAge().compareTo(s2.getAge())).collect(toList());
        System.out.println("按照年龄从小到大排序 " + l3);

        //求年龄最小的两个学生
        List<Student> l4 = l3.stream().limit(2).collect(toList());
        System.out.println("求年龄最小的两个学生 " + l4);

        //获取所有的名字，组成一条语句
        String str = list.stream().map(Student::getName).collect(Collectors.joining(","));
        System.out.println("获取所有的名字，组成一条语句 " + str);

        //获取年龄的最大值、最小值、平均值、求和等等
        IntSummaryStatistics intSummaryStatistics = list.stream().mapToInt(Student::getAge).summaryStatistics();
        System.out.println(intSummaryStatistics.getMax());
    }

    @Data
    @AllArgsConstructor
    static class Student {
        String name;
        String sex;
        Integer age;

    }


    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        sc.useDelimiter("\n");
        String next = sc.next();
        String[] s = next.split(" ");
        String low = s[0];
        String high = s[1];
        List<Integer> mathList = new ArrayList<>();
        a:
        for (int i = Integer.valueOf(low); i < Integer.valueOf(high); i++) {
            if (i % 2 == 0 && i != 2) {
                continue;
            }
            b:
            for (int j = 2; j <= i / 2; j++) {
                if (i % j == 0) {
                    continue a;
                }
            }
            mathList.add(i);
        }
        int n1 = 0;
        int n2 = 0;
        for (int num : mathList) {
            int i1 = num % 10;
            int i2 = (num % 100) / 10;
            n1 += i1;
            n2 += i2;
        }
        System.out.println(n1 >= n2 ? n2 : n1);
    }

    @Test
    public void hutool() {
        //System.out.println(this.caseFormatStr("sourceId", 2));
    }

    @Test
    public void test10() throws Exception {
        List<String> pathList = this.getAttributesPath(User.class, "isLose");
        System.out.println("路径 -> " + pathList);
    }

    public List<String> getAttributesPath(Class<?> classType, String fieldName) {
        List<String> pathList = new ArrayList<>();
        Field[] fields = classType.getDeclaredFields();
        List<Field> fieldListByName = Arrays.stream(fields).filter(field -> field.getName().equals(fieldName)).collect(toList());
        if (!CollectionUtil.isEmpty(fieldListByName)) {
            pathList.add(fieldName);
        }
        for (Field field : fields) {
            String name = field.getName();
            Class<?> fieldClass = field.getType();
            if (fieldClass.isArray() || fieldClass.equals(List.class) || fieldClass.equals(Set.class)) {
                Type genericType = field.getGenericType();
                if (genericType instanceof ParameterizedType) {
                    ParameterizedType pt = (ParameterizedType) genericType;
                    // 得到泛型里的class类型对象
                    Class<?> actualTypeArgument = (Class<?>) pt.getActualTypeArguments()[0];
                    List<String> attributesPath = this.getAttributesPath(actualTypeArgument, fieldName);
                    attributesPath.forEach(path -> pathList.add(name + "." + path));
                }
            } else if (isWrapClass(fieldClass) || fieldClass.isPrimitive() || fieldClass.equals(String.class)) {
                //System.out.println( "判断是为基础类型或者包装类型" + field.getName() + " -> " + fieldClass);
            } else {
                List<String> attributesPath = this.getAttributesPath(fieldClass, fieldName);
                attributesPath.forEach(path -> pathList.add(name + "." + path));
            }
        }
        return pathList;
    }

    /**
     * 判断是否是基础数据类型的包装类型
     *
     * @param clz
     *
     * @return
     */
    public static boolean isWrapClass(Class<?> clz) {
        try {
            return ((Class<?>) clz.getField("TYPE").get(null)).isPrimitive();
        } catch (Exception e) {
            return false;
        }
    }

    @Test
    public void testFont() {
        int[] fontStyles = {Font.TRUETYPE_FONT, Font.ITALIC, Font.PLAIN}; // Font.BOLD,
        Random random = new Random();
        for (int i = 0; i < 100; i++) {
            System.out.println(fontStyles[random.nextInt(fontStyles.length)]);
        }
    }
}
