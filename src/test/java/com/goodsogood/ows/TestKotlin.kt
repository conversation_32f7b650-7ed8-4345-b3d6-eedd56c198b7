package com.goodsogood.ows

import cn.hutool.crypto.SmUtil
import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.commons.codec.binary.Base64
import org.apache.commons.io.FileUtils
import org.junit.jupiter.api.Test
import java.util.*

class TestKotlin {

    @Test
    fun test00() {
        val random = arrayOf(2, 3, 5, 6).random()
        println(random)
    }

    @Test
    fun test01() {
        val uuid = UUID.randomUUID().toString()
        println(uuid)
    }

    /**
     * 图片转换为Base64字符串
     */
    @Test
    fun test02() {
        println(
            Base64.encodeBase64String(
                FileUtils.fileRead("/Users/<USER>/Downloads/Picture2.png").toByteArray(Charsets.UTF_8)
            )
        )
    }

    /**
     * SM3 hash
     */
    @Test
    fun test03() {
        val appid = "0f5a868f-1b9e-44e1-b4d7-ced17d00c56e"
        val str = appid + 1.18 + 1 + "中文"
        println(SmUtil.sm3().digestHex(str))
        // 9c300a88bd579773d09739e985e53f7b02b7737bd872173733e17ed10aee561b
    }

    /**
     * SM4 加密
     */
    @Test
    fun test04() {
        val key = "zHcXdRxZrCf7ivPx" // 密钥
        val json = """{"str_1":"中文","int_1":1,"float_1":"1.18"}""" // data
        val ciphertext = SmUtil.sm4(key.toByteArray(Charsets.UTF_8)).encryptHex(json)
        println(ciphertext)
        // d808e741511a7baf46b63f7d143b40ae07c79c3e2227b7834706bd5eddaf3a3bb6b5d80aa4e67032248e05ce37ac671e
        println(SmUtil.sm4(key.toByteArray(Charsets.UTF_8)).decryptStr(ciphertext))
        // {"str_1":"中文","int_1":1,"float_1":"1.18"}
    }

    data class RequestData(
        val id: String,
        val title: String,
        val summary: String,
        val area: String,
        val time: String,
        val people: String,
        val phone: String,
        val level: Int,
        val has_img: Boolean
    )

    data class Request(
        val data: RequestData,
        val files: List<String>? = null,
        val imgs: List<String>?
    )

    @Test
    fun test05() {
        val request = Request(
            data = RequestData(
                UUID.randomUUID().toString(),
                "事件标题",
                "事件描述",
                "发生地点",
                "2022-01-01",
                "反映人（非必填）",
                "18000000000",
                4,
                true
            ),
            imgs = mutableListOf("img_1_base64", "img_2_base64", "img_3_base64")
        )
        val mapper = ObjectMapper()
        println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(request))
    }
}