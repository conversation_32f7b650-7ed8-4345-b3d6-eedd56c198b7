package com.goodsogood.ows;

import com.goodsogood.ows.model.vo.BroadcastUserForm;
import com.goodsogood.ows.service.BroadcastService;
import com.goodsogood.ows.service.EcsService;
import com.goodsogood.ows.service.OpenService;
import com.goodsogood.ows.service.UserService;
import com.utils.XEngineKitException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @ClassName OpenServiceTests
 * @date 2018-08-16 8:55
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest()
@ActiveProfiles("dev")
public class OpenServiceTests {

    @Autowired
    private OpenService openService;

    @Autowired
    private UserService userService;

    @Autowired
    private BroadcastService broadcastService;

    @Autowired
    private EcsService ecsService;

    @Test
    public void contextLoads() {
        // this.openService.dealOrgRole();
    }

    @Test
    public void TestUserInfo() {

        BroadcastUserForm form = new BroadcastUserForm();
        form.setUserId(1234456789l);
        form.setName("测试用户");
        form.setGender("男");
        form.setIsRetire(2);
        form.setEducation("大学本科");
        form.setBirthday("1988-01-01");
        form.setPoliticalType("党员");
        form.setBirthPlace("重庆市");
        form.setWorkStatus("在岗");

        this.broadcastService.userInfoBroadcast(form, null);

    }

    @Test
    public void testOther() {
    }

    @Test
    public void testEcs() throws XEngineKitException {
        // hmac数据
        String result = this.ecsService.hmacData(2, "123456asdfasdfasdfasdfasdfasdfasdfasdfasdfasdfa" +
                "asfdfasdfasdfsdfasdfasdfasdfasdfasdfasdfasdfasdfasdfasdfasdf");
        System.out.println("*****************************************************************");
        System.out.println(result);
    }


    public static Integer countTwoDayWeek(String startDate, String endDate) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date start = format.parse(startDate);
        Date end = format.parse(endDate);
        Calendar cal = Calendar.getInstance();
        cal.setTime(start);
        long time1 = cal.getTimeInMillis();
        cal.setTime(end);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        Double days = Double.parseDouble(String.valueOf(between_days));
        if ((days / 7) > 0 && (days / 7) <= 1) {
            return 1;
        } else if (days / 7 > 1) {
            int day = days.intValue();
            if (day % 7 > 0) {
                return day / 7 + 1;

            } else {
                return day / 7;

            }
        } else if ((days / 7) == 0) {
            return 0;
        } else {
            return null;
        }
    }


    /**
     * 补全给定时间内的所有周，包含最开始的
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Set<String> getWeeksBetweenDates(String startTime, String endTime) {
        Set<String> result = new HashSet<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        int startYear = Integer.parseInt(startTime.substring(0, 4));
        int endYear = Integer.parseInt(endTime.substring(0, 4));
        Date start;
        Date end;
        try {
            start = format.parse(startTime);
            end = format.parse(endTime);
            Calendar calendar = Calendar.getInstance();
            calendar.setFirstDayOfWeek(Calendar.MONDAY);
            calendar.setTime(start);
            result.add(calendar.get(Calendar.YEAR) + "-" + calendar.get(Calendar.WEEK_OF_YEAR));
            while (calendar.getTime().before(end)) {
                calendar.setTime(end);
                calendar.add(Calendar.WEEK_OF_YEAR, 1);
                String weekStr;
                if (startYear < endYear) {
                    weekStr = calendar.get(Calendar.YEAR) - 1 + "-" + calendar.get(Calendar.WEEK_OF_YEAR);
                } else {
                    weekStr = calendar.get(Calendar.YEAR) + "-" + calendar.get(Calendar.WEEK_OF_YEAR);
                }
                result.add(weekStr);
            }
        } catch (ParseException e) {
        }
        return result;
    }


    public static void main(String[] args) throws ParseException {
        if ("万州区局（分公司）".endsWith("（分公司）")) {
            "万州区局（分公司）".replace("（分公司）", "");
            System.out.println("万州区局（分公司）".replace("（分公司）", ""));
        }
    }
}
