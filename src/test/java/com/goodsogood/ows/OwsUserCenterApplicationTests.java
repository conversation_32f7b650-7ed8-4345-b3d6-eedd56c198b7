package com.goodsogood.ows;

import com.goodsogood.ows.common.Constants;
import com.goodsogood.ows.component.SyncJDGJScheduler;
import com.goodsogood.ows.mapper.OrgMapper;
import com.goodsogood.ows.mapper.OrganizationMapper;
import com.goodsogood.ows.model.db.MenuUrlEntity;
import com.goodsogood.ows.model.db.OrganizationEntity;
import com.goodsogood.ows.service.MenuUrlService;
import com.goodsogood.ows.service.MessageService;
import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@ExtendWith(SpringExtension.class)
@TestPropertySource("classpath:/application.yml")
@SpringBootTest
public class OwsUserCenterApplicationTests {
    @Autowired
    private SyncJDGJScheduler syncJDGJScheduler;

    @Autowired
    private MenuUrlService menuUrlService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Value("${org-profile.information}")
    private String orgInformation;

    @Test
    public void contextLoads() {
    }


    @Test
    public void testSync() {
        syncJDGJScheduler.sync();
    }

    @Test
    public void testAddMenuUrl() throws UnsupportedEncodingException, InterruptedException {
        String method = "GET";
        String uri = "/mt/type/list-all1";
        MenuUrlEntity menuUrlEntity = new MenuUrlEntity();
        menuUrlEntity.setMethod(method.toUpperCase());
        menuUrlEntity.setLinkUrl(URLDecoder.decode(uri, "UTF-8"));
        menuUrlEntity.setCreateTime(DateTime.now().toDate());
        menuUrlEntity.setLastChangeUser(-1L);
        //默认为禁用
        menuUrlEntity.setStatus(2);
        this.menuUrlService.addMenuUrl("0402", menuUrlEntity);
        Thread.sleep(30 * 1000);
    }

    @Test
    public void testSendMessage() {
        this.messageService.sendCode("18523107850");
    }

    @Test
    public void testOrgProfile() {
        Long orgId = 108L;
        // 先查询信息完整度
        String[] split = orgInformation.split(",");
        List<String> orgInformationList = new ArrayList<>(Arrays.asList(split));
        // 判断是不是党组织, 如果是党组织，则加入党组织特定字段
        OrganizationEntity org = this.getById(orgId);
        if (null != org && org.getOrgType() == Constants.ORG_TYPE_COMMUNIST) {
            String[] communistOrgField = Constants.COMMUNIST_ORG_SPECIFIC_FIELD.split(",");
            List<String> communistOrgFieldList = new ArrayList<>(Arrays.asList(communistOrgField));
            orgInformationList.addAll(communistOrgFieldList);
        }
        Integer integrity = this.orgMapper.getOrgInformationIntegrity(orgInformationList, orgId);

    }

    public OrganizationEntity getById(Long orgId) {
        return organizationMapper.selectByPrimaryKey(orgId);
    }
}
