package com.goodsogood.ows;

import com.goodsogood.ows.mapper.LeaderOrgMapper;
import com.goodsogood.ows.service.LeaderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

/**
 * Auther: ruoyu
 * Date: 19-4-18
 * Description:
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = OwsUserCenterApplication.class)
@ActiveProfiles("dev")
public class LeaderServiceTest {

    @Autowired
    private LeaderOrgMapper leaderOrgMapper;

    @Value("${tog-services.meeting}")
    private String meetingService;

    @Autowired
    private LeaderService leaderService;

    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void test1() {

        leaderService.toMeetingMessage(3L, null);

    }

    @Test
    public void test2() {

        try {
            leaderService.toUserTag(3963L, 3L);
        } catch (Exception e) {
            throw e;
        }


    }

}
