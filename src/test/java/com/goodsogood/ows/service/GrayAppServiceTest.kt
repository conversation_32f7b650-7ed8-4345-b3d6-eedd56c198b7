package com.goodsogood.ows.service

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@SpringBootTest
@ActiveProfiles("test")
internal class GrayAppServiceTest {
    val log: Logger = LoggerFactory.getLogger(GrayAppServiceTest::class.java)

    @Autowired
    val garyAppService: GrayAppService? = null

    @Test
    fun findByRegionId() {
        garyAppService?.findByRegionId(3)?.forEach {
            log.debug("GrayApp->regionId:${it.regionId},serverName:${it.serverName},createTime:${it.createTime},createUser:${it.createUser}")
        }
    }

    @Test
    fun updateApps() {
        garyAppService?.updateApps(listOf("WORKFLOW-TEST", "FILE-CENTER-TEST", "CLICK-SPY-TEST"), 3, 53592)?.forEach {
            log.debug("GrayApp->regionId:${it.regionId},serverName:${it.serverName},createTime:${it.createTime},createUser:${it.createUser}")
        }
    }
}