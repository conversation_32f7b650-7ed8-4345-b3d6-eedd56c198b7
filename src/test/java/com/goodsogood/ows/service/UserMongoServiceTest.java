package com.goodsogood.ows.service;

import com.goodsogood.ows.common.DateUtils;
import com.goodsogood.ows.model.mongo.User;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;
import java.util.Objects;

/**
 * UserMongoService Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 03/01/2021
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class UserMongoServiceTest {

    @Autowired
    UserMongoService userMongoService;


    /**
     * Method: insert(User user)
     */
    @Test
    public void testInsert() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: insertOrUpdateAll(List<User> userList)
     */
    @Test
    public void testInsertOrUpdateAll() throws Exception {
        //TODO: Test goes here...
    }

    /**
     * Method: remove(Long userId)
     */
    @Test
    public void testRemove() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: update(User user)
     */
    @Test
    public void testUpdate() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: getUserById(Long userId)
     */
    @Test
    public void testGetUserById() throws Exception {
        User user = this.userMongoService.getUserById(63511L);
        System.out.println(user);
        //TODO: Test goes here...
    }

    /**
     * Method: getUserByIds(List<Long> userIds)
     */
    @Test
    public void testGetUserByIds() throws Exception {
        //TODO: Test goes here...
    }

    /**
     * Method: getUserListByWhere(Map<String, Object> queryMap)
     */
    @Test
    public void testGetUserListByWhere() throws Exception {
        //TODO: Test goes here...
    }

    /**
     * Method: addAllUser()
     */
    @Test
    public void testAddAllUser() throws Exception {
        List<User> userList = this.userMongoService.getUserListByWhere(null);
        userList.forEach(user -> {
            if (Objects.nonNull(user.getJoiningTime())) {
                user.setJoiningTime(DateUtils.dateFormat(DateUtils.stringToDate(user.getJoiningTime(), null), "yyyy-MM-dd"));
            }
            if (Objects.nonNull(user.getJoiningPositiveTime())) {
                //user.setJoiningPositiveTime1(DateUtils.dateFormat(user.getJoiningPositiveTime(), "yyyy-MM-dd"));
            }
        });
        this.userMongoService.insertOrUpdateAll(userList);
        //this.userMongoService.addAllUser();;
    }

    /**
     * Method: conversionUser(Long userId, Map<String, Object> fieldMap, Long lastChangeUserId)
     */
    @Test
    public void testConversionUserForUserIdFieldMapLastChangeUserId() throws Exception {
        //TODO: Test goes here...
    }

    /**
     * Method: conversionUser(List<Long> userIds)
     */
    @Test
    public void testConversionUserUserIds() throws Exception {
        //TODO: Test goes here...
    }

    /**
     * Method: getUserList(Map<String, Object> queryMap, PageNumber pageNumber)
     */
    @Test
    public void testGetUserList() throws Exception {
//        Map<String, Object> map = new HashMap<>();
//        map.put("name", "安素兰1");
//        map.put("phone", "17723196076");
//        List<Map> userList = this.userMongoService.getUserList(map, new PageNumber(1, 10), null);
        //System.out.println("查询结果 -> [ "+ userList +" ]");
    }

    /**
     * Method: conversionUser(UserEntity userEntity, Map<String, Object> fieldMap, Long lastChangeUserId)
     */
    @Test
    public void testConversionUser() throws Exception {
        //TODO: Test goes here...
        /*
        try {
           Method method = UserMongoService.getClass().getMethod("conversionUser", UserEntity.class, Map<String,.class, Long.class);
           method.setAccessible(true);
           method.invoke(<Object>, <Parameters>);
        } catch(NoSuchMethodException e) {
        } catch(IllegalAccessException e) {
        } catch(InvocationTargetException e) {
        }
        */
    }

    /**
     * Method: getOption(String code, Long opKey)
     */
    @Test
    public void testGetOption() throws Exception {
        //TODO: Test goes here...
        /*
        try {
           Method method = UserMongoService.getClass().getMethod("getOption", String.class, Long.class);
           method.setAccessible(true);
           method.invoke(<Object>, <Parameters>);
        } catch(NoSuchMethodException e) {
        } catch(IllegalAccessException e) {
        } catch(InvocationTargetException e) {
        }
        */
    }

    /**
     * Method: getArea(Long adCode)
     */
    @Test
    public void testGetArea() throws Exception {
        //TODO: Test goes here...
        /*
        try {
           Method method = UserMongoService.getClass().getMethod("getArea", Long.class);
           method.setAccessible(true);
           method.invoke(<Object>, <Parameters>);
        } catch(NoSuchMethodException e) {
        } catch(IllegalAccessException e) {
        } catch(InvocationTargetException e) {
        }
        */
    }

    /**
     * Method: addDynamicFieldMysql(Long id, Integer type, Map<String, Object> fieldMap, Long userId)
     */
    @Test
    public void testAddDynamicFieldMysql() throws Exception {
        //TODO: Test goes here...
        /*
        try {
           Method method = UserMongoService.getClass().getMethod("addDynamicFieldMysql", Long.class, Integer.class, Map<String,.class, Long.class);
           method.setAccessible(true);
           method.invoke(<Object>, <Parameters>);
        } catch(NoSuchMethodException e) {
        } catch(IllegalAccessException e) {
        } catch(InvocationTargetException e) {
        }
        */
    }

}
