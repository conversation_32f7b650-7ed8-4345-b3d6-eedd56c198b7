package com.goodsogood.ows.service

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
@SpringBootTest
@ActiveProfiles("test")
internal class GrayUserServicesTest {
    val log: Logger = LoggerFactory.getLogger(GrayUserServicesTest::class.java)

    @Autowired
    val grayUserService: GrayUserService? = null

    @Test
    fun isGray() {
        log.debug("isGray->${grayUserService?.isGray("123456", 3)}")
    }

    @Test
    fun updateUsers() {
//        val users: List<String> = (0..10).map {
//            "12345$it"
//        }
        val users = listOf("ofhYawYLCAHtgjnO3uO_i2kjlMlo")
        grayUserService?.updateUsers(users, 3, 1)?.forEach {
            log.debug("GaryUser->thirdId:${it.thirdId},userId:${it.userId},userName:${it.userName}")
        }
    }

    @Test
    fun getUsers() {
        grayUserService?.getUsers(3)?.forEach {
            log.debug("GaryUser->thirdId:${it.thirdId},userId:${it.userId},userName:${it.userName}")
        }
    }
}