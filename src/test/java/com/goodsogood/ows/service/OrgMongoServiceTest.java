package com.goodsogood.ows.service;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * OrgMongoService Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 01/13/2021
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class OrgMongoServiceTest {

    @Autowired
    OrgMongoService orgMongoService;

    @Autowired
    OrgService orgService;

    /**
     * Method: addOrg(Org org)
     */
    @Test
    public void testAddOrg() throws Exception {

    }

    /**
     * Method: remove(Long orgId)
     */
    @Test
    public void testRemove() throws Exception {
        this.orgMongoService.remove(4685L);
    }

    /**
     * Method: update(Org org)
     */
    @Test
    public void testUpdate() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: getOrgById(long page, long pageSize, Long orgId)
     */
//    @Test
//    public void testGetOrgById() throws Exception {
//        List<Org> orgs = this.orgMongoService.getChildOrgById(1, 1, 4685L,3L);
//        orgs.forEach(System.out::println);
//    }

}