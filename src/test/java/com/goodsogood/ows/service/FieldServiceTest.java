package com.goodsogood.ows.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodsogood.ows.model.vo.FieldForm;
import com.goodsogood.ows.model.vo.FieldRuleForm;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * FieldService Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @date 01/20/2021
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles("dev")
@Log4j2
public class FieldServiceTest {
    private final static ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    FieldService fieldService;

    /**
     * Method: addField(FieldForm form, int own, Long userId)
     */
    @Test
    public void testAddField() throws Exception {
        FieldRuleForm fieldRuleForm = new FieldRuleForm();
        fieldRuleForm.setRuleType("8");
        fieldRuleForm.setMessage("格式出现错误");
        fieldRuleForm.setRequired(0);
        fieldRuleForm.setSingle(0);
        fieldRuleForm.setMaxLength(18L);
        fieldRuleForm.setMinLength(18L);
        int[] types = {3};
        fieldRuleForm.setCheckTypes(types);
        FieldForm fieldForm = new FieldForm();
        fieldForm.setAlias("企业编号");
        fieldForm.setIsShow(1);
        fieldForm.setOrgTreeId(2L);
        fieldForm.setFormOrder(1);
        fieldForm.setComponentType(1);
        fieldForm.setRule(fieldRuleForm);
        System.out.println(objectMapper.writeValueAsString(fieldRuleForm));
        //this.fieldService.addField(fieldForm, 2, 63511L,102803, null);
    }


    /**
     * Method: updateField(FieldForm form, int own, Long userId)
     */
    @Test
    public void testUpdateField() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: deleteField(Long fieldId, Long userId)
     */
    @Test
    public void testDeleteField() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: getField(Long fieldId)
     */
    @Test
    public void testGetField() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: getFieldList(Long orgTreeId, int own)
     */
    @Test
    public void testGetFieldList() throws Exception {
        //TODO: Test goes here... 
    }

    /**
     * Method: isNotExistAlias(String alias, Integer own, Long orgTreeId, Long fieldId)
     */
    @Test
    public void testIsNotExistAlias() throws Exception {
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = FieldService.getClass().getMethod("isNotExistAlias", String.class, Integer.class, Long.class, Long.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */
    }

    /**
     * Method: updateFormOrder(int formOrder, int own, Long orgTreeId, Long fieldId, Date date, Long userId)
     */
    @Test
    public void testUpdateFormOrder() throws Exception {
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = FieldService.getClass().getMethod("updateFormOrder", int.class, int.class, Long.class, Long.class, Date.class, Long.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */
    }

    /**
     * Method: getFieldName()
     */
    @Test
    public void testGetFieldName() throws Exception {
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = FieldService.getClass().getMethod("getFieldName"); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */
    }

    /**
     * Method: generateRegular(int[] types)
     */
    @Test
    public void testGenerateRegular() throws Exception {
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = FieldService.getClass().getMethod("generateRegular", int[].class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */
    }

    /**
     * Method: saveFieldHis(FieldEntity afterField, FieldRuleEntity afterFieldRule, FieldEntity beforeField, FieldRuleEntity beforeFieldRule, int type, Date date, Long userId)
     */
    @Test
    public void testSaveFieldHis() throws Exception {
        //TODO: Test goes here... 
        /* 
        try { 
           Method method = FieldService.getClass().getMethod("saveFieldHis", FieldEntity.class, FieldRuleEntity.class, FieldEntity.class, FieldRuleEntity.class, int.class, Date.class, Long.class); 
           method.setAccessible(true); 
           method.invoke(<Object>, <Parameters>); 
        } catch(NoSuchMethodException e) { 
        } catch(IllegalAccessException e) { 
        } catch(InvocationTargetException e) { 
        } 
        */
    }

} 
